<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

        <!-- Carrier -->

        <record id="product_product_delivery_poste" model="product.product">
            <field name="name">The Poste</field>
            <field name="default_code">Delivery_009</field>
            <field name="type">service</field>
            <field name="categ_id" ref="delivery.product_category_deliveries"/>
            <field name="sale_ok" eval="False"/>
            <field name="purchase_ok" eval="False"/>
            <field name="list_price">20.0</field>
            <field name="invoice_policy">order</field>
        </record>

        <record id="delivery_carrier" model="delivery.carrier">
            <field name="name">The Poste</field>
            <field name="fixed_price">20.0</field>
            <field name="sequence">2</field>
            <field name="delivery_type">base_on_rule</field>
            <field name="product_id" ref="delivery.product_product_delivery_poste"/>
        </record>

        <!-- Local Delivery -->
        <record id="product.product_product_local_delivery" model="product.product">
            <field name="categ_id" ref="delivery.product_category_deliveries"/>
        </record>

        <record id="delivery_local_delivery" model="delivery.carrier">
            <field name="name">Local Delivery</field>
            <field name="fixed_price">5.0</field>
            <field name="free_over" eval="True"/>
            <field name="amount">50</field>
            <field name="sequence">4</field>
            <field name="delivery_type">fixed</field>
            <field name="product_id" ref="product.product_product_local_delivery"/>
        </record>

        <record id="delivery_price_rule1" model="delivery.price.rule">
            <field name="carrier_id" ref="delivery_carrier"/>
            <field eval="5" name="max_value"/>
            <field eval="20" name="list_base_price"/>
        </record>
        <!--  delivery charge of product if weight more than 5kg-->
        <record id="delivery_price_rule2" model="delivery.price.rule">
            <field name="carrier_id" ref="delivery_carrier"/>
            <field name="operator">&gt;=</field>
            <field eval="5" name="max_value"/>
            <field eval="50" name="list_base_price"/>
        </record>

        <!--  free delivery charge if price more than 300-->
        <record id="delivery_price_rule3" model="delivery.price.rule">
            <field name="carrier_id" ref="delivery_carrier"/>
            <field eval="300" name="max_value"/>
            <field name="operator">&gt;=</field>
            <field name="variable">price</field>
            <field eval="0" name="list_base_price"/>
        </record>

        <function model="ir.default" name="set" eval="('res.partner', 'property_delivery_carrier_id', obj().env.ref('delivery.delivery_local_delivery').id)"/>

</odoo>
