<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="project_create_invoice_view_form" model="ir.ui.view">
        <field name="name">project.create.invoice.view.form</field>
        <field name="model">project.create.invoice</field>
        <field name="arch" type="xml">
            <form string="Create Sales Order from Project">
                <group>
                    <field name="project_id" readonly="1"/>
                    <field name="_candidate_orders" invisible="1"/>
                    <field name="sale_order_id" options="{'no_create_edit': True}" context="{'sale_show_partner_name': True}"/>
                    <field name="amount_to_invoice"/>
                </group>
                <footer>
                    <button string="Create Invoice" type="object" name="action_create_invoice" class="oe_highlight" data-hotkey="q"/>
                    <button string="Discard" special="cancel" data-hotkey="x" type="object" class="btn btn-secondary oe_inline"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="project_project_action_multi_create_invoice" model="ir.actions.act_window">
        <field name="name">Create Invoice</field>
        <field name="res_model">project.create.invoice</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="project_create_invoice_view_form"/>
        <field name="target">new</field>
    </record>

</odoo>
