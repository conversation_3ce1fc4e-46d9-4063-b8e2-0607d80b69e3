# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sign
# 
# Translators:
# <PERSON>il <PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Abe Manyo, 2025\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_partner__signature_count
#: model:ir.model.fields,field_description:sign.field_res_users__signature_count
msgid "# Signatures"
msgstr "# Tanda Tangan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "#7898678"
msgstr "#7898678"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid ""
"%(partner)s validated the signature by SMS with the phone number "
"%(phone_number)s."
msgstr ""
"%(partner)s memvalidasi tanda tangan menggunakan SMS dengan nomor telepon "
"%(phone_number)s."

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid "%s couldn't sign the document due to an insufficient credit error."
msgstr ""
"%s tidak dapat menandatangani dokumen oleh karena error kredit tidak "
"mencukupi."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "%s has been edited and signed"
msgstr "%s telah diedit dan ditandatangani"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "%s has been linked to this sign request."
msgstr "%s telah dihubungkan ke permintaan tanda tangan ini."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "%s has been signed"
msgstr "%s telah ditandatangani"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "%s: missing credits for extra-authentication"
msgstr "%s: kredit hilang untuk autentikasi-ekstra"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "(UTC)"
msgstr "(UTC)"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
msgid ""
")\n"
"            has refused the document"
msgstr ""
")\n"
"            telah menolak dokumen"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid ""
")\n"
"        has requested your signature on the document"
msgstr ""
")\n"
"        telah meminta tandatangan Anda pada dokumen"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "******1234"
msgstr "******1234"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid ""
"- <em>\n"
"                                            Waiting Signature\n"
"                                        </em>"
msgstr ""
"- <em>\n"
"                                            Menunggu Tandatangan\n"
"                                        </em>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "- <em>Cancelled</em>"
msgstr "- <em>Dibatalkan</em>"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "- or -"
msgstr "- atau -"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "1001"
msgstr "1001"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "192.168.1.1"
msgstr "192.168.1.1"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "20-03-2000"
msgstr "20-03-2000"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "2023-08-18"
msgstr "2023-08-18"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "2023-08-18 - 12:30:45"
msgstr "2023-08-18 - 12:30:45"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Created by:</b>"
msgstr "<b>Dibuat oleh:</b>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Created on:</b>"
msgstr "<b>Dibuat pada:</b>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Creation IP Address:</b>"
msgstr "<b>Pembuatan Alamat IP:</b>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Document ID:</b>"
msgstr "<b>ID Dokumen:</b>"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "<b>Drag & drop “Signature”</b> into the bottom of the document."
msgstr "<b>Tarik & Lepas “Tandatangan”</b> ke bagian bawah dokumen."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Gas:</b> 18,700 Pounds of CO² = 18,700*0.4536 = 8482.32 kg"
msgstr "<b>Gas:</b> 18,700 Pounds CO² = 18,700*0.4536 = 8482.32 kg"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Signature:</b>"
msgstr "<b>Tanda Tangan:</b>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Signers:</b>"
msgstr "<b>Penandatangan:</b>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Solid Waste:</b> 1290 Pounds = 1290*0.4536 = 585.14 kg"
msgstr "<b>Limbah Padat:</b> 1290 Pounds = 1290*0.4536 = 585.14 kg"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Total Energy:</b> 27 Millions BTU = 27,000,000*0.0002931 = 7.91 kWh"
msgstr "<b>Total Energy:</b> 27 Juta BTU = 27,000,000*0.0002931 = 7.91 kWh"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Water:</b> 23,400 Gallons = 23,400*3.78541 = 88578.59 L"
msgstr "<b>Air:</b> 23,400 Gallons = 23,400*3.78541 = 88578.59 L"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Wood Use:</b> 4 US Short Tons = 4*907.18474 = 3628.73 kg"
msgstr "<b>Penggunaan Kayu:</b> 4 US Ton = 4*907.18474 = 3628.73 kg"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "<br/>(the email access has not been sent)"
msgstr "<br/>(akses email belum dikirim)"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid "<i class=\"fa fa-check\"/> A fresh link has just been sent to your inbox!"
msgstr "<i class=\"fa fa-check\"/> Link baru saja dikirim ke inbox Anda!"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<i class=\"fa fa-check\"/> The document's integrity is valid."
msgstr "<i class=\"fa fa-check\"/> Integritas dokumen valid."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid ""
"<i class=\"fa fa-exclamation-circle\"/> The document's integrity could not "
"be verified."
msgstr ""
"<i class=\"fa fa-exclamation-circle\"/> Integritas dokumen tidak dapat "
"diverifikasi."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<i class=\"fa fa-globe\"/> View"
msgstr "<i class=\"fa fa-globe\"/> Lihat"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid ""
"<i class=\"fa fa-info-circle\"/> Links sent via email expire after a set "
"delay to increase security."
msgstr ""
"<i class=\"fa fa-info-circle\"/> Links dikirim melalui email kadaluwarsa "
"setelah jeda untuk meningkatkan keamanan."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_terms_conditions_setting_banner
msgid "<i class=\"oi oi-arrow-right me-1\"/>Back to settings"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>Kembali ke pengaturan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                            Preview"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                            Pratinjau"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "<small><i> Cancelled </i></small>"
msgstr "<small><i> Dibatalkan </i></small>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "<small><i> Waiting Signature </i></small>"
msgstr "<small><i> Menunggu Tandatangan </i></small>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid ""
"<small>Email Verification: The signatory has confirmed control of their "
"email inbox by clicking on a unique link</small>"
msgstr ""
"<small>Verifikasi Email: Penandatangan telah mengonfirmasi kontrol email "
"inbox mereka dengan mengeklik pada link unik</small>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid ""
"<small>SMS: The signatory has confirmed their control of the phone number "
"using a unique code sent by SMS</small>"
msgstr ""
"<small>SMS: Penandatangan telah mengonfirmasi kontrol telepon mereka "
"menggunakan kode unik yang dikirim melalui SMS</small>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_partner_view_form
msgid "<span class=\"o_stat_text\">Signature Requested</span>"
msgstr "<span class=\"o_stat_text\">Tandatangan Diminta</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "<span class=\"o_stat_text\">Signed Document</span>"
msgstr "<span class=\"o_stat_text\">Dokumen Ditandatangani</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_share_view_form
msgid ""
"<span class=\"text-muted\" invisible=\"not share_link\">Sharing will create "
"a copy of the file to sign. That file can be reached by the link below. "
"Every public user using the link will generate a document when the Signature"
" is complete. The link is private, only those that receive the link will be "
"able to sign it.</span>"
msgstr ""
"<span class=\"text-muted\" invisible=\"not share_link\">Sharing akan membuat"
" salinan file untuk ditandatangani. File akan dicapai dengan link di bawah. "
"Setiap user publik yang menggunakan link akan membuat dokumen saat "
"Tandatangan selesai. Link bersifat pribadi, hanya mereka yang menerima link "
"akan dapat menandatangani-nya.</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<span class=\"text-muted\">Not available</span>"
msgstr "<span class=\"text-muted\">Tidak tersedia</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_type_view_form
msgid "<span>(1.0 = full page size)</span>"
msgstr "<span>(1.0 = halaman ukuran penuh)</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_terms_conditions_setting_banner
msgid "<span>This is a preview of your Terms &amp; Conditions.</span>"
msgstr "<span>Ini adalah pratinjau dari Syarat &amp; Ketentuan anda.</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.cancel_sign_request_item_with_confirmation
msgid ""
"<span>You won't receive any notification for this signature request "
"anymore.</span>"
msgstr ""
"<span>Anda tidak akan menerima notifikasi apa pun untuk permintaan tanda "
"tangan ini lagi.</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "<strong>Creation Date:</strong>"
msgstr "<strong>Tanggal Pembuatan:</strong>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
msgid ""
"<strong>Warning</strong> do not forward this email to other people!<br/>"
msgstr ""
"<strong>Peringatan</strong> jangan forward email ini ke orang lain!<br/>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid ""
"<strong>Warning</strong> do not forward this email to other people!<br/>\n"
"            They will be able to access this document and sign it as yourself.<br/>\n"
"            <span>Your IP address and localization are associated to your signature to ensure traceability.</span>"
msgstr ""
"<strong>Peringatan</strong> jangan forward email ini ke orang lain!<br/>\n"
"            Mereka akan dapat mengakses dokumen ini dan memberikan tanda tangan sebagai Anda.<br/>\n"
"            <span>IP address dan lokalisasi terhubung ke tanda tangan Anda untuk memastikan ketertelusuran.</span>"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid ""
"A SMS will be sent to the following phone number. Please update it if it's "
"not relevant."
msgstr ""
"SMS akan dikirim ke nomor telepon berikut. Mohon update bila tidak relevan."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.message_signature_link
msgid "A document has been signed and a copy attached to"
msgstr "Dokumen telah ditandatangani dan salinan dilampirkan ke"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"A non-shared sign request's should not have any signer with an empty partner"
msgstr ""
"Permintaan tanda tangan yang tidak dibagikan seharusnya tidak memiliki "
"penandatangan dengan partner kosong"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"A shared sign request should only have one signer with an empty partner"
msgstr ""
"Permintaan tanda tangan yang dibagikan seharusnya hanya memiliki satu "
"penandatangan dengan partner kosong"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "A shower uses approximately 65 L of water"
msgstr "Shower menggunakan kurang lebih 65 L air"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "A signature request has been linked to this document: %s"
msgstr "Permintaan tandatangan telah dihubungkan ke dokumen ini: %s"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "A valid sign request needs at least one sign request item"
msgstr ""
"Permintaan tanda tangan yang valid membutuhkan setidaknya satu item "
"permintaan tanda tangan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "ABCD1234"
msgstr "ABCD1234"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Access Logs"
msgstr "Akses Log"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__access_warning
msgid "Access warning"
msgstr "Peringatan akses"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.portal_my_home_sign
msgid "Access your signed documents"
msgstr "Akses dokumen Anda yang sudah ditandatangani"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__access_via_link
msgid "Accessed Through Token"
msgstr "Diakses Melalui Token"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_mail_activity_type__category
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Action"
msgstr "Tindakan"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_needaction
msgid "Action Needed"
msgstr "Tindakan Diperluka"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__action
msgid "Action Performed"
msgstr "Action Dilakukan"

#. module: sign
#: model:ir.model.fields,help:sign.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Tindakan dapat memicu perilaku spesifik seperti membuka tampilan kalender "
"atau secara otomatis ditandai sebagai selesai saat dokumen diunggah"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__active
#: model:ir.model.fields,field_description:sign.field_sign_template__active
msgid "Active"
msgstr "Aktif"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_ids
msgid "Activities"
msgstr "Aktivitas"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekorasi Pengecualian Aktivitas"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
#: model:ir.actions.report,name:sign.action_sign_request_print_logs
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Activity Logs"
msgstr "Log Kegiatan"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_state
msgid "Activity State"
msgstr "Status Aktivitas"

#. module: sign
#: model:ir.model,name:sign.model_mail_activity_type
msgid "Activity Type"
msgstr "Tipe Aktivitas"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikon Jenis Aktifitas"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__sign_log_ids
msgid "Activity logs linked to this request"
msgstr "Log kegiatan dilink ke permintaan ini"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/initials_all_pages_dialog.js:0
msgid "Add Initials"
msgstr "Tambahkan Inisial"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/initial_all_pages_dialog.xml:0
msgid "Add Once"
msgstr "Tambahkan Sekali"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Add document tags here"
msgstr "Tambahkan tag dokumen di sini"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/initial_all_pages_dialog.xml:0
msgid "Add to all pages"
msgstr "Tambahkan ke semua halaman"

#. module: sign
#: model:res.groups,name:sign.group_sign_manager
msgid "Administrator"
msgstr "Administrator"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.js:0
msgid "Adopt Your Signature"
msgstr "Tambahkan Tanda Tangan Anda"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__signed
msgid "After Signature"
msgstr "Setelah Tanda Tangan"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
#: model:ir.model.fields,field_description:sign.field_sign_item__alignment
msgid "Alignment"
msgstr "Alignment"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "All"
msgstr "Semua"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_all_request_action
#: model:ir.ui.menu,name:sign.sign_request_documents
msgid "All Documents"
msgstr "Semua Dokumen"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "All signers must have valid email addresses"
msgstr "Semua penandatangan harus memiliki alamat email valid"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"Allow signatories to provide their identity using itsme® (available in "
"Belgium and the Netherlands)."
msgstr ""
"Izinkan penandatangan untuk menyediakan identitas mereka menggunakan itsme® "
"(tersedia di Belgia dan Belanda)."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"Allow users to define the users or groups which have access to the template."
msgstr ""
"Izinkan user untuk mendefinisikan user atau kelompok yang memiliki akses ke "
"templat."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "An average computer will consume 750 Wh"
msgstr "Komputer rata-rata akan menggunakan 750 Wh"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Antwrep"
msgstr "Antwrep"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Archive"
msgstr "Arsip"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Archived"
msgstr "Diarsipkan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.cancel_sign_request_item_with_confirmation
msgid "Are you sure you want to cancel the sign request?"
msgstr "Apakah Anda yakin ingin membatalkan permintaan tanda tangan?"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__start_sign
msgid "At least one signer has signed the document."
msgstr "Setidakyna satu penandatangan telah menandatangani dokumen."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Attach a file"
msgstr "Lampirkan file"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__attachment_id
msgid "Attachment"
msgstr "Lampiran"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_attachment_count
msgid "Attachment Count"
msgstr "Hitungan Lampiran"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__attachment_ids
#: model:ir.model.fields,field_description:sign.field_sign_send_request__attachment_ids
msgid "Attachments"
msgstr "Lampiran"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Authenticate by SMS"
msgstr "Autentikasi dengan SMS"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__group_ids
msgid "Authorized Groups"
msgstr "Kelompok Berwenang"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Authorized Groups:"
msgstr "Kelompok Berwenang:"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__authorized_ids
msgid "Authorized Users"
msgstr "Pengguna Berwenang"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Authorized Users:"
msgstr "Pengguna Berwenang:"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__auto_field
msgid "Auto-fill Partner Field"
msgstr "Isi-otomatis Field Partner"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_option__available
msgid "Available in new templates"
msgstr "Tersedia di templat baru"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Back to %s"
msgstr "Kembali ke %s"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "Based on"
msgstr "Berdasarkan pada"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "Based on various websites, here are our comparisons:"
msgstr "Berdasarkan beragam website, berikut perbandingan kami:"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__sent
msgid "Before Signature"
msgstr "Sebelum Tanda Tangan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "By"
msgstr "Oleh"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
msgid ""
"By clicking Adopt & Sign, I agree that the chosen signature/initials will be"
" a valid electronic representation of my hand-written signature/initials for"
" all purposes when it is used on documents, including legally binding "
"contracts."
msgstr ""
"Dengan mengeklik tombol Ambil dan Tandai, saya setuju bahwa tanda "
"tangan/inisial tersebut akan menjadi representasi elektronik sah dari tanda "
"tangan/inisial untuk semua keperluan saat digunakan pada dokumen, termasuk "
"kontrak yang mengikat."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__message_cc
msgid "CC Message"
msgstr "CC Pesan"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
#: code:addons/sign/static/src/dialogs/public_signer_dialog.xml:0
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.xml:0
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__cancel
#: model_terms:ir.ui.view,arch_db:sign.sign_duplicate_template_with_pdf_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Cancel"
msgstr "Batal"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.cancel_sign_request_item_with_confirmation
msgid "Cancel Sign Request"
msgstr "Batalkan Permintaan Tanda Tangan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
msgid "Canceled"
msgstr "Dibatalkan"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__canceled
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__canceled
#: model:ir.model.fields.selection,name:sign.selection__sign_request_item__state__canceled
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "Cancelled"
msgstr "Dibatalkan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Carbon Emissions"
msgstr "Emisi Karbon"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.js:0
msgid "Center"
msgstr "Tengah"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Certificate <i class=\"fa fa-download\"/>"
msgstr "Sertifikat <i class=\"fa fa-download\"/>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Certificate of Completion<br/>"
msgstr "Sertifikat Kelulusan<br/>"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__change_authorized
#: model:ir.model.fields,field_description:sign.field_sign_request_item__change_authorized
msgid "Change Authorized"
msgstr "Ubah Otorisasi"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__checkbox
#: model:sign.item.type,name:sign.sign_item_type_checkbox
msgid "Checkbox"
msgstr "Kotak centang"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_template__is_sharing
msgid "Checked if this template has created a shared document for you"
msgstr "Centang bila templat ini telah membuat dokumen yang dibagikan ke Anda"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_item_navigator.js:0
msgid "Click to start"
msgstr "Klik untuk mulai"

#. module: sign
#. odoo-javascript
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
#: code:addons/sign/static/src/dialogs/thank_you_dialog.js:0
#: code:addons/sign/static/src/js/tours/sign.js:0
#: model:sign.template,redirect_url_text:sign.template_sign_1
#: model:sign.template,redirect_url_text:sign.template_sign_2
#: model:sign.template,redirect_url_text:sign.template_sign_3
#: model:sign.template,redirect_url_text:sign.template_sign_4
#: model:sign.template,redirect_url_text:sign.template_sign_5
#: model:sign.template,redirect_url_text:sign.template_sign_tour
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
#: model_terms:ir.ui.view,arch_db:sign.sign_terms_conditions_setting_banner
msgid "Close"
msgstr "Tutup"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__color
#: model:ir.model.fields,field_description:sign.field_sign_request__color
#: model:ir.model.fields,field_description:sign.field_sign_request_item__color
#: model:ir.model.fields,field_description:sign.field_sign_template__color
msgid "Color"
msgstr "Warna"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__color
msgid "Color Index"
msgstr "Indeks Warna"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Communication history"
msgstr "Sejarah komunikasi"

#. module: sign
#: model:ir.model,name:sign.model_res_company
msgid "Companies"
msgstr "Perusahaan"

#. module: sign
#: model:sign.item.type,name:sign.sign_item_type_company
#: model:sign.item.type,placeholder:sign.sign_item_type_company
msgid "Company"
msgstr "Perusahaan"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__communication_company_id
#: model:ir.model.fields,field_description:sign.field_sign_request_item__communication_company_id
msgid "Company used for communication"
msgstr "Perusahaan yang digunakan untuk komunikasi"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
#: model:ir.model.fields.selection,name:sign.selection__sign_request_item__state__completed
msgid "Completed"
msgstr "Selesai"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__completed_document
msgid "Completed Document"
msgstr "Dokumen yang Selesai"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__completed_document_attachment_ids
msgid "Completed Documents"
msgstr "Dokumen-Dokumen yang Selesai"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__nb_closed
msgid "Completed Signatures"
msgstr "Tandatangan yang Selesai"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__completion_date
msgid "Completion Date"
msgstr "Tanggal Selesai"

#. module: sign
#: model:ir.model,name:sign.model_res_config_settings
msgid "Config Settings"
msgstr "Pengaturan Konfigurasi"

#. module: sign
#: model:ir.ui.menu,name:sign.menu_sign_configuration
msgid "Configuration"
msgstr "Konfigurasi"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"Configure the field types that can be used to sign documents (placeholder, "
"auto-completion, ...), as well as the values for selection fields in "
"signable documents."
msgstr ""
"Konfigurasikan tipe field yang dapat digunakan untuk menandatangani dokumen "
"(placeholder, penyelesaian otomatis, ...), sekaligus value untuk field "
"pilihan di dokumen yang ditandatangani."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_duplicate_template_with_pdf_view_form
msgid "Confirm"
msgstr "Konfirmasi"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Congrats, your signature is ready to be submitted!"
msgstr "Selamat, tanda tangan Anda siap untuk diserahkan!"

#. module: sign
#: model_terms:web_tour.tour,rainbow_man_message:sign.sign_tour
msgid "Congratulations, you signed your first document!"
msgstr "Selamat, Anda menandatangani dokumen pertama Anda!"

#. module: sign
#: model:ir.model,name:sign.model_res_partner
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__partner_id
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Contact"
msgstr "Kontak"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Contacts"
msgstr "Kontak"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Contacts in copy"
msgstr "Kontrak di salin"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__cc_partner_ids
msgid ""
"Contacts in copy will be notified by email once the document is either fully"
" signed or refused."
msgstr ""
"Kontrak di slain akan dinotifikasi melalui email setelah dokumen sepenuhnya "
"ditandatangani atau ditolak."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__cc_partner_ids
#: model:ir.model.fields,field_description:sign.field_sign_send_request__cc_partner_ids
msgid "Copy to"
msgstr "Salin ke"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_tag_action
msgid "Create Sign Tags"
msgstr "Buat Tag Tandatanga"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
msgid "Create date"
msgstr "Buat tanggal"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_option__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_role__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_type__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_log__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_request__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_request_item__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_send_request__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_template__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item_option__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item_role__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item_type__create_date
#: model:ir.model.fields,field_description:sign.field_sign_log__create_date
#: model:ir.model.fields,field_description:sign.field_sign_request__create_date
#: model:ir.model.fields,field_description:sign.field_sign_request_item__create_date
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__create_date
#: model:ir.model.fields,field_description:sign.field_sign_send_request__create_date
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__create_date
#: model:ir.model.fields,field_description:sign.field_sign_template__create_date
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__create
msgid "Creation"
msgstr "Pembuatan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Current status of the signature request"
msgstr "Status saat ini permintaan tandatangan"

#. module: sign
#: model:sign.item.role,name:sign.sign_item_role_customer
msgid "Customer"
msgstr "Pelanggan"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request_item__access_url
msgid "Customer Portal URL"
msgstr "Customer Portal URL"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
#: model:sign.item.type,name:sign.sign_item_type_date
#: model:sign.item.type,placeholder:sign.sign_item_type_date
msgid "Date"
msgstr "Tanggal"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Date (UTC)"
msgstr "Tanggal (UTC)"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__default
msgid "Default"
msgstr "Default"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__default_height
msgid "Default Height"
msgstr "Tinggi Default"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_mail_activity_type__default_sign_template_id
msgid "Default Signature Template"
msgstr "Templat Tanda Tangan Default"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__default_width
msgid "Default Width"
msgstr "Lebar Default"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__sequence
msgid "Default order"
msgstr "Order default"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Delete"
msgstr "Hapus"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
msgid "Delete sign"
msgstr "Hapus tandatangan"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
msgid "Delete sign item"
msgstr "Hapus item tandatangan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"Deliver one-time codes by SMS to identify signatories when signing a "
"document."
msgstr ""
"Kirim kode sekali-pakai melalui SMS untuk mengidentifikasi penandatangan "
"saat menandatangani dokumen."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo Action"
msgstr "Action Demo"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo IP"
msgstr "IP Demo"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo Latitude"
msgstr "Lintang demo"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo Longitude"
msgstr "Bujur Demo"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo Partner"
msgstr "Partner Demo"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
msgid "Details"
msgstr "Perincian"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_users__sign_initials
msgid "Digital Initials"
msgstr "Inisial Digital"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_users__sign_initials_frame
msgid "Digital Initials Frame"
msgstr "Frame Inisial Digital"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_users__sign_signature
msgid "Digital Signature"
msgstr "Tandatangan Digital"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_users__sign_signature_frame
msgid "Digital Signature Frame"
msgstr "Frame Tandatangan Digital"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_users_view_form
#: model_terms:ir.ui.view,arch_db:sign.view_users_form_simple_modif
msgid "Digital Signatures"
msgstr "Tandatangan-Tandatangan Digital"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_view_form
msgid "Display"
msgstr "Tampilan"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item_option__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item_role__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item_type__display_name
#: model:ir.model.fields,field_description:sign.field_sign_log__display_name
#: model:ir.model.fields,field_description:sign.field_sign_request__display_name
#: model:ir.model.fields,field_description:sign.field_sign_request_item__display_name
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__display_name
#: model:ir.model.fields,field_description:sign.field_sign_send_request__display_name
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__display_name
#: model:ir.model.fields,field_description:sign.field_sign_template__display_name
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__sign_preview_ready
msgid "Display sign preview button"
msgstr "Tampilkan tombol pratinjau tandatangan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Document"
msgstr "Dokumen"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Document <i class=\"fa fa-download\"/>"
msgstr "Dokumen <i class=\"fa fa-download\"/>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Document Details"
msgstr "Rincian Dokumen"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__reference
#: model:ir.model.fields,field_description:sign.field_sign_request_item__reference
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_search
msgid "Document Name"
msgstr "Nama Dokumen"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__page
msgid "Document Page"
msgstr "Halaman Dokumen"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__template_id
msgid "Document Template"
msgstr "Contoh Dokumen"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.js:0
msgid "Document saved as Template."
msgstr "Dokumen disimpan sebagai Templat."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Document to send for signature or to sign yourself."
msgstr "Dokumen untuk dikirim untuk ditandatangani atau tandatangani sendiri."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.portal_my_home_sign
msgid "Document(s) to sign"
msgstr "Dokumen untuk ditandatangani"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Document/Signer"
msgstr "Dokumen/Penandatangan"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_request_action
#: model:ir.ui.menu,name:sign.sign_request_menu
msgid "Documents"
msgstr "Dokumen"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
msgid "Download"
msgstr "Unduh"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
msgid "Download Certificate"
msgstr "Unduh Sertifikat"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Download Document"
msgstr "Unduh Dokumen"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_type_buttons.xml:0
msgid "Drag & Drop a field in the PDF"
msgstr "Tarik & Lepas field in PDF"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Drag and drop"
msgstr "Tarik dan lepas"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"Draw your most beautiful signature!<br>You can also create one automatically"
" or load a signature from your computer."
msgstr ""
"Tulis tandatangan Anda yang paling indah!<br>Anda juga dapat membuatnya "
"otomatis atau memuat tandatangan dari komputer Anda."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Dropdown menu"
msgstr "Menu dropdown"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "ERROR: Invalid PDF file!"
msgstr "ERROR: File PDF tidak valid!"

#. module: sign
#: model:ir.actions.report,name:sign.sign_report_green_savings_action
msgid "Ecological Savings by using Electronic Signatures"
msgstr "Ramah Lingkungan dengan menggunakan Tandatangan Elektronik"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/signable_sign_request_control_panel.xml:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Edit"
msgstr "Edit"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.js:0
msgid "Edit Template"
msgstr "Edit Templat"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Edit field types"
msgstr "Edit tipe field"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Edit selection values"
msgstr "Edit value pilihan"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Edit template name"
msgstr "Edit nama templat"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__signer_email
#: model:sign.item.type,name:sign.sign_item_type_email
#: model:sign.item.type,placeholder:sign.sign_item_type_email
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Email"
msgstr "Email"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__subject
msgid "Email Subject"
msgstr "Subject Email"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Email Verification"
msgstr "Verifikasi Email"

#. module: sign
#: model:sign.item.role,name:sign.sign_item_role_employee
msgid "Employee"
msgstr "Karyawan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Energy"
msgstr "Energi"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Enter the code received through SMS to complete your signature"
msgstr ""
"Masukkan kode yang diterima melalui SMS untuk menyelesaikan tandatangan Anda"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.js:0
msgid "Error"
msgstr "Error!"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid "Error 404"
msgstr "Error 404"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Existing sign items are not allowed to be changed"
msgstr "Item tandatangan yang tersedia tidak diizinkan untuk diubah"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__expired
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__expired
msgid "Expired"
msgstr "Berakhir"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Expiring Soon"
msgstr "Sebentar Lagi Kadaluwarsa"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__auth_method
msgid "Extra Authentication Step"
msgstr "Langkah Autentikasi Ekstra"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__favorited_ids
msgid "Favorite of"
msgstr "Favorit dari"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__favorited_ids
msgid "Favorited Users"
msgstr "User Favorit"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__name
#: model:ir.model.fields,field_description:sign.field_sign_item_type__name
msgid "Field Name"
msgstr "Nama Kolom"

#. module: sign
#: model:ir.ui.menu,name:sign.sign_item_type_menu
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Field Types"
msgstr "Tipe-Tipe Field"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_type_buttons.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Fields"
msgstr "Kolom"

#. module: sign
#: model:ir.model,name:sign.model_sign_item
msgid "Fields to be sign on Document"
msgstr "Field untuk ditandatangani pada Dokume"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__datas
msgid "File Content (base64)"
msgstr "Konten File (base64)"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/hooks.js:0
msgid "File Error"
msgstr "File Error"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__new_pdf
msgid "File name"
msgstr "Nama file"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__filename
msgid "Filename"
msgstr "Nama file"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
msgid "Filled by"
msgstr "Diisi oleh"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/public_signer_dialog.js:0
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.js:0
msgid "Final Validation"
msgstr "Akhir validasi"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Follow the guide to sign the document."
msgstr "Ikuti pedoman untuk menandatangani dokumen."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_follower_ids
msgid "Followers"
msgstr "Follower"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_partner_ids
msgid "Followers (Partners)"
msgstr "Follower (Mitra)"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Ikon font awesome, misalnya fa-tasks"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid ""
"For 1000kg of paper usage, with 10% of recycled paper, environmental savings"
" are based on"
msgstr ""
"Untuk 1000kg penggunaan kertas, dengan 10% kertas didaur ulang, perhitungan "
"ramah lingkungan didasarkan pada"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_item_role__auth_method
msgid "Force the signatory to identify using a second authentication method"
msgstr ""
"Paksa penandatangan untuk mengidentifikasi mereka menggunakan metode "
"autentikasi kedua"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
msgid "Frame"
msgstr "Frame"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__frame_has_hash
msgid "Frame Has Hash"
msgstr "Frame Memiliki Hash"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__frame_hash
msgid "Frame Hash"
msgstr "Frame Hash"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__frame_value
msgid "Frame Value"
msgstr "Frame Value"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__signed
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Fully Signed"
msgstr "Sepenuhnya Ditandatangani"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Future Activities"
msgstr "Kegiatan - Kegiatan Mendatang"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/encrypted_dialog.xml:0
msgid "Generate PDF"
msgstr "Buat PDF"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Geolocation"
msgstr "Geolokasi"

#. module: sign
#: model:ir.ui.menu,name:sign.sign_report_green_savings
msgid "Green Savings"
msgstr "Green Savings"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Green Savings Report"
msgstr "Laporan Green Savings"

#. module: sign
#: model:ir.model,name:sign.model_report_sign_green_savings_report
msgid "Green Savings Report model"
msgstr "Model Laporan Green Savings"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
msgid "Green Savings Summary"
msgstr "Ringkasan Green Savings"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Group By"
msgstr "Dikelompokkan berdasarkan"

#. module: sign
#: model:sign.template.tag,name:sign.sign_template_tag_1
msgid "HR"
msgstr "HR"

#. module: sign
#: model:ir.model,name:sign.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP routing"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__has_default_template
msgid "Has Default Template"
msgstr "Memiliki Templat Default"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__has_message
msgid "Has Message"
msgstr "Memiliki Pesan"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__has_sign_requests
msgid "Has Sign Requests"
msgstr "Memiliki Permintaan Tandatangan"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__height
msgid "Height"
msgstr "Tinggi"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "Hello"
msgstr "Halo"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_item_type__tip
msgid "Hint displayed in the signing hint"
msgstr "Hint ditampilkan di hint tandatangan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Home"
msgstr "Beranda"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
msgid "How are these results calculated?"
msgstr "Bagaimana hasil-hasil ini dihitung?"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "How do we calculate?"
msgstr "Bagaimana cara kami menghitung?"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "I've got a total weight, and now?"
msgstr "Saya memiliki total berat, dan sekarang?"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__id
#: model:ir.model.fields,field_description:sign.field_sign_item__id
#: model:ir.model.fields,field_description:sign.field_sign_item_option__id
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__id
#: model:ir.model.fields,field_description:sign.field_sign_item_role__id
#: model:ir.model.fields,field_description:sign.field_sign_item_type__id
#: model:ir.model.fields,field_description:sign.field_sign_log__id
#: model:ir.model.fields,field_description:sign.field_sign_request__id
#: model:ir.model.fields,field_description:sign.field_sign_request_item__id
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__id
#: model:ir.model.fields,field_description:sign.field_sign_send_request__id
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__id
#: model:ir.model.fields,field_description:sign.field_sign_template__id
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__id
msgid "ID"
msgstr "ID"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "IP"
msgstr "IP"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "IP Address"
msgstr "Alamat IP"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__ip
msgid "IP address of the visitor"
msgstr "Alamat IP pengunjung"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__icon
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon untuk menunjukkan sebuah aktivitas pengecualian."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__module_sign_itsme
msgid "Identify with itsme®"
msgstr "Identifikasi dengan itsme®"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Jika dicentang, pesan baru memerlukan penanganan dan perhatian Anda."

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_item_role__change_authorized
#: model:ir.model.fields,help:sign.field_sign_request_item__change_authorized
msgid ""
"If checked, recipient of a document with this role can be changed after "
"having sent the request. Useful to replace a signatory who is out of office,"
" etc."
msgstr ""
"Bila dicentang, penerima dokumen dengan peran ini akan diubah setelah "
"mengirim permintaan. Berguna untuk mengganti penandatangan yang diluar "
"kantor, dsb."

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__message_has_error
#: model:ir.model.fields,help:sign.field_sign_request__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Jika dicentang, beberapa pesan mempunyai kesalahan dalam pengiriman."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid ""
"If you do not want to receive these notifications anymore, you can disable the extra-authentication step in the\n"
"                        <code>\n"
"                            Sign &gt; Configuration &gt; Roles\n"
"                        </code>\n"
"                        menu."
msgstr ""
"Bila Anda tidak ingin menerima notifikasi ini lagi, Anda dapat menonaktifkan langkah autentikasi ekstra di menu\n"
"                        <code>\n"
"                            Tandatangan &gt; Konfigurasi &gt; Peran\n"
"                         </code>\n"
"                         menu."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "If you do not wish to receive future reminders about this document,"
msgstr ""
"Apakah Anda tidak ingin menerima pengingat di masa depan mengenai dokumen "
"ini,"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid ""
"If you wish, you can request the document again after buying more credits "
"for the operation."
msgstr ""
"Bila Anda ingin, Anda dapat meminta dokumen lagi setelah membeli lebih "
"banyak kredit untuk operasi."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__in_progress_count
msgid "In Progress Count"
msgstr "Jumlah Sedang Berlangsung"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "In favorites, remove it"
msgstr "Di favorit, hapus"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__log_hash
msgid "Inalterability Hash"
msgstr "Inalterability Hash"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
msgid "Include a visual security frame around your signature"
msgstr "Termasuk frame keamanan visual di sekitar tandatangan Anda"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_view_form
msgid "Information"
msgstr "Informasi"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__initial
msgid "Initial"
msgstr "Inisial"

#. module: sign
#: model:sign.item.type,name:sign.sign_item_type_initial
#: model:sign.item.type,placeholder:sign.sign_item_type_initial
msgid "Initials"
msgstr "Inisial-Inisial"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Insert your terms & conditions here..."
msgstr "Masukkan syarat & ketentuan Anda di sini..."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__integrity
msgid "Integrity of the Sign request"
msgstr "Integritas permintaan Tandatangan"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_is_follower
msgid "Is Follower"
msgstr "Adalah Follower"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__is_mail_sent
msgid "Is Mail Sent"
msgstr "Apakah Email Dikirim"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__is_sharing
msgid "Is Sharing"
msgstr "Apakah Sharing"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "Is Signing"
msgstr "Apakah Menandatangani"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__is_user_signer
msgid "Is User Signer"
msgstr "Apakah User Penandatangan"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "It's signed!"
msgstr "Sudah ditandatangani!"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "John Doe"
msgstr "John Doe"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "John Smith"
msgstr "John Smith"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "L of water saved"
msgstr "L air yang dihemat"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__last_action_date
msgid "Last Action Date"
msgstr "Tanggal Action Terakhir"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_option__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_role__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_type__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_log__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_request__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_request_item__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_send_request__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_template__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__write_uid
msgid "Last Updated by"
msgstr "Terakhir Diperbarui oleh"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item_option__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item_role__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item_type__write_date
#: model:ir.model.fields,field_description:sign.field_sign_log__write_date
#: model:ir.model.fields,field_description:sign.field_sign_request__write_date
#: model:ir.model.fields,field_description:sign.field_sign_request_item__write_date
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__write_date
#: model:ir.model.fields,field_description:sign.field_sign_send_request__write_date
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__write_date
#: model:ir.model.fields,field_description:sign.field_sign_template__write_date
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__write_date
msgid "Last Updated on"
msgstr "Terakhir Diperbarui pada"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__last_reminder
msgid "Last reminder"
msgstr "Pengingat terakhir"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Late Activities"
msgstr "Aktifitas terakhir"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__latitude
#: model:ir.model.fields,field_description:sign.field_sign_request_item__latitude
msgid "Latitude"
msgstr "Lintang"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__validity
msgid "Leave empty for requests without expiration."
msgstr "Biarkan kosong untuk permintaan tanpa kadaluwarsa."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.js:0
msgid "Left"
msgstr "Kiri"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Let's <b>prepare & sign</b> our first document."
msgstr "Ayo <b>siapkan & tandatangani</b> dokumen pertama kami."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Let's send the request by email."
msgstr "Ayo kirim permintaan melalui email."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__redirect_url_text
msgid "Link Label"
msgstr "Label Link"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__activity_id
msgid "Linked Activity"
msgstr "Linked Activity"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__reference_doc
msgid "Linked To"
msgstr "Terhubung Ke"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__reference_doc
msgid "Linked to"
msgstr "Terhubung ke"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__log_date
msgid "Log Date"
msgstr "Catat Tanggal"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_log.py:0
msgid "Log history of sign requests cannot be deleted!"
msgstr "Log sejarah permintaan tandatangan tidak dapat dihapus!"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_log.py:0
msgid "Log history of sign requests cannot be modified!"
msgstr "Log sejarah permintaan tandatangan tidak dapat dimodifikasi!"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Logo"
msgstr "Logo"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__sign_log_ids
#: model_terms:ir.ui.view,arch_db:sign.sign_log_view_tree
msgid "Logs"
msgstr "Catatan"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__longitude
#: model:ir.model.fields,field_description:sign.field_sign_request_item__longitude
msgid "Longitude"
msgstr "Bujur"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__mail_sent_order
msgid "Mail Sent Order"
msgstr "Order Email Terkirim"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__update_mail
msgid "Mail Update"
msgstr "Update Email"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "Malformed expression: %(exp)s"
msgstr "Malformed expression: %(exp)s"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__group_manage_template_access
#: model:res.groups,name:sign.manage_template_access
msgid "Manage template access"
msgstr "Kelola akses templat"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
msgid "Mandatory field"
msgstr "Field wajib"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__message
msgid "Message"
msgstr "Pesan"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_has_error
msgid "Message Delivery error"
msgstr "Kesalahan Pengiriman Pesan"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__message_cc
msgid "Message to be sent to contacts in copy of the signed document"
msgstr "Pesan untuk dikirim ke kontak di salinan dokumen yang ditandatangani"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__message
msgid "Message to be sent to signers of the specified document"
msgstr "Pesan untuk dikirim ke penandatangan dokumen"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_ids
msgid "Messages"
msgstr "Pesan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.encrypted_ask_password
msgid "Missing Password"
msgstr "Kurang Passwor"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__sms_number
msgid "Mobile"
msgstr "Telepon Mobile"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Modify Template"
msgstr "Modifikasi Templat"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__textarea
#: model:sign.item.type,name:sign.sign_item_type_multiline_text
#: model:sign.item.type,placeholder:sign.sign_item_type_multiline_text
msgid "Multiline Text"
msgstr "Teks Multiline"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_share_view_form
msgid "Multiple Signature Requests"
msgstr "Lebih Dari Satu Permintaan Tandatangan"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Deadline Kegiatan Saya"

#. module: sign
#: model:ir.ui.menu,name:sign.sign_request_my_documents
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "My Documents"
msgstr "Dokumen saya"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_search
msgid "My Favorites"
msgstr "Favorit Saya"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "My Requests"
msgstr "Permintaan Saya"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_search
msgid "My Templates"
msgstr "Templat-Templat Saya"

#. module: sign
#: model:sign.template.tag,name:sign.sign_template_tag_2
msgid "NDA"
msgstr "NDA"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__name
#: model:ir.model.fields,field_description:sign.field_sign_template__name
#: model:sign.item.type,name:sign.sign_item_type_name
#: model:sign.item.type,placeholder:sign.sign_item_type_name
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Name"
msgstr "Nama"

#. module: sign
#: model:ir.model.constraint,message:sign.constraint_sign_item_role_name_uniq
msgid "Name already exists!"
msgstr "Nama sudah ada!"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Name for the file"
msgstr "Nama untuk file"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Name of the file"
msgstr "Nama file"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Nearly there, keep going!"
msgstr "Hampir selesai, terus lanjutkan!"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__need_my_signature
msgid "Need My Signature"
msgstr "Butuh Tandatangan Saya"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/PDF_iframe.js:0
msgid "Need a valid PDF to add signature fields!"
msgstr "Butuh PDF Valid untuk menambahkan field tandatangan!"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "Need to sign documents?"
msgstr "Butuh menandatangani dokumen?"

#. module: sign
#: model:ir.actions.act_window,name:sign.action_sign_send_request
msgid "New Signature Request"
msgstr "Permintaan Tandatangan Baru"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__new_template
msgid "New Template Name"
msgstr "Nama Templat Baru"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "Newest"
msgstr "Terbaru"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Kalender Acara Aktivitas Berikutnya"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Batas Waktu Aktivitas Berikutnya"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_summary
msgid "Next Activity Summary"
msgstr "Ringkasan Aktivitas Berikutnya"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_type_id
msgid "Next Activity Type"
msgstr "Tipe Aktivitas Berikutnya"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
msgid "Next Document"
msgstr "Dokumen Berikutnya"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.xml:0
msgid "Next signatory ("
msgstr "Penandatangan berikutnya ("

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "No attachment was provided"
msgstr "No attachment was provided"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_all_request_action
#: model_terms:ir.actions.act_window,help:sign.sign_request_action
msgid "No document yet"
msgstr "Belum ada dokumen"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "No specified reason"
msgstr "Tidak ada alasan spesifik"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "No template yet"
msgstr "Belum ada template"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "None"
msgstr "Tidak Ada"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Not in favorites, add it"
msgstr "Tidak di favorit, tambahkan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Not in favorites, set it"
msgstr "Tidak di favorit, tetapkan sebagai favorit"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_needaction_counter
msgid "Number of Actions"
msgstr "Jumlah Action"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__num_options
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__num_options
msgid "Number of Radio Button options"
msgstr "Jumlah opsi Tombol Radio"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Number of documents in progress for this template."
msgstr "Jumlah dokumen yang sedang berlangsung untuk templat ini."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Number of documents signed for this template."
msgstr "Jumlah dokumen yang ditandatangani untuk templat ini."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_has_error_counter
msgid "Number of errors"
msgstr "Jumlah kesalahan"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Jumlah pesan yang membutuhkan tindakan"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Jumlah pesan dengan kesalahan pengiriman"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__num_pages
msgid "Number of pages"
msgstr "Jumlah halaman"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "Odoo Sign"
msgstr "Tanda Tangan Odoo"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "One can weighs 15 g"
msgstr "Satu dapat memiliki bobot 15 g"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "One liter of gas fuel will produce 8.9 kg of CO²"
msgstr "Satu liter bahan bakar gas dapat membuat 8.9 kg CO²"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "One or more selection items have no associated options"
msgstr "Satu atau lebih item pilihan tidak memiliki opsi yang terkait"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "One uploaded file cannot be read. Is it a valid PDF?"
msgstr ""
"Satu file yang diunggah tidak dapat dibaca. Apakah ini adalah PDF valid?"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
#: code:addons/sign/controllers/terms.py:0
msgid "Oops"
msgstr "Oops"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
msgid "Operation successful"
msgstr "Operasi sukses"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_option__value
msgid "Option"
msgstr "Opsi"

#. module: sign
#: model:ir.model,name:sign.model_sign_item_option
msgid "Option of a selection Field"
msgstr "Opsi dari Field pilihan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Optional Message..."
msgstr "Pesan Opsional..."

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_template__redirect_url
msgid "Optional link for redirection after signature"
msgstr "Link opsional untuk mengalihkan ulang setelah tandatangan"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_template__redirect_url_text
msgid "Optional text to display on the button link"
msgstr "Teks opsional untuk ditampilkan pada link tombol"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Options"
msgstr "Opsi"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__original_template_id
msgid "Original File"
msgstr "File Original"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/encrypted_dialog.js:0
#: model_terms:ir.ui.view,arch_db:sign.encrypted_ask_password
msgid "PDF is encrypted"
msgstr "PDF dienkripsi"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Paper Savings"
msgstr "Menghemat Kertas"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Participants"
msgstr "Partisipan"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__partner_id
msgid "Partner"
msgstr "Rekanan"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/encrypted_dialog.js:0
msgid "Password is incorrect."
msgstr "Kata sandi salah."

#. module: sign
#: model:sign.item.type,name:sign.sign_item_type_phone
#: model:sign.item.type,placeholder:sign.sign_item_type_phone
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Phone"
msgstr "Telepon"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Phone Number"
msgstr "Nomor Telepon"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
#: model:ir.model.fields,field_description:sign.field_sign_item_type__placeholder
msgid "Placeholder"
msgstr "Placeholder"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Please configure senders'(%s) email addresses"
msgstr "Mohon konfigurasikan alamat email pengirim(%s)"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "Please select recipients for the following roles: %(roles)s"
msgstr "Mohon pilih penerima untuk peran-peran berikut: %(roles)s"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__access_url
msgid "Portal Access URL"
msgstr "Portal Access URL"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__posX
msgid "Position X"
msgstr "Posisi X"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__posY
msgid "Position Y"
msgstr "Posisi Y"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Preview"
msgstr "Pratinjau"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Printed on"
msgstr "Dicetak pada"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__progress
msgid "Progress"
msgstr "Kemajuan"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Public User"
msgstr "Pengguna Umum"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__radio
#: model:sign.item.type,name:sign.sign_item_type_radio
msgid "Radio Buttons"
msgstr "Tombol Radio"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__radio_items
msgid "Radio Items"
msgstr "Item-Item Radio"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__radio_set_id
msgid "Radio button options"
msgstr "Opsi-opsi tombol radio"

#. module: sign
#: model:ir.model,name:sign.model_sign_item_radio_set
msgid "Radio button set for keeping radio button items together"
msgstr ""
"Tombol radio yang ditetapkan agar item-item tombol radio tetap bersama."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__rating_ids
msgid "Ratings"
msgstr "Rating"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Re-send SMS"
msgstr "Kirim ulang SMS"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__redirect_url
msgid "Redirect Link"
msgstr "Alihkan Ulang Link"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.xml:0
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__refuse
msgid "Refuse"
msgstr "Tolak"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/signable_sign_request_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Refuse Document"
msgstr "Tolak Dokumen"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.js:0
msgid "Refuse to sign"
msgstr "Menolak untuk menandatangani"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__refused
msgid "Refused Signature"
msgstr "Tandatangan yang Ditola"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__reminder
#: model:ir.model.fields,field_description:sign.field_sign_send_request__reminder
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Reminder"
msgstr "Pengingat"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__reminder_enabled
#: model:ir.model.fields,field_description:sign.field_sign_send_request__reminder_enabled
msgid "Reminder Enabled"
msgstr "Pengingat Diaktifka"

#. module: sign
#: model:ir.ui.menu,name:sign.sign_reports
msgid "Reports"
msgstr "Laporan"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__request_item_infos
msgid "Request Item Infos"
msgstr "Minta Info Item"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/activity/activity_patch.xml:0
#: code:addons/sign/static/src/backend_components/cog_menu/sign_request_cog_menu.xml:0
#: model:ir.model.fields.selection,name:sign.selection__mail_activity_type__category__sign_request
#: model:mail.activity.type,name:sign.mail_activity_data_signature_request
msgid "Request Signature"
msgstr "Minta Tandatangan"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "Request expiration date must be set in the future."
msgstr "Tanggal kadaluwarsa permintaan harus di masa depan."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__nb_total
msgid "Requested Signatures"
msgstr "Tandatangan-Tandatangan yang Diminta"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__required
msgid "Required"
msgstr "Wajib"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_item_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
msgid "Resend"
msgstr "Kirim ulang"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
msgid "Resend the invitation"
msgstr "Kirim ulang undangan"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
msgid "Resent!"
msgstr "Dikirim ulang!"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/initial_all_pages_dialog.xml:0
#: model:ir.model.fields,field_description:sign.field_sign_item__responsible_id
#: model:ir.model.fields,field_description:sign.field_sign_template__user_id
msgid "Responsible"
msgstr "Penanggung Jawab"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__responsible_count
msgid "Responsible Count"
msgstr "Jumlah Penanggungjawab"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_user_id
msgid "Responsible User"
msgstr "Tanggung-jawab"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Restore"
msgstr "Pulihkan"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.js:0
msgid "Right"
msgstr "Kanan"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__role_id
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__role_id
msgid "Role"
msgstr "Peran"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_role_view_tree
msgid "Role Name"
msgstr "Nama Peran"

#. module: sign
#: model:ir.ui.menu,name:sign.sign_item_role_menu
msgid "Roles"
msgstr "Peran-Peran"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_iframe.js:0
msgid "Rotate Clockwise"
msgstr "Putar Arah Jarum Jam"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Kesalahan Pengiriman SMS`"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "SMS Sent"
msgstr "Kirim SMS"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__sms_token
msgid "SMS Token"
msgstr "Token SMS"

#. module: sign
#: model:sign.template.tag,name:sign.sign_template_tag_3
msgid "Sales"
msgstr "Penjualan"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__save
msgid "Save"
msgstr "Simpan"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Save as Template"
msgstr "Simpan sebagai Templat"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_body.js:0
msgid "Saved"
msgstr "Disimpan"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "Search <span class=\"nolabel\"> (in Document)</span>"
msgstr "Cari <span class=\"nolabel\"> (di dalam Dokumen)</span>"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__access_token
#: model:ir.model.fields,field_description:sign.field_sign_request_item__access_token
msgid "Security Token"
msgstr "Token Keamanan"

#. module: sign
#: model:sign.item.type,tip:sign.sign_item_type_selection
msgid "Select an option"
msgstr "Pilih opsi"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"Select the contact who should sign, according to their role.<br>In this "
"example, select your own contact to sign the document yourself."
msgstr ""
"Pilih kontak yang akan menandatangani, menurut peran mereka.<br>Di contoh "
"ini, pilih kontak Anda sendiri untuk menandatangani dokumen tersebut."

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__selection
#: model:sign.item.type,name:sign.sign_item_type_selection
#: model:sign.item.type,placeholder:sign.sign_item_type_selection
msgid "Selection"
msgstr "Seleksi"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__option_ids
msgid "Selection options"
msgstr "Opsi pilihan"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_item_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Send"
msgstr "Kirim"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Send SMS"
msgstr "Kirim SMS"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__signer_id
msgid "Send To"
msgstr "Kirim Ke"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid "Send a new link"
msgstr "Kirim link baru"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Send a reminder"
msgstr "Kirim pengingat"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
msgid "Send the invitation"
msgstr "Kirim undangan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Sent"
msgstr "Terkirim"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__nb_wait
msgid "Sent Requests"
msgstr "Kirim Permintaa"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "Sent by"
msgstr "Dikirim oleh"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_settings_action
#: model:ir.ui.menu,name:sign.sign_item_settings_menu
msgid "Settings"
msgstr "Pengaturan"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Share"
msgstr "Bagikan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_share_view_form
msgid "Share & Close"
msgstr "Bagikan & Tutup"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "Share Document by Link"
msgstr "Bagikan Dokumen menggunakan Link"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__share_link
msgid "Share Link"
msgstr "Bagikan Link"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Shareable"
msgstr "Shareable"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
#: code:addons/sign/models/sign_template.py:0
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__shared
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__shared
msgid "Shared"
msgstr "Dibagikan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Show all records which has next action date is before today"
msgstr "Tampilkan semua dokumen dengan aksi berikut sebelum hari ini"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Show standard terms & conditions on signature requests"
msgstr "Tunjukkan syarat & ketentuan standar pada permintaan tandatangan"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.js:0
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
#: model:ir.ui.menu,name:sign.menu_document
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Sign"
msgstr "Sign"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__use_sign_terms
msgid "Sign Default Terms & Conditions"
msgstr "Tandatangani Syarat & Ketentuan Default"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_company__sign_terms
msgid "Sign Default Terms and Conditions"
msgstr "Tandatangani Syarat dan Ketentuan Default"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_company__sign_terms_html
msgid "Sign Default Terms and Conditions as a Web page"
msgstr "Tandatangani Syarat dan Ketentuan Default sebagai halaman Website"

#. module: sign
#: model:ir.model,name:sign.model_sign_duplicate_template_pdf
msgid "Sign Duplicate Template with new PDF"
msgstr "Tandatangani Templat Duplikat dengan PDF baru"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.js:0
msgid "Sign Next Document"
msgstr "Tandatangani Dokumen Baru"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_share_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Sign Now"
msgstr "Tandatangani Sekarang"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__mail_sent_order
msgid "Sign Order"
msgstr "Tandatangani Order"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__sign_request_id
msgid "Sign Request"
msgstr "Permintaan Tandatangan"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__sign_request_item_id
msgid "Sign Request Item"
msgstr "Item Permintaan Tandatangan"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__sign_send_request_id
msgid "Sign Send Request"
msgstr "Kirim Permintaan Tandatangan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Sign Settings"
msgstr "Pengaturan Tandatangan"

#. module: sign
#: model:ir.model,name:sign.model_sign_template_tag
msgid "Sign Template Tag"
msgstr "Tag Templat Tandatangan"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__sign_terms
msgid "Sign Terms & Conditions"
msgstr "Syarat & Ketentuan Tandatangan"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__sign_terms_html
msgid "Sign Terms & Conditions as a Web page"
msgstr "Syarat & Ketentuan Tandatangan sebagai halaman Website"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_company__sign_terms_type
#: model:ir.model.fields,field_description:sign.field_res_config_settings__sign_terms_type
msgid "Sign Terms & Conditions format"
msgstr "Format Syarat & Ketentuan Tandatangan"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
msgid "Sign all"
msgstr "Tandatangani semua"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "Sign document"
msgstr "Tandatangani dokumen"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "Sign now"
msgstr "Tandatangani sekarang"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "Sign requests"
msgstr "Permintaan tandatangan"

#. module: sign
#: model:ir.model,name:sign.model_sign_log
msgid "Sign requests access history"
msgstr "Sejarah akses permintaan tandatangan"

#. module: sign
#: model:ir.model,name:sign.model_sign_send_request
msgid "Sign send request"
msgstr "Tandatangani permintaan pengiriman"

#. module: sign
#: model:ir.model,name:sign.model_sign_send_request_signer
msgid "Sign send request signer"
msgstr "Tandatangan kirim permintaan penandatangan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid ""
"Sign up for Odoo Sign to manage your own documents and signature requests!"
msgstr ""
"Sign up untuk Odoo Sign untuk mengelola dokumen Anda sendiri dan permintaan "
"tandatangan!"

#. module: sign
#: model:ir.actions.server,name:sign.sign_reminder_cron_ir_actions_server
msgid "Sign: Send mail reminder"
msgstr "Sign: Kirim email pengingat"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Signatory"
msgstr "Penandatangan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Signatory's hash:"
msgstr "Hash penandatangan:"

#. module: sign
#. odoo-javascript
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
#: code:addons/sign/static/src/js/tours/sign.js:0
#: model:ir.model.fields,field_description:sign.field_sign_request_item__signature
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__signature
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__sign
#: model:sign.item.type,name:sign.sign_item_type_signature
#: model:sign.item.type,placeholder:sign.sign_item_type_signature
#: model_terms:ir.ui.view,arch_db:sign._doc_sign
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "Signature"
msgstr "Tanda Tangan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
msgid "Signature Date"
msgstr "Tanggal Tandatangan"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__sign_item_id
msgid "Signature Item"
msgstr "Item Tandatangan"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_item_option_action
msgid "Signature Item Options"
msgstr "Opsi Item Tandatangan"

#. module: sign
#: model:ir.model,name:sign.model_sign_item_role
msgid "Signature Item Party"
msgstr "Party Item Tandatangan"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_item_role_action
msgid "Signature Item Role"
msgstr "Peran Item Tandatangan"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_item_type_action
#: model:ir.model,name:sign.model_sign_item_type
msgid "Signature Item Type"
msgstr "Tipe Item Tandatangan"

#. module: sign
#: model:ir.model,name:sign.model_sign_request_item_value
msgid "Signature Item Value"
msgstr "Value Item Tandatangan"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__sign_item_ids
msgid "Signature Items"
msgstr "Item-Item Tandatangan"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/activity/activity_model_patch.js:0
#: code:addons/sign/static/src/backend_components/cog_menu/sign_request_cog_menu.js:0
#: model:ir.model,name:sign.model_sign_request
#: model:ir.model.fields,field_description:sign.field_sign_request_item__sign_request_id
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__sign_request_id
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Signature Request"
msgstr "Permintaan Tanda Tangan"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "Signature Request - %(file_name)s"
msgstr "Permintaan Tandatangan - %(file_name)s"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "Signature Request - %s"
msgstr "Permintaan Tanda Tangan - %s"

#. module: sign
#: model:ir.model,name:sign.model_sign_request_item
msgid "Signature Request Item"
msgstr "Tandatangan Permintaan Item"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_request_item_action
msgid "Signature Request Items"
msgstr "Tandatangan Permintaan Item-Item"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__sign_request_item_id
msgid "Signature Request item"
msgstr "Tandatangan Permintaan item"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__sign_request_ids
msgid "Signature Requests"
msgstr "Permintaan Tanda Tangan"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__start_sign
msgid "Signature Started"
msgstr "Tandatangan Dimulai"

#. module: sign
#: model:ir.model,name:sign.model_sign_template
msgid "Signature Template"
msgstr "Templat Tanda Tangan"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
msgid "Signature configuration"
msgstr "Konfigurasi tandatangan"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid ""
"Signature requested for template: %(template)s\n"
"Signatories: %(signatories)s"
msgstr ""
"Tandatangan diminta untuk templat: %(template)s\n"
"Penandatangan: %(signatories)s"

#. module: sign
#. odoo-python
#: code:addons/sign/models/res_partner.py:0
msgid "Signature(s)"
msgstr "Tandatangan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Signature:"
msgstr "Tandatangan:"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.portal_my_home_menu_sign
#: model_terms:ir.ui.view,arch_db:sign.portal_my_home_sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
msgid "Signatures"
msgstr "Tandatangan-Tandatangan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Signed"
msgstr "Ditandatangani"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__signed_count
msgid "Signed Count"
msgstr "Jumlah yang Ditandatangani"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Signed Documents"
msgstr "Dokumen yang Ditandatangani"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__signed_without_extra_auth
msgid "Signed Without Extra Authentication"
msgstr "Ditandatangani Tanpa Autentikasi Ekstra"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "Signed document"
msgstr "Dokumen Ditandatangani"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__signing_date
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "Signed on"
msgstr "Ditandatangani pada"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__partner_id
msgid "Signer"
msgstr "Penandatanga"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/fields/signer_x2many.js:0
msgid "Signer One 2 Many"
msgstr "Penandatangan One2Many"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__request_item_ids
#: model:ir.model.fields,field_description:sign.field_sign_send_request__signer_ids
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Signers"
msgstr "Penandatangan-Penandatangan"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__signers_count
msgid "Signers Count"
msgstr "Jumlah Penandatangan"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "Signing Date"
msgstr "Tanggal Penandatanganan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Signing Events"
msgstr "Acara Penandatanganan"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__set_sign_order
msgid "Signing Order"
msgstr "Urutan Tandatangan"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"Since you're the one signing this document, you can do it directly within "
"Odoo.<br>External users can use the link provided by email."
msgstr ""
"Karena Anda yang menandatangani dokumen ini, Anda dapat melakukannya "
"langsung dalam Odoo.<br>User eksternal dapat menggunakan link yang "
"disediakan melalui email."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid "Some fields have still to be completed"
msgstr "Beberapa field masih belum diselesaika"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Some required items are not filled"
msgstr "Beberapa item yang diperlukan tidak diisi"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Some unauthorised items are filled"
msgstr "Beberapa item yang tidak diotorisasi sudah diisi"

#. module: sign
#. odoo-javascript
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_body.js:0
msgid "Somebody is already filling a document which uses this template"
msgstr "Ada yang sudah mengisi dokumen yang menggunakan templat ini"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid "Sorry, an error occurred, please try to fill the document again."
msgstr "Maaf, terjadi error, silakan coba untuk mengisi dokumennya lagi."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.js:0
msgid "Sorry, you cannot refuse this document"
msgstr "Maaf, Anda tidak dapat menolak dokumen ini"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__set_sign_order
msgid ""
"Specify the order for each signer. The signature request only gets sent to                                     the next signers in the sequence when all signers from the previous level have                                     signed the document.\n"
"                                    "
msgstr ""
"Tentukan urutan untuk setiap penandatangan. Permintaan tandatangan hanya dikirim ke penandatangan berikutnya sesuai urutan saat semua penandatangan sebelumnya telah                                     menandatangani dokumen.\n"
"                                    "

#. module: sign
#: model:sign.item.role,name:sign.sign_item_role_default
msgid "Standard"
msgstr "Standar"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__state
#: model:ir.model.fields,field_description:sign.field_sign_request_item__state
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "State"
msgstr "Status"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__request_state
msgid "State of the request on action log"
msgstr "Status permintaan pada action log"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
msgid "Status"
msgstr "Status"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status berdasarkan aktivitas\n"
"Terlambat: Batas waktu telah terlewati\n"
"Hari ini: Tanggal aktivitas adalah hari ini\n"
"Direncanakan: Aktivitas yang akan datang."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Stop Sharing"
msgstr "Berhenti Sharing"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__subject
msgid "Subject"
msgstr "Judul"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Summary"
msgstr "Ringkasan"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__name
msgid "Tag Name"
msgstr "Nama Tag"

#. module: sign
#: model:ir.model.constraint,message:sign.constraint_sign_template_tag_name_uniq
msgid "Tag name already exists!"
msgstr "Nama tag sudah ada!"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_template_tag_action
#: model:ir.model.fields,field_description:sign.field_sign_request__template_tags
#: model:ir.model.fields,field_description:sign.field_sign_template__tag_ids
#: model:ir.ui.menu,name:sign.sign_template_tag_menu
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_template_tag_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_template_tag_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Tags"
msgstr "Label"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Tags:"
msgstr "Tag:"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_item_type__auto_field
msgid ""
"Technical name of the field on the partner model to auto-complete this "
"signature field at the time of signature."
msgstr ""
"Nama teknis field pada model partner untuk mengisi otomatis field "
"tandatangan pada saat tandatangan."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__template_id
#: model:ir.model.fields,field_description:sign.field_sign_send_request__template_id
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Template"
msgstr "Template"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.js:0
#: code:addons/sign/static/src/views/hooks.js:0
msgid "Template %s"
msgstr "Templat %s"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Template Properties"
msgstr "Properti-Properti Templat"

#. module: sign
#: model:ir.actions.server,name:sign.sign_template_tour_trigger_action
msgid "Template Sample Contract.pdf trigger"
msgstr "Pemicu Sampel Templat Kontrak.pdf "

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Template or Tag"
msgstr "Templat atau Tag"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
#: model:ir.actions.act_window,name:sign.sign_template_action
#: model:ir.ui.menu,name:sign.sign_template_menu
msgid "Templates"
msgstr "Contoh"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "Terms &amp; Conditions"
msgstr "Syarat &amp; Ketentuan"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__res_company__sign_terms_type__html
msgid "Terms as Web Page"
msgstr "Syarat sebagai Halaman Web"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__res_company__sign_terms_type__plain
msgid "Terms in Email"
msgstr "Syarat dalam Email"

#. module: sign
#: model:ir.model.fields,help:sign.field_res_company__sign_terms_type
#: model:ir.model.fields,help:sign.field_res_config_settings__sign_terms_type
msgid ""
"Terms in Email - The text will be displayed at the bottom of every signature request email.\n"
"\n"
"        Terms as Web Page - A link will be pasted at the bottom of every signature request email, leading to your content.\n"
"        "
msgstr ""
"Syarat dalam Email - Teks akan ditampilkan di bawah setiap email permintaan tandatangan.\n"
"\n"
"        Syarat sebagai Halaman Web - Link akan dipasang di bawah setiap email permintaan, yang akan menuju konten Anda.\n"
" "

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__text
#: model:sign.item.type,name:sign.sign_item_type_text
#: model:sign.item.type,placeholder:sign.sign_item_type_text
msgid "Text"
msgstr "Teks"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.js:0
msgid "Thank You!"
msgstr "Terima Kasih!"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"That's it, all done!<br>The document is signed, and a copy has been sent by "
"email to all participants, along with a traceability report."
msgstr ""
"Semuanya sudah selesai!<br>Dokumen telah ditandatangani, dan salinan telah "
"dikirim melalui email ke semua peserta, bersamaan dengan laporan "
"ketelusuran."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid ""
"The Odoo Sign document you are trying to reach does not exist. The signature"
" request might have been deleted or modified."
msgstr ""
"Dokumen Odoo Sign yang Anda ingin temukantidak ada. Permintaan tanda tangan "
"mungkin sudah dihapus atau dimodifikasi."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.encrypted_ask_password
msgid "The PDF's password is required to generate the final document."
msgstr "Password PDF dibutuhkan untuk membuat dokumen final."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"The completed document cannot be created because the sign request is not "
"fully signed"
msgstr ""
"Dokumen yang selesai tidak dapat dibuat karena permintaan tandatangan tidak "
"sepenuhnya ditandatangani"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "The computation is based on the website"
msgstr "Perhitungan berdasarkan website"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"The contact of %(role)s has been changed from %(old_partner)s to "
"%(new_partner)s."
msgstr ""
"Kontak %(role)s telah diubah dari %(old_partner)s menjadi %(new_partner)s."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid "The document"
msgstr "Dokumen"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The document %s has been fully signed."
msgstr "Dokumen %s telah lengkap ditandatangani."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The document (%s) has been rejected by one of the signers"
msgstr "Dokumen (%s) telah ditolak oleh salah satu penandatangan"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.js:0
msgid "The document has been refused"
msgstr "Dokumen telah ditolak"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The document has been signed by a signer and cannot be edited"
msgstr ""
"Dokumen telah ditandatangani oleh penandatangan dan tidak dapat diedit"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid ""
"The final document and this completion history have been sent by email "
"on&amp;nbsp;"
msgstr ""
"Dokumen final dan sejarah penyelesaian telah dikirim melalui email "
"pada&amp;nbsp;"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid ""
"The integrity of the document's history cannot be verified. This could mean "
"that signature values or the underlying PDF document may have been modified "
"after the fact."
msgstr ""
"Integrasi sejarah dokumen tidak dapat divalidasi. Ini dapat berarti value "
"tandatangan atau dokumen PDF awal mungkin telah telah dimodifikasi."

#. module: sign
#. odoo-python
#: code:addons/sign/models/res_partner.py:0
msgid ""
"The mail address of %(partner)s has been updated. The request will be "
"automatically resent."
msgstr ""
"Alamat email %(partner)s telah diupdate. Permintaan akan secara otomatis "
"dikirim ulang."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The mail has been sent to contacts in copy: %(contacts)s"
msgstr "Email telah dikirim ke kontak di salinan: %(contacts)s"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/terms.py:0
msgid "The requested page is invalid, or doesn't exist anymore."
msgstr "Halaman yang diminta tidak valid, atau sudah tidak tersedia lagi."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "The role %s is required by the Sign application and cannot be deleted."
msgstr ""
"Peran %s diperlukan oleh aplikasi Tandatangan dan tidak dapat dihapus."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The sign request has not been fully signed"
msgstr "Permintaan tandatangan belum sepenuhnya ditandatangani"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid "The signature has been canceled by %(partner)s(%(role)s)"
msgstr "Tanda Tangan ini telah dibatalkan oleh %(partner)s(%(role)s)"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The signature has been refused by %(partner)s(%(role)s)"
msgstr "Tandatangan ditolak oleh %(partner)s(%(role)s)"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The signature mail has been sent to: "
msgstr "Email tandatangan telah dikirim ke:"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request_item__is_mail_sent
msgid "The signature mail has been sent."
msgstr "Email tandatangan telah dikirim."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
msgid "The signature request has been cancelled"
msgstr "Permintaan tandatangan telah dibatalkan"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The signature request has been edited by: %s."
msgstr "Permintaan tandatangan telah diedit oleh: %s."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_action.js:0
msgid "The template doesn't exist anymore."
msgstr "Templat tidak tersedia lagi."

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_duplicate_template_with_pdf.py:0
msgid ""
"The template has more pages than the current file, it can't be applied."
msgstr ""
"Templat memiliki lebih banyak halaman dari file saat ini, tidak dapat "
"diterapkan."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid ""
"The total of sheets you saved is based on: the number of sent sign requests "
"x number of sheets in the document x (number of contacts who need to sign + "
"number of contacts in copy if the sign request is signed) = total of pages. "
"We assume that one page weights 0.005 kilograms."
msgstr ""
"Total sheet yang Anda simpan akan berdasarkan: jumlah permintaan tandatangan"
" yang dikirim x jumlah sheet dalam dokumen x (jumlah kontak yang harus "
"menandatangani + jumlah kontak dalam salinan bila permintaan tandatangan "
"dipenuhi) = total halaman. Kami mengasumsikan bahwa satu halaman memiliki "
"berat 0.005 kilogram."

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_duplicate_template_with_pdf.py:0
msgid "The uploaded file is not a valid PDF. Please upload a valid PDF file."
msgstr ""
"File yang diunggah bukan merupakan PDF valid. Silakan menunggah file PDF "
"valid."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
msgid "There are no signatures request."
msgstr "Tidak ada permintaan tandatangan."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "There are other documents waiting for your signature:"
msgstr "Ada dokumen-dokumen lain yang menunggu tandatangan Anda:"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"There was an issue downloading your document. Please contact an "
"administrator."
msgstr ""
"Terdapat masalah saat mengunduh dokumen Anda. Silakan hubungi administrator."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid ""
"There's no reason to panic, <br/>\n"
"                        you can still sign your document in a few clicks!"
msgstr ""
"Tidak ada alasan untuk panik, <br/>\n"
"                        Anda masih dapat menandatangani dokumen Anda dalam beberapa klik saja!"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/template_alert_dialog/template_alert_dialog.xml:0
msgid "These files cannot be read, they may be corrupted or encrypted."
msgstr ""
"File-file ini tidak dapat dibaca, mereka mungkin corrupt atau dienkripsi."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/template_alert_dialog/template_alert_dialog.xml:0
msgid "They will be removed from the uploaded files"
msgstr "Mereka akan dihapus dari file yang diunggah"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This function can only be called with sudo."
msgstr "Fungsi ini hanya dapat dipanggil dengan sudo."

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__reference
#: model:ir.model.fields,help:sign.field_sign_request_item__reference
msgid "This is how the document will be named in the mail"
msgstr "Ini cara dokumen akan dinamakan di email"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid "This link has expired."
msgstr "Link ini telah kadaluwarsa."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request cannot be refused"
msgstr "Permintaan tanda tangan ini tidak dapat ditolak"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request cannot be signed"
msgstr "Permintaan tanda tangan ini tidak dapat ditandatangani"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request is not valid anymore"
msgstr "Permintaan tanda tangan ini tidak lagi valid"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request item cannot be filled"
msgstr "Item permintaan tanda tangan ini tidak dapat diisi"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request item cannot be refused"
msgstr "Item permintaan tanda tangan ini tidak dapat ditolak"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request item cannot be signed"
msgstr "Item permintaan tanda tangan ini tidak dapat ditandatangani"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid ""
"This will keep all the already completed signature of this request and "
"disable every sent access, are you sure?"
msgstr ""
"Ini akan menyimpan semua tandatangan yang selesai untuk permintaan ini dan "
"menonaktifkan setiap akses yang dikirim, apakah Anda yakin?"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__tip
msgid "Tip"
msgstr "Tip"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__sent
#: model:ir.model.fields.selection,name:sign.selection__sign_request_item__state__sent
msgid "To Sign"
msgstr "Untuk Ditandatangani"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "To produce 1000 kg of wood, we have to cut 12 trees"
msgstr "Untuk memproduksi 1000 kg kayu, kita harus memotong 12 pohon"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "To sign"
msgstr "Untuk ditandatangani"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Today Activities"
msgstr "Aktivitas Hari ini"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__transaction_id
msgid "Transaction"
msgstr "Transaksi"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid "Try Odoo Sign"
msgstr "Coba Odoo Sign"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Try a sample contract"
msgstr "Coba sampel kontra"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "Try our sample document"
msgstr "Coba sampel dokumen kami"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Try out this sample contract."
msgstr "Coba sampel kontrak ini."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__type_id
#: model:ir.model.fields,field_description:sign.field_sign_item_type__item_type
msgid "Type"
msgstr "Jenis"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/fields/signer_x2many.js:0
msgid "Type a name or email..."
msgstr "Ketik nama atau email..."

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Jenis dari aktivitas pengecualian pada rekaman data."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_tag_view_form
msgid "Type tag name here"
msgstr "Ketik nama tag di sini"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "UTC"
msgstr "UTC"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.js:0
msgid "Unable to send the SMS, please contact the sender of the document."
msgstr "Tidak dapat mengirim SMS, silakan hubungi pengirim dokumen."

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid ""
"Unable to sign the document due to missing required data. Please contact an "
"administrator."
msgstr ""
"Tidak dapat menandatangani dokumen oleh karena kurang data yang diperlukan. "
"Silakan hubungi administrator."

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_role__auth_method__sms
msgid "Unique Code via SMS"
msgstr "Kode Unik melalui SMS"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__update
msgid "Update"
msgstr "Perbarui"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Upload"
msgstr "Unggah"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/common.xml:0
msgid "Upload PDF"
msgstr "Unggah PDF"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "Upload a PDF"
msgstr "Unggah PDF"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/common.xml:0
msgid "Upload a PDF & Sign"
msgstr "Unggah PDF & Tandatangani"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_all_request_action
#: model_terms:ir.actions.act_window,help:sign.sign_request_action
msgid "Upload a PDF file or use an existing template to begin."
msgstr "Unggah file PDF atau gunakan templat yang tersedia untuk memulai."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Upload a PDF file to create a reusable template."
msgstr "Unggah file PDF untuk membuat templat yang dapat digunakan ulang."

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "Upload a PDF file to create your first template"
msgstr "Unggah file PDF untuk membuat templat pertama Anda"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/common.xml:0
msgid "Upload a pdf that you want to sign directly"
msgstr "Unggah PDF yang Anda ingin tandatangani secara langsung"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Use Layout"
msgstr "Gunakan Layout"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_tag_action
msgid "Use Tags to manage your Sign Templates and Sign Requests"
msgstr "Gunakan Tag untuk mengelola Templat dan Permintaan Tandatangan Anda"

#. module: sign
#: model:ir.actions.act_window,name:sign.action_sign_duplicate_template_with_pdf
msgid "Use the layout of fields on a new PDF"
msgstr "Gunakan layout field pada PDF baru"

#. module: sign
#: model:ir.model,name:sign.model_res_users
#: model:ir.model.fields,field_description:sign.field_sign_log__user_id
#: model:sign.item.role,name:sign.sign_item_role_user
msgid "User"
msgstr "Pengguna"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__token
msgid "User token"
msgstr "Token pengguna"

#. module: sign
#: model:res.groups,name:sign.group_sign_user
msgid "User: Own Templates"
msgstr "User: Templat Sendiri"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__validity
#: model:ir.model.fields,field_description:sign.field_sign_send_request__validity
msgid "Valid Until"
msgstr "Berlaku hingga"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
msgid "Validate"
msgstr "Validasi"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/public_signer_dialog.xml:0
msgid "Validate & Send"
msgstr "Validasi & Kirim"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/edit_while_signing_signable_pdf_iframe.js:0
msgid "Validate & the next signatory is “%s”"
msgstr "Validasi & penandatangan berikutnya adalah “%s”"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign._doc_sign
msgid "Validate &amp; Send Completed Document"
msgstr "Validasi &amp; Kirim Dokumen yang Selesai"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Validation Code"
msgstr "Kode Validasi"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__sign_item_value_ids
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__value
msgid "Value"
msgstr "Value"

#. module: sign
#: model:ir.model.constraint,message:sign.constraint_sign_item_option_value_uniq
msgid "Value already exists!"
msgstr "Value sudah ada!"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Verify"
msgstr "Verify"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "View Document"
msgstr "Lihat Dokume"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
msgid "View document"
msgstr "Lihat dokumen"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__open
msgid "View/Download"
msgstr "Lihat/Unduh"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Viewed/downloaded by"
msgstr "Dilihat/diunduh oleh"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Waiting for me"
msgstr "Menunggu saya"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Waiting for others"
msgstr "Menunggu orang lain"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_action.js:0
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid "Warning"
msgstr "Peringatan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Waste"
msgstr "Limbah"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
msgid "Water"
msgstr "Air"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"We can only send reminders in the future - as soon as we find a way to send reminders in the past we'll notify you.\n"
"In the mean time, please make sure to input a positive number of days for the reminder interval."
msgstr ""
"Kami hanya mengirim pengingat di masa depan - apabila kita menemukan cara untuk mengirimkan pengingat di masa lalu kami akan memberitahu Anda.\n"
"Selagi menunggu, mohon pastikan untuk menginput jumlah hari yang positif untuk interval pengingat."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid "We couldn't find the signature request you're looking for!"
msgstr "Kami tidak dapat menemukan permintaan tandatangan yang Anda cari!"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid ""
"We display to you a ratio based on the saved weight versus 1000 kg of paper "
"usage."
msgstr ""
"Kami menampilkan ke Anda rasio berdasarkan berat yang dihemat vs penggunaan "
"1000 kg kertas"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.xml:0
msgid "We will send you this document by email once everyone has signed."
msgstr ""
"Kami akan mengirim Anda dokumen ini melalui email setelah semua orang "
"menandatangani."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.js:0
msgid ""
"We'll send an email to warn other contacts in copy & signers with the reason"
" you provided."
msgstr ""
"Kami akan mengirim email untuk mengingatkan kontak lain dalam salinan & "
"penandatangan alasan yang Anda sediakan."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__website_message_ids
msgid "Website Messages"
msgstr "Pesan situs"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__website_message_ids
msgid "Website communication history"
msgstr "Sejarah komunikasi situs"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"Well done, your document is ready!<br>Let's send it to get our first "
"signature."
msgstr ""
"Bagus, dokumen Anda siap!<br>Ayo kirim untuk mendapatkan tandatangan pertama"
" kami."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "What about those conversions I see?"
msgstr "Konversi yang saya lihat itu apa?"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.xml:0
msgid "Why do you refuse to sign this document?"
msgstr "Kenapa Anda menolak menandatangani dokumen ini?"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__width
msgid "Width"
msgstr "Lebar"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Wood"
msgstr "Kayu"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Write email or search contact..."
msgstr "Tulis email atau cari kontak..."

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid "Wrong password"
msgstr "Kata sandi salah"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "XYZ123456"
msgstr "XYZ123456"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid ""
"You can contact the person who invited you to sign the document by email for"
" help."
msgstr ""
"Anda dapat meminta bantuan orang yang mengundang Anda untuk menandatangani "
"dokumen melalui email."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You can only add new items for the current role"
msgstr "Anda hanya dapat menambahkan item baru untuk peran saat ini"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid ""
"You can request a new link to access your document and sign it, it will  be "
"delivered in your inbox right away."
msgstr ""
"Anda dapat meminta link baru untuk mengakses dokumen Anda dan "
"menandatanganinya, akan secepatnya dikirim ke inbox Anda."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid ""
"You can't delete a template for which signature requests exist but you can "
"archive it instead."
msgstr ""
"Anda tidak dapat menghapus templat untuk mana permintaan tanda tangan "
"tersedia tapi Anda dapat mengarsipnya."

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You cannot reassign this signatory"
msgstr "Anda tidak dapat menugaskan ulang penandatangan ini"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid ""
"You cannot share this document by link, because it has fields to be filled "
"by different roles. Use Send button instead."
msgstr ""
"Anda tidak dapat membagikan dokumen ini menggunakan link, karena memiliki "
"field yang harus diisi oleh peran yang berbeda-beda. Gunakan tombol Kirim."

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid ""
"You do not have access to these documents, please contact a Sign "
"Administrator."
msgstr ""
"Anda tidak memiliki akses ke dokumen-dokumen ini, silakan hubungi "
"Administrator Sign."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
msgid "You have refused the document"
msgstr "Anda menolak dokumen"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "You have until"
msgstr "Anda memiliki waktu sampai"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You must specify one signer for each role of your sign template"
msgstr ""
"Anda harus menentukan satu penandatangan untuk setiap peran dari templat "
"tandatangan Anda"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You need to define a signatory"
msgstr "Anda harus mendefinisikan penandatangan"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You should select at least one document to download."
msgstr "Anda harus memilih setidaknya satu dokumen untuk diunduh."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.js:0
msgid "You will get the signed document by email."
msgstr "Anda akan mendapatkan dokumen yang ditandatangani melalui email."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
msgid "You won't receive any notification for this signature request anymore."
msgstr ""
"Anda tidak akan menerima notifikasi apapun untuk permintaan tanda tangan ini"
" lagi."

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_all_request_action
#: model_terms:ir.actions.act_window,help:sign.sign_request_action
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "You're one click away from automating your signature process!"
msgstr "Anda satu klik lagi dari mengotomatisasi proses tandatangan Anda!"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Your Information"
msgstr "Informasi Anda"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Your confirmation code is %s"
msgstr "Kode konfirmasi Anda adalah %s"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/public_signer_dialog.xml:0
msgid "Your email"
msgstr "Email Anda"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/encrypted_dialog.xml:0
msgid ""
"Your file is encrypted, PDF's password is required to generate final "
"document. The final document will be encrypted with the same password."
msgstr ""
"File Anda dienkripsi, password PDf dibutuhkan untuk membuat dokumen final. "
"Dokumen final akan dienkripsi dengan password yang sama."

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/public_signer_dialog.xml:0
msgid "Your name"
msgstr "Nama anda"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.xml:0
msgid "Your signature has been saved. Next signatory is"
msgstr "Tandatangan Anda telah disimpan. Penandatangan berikutnya adalah"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid ""
"Your signature was not submitted. Ensure the SMS validation code is correct."
msgstr ""
"Tandatangan Anda tidak diserahkan. Pastikan kode validasi SMS Anda tepat."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "and"
msgstr "dan"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/template_alert_dialog/template_alert_dialog.xml:0
msgid "and the process will continue"
msgstr "dan proses akan dilanjutkan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "and:"
msgstr "dan:"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid ""
"because you don't have enough credits for this operation.\n"
"                    The signatory was able to finish signing, but was not asked to authenticate fully."
msgstr ""
"karena Anda tidak memiliki kredit yang mencukupi untuk operasi ini.\n"
"                    Penandatangan akan dapat menyelesaikan tandatangan, tapi tidak diminta untuk mengautentikasi secara penuh."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "can"
msgstr "can"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "cans"
msgstr "kaleng"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "click here to cancel it."
msgstr "klik di sini untuk membatalkannya."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "days."
msgstr "hari."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "e.g. +1 415 555 0100"
msgstr "misal: +1 415 555 0100"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "e.g. 314159"
msgstr "misal: 314159"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_role_view_form
msgid "e.g. Employee"
msgstr "contoh Karyawan"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "e.g. Non-disclosure agreement"
msgstr "contoh NDA"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "every"
msgstr "setiap"

#. module: sign
#: model:sign.item.type,tip:sign.sign_item_type_checkbox
#: model:sign.item.type,tip:sign.sign_item_type_company
#: model:sign.item.type,tip:sign.sign_item_type_date
#: model:sign.item.type,tip:sign.sign_item_type_email
#: model:sign.item.type,tip:sign.sign_item_type_multiline_text
#: model:sign.item.type,tip:sign.sign_item_type_name
#: model:sign.item.type,tip:sign.sign_item_type_phone
#: model:sign.item.type,tip:sign.sign_item_type_radio
#: model:sign.item.type,tip:sign.sign_item_type_text
msgid "fill in"
msgstr "isi"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "has been completed and signed by"
msgstr "telah diselesaikan dan ditandatangani oleh"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "has been edited, completed and signed by"
msgstr "telah diedit, diselesaikan dan ditandatangani oleh"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid "has been signed by"
msgstr "telah ditandatangani oleh"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "hour of computer use"
msgstr "jam penggunaan komputer"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "hours of computer use"
msgstr "jam-jam penggunaan komputer"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "https://c.environmentalpaper.org/"
msgstr "https://c.environmentalpaper.org/"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "ip"
msgstr "ip"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "is free, forever, with unlimited users - and it's fun to use!"
msgstr ""
"gratis, selamanya, dengan user tanpa batas - dan menyenangkan untuk "
"digunakan!"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "kWh of energy saved"
msgstr "kWh energi yang dihemat"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "kg of reduced carbon emissions"
msgstr "kg emisi karbon yang dikurang"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "kg of waste prevented"
msgstr "kg limbah yang dicegah"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "kg of wood saved"
msgstr "kg kayu yang dihemat"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "liter of car fuel"
msgstr "liter bahan bakar mobil"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "liters of car fuel"
msgstr "liter-liter bahan bakar mobil"

#. module: sign
#: model:sign.item.type,tip:sign.sign_item_type_initial
msgid "mark it"
msgstr "tandai"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid "next"
msgstr "berikutnya"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "on"
msgstr "pada"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "or"
msgstr "atau"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "sheets of paper saved"
msgstr "lembar kertas yang dihemat"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "shower"
msgstr "shower"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "showers"
msgstr "shower"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
msgid "sign"
msgstr "sign"

#. module: sign
#: model:sign.item.type,tip:sign.sign_item_type_signature
msgid "sign it"
msgstr "tandatangani"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message
msgid "sign.message"
msgstr "sign.message"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_cc
msgid "sign.message_cc"
msgstr "sign.message_cc"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "tags"
msgstr "tag"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_el
#: model_terms:ir.ui.view,arch_db:sign.green_report_el_pdf
msgid "that's"
msgstr "that's"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "to sign the document."
msgstr "untuk menandatangani dokumen."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "to:"
msgstr "untuk:"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "tree"
msgstr "tree"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "trees"
msgstr "pohon"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "we've got a list of consumption for 1000 kg of paper usage."
msgstr ""
"kita memiliki daftar konsumsi untuk penggunaan kertas seberat 1000 kg."

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid "without the requested extra-authentification step ("
msgstr "tanpa langkah autentikasi-ekstra yang diminta ("

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "you"
msgstr "Anda"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_el
#: model_terms:ir.ui.view,arch_db:sign.green_report_el_pdf
msgid "{{green_report_el_title}}"
msgstr "{{green_report_el_title}}"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_el
#: model_terms:ir.ui.view,arch_db:sign.green_report_el_pdf
msgid "{{green_report_el_title}} Summary"
msgstr "Ringkasan {{green_report_el_title}}"
