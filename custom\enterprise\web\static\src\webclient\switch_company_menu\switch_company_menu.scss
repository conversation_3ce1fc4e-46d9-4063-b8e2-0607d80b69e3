
.o_switch_company_menu_dropdown {
    min-width: 20rem;
}

.o_switch_company_menu_items {
    max-height: 20rem;
    overflow-x: auto;

    .o_switch_company_item {
        height: 33px;

        [role=button] {
            min-width: 0;
        }

        &.focus,
        &.focus > * {
            background-color: var(--gray-200);
        }

        .disabled {
            cursor: default;
        }
    }
}

.o_switch_company_menu_buttons {
    .btn-primary {
        $color-theme: map-get($o-btns-bs-override, "primary");
        $background: map-get($color-theme, "background");
        color: color-contrast($background);
    }

    .focus {
        &.btn-primary {
            outline: 2px solid var(--o-cc1-text);
        }

        &.btn-secondary {
            outline: 2px solid var(--o-cc1-text);
        }
    }
}

.o_switch_company_menu .oe_topbar_name {
    max-width: 15rem;
}
