# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* snailmail
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# Halil, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__attachment_error
msgid "ATTACHMENT_ERROR"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_cover
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_cover
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__snailmail_cover
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Add a Cover Page"
msgstr "Kapak Sayfası Ekleme"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Address"
msgstr "Adres"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core/failure_model_patch.js:0
msgid ""
"An error occurred when sending a letter with Snailmail on “%(record_name)s”"
msgstr ""

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core/failure_model_patch.js:0
msgid "An error occurred when sending a letter with Snailmail."
msgstr "Snailmail ile mektup gönderilirken bir hata oluştu."

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "An error occurred when sending the document by post.<br>Error: %s"
msgstr "Belge posta ile gönderilirken bir hata oluştu.<br>Hata: %s"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "An unknown error happened. Please contact the support."
msgstr "Bilinmeyen bir hata oluştu. Lütfen destek ekibiyle iletişime geçin."

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid "An unknown error occurred. Please contact our"
msgstr "Bilinmeyen bir hata oluştu. Lütfen bizimle iletişime geçin"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_id
msgid "Attachment"
msgstr "Ek"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_fname
msgid "Attachment Filename"
msgstr "Ek Dosya Adı"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core/notification_model_patch.js:0
msgid "Awaiting Dispatch"
msgstr "Gönderim Bekliyor"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__duplex
msgid "Both side"
msgstr "Her iki taraf"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_duplex
msgid "Both sides"
msgstr "Her iki taraf da"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid "Buy credits"
msgstr "Kredi Satınal"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__credit_error
msgid "CREDIT_ERROR"
msgstr "CREDIT_ERROR"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_form
msgid "Cancel"
msgstr "İptal"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Cancel Letter"
msgstr "Mektubu iptal et"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Cancel letter"
msgstr "Mektubu iptal et"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Cancel notification in failure"
msgstr "Bildirimi başarısız olarak iptal et"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core/notification_model_patch.js:0
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__canceled
msgid "Cancelled"
msgstr "İptal Edildi"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__city
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__city
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "City"
msgstr "Semt/İlçe"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Close"
msgstr "Kapat"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__color
msgid "Color"
msgstr "Renk"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_company
msgid "Companies"
msgstr "Şirketler"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__company_id
msgid "Company"
msgstr "Firma"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_config_settings
msgid "Config Settings"
msgstr "Yapılandırma Ayarları"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_partner
msgid "Contact"
msgstr "Kontak"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__country_id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__country_id
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Country"
msgstr "Ülke"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__cover
msgid "Cover Page"
msgstr "Kapak Sayfası"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__create_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__create_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__create_uid
msgid "Created by"
msgstr "Tarafından oluşturuldu"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__create_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__create_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__create_date
msgid "Created on"
msgstr "Oluşturuldu"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__display_name
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__display_name
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__display_name
msgid "Display Name"
msgstr "İsim Göster"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_datas
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_list
msgid "Document"
msgstr "Belge"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__res_id
msgid "Document ID"
msgstr "Belge ID"

#. module: snailmail
#: model:ir.model,name:snailmail.model_mail_thread
msgid "Email Thread"
msgstr "E-Posta İşlemleri"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core/notification_model_patch.js:0
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__error_code
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__error
msgid "Error"
msgstr "Hata"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__format_error
msgid "FORMAT_ERROR"
msgstr "FORMAT_ERROR"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
#: model:ir.actions.act_window,name:snailmail.snailmail_letter_missing_required_fields_action
msgid "Failed letter"
msgstr "Başarısız mektup"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_notification__failure_type
msgid "Failure type"
msgstr "Başarısızlık türü"

#. module: snailmail
#: model:ir.actions.act_window,name:snailmail.snailmail_letter_format_error_action
msgid "Format Error"
msgstr "Biçim Hatası"

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter_format_error
msgid "Format Error Sending a Snailmail Letter"
msgstr "Snailmail ile mektup gönderilirken bir hata oluştu"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__id
msgid "ID"
msgstr "ID"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__pending
msgid "In Queue"
msgstr "Sırada"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__info_msg
msgid "Information"
msgstr "Bilgi"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "Invalid recipient name."
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__write_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__write_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__write_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__write_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_mail__letter_ids
#: model:ir.model.fields,field_description:snailmail.field_mail_message__letter_ids
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__letter_id
msgid "Letter"
msgstr "Mektup"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "Letter sent by post with Snailmail"
msgstr "Snailmail ile posta yoluyla gönderilen mektup"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_list
msgid "Letters"
msgstr "Mektuplar"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__missing_required_fields
msgid "MISSING_REQUIRED_FIELDS"
msgstr "MISSING_REQUIRED_FIELDS"

#. module: snailmail
#: model:ir.model,name:snailmail.model_mail_message
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__message_id
msgid "Message"
msgstr "Mesaj"

#. module: snailmail
#: model:ir.model,name:snailmail.model_mail_notification
msgid "Message Notifications"
msgstr "Mesaj Bildirimleri"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__model
msgid "Model"
msgstr "Model"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__no_price_available
msgid "NO_PRICE_AVAILABLE"
msgstr "NO_PRICE_AVAILABLE"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "Not enough credits for Snail Mail"
msgstr "Snail Mail için yeterli kredi yok"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "Bildirim türü"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__notification_ids
msgid "Notifications"
msgstr "Bildirimler"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "One or more required fields are empty."
msgstr "Bir veya daha fazla zorunlu alan boş."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__report_template
msgid "Optional report to print and attach"
msgstr "Yazdırıp eklemek için seçmeli çıktı"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid ""
"Our service cannot read your letter due to its format.<br/>\n"
"                Please modify the format of the template or update your settings\n"
"                to automatically add a blank cover page to all letters."
msgstr ""
"Hizmetimiz formatı nedeniyle mektubunuzu okuyamıyor.<br/>\n"
"                Lütfen şablonun biçimini değiştirin veya ayarlarınızı güncelleyin\n"
"                tıklayarak tüm harflere otomatik olarak boş bir kapak sayfası ekleyin."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__partner_id
msgid "Partner"
msgstr "İş Ortağı"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "Please use an A4 Paper format."
msgstr "Lütfen A4 Kağıt formatı kullanın."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_duplex
msgid "Print Both sides"
msgstr "Her iki tarafı da yazdır"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_color
msgid "Print In Color"
msgstr "Renkli Yazdır"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid "Re-send letter"
msgstr "Mektubu tekrar gönder"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__partner_id
msgid "Recipient"
msgstr "Alıcı"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__reference
msgid "Related Record"
msgstr "İlgili Kayıt"

#. module: snailmail
#: model:ir.model,name:snailmail.model_ir_actions_report
msgid "Report Action"
msgstr "Rapor işlemi"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_form
msgid "Send Now"
msgstr "Şimdi Gönder"

#. module: snailmail
#: model:iap.service,description:snailmail.iap_service_snailmail
msgid "Send your customer invoices and follow-up reports by post, worldwide."
msgstr ""

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core/notification_model_patch.js:0
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__sent
msgid "Sent"
msgstr "Gönderildi"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__user_id
msgid "Sent by"
msgstr "Gönderen"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "Snail Mails are successfully sent"
msgstr "Snail Postaları başarıyla gönderildi"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_message__message_type__snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__notification_type__snail
msgid "Snailmail"
msgstr "Snailmail"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_color
msgid "Snailmail Color"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_cover_readonly
msgid "Snailmail Cover Readonly"
msgstr ""

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_credit
msgid "Snailmail Credit Error"
msgstr "Snailmail Kredi Hatası"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/messaging_menu/messaging_menu_patch.js:0
msgid "Snailmail Failure: %(modelName)s"
msgstr ""

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/messaging_menu/messaging_menu_patch.js:0
msgid "Snailmail Failures"
msgstr "Snailmail Hataları"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_format
msgid "Snailmail Format Error"
msgstr "Snailmail Format Hatası"

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter
#: model:ir.model.fields,field_description:snailmail.field_mail_notification__letter_id
msgid "Snailmail Letter"
msgstr "Snailmail Mektubu"

#. module: snailmail
#: model:ir.actions.act_window,name:snailmail.action_mail_letters
#: model:ir.ui.menu,name:snailmail.menu_snailmail_letters
msgid "Snailmail Letters"
msgstr "Snailmail Mektupları"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_fields
msgid "Snailmail Missing Required Fields"
msgstr "Snailmail Gerekli Alanları Eksik"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_price
msgid "Snailmail No Price Available"
msgstr "Snailmail Fiyat Yok"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__message_id
msgid "Snailmail Status Message"
msgstr "Snailmail Durum İletisi"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_trial
msgid "Snailmail Trial Error"
msgstr "Snailmail Deneme Hatası"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_error
msgid "Snailmail Unknown Error"
msgstr "Snailmail Bilinmeyen Hata"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_mail__snailmail_error
#: model:ir.model.fields,field_description:snailmail.field_mail_message__snailmail_error
msgid "Snailmail message in error"
msgstr "Snailmail mesajı hatalı"

#. module: snailmail
#: model:ir.actions.server,name:snailmail.snailmail_print_ir_actions_server
msgid "Snailmail: process letters queue"
msgstr "Snailmail: işlem mektupları kuyruğu"

#. module: snailmail
#: model:iap.service,unit_name:snailmail.iap_service_snailmail
msgid "Stamps"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__state_id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__state_id
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "State"
msgstr "İl/Eyalet"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__state
msgid "Status"
msgstr "Durumu"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__street
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__street
msgid "Street"
msgstr "Sokak/Cadde"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Street 2..."
msgstr "Adres 2..."

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Street..."
msgstr "Adres..."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__street2
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__street2
msgid "Street2"
msgstr "Adres2"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__trial_error
msgid "TRIAL_ERROR"
msgstr "TRIAL_ERROR"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "The address of the recipient is not complete"
msgstr "Alıcının adresi tam değil"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid ""
"The attachment of the letter could not be sent. Please check its content and"
" contact the support if the problem persists."
msgstr ""
"Mektubun eki gönderilemedi. Lütfen içeriğini kontrol edin ve sorun devam "
"ederse destek ekibiyle iletişime geçin."

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "The country of the partner is not covered by Snailmail."
msgstr "Ortağın ülkesi Snailmail kapsamında değildir."

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid ""
"The country to which you want to send the letter is not supported by our "
"service."
msgstr ""
"Mektubu göndermek istediğiniz ülke hizmetimiz tarafından "
"desteklenmemektedir."

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid ""
"The customer address is not complete. Update the address here and re-send "
"the letter."
msgstr ""
"Müşteri adresi tam değil. Adresi buradan güncelleyin ve mektubu yeniden "
"gönderin."

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "The document was correctly sent by post.<br>The tracking id is %s"
msgstr ""
"Belge posta yoluyla doğru bir şekilde gönderildi.<br>İzleme kimliği: %s"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid ""
"The letter could not be sent due to insufficient credits on your IAP "
"account."
msgstr "IAP hesabınızdaki yetersiz kredi nedeniyle mektup gönderilemedi."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:snailmail.field_mail_message__message_type
msgid "Type"
msgstr "Tip"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__unknown_error
msgid "UNKNOWN_ERROR"
msgstr "UNKNOWN_ERROR"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Update Config and Re-send"
msgstr "Yapılandırmayı Güncelle ve Yeniden Gönder"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Update address and re-send"
msgstr "Adresi güncelleme ve yeniden gönderme"

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter_missing_required_fields
msgid "Update address of partner"
msgstr "İş ortağının adresini güncelleme"

#. module: snailmail
#: model:ir.model.fields,help:snailmail.field_mail_mail__message_type
#: model:ir.model.fields,help:snailmail.field_mail_message__message_type
msgid ""
"Used to categorize message generator\n"
"'email': generated by an incoming email e.g. mailgateway\n"
"'comment': generated by user input e.g. through discuss or composer\n"
"'email_outgoing': generated by a mailing\n"
"'notification': generated by system e.g. tracking messages\n"
"'auto_comment': generated by automated notification mechanism e.g. acknowledgment\n"
"'user_notification': generated for a specific recipient"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,help:snailmail.field_snailmail_letter__state
msgid ""
"When a letter is created, the status is 'Pending'.\n"
"If the letter is correctly sent, the status goes in 'Sent',\n"
"If not, it will got in state 'Error' and the error message will be displayed in the field 'Error Message'."
msgstr ""
"Bir mektup oluşturulduğunda, durum 'Beklemede' olur.\n"
"Mektup doğru bir şekilde gönderilirse, durum 'Gönderildi' olarak geçer,\n"
"Aksi takdirde, 'Hata' durumuna geçecek ve hata mesajı 'Hata Mesajı' alanında görüntülenecektir."

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid ""
"You don't have an IAP account registered for this service.<br>Please go to "
"<a href=%s target=\"new\">iap.odoo.com</a> to claim your free credits."
msgstr ""
"Bu hizmet için kayıtlı bir IAP hesabınız yok.<br>Ücretsiz kredilerinizi "
"almak için <a href=%s target=\"new\">iap.odoo.com</a> adresine gidin."

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid ""
"You don't have enough credits to perform this operation.<br>Please go to "
"your <a href=%s target=\"new\">iap account</a>."
msgstr ""
"Bu işlemi gerçekleştirmek için yeterli krediniz yok.<br>Lütfen <a href=%s "
"target=\"new\">iap hesabınıza</a> gidin."

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid "You need credits on your IAP account to send a letter."
msgstr "Mektup göndermek için IAP hesabınızda krediye ihtiyacınız vardır."

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "ZIP"
msgstr "PK"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__zip
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__zip
msgid "Zip"
msgstr "PK"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid "for further assistance."
msgstr "daha fazla yardım için."

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid "support"
msgstr "destek"
