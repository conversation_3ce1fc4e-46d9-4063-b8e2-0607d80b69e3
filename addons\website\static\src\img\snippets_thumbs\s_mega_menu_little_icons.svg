<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <linearGradient id="linearGradient-1" x1="0%" x2="100%" y1="0%" y2="100%">
      <stop offset="0%" stop-color="#00A09D"/>
      <stop offset="100%" stop-color="#00E2FF"/>
    </linearGradient>
	  <rect id="path-2" width="4" height="2" x="0" y="0"/>
    <path id="path-3" d="M0 0L11 0 11 1 0 1M0 2L8 2 8 3 0 3M0 4L11 4 11 5 0 5"/>
    <filter id="filter-4" width="105.6%" height="128.6%" x="-2.8%" y="-7.1%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <rect id="path-5" width="10" height="1" x="34" y="3"/>
    <filter id="filter-6" width="104.8%" height="300%" x="-2.4%" y="-50%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_mega_menu_little_icons">
      <rect width="82" height="60" class="bg"/>
      <g class="group" transform="translate(15 21)">
        <g class="group" transform="translate(0 1)">
          <circle cx="2" cy="2" r="2" fill="url(#linearGradient-1)" class="oval"/>
          <g class="rectangle" transform="translate(5 1)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-2"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
          </g>
          <circle cx="2" cy="2" r="2" transform="translate(12)" fill="url(#linearGradient-1)" class="oval"/>
          <g class="rectangle" transform="translate(17 1)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-2"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
          </g>
          <circle cx="2" cy="2" r="2" transform="translate(24)" fill="url(#linearGradient-1)" class="oval"/>
          <g class="rectangle" transform="translate(29 1)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-2"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
          </g>
          <circle cx="2" cy="2" r="2" transform="translate(0 6)" fill="url(#linearGradient-1)" class="oval"/>
          <g class="rectangle" transform="translate(6 7)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-2"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
          </g>
          <circle cx="2" cy="2" r="2" transform="translate(12 6)" fill="url(#linearGradient-1)" class="oval"/>
          <g class="rectangle" transform="translate(17 7)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-2"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
          </g>
          <circle cx="2" cy="2" r="2" transform="translate(24 6)" fill="url(#linearGradient-1)" class="oval"/>
          <g class="rectangle" transform="translate(29 7)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-2"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
          </g>
          <circle cx="2" cy="2" r="2" transform="translate(0 12)" fill="url(#linearGradient-1)"  class="oval"/>
          <g class="rectangle" transform="translate(6 13)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-2"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
          </g>
          <circle cx="2" cy="2" r="2" transform="translate(12 12)" fill="url(#linearGradient-1)" class="oval"/>
          <g class="rectangle" transform="translate(17 13)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-2"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
          </g>
          <circle cx="2" cy="2" r="2" transform="translate(24 12)" fill="url(#linearGradient-1)" class="oval"/>
          <g class="rectangle" transform="translate(29 13)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-2"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
          </g>
        </g>
        <rect width="17" height="18" fill="#D8D8D8" class="rectangle" opacity=".219" transform="translate(36)"/>
        <g class="combined_shape" transform="translate(39 7)">
          <use fill="#000" filter="url(#filter-12)" xlink:href="#path-3"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
        </g>
        <g class="rectangle_copy" transform="translate(5)">
          <use fill="#000" filter="url(#filter-6)" xlink:href="#path-5"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-5"/>
        </g>
      </g>
    </g>
  </g>
</svg>
