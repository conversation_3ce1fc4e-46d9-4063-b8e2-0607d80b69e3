# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* portal
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 13:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "\" to validate your action."
msgstr "\" per convalidare la tua azione."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_sidebar.js:0
msgid "%s days overdue"
msgstr "%s giorni di ritardo"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "1. Enter your password to confirm you own this account"
msgstr ""
"1. Inserisci la password per confermare che sei il proprietario dell'account"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid ""
"2. Confirm you want to delete your account by\n"
"                                        copying down your login ("
msgstr ""
"2. Conferma di voler eliminare l'account\n"
"                                        copiando il login ("

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.my_account_link
msgid ""
"<i class=\"fa fa-fw fa-id-card-o me-1 small text-primary text-primary-"
"emphasis\"/> My Account"
msgstr ""
"<i class=\"fa fa-fw fa-id-card-o me-1 small text-primary text-primary-"
"emphasis\"/> Il mio account"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.user_dropdown
msgid ""
"<i class=\"fa fa-fw fa-sign-out me-1 small text-primary text-primary-"
"emphasis\"/> Logout"
msgstr ""
"<i class=\"fa fa-fw fa-sign-out me-1 small text-primary text-primary-"
"emphasis\"/> Logout"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.user_dropdown
msgid ""
"<i class=\"fa fa-fw fa-th me-1 small text-primary text-primary-emphasis\"/> "
"Apps"
msgstr ""
"<i class=\"fa fa-fw fa-th me-1 small text-primary text-primary-emphasis\"/> "
"Applicazioni"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.side_content
msgid "<i class=\"fa fa-pencil\"/> Edit information"
msgstr "<i class=\"fa fa-pencil\"/> Modifica informazioni"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
msgid "<i class=\"oi oi-arrow-right me-1\"/>Back to edit mode"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>Torna alla modalità di modifica"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.record_pager
msgid ""
"<i class=\"oi oi-chevron-left\" role=\"img\" aria-label=\"Previous\" "
"title=\"Previous\"/>"
msgstr ""
"<i class=\"oi oi-chevron-left\" role=\"img\" aria-label=\"Previous\" "
"title=\"Previous\"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.record_pager
msgid "<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"Next\" title=\"Next\"/>"
msgstr "<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"Next\" title=\"Next\"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>"
msgstr "<i title=\"Documentazione\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">Nazione...</option>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "<option value=\"\">select...</option>"
msgstr "<option value=\"\">seleziona...</option>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid ""
"<small class=\"form-text text-muted\">\n"
"                Company name, VAT Number and country can not be changed once document(s) have been issued for your account.\n"
"                <br/>Please contact us directly for that operation.\n"
"            </small>"
msgstr ""
"<small class=\"form-text text-muted\">\n"
"                Il nome dell'azienda, il numero di Partita IVA e il Paese non possono essere modificati una volta che i documenti per il tuo conto sono stati emessi.\n"
"                <br/>Contattaci direttamente per quest'operazione.\n"
"            </small>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.pager
msgid ""
"<span class=\"fa fa-chevron-left\" role=\"img\" aria-label=\"Previous\" "
"title=\"Previous\"/>"
msgstr ""
"<span class=\"fa fa-chevron-left\" role=\"img\" aria-label=\"Previous\" "
"title=\"Previous\"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.pager
msgid "<span class=\"fa fa-chevron-right\" role=\"img\" aria-label=\"Next\" title=\"Next\"/>"
msgstr "<span class=\"fa fa-chevron-right\" role=\"img\" aria-label=\"Next\" title=\"Next\"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small me-1 navbar-text\">Filter By:</span>"
msgstr "<span class=\"small me-1 navbar-text\">Filtra per:</span>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small me-1 navbar-text\">Group By:</span>"
msgstr "<span class=\"small me-1 navbar-text\">Raggruppa per:</span>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small me-1 navbar-text\">Sort By:</span>"
msgstr "<span class=\"small me-1 navbar-text\">Ordina per:</span>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "<strong>Open </strong>"
msgstr "<strong>Apri </strong>"

#. module: portal
#: model:mail.template,body_html:portal.mail_template_data_portal_welcome
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Account</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.user_id.name or ''\">Marc Demo</span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.user_id.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.user_id.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.user_id.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"object.user_id.name or ''\">Marc Demo</t>,<br/> <br/>\n"
"                        Welcome to <t t-out=\"object.user_id.company_id.name\">YourCompany</t>'s Portal!<br/><br/>\n"
"                        An account has been created for you with the following login: <t t-out=\"object.user_id.login\">demo</t><br/><br/>\n"
"                        Click on the button below to pick a password and activate your account.\n"
"                        <div style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"                            <a t-att-href=\"object.user_id.partner_id._get_signup_url()\" style=\"display: inline-block; padding: 10px; text-decoration: none; font-size: 12px; background-color: #875A7B; color: #fff; border-radius: 5px;\">\n"
"                                <strong>Activate Account</strong>\n"
"                            </a>\n"
"                        </div>\n"
"                        <t t-out=\"object.wizard_id.welcome_message or ''\">Welcome to our company's portal.</t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.user_id.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.user_id.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.user_id.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.user_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.user_id.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.user_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=portalinvite\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Il tuo account</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.user_id.name or ''\">Marc Demo</span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.user_id.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.user_id.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.user_id.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Gentile <t t-out=\"object.user_id.name or ''\">Marc Demo</t>,<br/> <br/>\n"
"                        benvenuto nel portale dell'azienda <t t-out=\"object.user_id.company_id.name\">YourCompany</t>!<br/><br/>\n"
"                        È stato creato un account per te, con le seguenti credenziali: <t t-out=\"object.user_id.login\">demo</t><br/><br/>\n"
"                        Fai clic sul pulsante in basso per scegliere una password e attivare il tuo account.\n"
"                        <div style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"                            <a t-att-href=\"object.user_id.partner_id._get_signup_url()\" style=\"display: inline-block; padding: 10px; text-decoration: none; font-size: 12px; background-color: #875A7B; color: #fff; border-radius: 5px;\">\n"
"                                <strong>Attiva account</strong>\n"
"                            </a>\n"
"                        </div>\n"
"                        <t t-out=\"object.wizard_id.welcome_message or ''\">Benvenuto nel portale della nostra azienda.</t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.user_id.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.user_id.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.user_id.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.user_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.user_id.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.user_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=portalinvite\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: portal
#: model:ir.model,name:portal.model_res_users_apikeys_description
msgid "API Key Description"
msgstr "Descrizione chiave API"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
msgid "API Key Ready"
msgstr "Chiave API pronta"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/signature_form/signature_form.js:0
msgid "Accept & Sign"
msgstr "Accetta e firma"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_warning
#: model:ir.model.fields,field_description:portal.field_portal_share__access_warning
msgid "Access warning"
msgstr "Avviso di accesso"

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Account deleted!"
msgstr "Account eliminato!"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Add a note"
msgstr "Aggiungi nota"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Add attachment"
msgstr "Aggiunge allegato"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Add contacts to share the document..."
msgstr "Aggiungi contatti ai quali condividere il documento..."

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_share__note
msgid "Add extra content to display in the email"
msgstr "Aggiungi contenuti extra da visualizzare nell'e-mail"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Added On"
msgstr "Aggiunto il"

#. module: portal
#: model:ir.model.fields.selection,name:portal.selection__portal_wizard_user__email_state__exist
msgid "Already Registered"
msgstr "Già registrato"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Are you sure you want to do this?"
msgstr "Sei sicuro di volerlo fare?"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Avatar"
msgstr "Avatar"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Cancel"
msgstr "Annulla"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Change Password"
msgstr "Modifica password"

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid ""
"Changing VAT number is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"Impossibile modificare la partita IVA dopo l'emissione dei documenti per il "
"conto. Per questa operazione contattarci direttamente."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid ""
"Changing company name is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"Impossibile modificare il nome dell'azienda dopo l'emissione dei documenti "
"per il conto. Per questa operazione contattarci direttamente."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid ""
"Changing the country is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"Non è possibile modificare il Paese una volta che i documenti per il tuo "
"conto sono stati emessi. Contattaci direttamente per quest'operazione."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
msgid "Check failed"
msgstr "Controllo fallito"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "City"
msgstr "Città"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/signature_form/signature_form.xml:0
msgid "Click here to see your document."
msgstr "Clicca qui per vedere il tuo documento."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
#: model_terms:ir.ui.view,arch_db:portal.side_content
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Close"
msgstr "Chiudi"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Company Name"
msgstr "Nome azienda"

#. module: portal
#: model:ir.model,name:portal.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni di configurazione"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_home
msgid "Configure your connection parameters"
msgstr "Configura i parametri della tua connessione"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
msgid "Confirm"
msgstr "Conferma"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
msgid "Confirm Password"
msgstr "Conferma password"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_home
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Connection &amp; Security"
msgstr "Connessione e sicurezza"

#. module: portal
#: model:ir.model,name:portal.model_res_partner
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__partner_id
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
#: model_terms:ir.ui.view,arch_db:portal.portal_my_contact
#: model_terms:ir.ui.view,arch_db:portal.side_content
msgid "Contact"
msgstr "Contatto"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Contact Details"
msgstr "Dettagli Contatto"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Contacts"
msgstr "Contatti"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_composer.js:0
msgid "Could not save file <strong>%s</strong>"
msgstr "Impossibile salvare il file <strong>%s</strong>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Country"
msgstr "Nazione"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__create_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard__create_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__create_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard__create_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_res_config_settings__portal_allow_api_keys
msgid "Customer API Keys"
msgstr "Chiavi API Clienti"

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_mixin__access_url
msgid "Customer Portal URL"
msgstr "URL del portale clienti"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.res_config_settings_view_form
msgid "Customers can generate API Keys"
msgstr "I clienti possono generare chiavi API"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "Dear"
msgstr "Spett.le"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Delete"
msgstr "Elimina"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Delete Account"
msgstr "Elimina account"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Description"
msgstr "Descrizione"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_breadcrumbs
msgid "Details"
msgstr "Dettagli"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Developer API Keys"
msgstr "Chiavi API sviluppatore"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid ""
"Disable your account, preventing any further login.<br/>\n"
"                                        <b>\n"
"                                            <i class=\"fa fa-exclamation-triangle text-danger\"/>\n"
"                                            This action cannot be undone.\n"
"                                        </b>"
msgstr ""
"Disattiva il tuo account per prevenire ogni altro accesso.<br/>\n"
"                                        <b>\n"
"                                            <i class=\"fa fa-exclamation-triangle text-danger\"/>\n"
"                                            L'azione non può essere annullata.\n"
"                                        </b>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Discard"
msgstr "Abbandona"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__display_name
#: model:ir.model.fields,field_description:portal.field_portal_wizard__display_name
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_sidebar.js:0
msgid "Due in %s days"
msgstr "Scade tra %s giorni"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_sidebar.js:0
msgid "Due today"
msgstr "Scade oggi"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__email
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Email"
msgstr "E-mail"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Email Address already taken by another user"
msgstr "Indirizzo e-mail preso da un altro utente"

#. module: portal
#: model:ir.model,name:portal.model_mail_thread
msgid "Email Thread"
msgstr "Discussione e-mail"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Enter a description of and purpose for the key."
msgstr "Inserisci una descrizione e lo scopo della chiave."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Expiration Date"
msgstr "Data di scadenza"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Forgot password?"
msgstr "Password dimenticata?"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Give a duration for the key's validity"
msgstr "Dai una durata della validità della chiave"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Go Back"
msgstr "Indietro"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Grant Access"
msgstr "Concedi accesso"

#. module: portal
#: model:ir.model,name:portal.model_portal_wizard
msgid "Grant Portal Access"
msgstr "Concedi accesso al portale"

#. module: portal
#: model:ir.actions.act_window,name:portal.partner_wizard_action
#: model:ir.actions.server,name:portal.partner_wizard_action_create_and_open
msgid "Grant portal access"
msgstr "Concedi accesso al portale"

#. module: portal
#: model:ir.model,name:portal.model_ir_http
msgid "HTTP Routing"
msgstr "Instradamento HTTP"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid ""
"Here is your new API key, use it instead of a password for RPC access.\n"
"                Your login is still necessary for interactive usage."
msgstr ""
"Ecco la tua nuova chiave API, utilizzala al posto di una password per l'accesso RPC.\n"
"                Il login è ancora necessario per l'utilizzo interattivo."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_breadcrumbs
msgid "Home"
msgstr "Home"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__id
#: model:ir.model.fields,field_description:portal.field_portal_wizard__id
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__id
msgid "ID"
msgstr "ID"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Important:"
msgstr "Importante:"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Internal User"
msgstr "Utente interno"

#. module: portal
#: model:ir.model.fields.selection,name:portal.selection__portal_wizard_user__email_state__ko
msgid "Invalid"
msgstr "Non valido"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Invalid Email Address"
msgstr "Indirizzo e-mail non valido"

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Invalid Email! Please enter a valid email address."
msgstr "L'e-mail non è valida, inserire un indirizzo corretto."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Invalid report type: %s"
msgstr "Tipo di documento non valido: %s"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__welcome_message
msgid "Invitation Message"
msgstr "Messaggio di invito"

#. module: portal
#: model:mail.template,description:portal.mail_template_data_portal_welcome
msgid "Invitation email to contacts to create a user account"
msgstr "E-mail di invito ai contatti per la creazione di un account utente"

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_share.py:0
msgid "Invitation to access %s"
msgstr "Invito per accedere a %s"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__is_internal
msgid "Is Internal"
msgstr "È interno"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__is_portal
msgid "Is Portal"
msgstr "È portale"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid ""
"It is very important that this description be clear\n"
"                and complete,"
msgstr ""
"È molto importante che questa descrizione sia chiara\n"
"e completa,"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Key Description"
msgstr "Descrizione chiave"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__write_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard__write_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__write_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard__write_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__login_date
msgid "Latest Authentication"
msgstr "Ultima autenticazione"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Leave a comment"
msgstr "Lascia un commento"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__share_link
msgid "Link"
msgstr "Link"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Log out from all devices"
msgstr "Esci da tutti i dispositivi"

#. module: portal
#: model:ir.model,name:portal.model_mail_message
msgid "Message"
msgstr "Messaggio"

#. module: portal
#. odoo-python
#: code:addons/portal/models/mail_thread.py:0
msgid ""
"Model %(model_name)s does not support token signature, as it does not have "
"%(field_name)s field."
msgstr ""
"Il modello %(model_name)s non supporta la firma del token, non contiene il "
"campo %(field_name)s."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Multi company reports are not supported."
msgstr "I rendiconti per aziende multiple non sono supportati."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
msgid "My account"
msgstr "Il mio account"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Name"
msgstr "Nome"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Name your key"
msgstr "Dai un nome alla tua chiave"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "New API Key"
msgstr "Nuova chiave API"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "New Password:"
msgstr "Nuova password:"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Next"
msgstr "Succ"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__note
msgid "Note"
msgstr "Nota"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "Odoo Logo"
msgstr "Logo Odoo"

#. module: portal
#. odoo-python
#: code:addons/portal/models/res_users_apikeys_description.py:0
msgid "Only internal and portal users can create API keys"
msgstr ""
"Le chiavi API possono essere create solo da utenti interni e del portale"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Oops! Something went wrong. Try to reload the page and log in."
msgstr ""
"Ops! Qualcosa è andato storto. Ricaricare la pagina e provare nuovamente ad "
"accedere."

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__partner_ids
msgid "Partners"
msgstr "Partner"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Password"
msgstr "Password"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Password Updated!"
msgstr "Password aggiornata!"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Password:"
msgstr "Password:"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Phone"
msgstr "Telefono"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Please enter your password to confirm you own this account"
msgstr ""
"Inserisci la tua password per confermare che sei il proprietario di questo "
"account"

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Portal Access Management"
msgstr "Gestione degli accessi al portale"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_url
msgid "Portal Access URL"
msgstr "URL di accesso al portale"

#. module: portal
#: model:ir.model,name:portal.model_portal_mixin
msgid "Portal Mixin"
msgstr "Mixin portale"

#. module: portal
#: model:ir.model,name:portal.model_portal_share
msgid "Portal Sharing"
msgstr "Condivisione portale"

#. module: portal
#: model:ir.model,name:portal.model_portal_wizard_user
msgid "Portal User Config"
msgstr "Configurazione Utente Portale"

#. module: portal
#: model:mail.template,name:portal.mail_template_data_portal_welcome
msgid "Portal: User Invite"
msgstr "Portale: invito utente"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "Powered by"
msgstr "Fornito da"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Previous"
msgstr "Precedente"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid ""
"Put my email and phone in a block list to make sure I'm never contacted "
"again"
msgstr ""
"Inserisci il mio indirizzo e-mail e numero di telefono in una lista di "
"blocco per essere sicuro di non essere più contattato"

#. module: portal
#: model:ir.model,name:portal.model_ir_qweb
msgid "Qweb"
msgstr "QWeb"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Re-Invite"
msgstr "Reinvitare"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__partner_ids
msgid "Recipients"
msgstr "Destinatari"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__resource_ref
msgid "Related Document"
msgstr "Documento collegato"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__res_id
msgid "Related Document ID"
msgstr "ID documento collegato"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__res_model
msgid "Related Document Model"
msgstr "Modello documento collegato"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Revoke Access"
msgstr "Revoca l'accesso"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Revoke All Sessions"
msgstr "Revoca tutte le sessioni"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Save"
msgstr "Salva"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Scope"
msgstr "Ambito"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "Search"
msgstr "Ricerca"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Security"
msgstr "Sicurezza"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Security Control"
msgstr "Controllo di sicurezza"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_token
msgid "Security Token"
msgstr "Token di sicurezza"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid ""
"Select which contacts should belong to the portal in the list below.\n"
"                        The email address of each selected contact must be valid and unique.\n"
"                        If necessary, you can fix any contact's email address directly in the list."
msgstr ""
"Seleziona quali contatti dovrebbero poter accedere al portale dalla lista sottostante.\n"
"                        L'indirizo email di ogni contatto selezionato deve essere univoco e valido.\n"
"                        Se necessario, puoi modificare l'indirizzo email di un contatto direttamente all'interno della lista."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_composer.js:0
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Send"
msgstr "Invia"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_contact
msgid "Send message"
msgstr "Invia messaggio"

#. module: portal
#: model:ir.actions.act_window,name:portal.portal_share_action
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Share Document"
msgstr "Condividi documento"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_ir_ui_view__customize_show
msgid "Show As Optional Inherit"
msgstr "Mostra come eredità opzionale"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.user_sign_in
msgid "Sign in"
msgstr "Accedi"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.frontend_layout
msgid "Skip to Content"
msgstr "Passa al contenuto"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_composer.js:0
msgid ""
"Some fields are required. Please make sure to write a message or attach a "
"document"
msgstr ""
"Alcuni campi sono obbligatori. Assicurati di scrivere un messaggio o di "
"allegare un documento"

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Some required fields are empty."
msgstr "Alcuni campi obbligatori sono vuoti."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "State / Province"
msgstr "Stato / Provincia"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__email_state
msgid "Status"
msgstr "Stato"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Street"
msgstr "Indirizzo"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Street 2"
msgstr "Indirizzo 2"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/signature_form/signature_form.xml:0
msgid "Thank You!"
msgstr "Grazie!"

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "The attachment %s cannot be removed because it is linked to a message."
msgstr ""
"Impossibile rimuovere l'allegato %s perché è collegato a un messaggio."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid ""
"The attachment %s cannot be removed because it is not in a pending state."
msgstr ""
"Impossibile rimuovere l'allegato %s, non si trova in uno stato in sospeso."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid ""
"The attachment does not exist or you do not have the rights to access it."
msgstr "L'allegato non esiste o mancano i diritti di accesso."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "The contact \"%s\" does not have a valid email."
msgstr "Il contatto \"%s\" non ha un indirizzo e-mail valido."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "The contact \"%s\" has the same email as an existing user"
msgstr "Il contatto \"%s\" ha la stessa e-mail di un utente già esistente"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "The key cannot be retrieved later and provides"
msgstr "La chiave non può essere recuperata più tardi e fornisce"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "The key will be deleted once this period has elapsed."
msgstr "La chiave verrà eliminata una volta che il periodo è trascorso."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "The new password and its confirmation must be identical."
msgstr "La nuova password e quella di conferma devono corrispondere."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid ""
"The old password you provided is incorrect, your password was not changed."
msgstr ""
"La password precedente fornita non è corretta, nessuna modifica effettuata."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "The partner \"%s\" already has the portal access."
msgstr "Il partner \"%s\" ha già accesso al portale."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "The partner \"%s\" has no portal access or is internal."
msgstr "Il partner \"%s\" non ha accesso al portale o è interno."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid ""
"The template \"Portal: new user\" not found for sending email to the portal "
"user."
msgstr ""
"Template \"Portale: nuovo utente\" non trovato per l'invio di email "
"all'utente del portale."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "This document does not exist."
msgstr "Questo documento non esiste."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
msgid "This is a preview of the customer portal."
msgstr "Questa è una anteprima del portale clienti."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid ""
"This partner is linked to an internal User and already has access to the "
"Portal."
msgstr ""
"Questo partner è collegato a un utente interno e ha già accesso al portale."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid ""
"This text is included at the end of the email sent to new portal users."
msgstr ""
"Questo testo è incluso alla fine dell'e-mail inviata ai nuovi utenti del "
"portale."

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_wizard__welcome_message
msgid "This text is included in the email sent to new users of the portal."
msgstr ""
"Questo testo è contenuto nell'e-mail inviata ai nuovi utenti del portale."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "Toggle filters"
msgstr "Commuta filtri"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__user_id
msgid "User"
msgstr "Utente"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__user_ids
msgid "Users"
msgstr "Utenti"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "VAT Number"
msgstr "Partita IVA"

#. module: portal
#: model:ir.model.fields.selection,name:portal.selection__portal_wizard_user__email_state__ok
msgid "Valid"
msgstr "Valido"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Valid Email Address"
msgstr "Indirizzo e-mail valido"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Verify New Password:"
msgstr "Verifica nuova password:"

#. module: portal
#: model:ir.model,name:portal.model_ir_ui_view
msgid "View"
msgstr "Vista"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_account_analytic_account__website_message_ids
#: model:ir.model.fields,field_description:portal.field_calendar_event__website_message_ids
#: model:ir.model.fields,field_description:portal.field_crm_team__website_message_ids
#: model:ir.model.fields,field_description:portal.field_crm_team_member__website_message_ids
#: model:ir.model.fields,field_description:portal.field_discuss_channel__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle_log_contract__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle_log_services__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle_model__website_message_ids
#: model:ir.model.fields,field_description:portal.field_gamification_badge__website_message_ids
#: model:ir.model.fields,field_description:portal.field_gamification_challenge__website_message_ids
#: model:ir.model.fields,field_description:portal.field_iap_account__website_message_ids
#: model:ir.model.fields,field_description:portal.field_lunch_supplier__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_cc__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_main_attachment__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_phone__website_message_ids
#: model:ir.model.fields,field_description:portal.field_maintenance_equipment__website_message_ids
#: model:ir.model.fields,field_description:portal.field_maintenance_equipment_category__website_message_ids
#: model:ir.model.fields,field_description:portal.field_maintenance_request__website_message_ids
#: model:ir.model.fields,field_description:portal.field_phone_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_category__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_pricelist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_product__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_template__website_message_ids
#: model:ir.model.fields,field_description:portal.field_rating_mixin__website_message_ids
#: model:ir.model.fields,field_description:portal.field_res_partner__website_message_ids
#: model:ir.model.fields,field_description:portal.field_res_users__website_message_ids
msgid "Website Messages"
msgstr "Messaggi sito web"

#. module: portal
#: model:ir.model.fields,help:portal.field_account_analytic_account__website_message_ids
#: model:ir.model.fields,help:portal.field_calendar_event__website_message_ids
#: model:ir.model.fields,help:portal.field_crm_team__website_message_ids
#: model:ir.model.fields,help:portal.field_crm_team_member__website_message_ids
#: model:ir.model.fields,help:portal.field_discuss_channel__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle_log_contract__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle_log_services__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle_model__website_message_ids
#: model:ir.model.fields,help:portal.field_gamification_badge__website_message_ids
#: model:ir.model.fields,help:portal.field_gamification_challenge__website_message_ids
#: model:ir.model.fields,help:portal.field_iap_account__website_message_ids
#: model:ir.model.fields,help:portal.field_lunch_supplier__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_cc__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_main_attachment__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_phone__website_message_ids
#: model:ir.model.fields,help:portal.field_maintenance_equipment__website_message_ids
#: model:ir.model.fields,help:portal.field_maintenance_equipment_category__website_message_ids
#: model:ir.model.fields,help:portal.field_maintenance_request__website_message_ids
#: model:ir.model.fields,help:portal.field_phone_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_product_category__website_message_ids
#: model:ir.model.fields,help:portal.field_product_pricelist__website_message_ids
#: model:ir.model.fields,help:portal.field_product_product__website_message_ids
#: model:ir.model.fields,help:portal.field_product_template__website_message_ids
#: model:ir.model.fields,help:portal.field_rating_mixin__website_message_ids
#: model:ir.model.fields,help:portal.field_res_partner__website_message_ids
#: model:ir.model.fields,help:portal.field_res_users__website_message_ids
msgid "Website communication history"
msgstr "Cronologia comunicazioni sito web"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "What's this key for?"
msgstr "A cosa serve questa chiave?"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__wizard_id
msgid "Wizard"
msgstr "Procedura guidata"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Write a message..."
msgstr "Scrivi un messaggio..."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/chatter/core/composer_patch.js:0
msgid "Write a message…"
msgstr "Scrivi un messaggio..."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Write down your key"
msgstr "Annota la tua chiave"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Wrong password."
msgstr "Password errata."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "You cannot leave any password empty."
msgstr "La password non può essere vuota."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "You must be"
msgstr "Devi essere"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "You should enter \""
msgstr "Dovresti inserire \""

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "You should first grant the portal access to the partner \"%s\"."
msgstr "Devi prima concedere l'accesso al portale al partner \"%s\"."

#. module: portal
#: model:mail.template,subject:portal.mail_template_data_portal_welcome
msgid "Your account at {{ object.user_id.company_id.name }}"
msgstr "Il tuo account con {{ object.user_id.company_id.name }}"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_contact
msgid "Your contact"
msgstr "Contatto"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Zip / Postal Code"
msgstr "CAP"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "full access"
msgstr "accesso completo"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "has invited you to access the following"
msgstr "ti ha invitato ad accedere al seguente"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid ""
"it will be the only way to\n"
"                identify the key once created"
msgstr ""
"sarà l'unico modo per\n"
"                individuare la chiave una volta creata"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "logged in"
msgstr "autenticato"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "odoo"
msgstr "odoo"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "password"
msgstr "password"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "to post a comment."
msgstr "per inviare un commento."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "to your user account, it is very important to store it securely."
msgstr ""
"per il tuo account utente, è molto importante custodirlo in sicurezza."
