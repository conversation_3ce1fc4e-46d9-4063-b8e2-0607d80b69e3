<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="hr_attendance.public_kiosk_mode" name="Attendance Kiosk">
        <t t-call="web.layout">
            <t t-set="html_data" t-value="{'lang': kiosk_backend_info['lang']}"/>
            <t t-set="head">
                <title>Attendance Kiosk</title>
                <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
                <meta http-equiv="content-type" content="text/html, charset=utf-8" />
                <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                <t t-call-assets="hr_attendance.assets_public_attendance" t-js="false"/>
                <t t-call-assets="hr_attendance.assets_public_attendance" t-css="false"/>
                <t t-call="web.conditional_assets_tests">
                    <t t-set="ignore_missing_deps" t-value="True"/>
                </t>

                <script type="text/javascript">
                    odoo.define("hr_attendance.public_kiosk_app", ["@hr_attendance/public_kiosk/public_kiosk_app"], function (require) {
                    var { createPublicKioskAttendance } = require("@hr_attendance/public_kiosk/public_kiosk_app");
                    createPublicKioskAttendance(document, <t t-out="json.dumps(kiosk_backend_info)"/>);
                    });
                </script>
            </t>
            <t t-set="body">
            </t>
            <body class="o_web_client o_hr_attendance_kiosk_body position-relative">
            </body>
        </t>
    </template>
</odoo>
