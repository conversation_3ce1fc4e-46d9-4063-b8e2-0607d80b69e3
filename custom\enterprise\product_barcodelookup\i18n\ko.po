# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_barcodelookup
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "API Key:"
msgstr "API 키:"

#. module: product_barcodelookup
#: model:ir.model.fields,field_description:product_barcodelookup.field_res_config_settings__barcodelookup_api_key
msgid "API key"
msgstr "API 키"

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "Barcode Database"
msgstr "바코드 데이터베이스"

#. module: product_barcodelookup
#: model:ir.model.fields,help:product_barcodelookup.field_res_config_settings__barcodelookup_api_key
msgid "Barcode Lookup API Key for create product from barcode."
msgstr "바코드에서 제품을 생성하기 위한 바코드 조회 API 키입니다."

#. module: product_barcodelookup
#: model:ir.model,name:product_barcodelookup.model_res_config_settings
msgid "Config Settings"
msgstr "환경 설정"

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "Create products by scanning using"
msgstr "다음으로 스캔하여 품목을 생성합니다."

#. module: product_barcodelookup
#: model:ir.model,name:product_barcodelookup.model_product_template
msgid "Product"
msgstr "품목"

#. module: product_barcodelookup
#: model:ir.model,name:product_barcodelookup.model_product_product
msgid "Product Variant"
msgstr "품목 세부사항"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_8
msgid "age group"
msgstr "연령대"

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "barcodelookup.com"
msgstr "barcodelookup.com"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_6
msgid "brand"
msgstr "브랜드"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_1
msgid "color"
msgstr "색상"

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "e.g. d7vctmiv2rwgenebha8bxq7irooudn"
msgstr "예: d7vctmiv2rwgenebha8bxq7irooudn"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_2
msgid "gender"
msgstr "성별"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_5
msgid "manufacturer"
msgstr "제조업체"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_3
msgid "material"
msgstr "재료"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_4
msgid "pattern"
msgstr "패턴"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_7
msgid "size"
msgstr "크기"
