# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_adyen
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Ra<PERSON><PERSON><PERSON> Lappiam, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_adyen
#: model_terms:ir.ui.view,arch_db:pos_adyen.res_config_settings_view_form
msgid "Add tip through payment terminal (Adyen)"
msgstr "เพิ่มทิปผ่านจุดชำระเงิน (Adyen)"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_api_key
msgid "Adyen API key"
msgstr "Adyen API คีย์"

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "Adyen Error"
msgstr "ข้อผิดพลาด Adyen"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_latest_response
msgid "Adyen Latest Response"
msgstr "การตอบสนอง Adyen ล่าสุด"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_terminal_identifier
msgid "Adyen Terminal Identifier"
msgstr "ตัวระบุสถานี Adyen"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_test_mode
msgid "Adyen Test Mode"
msgstr "โหมดทดลอง Adyen"

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "An unexpected error occurred. Message from Adyen: %s"
msgstr "เกิดข้อผิดพลาดทีคาดไม่ถึง ข้อความจาก Adyen: %s"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_config__adyen_ask_customer_for_tip
msgid "Ask Customers For Tip"
msgstr "ขอทิปจากลูกค้า"

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "Authentication failed. Please check your Adyen credentials."
msgstr "การตรวจสอบสิทธิ์ล้มเหลว โปรดตรวจสอบข้อมูลรับรอง Adyen ของคุณ"

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid ""
"Cancelling the payment failed. Please cancel it manually on the payment "
"terminal."
msgstr "การยกเลิกการชำระเงินล้มเหลว โปรดยกเลิกด้วยตนเองที่จุดชำระเงิน"

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "Cannot process transactions with negative amount."
msgstr "ไม่สามารถประมวลผลธุรกรรมที่มียอดติดลบได้"

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid ""
"Could not connect to the Odoo server, please check your internet connection "
"and try again."
msgstr ""
"ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ Odoo ได้ "
"โปรดตรวจสอบการเชื่อมต่ออินเตอร์เน็ตของคุณและลองอีกครั้ง"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_event_url
msgid "Event URL"
msgstr ""

#. module: pos_adyen
#. odoo-python
#: code:addons/pos_adyen/models/pos_payment_method.py:0
msgid "Invalid Adyen request"
msgstr "คำขอ Adyen ไม่ถูกต้อง"

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "Message from Adyen: %s"
msgstr "ข้อความจาก Adyen: %s"

#. module: pos_adyen
#. odoo-python
#: code:addons/pos_adyen/models/pos_config.py:0
msgid ""
"Please configure a tip product for POS %s to support tipping with Adyen."
msgstr "โปรดกำหนดค่าสินค้าทิปสำหรับ POS %sเพื่อรองรับการให้ทิปกับเอเดียน"

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_pos_config
msgid "Point of Sale Configuration"
msgstr "กำหนดค่าการขายหน้าร้าน"

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "วิธีการชำระเงินการขายหน้าร้าน"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_res_config_settings__pos_adyen_ask_customer_for_tip
msgid "Pos Adyen Ask Customer For Tip"
msgstr "Pos Adyen ขอทิปจากลูกค้า"

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_test_mode
msgid "Run transactions in the test environment."
msgstr "ดำเนินการธุรกรรมในสภาพแวดล้อมทดลอง"

#. module: pos_adyen
#. odoo-python
#: code:addons/pos_adyen/models/pos_payment_method.py:0
msgid ""
"Terminal %(terminal)s is already used in company %(company)s on payment "
"method %(payment_method)s."
msgstr ""
"เทอร์มินัล %(terminal)s ถูกใช้แล้วในบริษัท %(company)s "
"เกี่ยวกับวิธีการชำระเงิน %(pay_method)s"

#. module: pos_adyen
#. odoo-python
#: code:addons/pos_adyen/models/pos_payment_method.py:0
msgid ""
"Terminal %(terminal)s is already used on payment method %(payment_method)s."
msgstr "เทอร์มินัล %(terminal)s ถูกใช้แล้วกับวิธีการชำระเงิน %(pay_method)s"

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_event_url
msgid "This URL needs to be pasted on Adyen's portal terminal settings."
msgstr "ต้องวาง URL นี้ลงในส่วนการตั้งค่าเทอร์มินัลพอร์ทัลของ Adyen"

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_api_key
msgid ""
"Used when connecting to Adyen: https://docs.adyen.com/user-management/how-"
"to-get-the-api-key/#description"
msgstr ""
"ใช้เมื่อเชื่อมต่อกับ Adyen: https://docs.adyen.com/user-management/how-to-"
"get-the-api-key/#description"

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_terminal_identifier
msgid "[Terminal model]-[Serial number], for example: P400Plus-123456789"
msgstr "[Terminal model]-[Serial number] ตัวอย่างเช่น: P400Plus-123456789"
