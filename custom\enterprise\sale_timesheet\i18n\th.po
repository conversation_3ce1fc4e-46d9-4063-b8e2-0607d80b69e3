# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_timesheet
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>hipree<PERSON>, 2024
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:04+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Ra<PERSON><PERSON><PERSON> Lappiam, 2025\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/sale_order.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    No activities found. Let's start a new one!\n"
"                </p><p>\n"
"                    Track your working hours by projects every day and invoice this time to your customers.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    ไม่พบกิจกรรม มาเริ่มต้นใหม่กันเถอะ!\n"
"                </p><p>\n"
"                    ติดตามชั่วโมงทำงานของคุณทุกวันตามโปรเจ็กต์และออกใบแจ้งหนี้ให้กับลูกค้าของคุณในครั้งนี้\n"
"                </p>\n"
"            "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/account_move.py:0
#: code:addons/sale_timesheet/models/project_project.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Record timesheets\n"
"                </p><p>\n"
"                    You can register and track your workings hours by project every\n"
"                    day. Every time spent on a project will become a cost and can be re-invoiced to\n"
"                    customers if required.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    บันทึกเวลา\n"
"                </p><p>\n"
"                    คุณสามารถลงทะเบียนและติดตามชั่วโมงการทำงานของคุณในแต่ล่ะโปรเจ็กต์ได้ทุกวัน\n"
"                    ทุกช่วงเวลาที่ใช้ในโปรเจ็กต์รจะถูกคิดเงินและสามารถออกใบแจ้งหนี้ใหม่ให้แก่\n"
"                    ลูกค้าได้หากจำเป็น\n"
"                </p>\n"
"            "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
msgid ""
"'%(missing_plan_names)s' analytic plan(s) required on the analytic "
"distribution of the sale order item '%(so_line_name)s' linked to the "
"timesheet."
msgstr ""
"จำเป็นต้องมีแผนวิเคราะห์ '%(missing_plan_names)s' "
"สำหรับการแจกจ่ายวิเคราะห์ของรายการใบสั่งขาย '%(so_line_name)s' "
"ที่เชื่อมโยงกับตารางเวลา"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_sale_page
msgid "- Timesheet product"
msgstr "- ใบบันทึกเวลาสินค้า"

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "1 Onsite Interview"
msgstr "1 สัมภาษณ์นอกสถานที่"

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "1 Phone Call"
msgstr "1 สายเรียกเข้า"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "12 days / year, including <br>6 of your choice."
msgstr "12 วัน/ ปี รวมถึง <br>6 ของตัวเลือกของคุณ"

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "2 open days"
msgstr "2 วันเปิด"

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "4 Days after Interview"
msgstr "4 วันหลังการสัมภาษณ์"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "<b>Daily Cost: </b>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "<b>Unit Price: </b>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid ""
"<i class=\"fa fa-lightbulb-o\"/>\n"
"                        <span>\n"
"                            Define the rate at which an employee's time is billed based on their expertise, skills, or experience.\n"
"                            To bill the same service at a different rate, create separate sales order items.\n"
"                        </span>"
msgstr ""
"<i class=\"fa fa-lightbulb-o\"/>\n"
"                        <span>\n"
"                            กำหนดอัตราการเรียกเก็บเงินเวลาของพนักงานตามความเชี่ยวชาญ ทักษะ หรือประสบการณ์ของพวกเขา\n"
"                            หากต้องการเรียกเก็บเงินบริการเดียวกันในอัตราที่แตกต่างกัน ให้สร้างรายการใบสั่งขายแยกต่างหาก\n"
"                        </span>"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "<small><b>READ</b></small>"
msgstr "<small><b>อ่าน</b></small>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_form_inherit
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning\" title=\"The sales "
"order associated with this timesheet entry has been cancelled.\" "
"invisible=\"sale_order_state != 'cancel'\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning\" "
"title=\"ใบสั่งขายที่เกี่ยวข้องกับรายการใบบันทึกเวลานี้ถูกยกเลิกแล้ว\" "
"invisible=\"sale_order_state != 'cancel'\"/>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_form_inherit
msgid "<span class=\"o_stat_text\">Invoice</span>"
msgstr "<span class=\"o_stat_text\">ใบแจ้งหนี้</span>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.account_invoice_view_form_inherit_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
msgid "<span class=\"o_stat_text\">Recorded</span>"
msgstr "<span class=\"o_stat_text\">บันทึกแล้ว</span>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_form_inherit
msgid "<span class=\"o_stat_text\">Sales Order</span>"
msgstr "<span class=\"o_stat_text\">คำสั่งขาย</span>"

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "<span class=\"text-muted small\">Days to get an Offer</span>"
msgstr "<span class=\"text-muted small\">วันที่จะได้รับข้อเสนอ</span>"

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "<span class=\"text-muted small\">Process</span>"
msgstr "<span class=\"text-muted small\">กระบวนการ</span>"

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "<span class=\"text-muted small\">Time to Answer</span>"
msgstr "<span class=\"text-muted small\">เวลาที่จะตอบ</span>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Amount Due:</strong>"
msgstr "<strong>จำนวนเงินที่ครบกำหนด:</strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Invoiced:</strong>"
msgstr "<strong>แจ้งหนี้แล้ว:</strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Invoices:</strong>"
msgstr "<strong>แจ้งหนี้:</strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Sales Order:</strong>"
msgstr "<strong>คำสั่งขาย:</strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_timesheet_table_inherit
msgid "<strong>Time Remaining on SO: </strong>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "<u>Profitability</u>"
msgstr "<u>การได้กำไร</u>"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "A full-time position <br>Attractive salary package."
msgstr "ตำแหน่งงานเต็มเวลา <br>แพ็คเกจเงินเดือนที่น่าดึงดูด"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"ตามการกำหนดค่าของสินค้า ปริมาณที่ส่งมอบสามารถคำนวณโดยอัตโนมัติตามกลไก:\n"
"- กำหนดเอง: ปริมาณถูกตั้งค่าด้วยตนเองในรายการ\n"
"- วิเคราะห์จากค่าใช้จ่าย: ปริมาณคือผลรวมปริมาณจากค่าใช้จ่ายที่ผ่านรายการ\n"
"- ระบบบันทึกเวลา: ปริมาณคือผลรวมของชั่วโมงที่บันทึกไว้ในงานที่เชื่อมโยงกับรายการการขายนี้\n"
"- การเคลื่อนย้ายสต็อค: ปริมาณมาจากการเลือกที่ได้รับการยืนยัน\n"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Achieve monthly sales objectives"
msgstr "บรรลุเป้าหมายยอดขายประจำเดือน"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Additional languages"
msgstr "ภาษาเพิ่มเติม"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Administrative Work"
msgstr "งานธุรการ"

#. module: sale_timesheet
#: model:account.analytic.account,name:sale_timesheet.account_analytic_account_project_support
#: model:project.project,name:sale_timesheet.project_support
msgid "After-Sales Services"
msgstr "บริการหลังการขาย"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__allocated_hours
msgid "Allocated Time"
msgstr "เวลาที่ถูกจัดสรร"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__amount_to_invoice
msgid "Amount to invoice"
msgstr "จำนวนที่จะออกใบ้แจ้งหนี้"

#. module: sale_timesheet
#: model:ir.model.constraint,message:sale_timesheet.constraint_project_sale_line_employee_map_uniqueness_employee
msgid ""
"An employee cannot be selected more than once in the mapping. Please remove "
"duplicate(s) and try again."
msgstr ""
"ไม่สามารถเลือกพนักงานได้มากกว่าหนึ่งครั้งในแผนผัง "
"โปรดลบรายการที่ซ้ำกันและลองใหม่อีกครั้ง"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "รายการการวิเคราะห์"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__analytic_line_ids
msgid "Analytic lines"
msgstr "รายการการวิเคราะห์"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid ""
"As an employee of our company, you will <b>collaborate with each department\n"
"                        to create and deploy disruptive products.</b> Come work at a growing company\n"
"                        that offers great benefits with opportunities to moving forward and learn\n"
"                        alongside accomplished leaders. We're seeking an experienced and outstanding\n"
"                        member of staff.\n"
"                        <br><br>\n"
"                        This position is both <b>creative and rigorous</b> by nature you need to think\n"
"                        outside the box. We expect the candidate to be proactive and have a \"get it done\"\n"
"                        spirit. To be successful, you will have solid solving problem skills."
msgstr ""
"ในฐานะของพนักงานบริษัท คุณจะ<b>ร่วมมือกับแผนกต่างๆ \n"
"                       เพื่อสร้างและปรับใช้ผลิตภัณฑ์</b> มาทำงานในบริษัทที่กำลังเติบโต\n"
"                        ที่มอบประโยชน์มหาศาลพร้อมโอกาสแบบก้าวกระโดดและเรียนรู้\n"
"                        ควบคู่ไปกับหัวหน้าที่ประสบความสำเร็จ เรากำลังมองหาผู้มีประสบการณ์และโดดเด่น\n"
"                       เพื่อมาเป็นพนักงาน\n"
"                        <br/><br/>\n"
"                        ตำแหน่งนี้ต้องมีทั้ง<b>ความคิดสร้างสรรค์และความรอบคอบ</b> คุณต้องคิด\n"
"                        นอกกรอบได้โดยธรรมชาติ เราคาดหวังให้ผู้สมัครมีความกระตือรือร้นและ \"จิตวิญญาณแห่งความสำเร็จ\"\n"
"                        เพื่อประสบความสำเร็จ คุณจะต้องมีทักษะการแก้ปัญหาที่ชัดเจน"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Autonomy"
msgstr "มีอิสระ"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Bachelor Degree or Higher"
msgstr "ปริญญาตรีหรือสูงกว่า"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product_template.py:0
msgid "Based on Timesheets"
msgstr "อิงตามใบบันทึกเวลา"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__allow_billable
msgid "Billable"
msgstr "สามารถเรียกเก็บเงินได้"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__billable_time
msgid "Billable Time"
msgstr "เวลาที่เรียกเก็บเงินได้"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__timesheet_invoice_type
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__timesheet_invoice_type
msgid "Billable Type"
msgstr "ประเภทการเรียกเก็บเงิน"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Billed"
msgstr "ออกบิลแล้ว"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_manual
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_manual
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed Manually"
msgstr "เรียกเก็บเงินด้วยตนเอง"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed at a Fixed Price"
msgstr "เรียกเก็บเงินในราคาคงที่"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_fixed
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_fixed
msgid "Billed at a Fixed price"
msgstr "เรียกเก็บเงินในราคาคงที่"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_milestones
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_milestones
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed on Milestones"
msgstr "เรียกเก็บเงินตามเหตุการณ์สำคัญ"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_time
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_time
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed on Timesheets"
msgstr "เรียกเก็บเงินตามใบบันทึกเวลา"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Billing"
msgstr "ทำรายการบิล"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__billing_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billing Type"
msgstr "ประเภทการเรียกเก็บเงิน"

#. module: sale_timesheet
#: model:ir.ui.menu,name:sale_timesheet.menu_timesheet_billing_analysis
msgid "By Billing Type"
msgstr "โดยประเภทบิล"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__sale_order_id
msgid "Choose the Sales Order to invoice"
msgstr "เลือกคำสั่งขายที่จะออกใบแจ้งหนี้"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__commercial_partner_id
msgid "Commercial Partner"
msgstr "พาร์ทเนอร์ทางการค้า"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Configure your services"
msgstr "กำหนดค่าบริการของคุณ"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__cost
msgid "Cost"
msgstr "ต้นทุน"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Costs"
msgstr "ต้นทุน"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.project_project_action_multi_create_invoice
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Create Invoice"
msgstr "สร้างใบแจ้งหนี้"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_invoice
msgid "Create Invoice from project"
msgstr "สร้างใบแจ้งหนี้ของโปรเจ็กต์"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Create Sales Order from Project"
msgstr "สร้างคำสั่งขายจากโปรเจ็กต์"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Create content that will help our users on a daily basis"
msgstr "สร้างเนื้อหาที่จะช่วยเหลือผู้ใช้ของเราในแต่ละวัน"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__currency_id
msgid "Currency"
msgstr "สกุลเงิน"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__partner_id
msgid "Customer"
msgstr "ลูกค้า"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_order_timesheet_product_template
msgid "Customer Care (Prepaid Hours)"
msgstr "ฝ่ายดูแลลูกค้า (ชั่วโมงที่จ่ายล่วงหน้า)"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_kanban_inherit_sale_timesheet
msgid "Customer Ratings"
msgstr "การให้คะแนนลูกค้า"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Customer Relationship"
msgstr "ความสัมพันธ์กับลูกค้า"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Days Ordered,"
msgstr "วันที่สั่งแล้ว,"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Days Remaining)"
msgstr "วันคงเหลือ)"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Discard"
msgstr "ละทิ้ง"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Discover our products."
msgstr "ค้นพบสินค้าของเรา"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "Draft Invoice"
msgstr "ใบแจ้งหนี้ฉบับร่าง"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid ""
"Each employee has a chance to see the impact of his work.\n"
"                    You can make a real contribution to the success of the company.\n"
"                    <br>\n"
"                    Several activities are often organized all over the year, such as weekly\n"
"                    sports sessions, team building events, monthly drink, and much more"
msgstr ""
"พนักงานแต่ละคนมีโอกาสที่จะเห็นผลประโยชน์จากการทำงานของเขา\n"
"                    คุณสามารถมีส่วนร่วมอย่างแท้จริงต่อความสำเร็จของบริษัท\n"
"                    <br>\n"
"                    และมักจะมีการจัดกิจกรรมต่างๆ ตลอดทั้งปี เช่น รายสัปดาห์\n"
"                    เซสชันกีฬา กิจกรรมสร้างทีม เครื่องดื่มประจำเดือน และอื่นๆ อีกมากมาย"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Eat &amp; Drink"
msgstr "กินและดื่ม"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_product_elevator_installation_product_template
msgid "Elevator Installation"
msgstr "การติดตั้งลิฟต์"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_hr_employee
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__employee_id
msgid "Employee"
msgstr "พนักงาน"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__employee_rate
msgid "Employee rate"
msgstr "อัตราพนักงาน"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__date_end_invoice_timesheet
msgid "End Date"
msgstr "วันที่สิ้นสุด"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Expand your knowledge of various business industries"
msgstr "ขยายความรู้ของคุณในอุตสาหกรรมธุรกิจต่าง ๆ"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Expected"
msgstr "คาดหวัง"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Experience in writing online content"
msgstr "ประสบการณ์เขียนคอนเทนต์ออนไลน์"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Fixed price services"
msgstr "ราคาค่าบริการคงที่"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Fruit, coffee and <br>snacks provided."
msgstr "มีผลไม้ กาแฟ และ<br>ของว่าง"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_manual_product_template
msgid "Furniture Delivery (Manual)"
msgstr "การจัดส่งเฟอร์นิเจอร์ (ด้วยตนเอง)"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Google Adwords experience"
msgstr "ประสบการณ์ Google Adwords"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Great team of smart people, in a friendly and open culture"
msgstr "ทีมงานที่ยอดเยี่ยม ในวัฒนธรรมที่เป็นกันเองและเปิดกว้าง"

#. module: sale_timesheet
#: model:hr.job,name:sale_timesheet.job_labour
msgid "Handyman"
msgstr "ช่างซ่อม"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__has_multi_sol
msgid "Has Multi Sol"
msgstr "มีหลากหลาย Sol"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Highly creative and autonomous"
msgstr "มีความคิดสร้างสรรค์สูงและเป็นอิสระ"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__display_cost
msgid "Hourly Cost"
msgstr "ค่าใช้จ่ายรายชั่วโมง"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Hours Ordered,"
msgstr "ชั่วโมงที่สั่ง"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Hours Remaining)"
msgstr "ชั่วโมงที่เหลืออยู่)"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__id
msgid "ID"
msgstr "ไอดี"

#. module: sale_timesheet
#: model:hr.job,name:sale_timesheet.job_interior_designer
msgid "Interior Designer"
msgstr "นักออกแบบตกแต่งภายใน"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_product_interior_designing_product_template
msgid "Interior Designing"
msgstr "การออกแบบตกแต่งภายใน"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__timesheet_invoice_id
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__timesheet_invoice_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.report_timesheet_account_move
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Invoice"
msgstr "ใบแจ้งหนี้"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_res_config_settings__invoice_policy
msgid "Invoice Policy"
msgstr "นโยบายการออกใบแจ้งหนี้"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product_template.py:0
msgid "Invoice based on timesheets (delivered quantity)."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_analytic_line__timesheet_invoice_id
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__timesheet_invoice_id
msgid "Invoice created from the timesheet"
msgstr "ใบแจ้งหนี้ที่สร้างจากใบบันทึกเวลา"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Invoiced"
msgstr "ออกใบแจ้งหนี้แล้ว"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Invoices"
msgstr "การแจ้งหนี้"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Invoicing"
msgstr "ออกใบแจ้งหนี้"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__is_project_map_empty
msgid "Is Project map empty"
msgstr "แผนผังโครงการว่างเปล่า"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__is_so_line_edited
msgid "Is Sales Order Item Manually Edited"
msgstr "มีการแก้ไขรายการสั่งซื้อด้วยตนเอง"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_move
msgid "Journal Entry"
msgstr "รายการสมุดรายวัน"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_move_line
msgid "Journal Item"
msgstr "รายการบันทึก"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_timesheet_2_product_template
msgid "Junior Architect (Invoice on Timesheets)"
msgstr "สถาปนิกรุ่นเยาว์ (แจ้งหนี้ในใบบันทึกเวลา)"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_milestones_product_template
msgid "Kitchen Assembly (Milestones)"
msgstr "การประกอบครัว (เหตุการณ์สำคัญ)"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Lead the entire sales cycle"
msgstr "เป็นผู้นำในด้านวงจรการขายทั้งหมด"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__service_type
#: model:ir.model.fields,help:sale_timesheet.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"กำหนดปริมาณตามคำสั่งซื้อด้วยตนเอง: ใบแจ้งหนี้ตามปริมาณที่ป้อนด้วยตนเอง โดยไม่ต้องสร้างบัญชีวิเคราะห์\n"
"ใบบันทึกเวลาในสัญญา: ใบแจ้งหนี้ตามชั่วโมงที่ติดตามบนใบบันทึกเวลาที่เกี่ยวข้อง\n"
"สร้างงานและติดตามชั่วโมง: สร้างงานในการตรวจสอบคำสั่งขายและติดตามชั่วโมงทำงาน"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__margin
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Margin"
msgstr "กำไร"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Master demos of our software"
msgstr "การสาธิตหลักซอฟต์แวร์ของเรา"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Materials"
msgstr "วัสดุ"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "วิธีการอัปเดตจำนวนที่จัดส่ง"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Milestone services"
msgstr "บริการหลักที่สำคัญ"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Must Have"
msgstr "ต้องมี"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Negotiate and contract"
msgstr "เจรจาและทำสัญญา"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Nice to have"
msgstr "ควรจะมี"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "No Invoice"
msgstr "ไม่แจ้งหนี้"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.action_timesheet_from_invoice
msgid "No activities found"
msgstr "ไม่พบกิจกรรม"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_from_sales_order_item
msgid "No activities found. Let's start a new one!"
msgstr "ไม่พบกิจกรรม มาเริ่มกันใหม่!"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_billing_report
msgid "No data yet!"
msgstr "ยังไม่มีข้อมูล!"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "No dumb managers, no stupid tools to use, no rigid working hours"
msgstr ""
"ไม่มีผู้จัดการที่ไม่ฉลาด ไม่มีเครื่องมือโง่ ๆ ไม่มีเวลาทำงานที่เข้มงวด"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid ""
"No waste of time in enterprise processes, real responsibilities and autonomy"
msgstr ""
"ไม่ต้องเสียเวลาในกระบวนการต่าง ๆ ขององค์กร ที่มีความรับผิดชอบที่ชัดเจน "
"และความเป็นอิสระ"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__non_billable
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__non_billable
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Non-Billable"
msgstr "ไม่สามารถเรียกเก็บเงินได้"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_form_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_tree_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_task_view_form_inherit_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheete_analysis_report_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_list_inherited
msgid "Non-billable"
msgstr "ไม่สามารถเรียกเก็บเงินได้"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__non_billable_time
msgid "Non-billable Time"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Not Billed"
msgstr "ไม่ถูกเรียกเก็บเงิน"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__timesheet_revenues
msgid "Number of hours spent multiplied by the unit price per hour/day."
msgstr "จำนวนชั่วโมงที่ใช้คูณด้วยราคาต่อหน่วยต่อชั่วโมง/วัน"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__billable_time
msgid "Number of hours/days linked to a SOL."
msgstr "จำนวนชั่วโมง/วันที่เชื่อมโยงกับ SOL"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__non_billable_time
msgid "Number of hours/days not linked to a SOL."
msgstr "จำนวนชั่วโมง/วันที่ไม่เชื่อมโยงกับ SOL"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_advance_payment_inv__date_end_invoice_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_advance_payment_inv__date_start_invoice_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_advance_payment_inv_timesheet_view_form
msgid ""
"Only timesheets not yet invoiced (and validated, if applicable) from this "
"period will be invoiced. If the period is not indicated, all timesheets not "
"yet invoiced (and validated, if applicable) will be invoiced without "
"distinction."
msgstr ""
"เฉพาะใบบันทึกเวลาที่ยังไม่ได้ออกใบแจ้งหนี้ (และผ่านการตรวจสอบ หากมี) "
"จากช่วงเวลานี้เท่านั้นที่จะถูกออกใบแจ้งหนี้ หากไม่มีการระบุช่วงเวลา "
"ใบบันทึกเวลาทั้งหมดที่ยังไม่ได้ออกใบแจ้งหนี้ (และผ่านการตรวจสอบ หากมี) "
"จะถูกออกใบแจ้งหนี้โดยไม่มีส่วนต่าง"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Operation not supported"
msgstr "ไม่รองรับการทำงาน"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__order_id
msgid "Order Reference"
msgstr "อ้างอิงคำสั่ง"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__other_costs
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__other_costs
msgid "Other costs"
msgstr "ค่าใช้จ่ายอื่นๆ"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__other_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__other_revenues
msgid "Other revenues"
msgstr "รายได้อื่นๆ"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Our Product"
msgstr "ผลิตภัณฑ์ของเรา"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Passion for software products"
msgstr "ความหลงใหลในผลิตภัณฑ์ซอฟต์แวร์"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__service_upsell_threshold
#: model:ir.model.fields,help:sale_timesheet.field_product_template__service_upsell_threshold
msgid ""
"Percentage of time delivered compared to the prepaid amount that must be "
"reached for the upselling opportunity activity to be triggered."
msgstr ""
"เปอร์เซ็นต์ของเวลาที่จัดส่งเมื่อเปรียบเทียบกับจำนวนเงินที่ชำระล่วงหน้าซึ่งจะต้องมีพอสำหรับกิจกรรมโอกาสการขายต่อยอดที่จะถูกเรียกใช้"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Perfect written English"
msgstr "การเขียนภาษาอังกฤษที่สมบูรณ์แบบ"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Perks"
msgstr "ผลประโยชน์เพิ่มเติม"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Personal Evolution"
msgstr "วิวัฒนาการส่วนบุคคล"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Play any sport with colleagues, <br>the bill is covered."
msgstr "เล่นกีฬากับเพื่อนร่วมงาน <br>บิลได้ครอบคลุมแล้ว"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__pricing_type
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__pricing_type
msgid "Pricing"
msgstr "กำหนดราคา"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_template
msgid "Product"
msgstr "สินค้า"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_product
msgid "Product Variant"
msgstr "ตัวแปรสินค้า"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_project
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__project_id
msgid "Project"
msgstr "โปรเจ็กต์"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_sale_line_employee_map
msgid "Project Sales line, employee mapping"
msgstr "รายการการขายโปรเจ็กต์ แผนผังพนักงาน"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__project_template_id
msgid "Project Template"
msgstr "เทมเพลตโปรเจ็กต์"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_update
msgid "Project Update"
msgstr "การอัปเดตโปรเจ็กต์"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__fixed_rate
msgid "Project rate"
msgstr "อัตราของโปรเจ็กต์"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_invoice__project_id
msgid "Project to make billable"
msgstr "โปรเจ็กต์ที่จะทำให้เรียกเก็บเงินได้"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Qualify the customer needs"
msgstr "ตอบโจทย์ทุกความต้องการของลูกค้า"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Quotation"
msgstr "ใบเสนอราคา"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Real responsibilities and challenges in a fast evolving company"
msgstr "ความรับผิดชอบและความท้าทายที่แท้จริงในบริษัทที่มีการพัฒนาอย่างรวดเร็ว"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__remaining_hours_available
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__remaining_hours_available
msgid "Remaining Hours Available"
msgstr "ชั่วโมงคงเหลือที่มีอยู่"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Responsibilities"
msgstr "ความรับผิดชอบ"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Revenues"
msgstr "รายได้"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_billing_report
msgid ""
"Review your timesheets by billing type and make sure your time is billable."
msgstr ""
"ตรวจสอบใบบันทึกเวลาของคุณตามประเภทการเรียกเก็บเงิน "
"และตรวจสอบให้แน่ใจว่าเวลาของคุณสามารถเรียกเก็บเงินได้"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_product_roofing_product_template
msgid "Roofing"
msgstr "การมุงหลังคา"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_sale_page
msgid "S0001"
msgstr "S0001"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "ใบแจ้งหนี้การชำระเงินการขายล่วงหน้า"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
#: model:ir.model,name:sale_timesheet.model_sale_order
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__sale_order_id
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__order_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_report_search_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Sales Order"
msgstr "คำสั่งขาย"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__so_line
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__sale_line_id
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__so_line
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_timesheet_table_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.report_timesheet_sale_order
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Sales Order Item"
msgstr "รายการคำสั่งขาย"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_order_line
msgid "Sales Order Line"
msgstr "รายการคำสั่งขาย"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_kanban_inherit_sale_timesheet_so_button
msgid "Sales Orders"
msgstr "คำสั่งขาย"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__sale_line_employee_ids
msgid ""
"Sales order item that will be selected by default on the timesheets of the corresponding employee. It bypasses the sales order item defined on the project and the task, and can be modified on each timesheet entry if necessary. In other words, it defines the rate at which an employee's time is billed based on their expertise, skills or experience, for instance.\n"
"If you would like to bill the same service at a different rate, you need to create two separate sales order items as each sales order item can only have a single unit price at a time.\n"
"You can also define the hourly company cost of your employees for their timesheets on this project specifically. It will bypass the timesheet cost set on the employee."
msgstr ""
"รายการในใบสั่งขายที่จะถูกเลือกตามค่าเริ่มต้นในใบบันทึกเวลาของพนักงานที่เกี่ยวข้อง โดยจะข้ามรายการใบสั่งขายที่กำหนดไว้ในโปรเจ็กต์และงาน สามารถแก้ไขได้ในแต่ละรายการในใบบันทึกเวลา หากมีความจำเป็น ซึ่งหมายความว่า จะกำหนดอัตราที่เรียกเก็บเงินเวลาของพนักงานตามความเชี่ยวชาญ ทักษะ หรือประสบการณ์ เป็นต้น\n"
"หากคุณต้องการเรียกเก็บเงินบริการเดียวกันในอัตราที่แตกต่างกัน คุณต้องสร้างรายการในใบสั่งขายแยกกันสองรายการ เนื่องจากแต่ละรายการในใบสั่งขายสามารถมีราคาต่อหน่วยได้ครั้งละหนึ่งรายการเท่านั้น\n"
"คุณยังสามารถกำหนดต้นทุนรายชั่วโมงของบริษัทของพนักงานของคุณสำหรับระบบบันทึกเวลาในโปรเจ็กต์นี้ได้โดยเฉพาะ และมันจะข้ามต้นทุนการบันทึกเวลาที่กำหนดให้กับพนักงาน"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_analytic_line__so_line
msgid ""
"Sales order item to which the time spent will be added in order to be "
"invoiced to your customer. Remove the sales order item for the timesheet "
"entry to be non-billable."
msgstr ""
"รายการในใบสั่งขายที่จะใช้เวลาเพิ่มเพื่อออกใบแจ้งหนี้ให้กับลูกค้าของคุณ "
"ลบรายการใบสั่งขายสำหรับรายการใบบันทึกเวลาที่ไม่สามารถเรียกเก็บเงินได้"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr "คำสั่งขายที่มีการเชื่อมโยงงาน"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Search in Invoice"
msgstr "ค้นหาในใบแจ้งหนี้"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Search in Sales Order"
msgstr "ค้นหาในคำสั่งขาย"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Sell services and invoice time spent"
msgstr "ขายบริการและออกใบแจ้งหนี้ตามเวลาที่ใช้"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_timesheet_1_product_template
msgid "Senior Architect (Invoice on Timesheets)"
msgstr "สถาปนิกอาวุโส (ใบแจ้งหนี้ในบนใบบันทึกเวลา)"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__service_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__service_revenues
msgid "Service Revenues"
msgstr "รายได้บริการ"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.time_product_product_template
msgid "Service on Timesheets"
msgstr "บริการบนระบบบันทึกเวลา"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__timesheet_product_id
#: model:ir.model.fields,help:sale_timesheet.field_project_task__timesheet_product_id
msgid ""
"Service that will be used by default when invoicing the time spent on a "
"task. It can be modified on each task individually by selecting a specific "
"sales order item."
msgstr ""
"บริการที่จะใช้เป็นค่าเริ่มต้นเมื่อออกใบแจ้งหนี้เวลาที่ใช้ในงาน "
"สามารถปรับเปลี่ยนได้ในแต่ละงานโดยการเลือกรายการในใบสั่งขายเฉพาะ"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.product_template_action_default_services
#: model:project.project,label_tasks:sale_timesheet.project_support
msgid "Services"
msgstr "บริการ"

#. module: sale_timesheet
#: model:hr.job,name:sale_timesheet.job_engineer
msgid "Site Manager"
msgstr "ผู้จัดการไซต์"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_product_solar_installation_product_template
msgid "Solar Panel Installation"
msgstr "การติดตั้งแผงโซลาร์เซลล์"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Sport Activity"
msgstr "กิจกรรมกีฬา"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__date_start_invoice_timesheet
msgid "Start Date"
msgstr "วันที่เริ่ม"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__sale_order_state
msgid "Status"
msgstr "สถานะ"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Strong analytical skills"
msgstr "ทักษะการวิเคราะห์ที่แข็งแกร่ง"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_task
msgid "Task"
msgstr "งาน"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__task_rate
msgid "Task rate"
msgstr "อัตรางาน"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "การวิเคราะห์งาน"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Technical Expertise"
msgstr "ผู้เชี่ยวชาญด้านเทคนิค"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product_product.py:0
#: code:addons/sale_timesheet/models/product_template.py:0
msgid ""
"The %s product is required by the Timesheets app and cannot be archived nor "
"deleted."
msgstr ""
"%s สินค้าที่จำเป็นสำหรับแอประบบบันทึกเวลาและไม่สามารถเก็บถาวรหรือลบได้"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product_template.py:0
msgid ""
"The %s product is required by the Timesheets app and cannot be linked to a "
"company."
msgstr ""
"ผลิตภัณฑ์ %s จำเป็นสำหรับแอประบบบันทึกเวลาและไม่สามารถเชื่อมโยงกับบริษัทได้"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_invoice.py:0
msgid "The selected Sales Order should contain something to invoice."
msgstr "คำสั่งขายที่เลือกควรมีบางอย่างสำหรับออกใบแจ้งหนี้"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__pricing_type
#: model:ir.model.fields,help:sale_timesheet.field_project_task__pricing_type
msgid ""
"The task rate is perfect if you would like to bill different services to "
"different customers at different rates. The fixed rate is perfect if you "
"bill a service at a fixed rate per hour or day worked regardless of the "
"employee who performed it. The employee rate is preferable if your employees"
" deliver the same service at a different rate. For instance, junior and "
"senior consultants would deliver the same service (= consultancy), but at a "
"different rate because of their level of seniority."
msgstr ""
"อัตรางานนั้นสมบูรณ์แบบถ้าคุณต้องการเรียกเก็บเงินบริการต่าง ๆ "
"ให้กับลูกค้าที่แตกต่างกันในอัตราที่ต่างกัน "
"อัตราคงที่จะสมบูรณ์แบบถ้าคุณเรียกเก็บเงินค่าบริการในอัตราคงที่ต่อชั่วโมงหรือต่อวันโดยไม่คำนึงถึงพนักงานที่ดำเนินการ"
" อัตราพนักงานจะสำคัญถ้าพนักงานของคุณให้บริการเดียวกันในอัตราที่แตกต่างกัน "
"ตัวอย่างเช่น ที่ปรึกษาระดับเริ่มต้นและอาวุโสจะให้บริการเดียวกัน (= "
"การให้คำปรึกษา) แต่ในอัตราที่แตกต่างกันเนื่องจากระดับความอาวุโส"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_sale_line_employee_map__cost
msgid ""
"This cost overrides the employee's default employee hourly wage in "
"employee's HR Settings"
msgstr ""
"ต้นทุนนี้จะแทนที่ค่าจ้างรายชั่วโมงของพนักงานเริ่มต้นในการตั้งค่าทรัพยากรบุคคลของพนักงาน"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_upsell_threshold
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_upsell_threshold
msgid "Threshold"
msgstr "เกณฑ์"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Time Billing"
msgstr "เวลาออกบิล"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__remaining_hours_so
#: model:ir.model.fields,field_description:sale_timesheet.field_report_project_task_user__remaining_hours_so
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__remaining_hours
msgid "Time Remaining on SO"
msgstr "เวลาที่เหลืออยู่บน SO"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_graph_employee_per_date
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
msgid "Time Spent"
msgstr "เวลาที่ใช้ไป"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Time-based services"
msgstr "บริการตามเวลา"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_plan
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_plan_pivot
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_graph_employee_per_date
msgid "Timesheet"
msgstr "ใบบันทึกเวลา"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Timesheet Activities"
msgstr "กิจกรรมใบบันทึกเวลา"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_graph_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_pivot_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_graph_employee_per_date
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
msgid "Timesheet Costs"
msgstr "ต้นทุนใบบันทึกเวลา"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__timesheet_product_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__timesheet_product_id
msgid "Timesheet Product"
msgstr "ใบบันทึกเวลาสินค้า"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_report_search_sale_timesheet
msgid "Timesheet Report"
msgstr "รายงานใบบันทึกเวลา"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__timesheet_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__timesheet_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__timesheet_revenues
msgid "Timesheet Revenues"
msgstr "ใบบันทึกเวลารายได้"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_total_duration
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_total_duration
msgid "Timesheet Total Duration"
msgstr "ใบบันทึกเวลาระยะเวลาทั้งหมด"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/account_move.py:0
#: model:ir.actions.act_window,name:sale_timesheet.action_timesheet_from_invoice
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_sales_order
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_sales_order_item
#: model:ir.actions.report,name:sale_timesheet.timesheet_report_account_move
#: model:ir.actions.report,name:sale_timesheet.timesheet_report_sale_order
#: model:ir.model.fields.selection,name:sale_timesheet.selection__sale_order_line__qty_delivered_method__timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_graph_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
msgid "Timesheets"
msgstr "ใบบันทึกเวลา"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Billed Manually)"
msgstr "การบันทึกเวลา (เรียกเก็บเงินด้วยตนเอง)"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Billed on Milestones)"
msgstr "การบันทึกเวลา (เรียกเก็บเงินตามงานที่เสร็จ)"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Billed on Timesheets)"
msgstr "การบันทึกเวลา (เรียกเก็บเงินจากใบบันทึกเวลา)"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Fixed Price)"
msgstr "การบันทึกเวลา (ราคาคงที่)"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Non-Billable)"
msgstr "แผ่นเวลา (ไม่สามารถเรียกเก็บเงินได้)"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_pivot_invoice_type
msgid "Timesheets Analysis"
msgstr "การวิเคราะห์การบันทึกเวลา"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_timesheets_analysis_report
msgid "Timesheets Analysis Report"
msgstr "รายงานวิเคราะห์ระบบบันทึกเวลา"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_advance_payment_inv_timesheet_view_form
msgid "Timesheets Period"
msgstr "ช่วงเวลาใบบันทึกเวลา"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_billing_report
msgid "Timesheets by Billing Type"
msgstr "ใบบันทึกเวลาตามประเภทการเรียกเก็บเงิน"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_sale_page
msgid "Timesheets for the"
msgstr "การบันทึกเวลาสำหรับ"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets of %s"
msgstr "การบันทึกเวลาของ %s"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_type__timesheet
msgid "Timesheets on project (one fare per SO/Project)"
msgstr "ใบบันทึกเวลาในโปรเจ็กต์(หนึ่งค่าต่อ SO/โปรเจ็กต์)"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets revenues"
msgstr "รายได้การบันทึกเวลา"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__margin
msgid "Timesheets revenues minus the costs"
msgstr "รายได้จากระบบบันทึกเวลาด้วยลบต้นทุน"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Timesheets taken into account when invoicing your time"
msgstr "ใบบันทึกเวลาที่นำมาพิจารณาในการออกใบแจ้งหนี้เวลาของคุณ"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_res_config_settings__invoice_policy
msgid "Timesheets taken when invoicing time spent"
msgstr "ใบบันทึกเวลาที่ใช้ไปเพื่อออกใบแจ้งหนี้"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Timesheets without a sales order item are reported as"
msgstr "รายงานใบเวลาที่ไม่มีรายการใบสั่งขายเป็น"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "To Bill"
msgstr "เพื่อบิล"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "To Invoice"
msgstr "ออกใบแจ้งหนี้"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_list_inherited
msgid "Total"
msgstr "รวม"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_invoice__amount_to_invoice
msgid ""
"Total amount to invoice on the sales order, including all items (services, "
"storables, expenses, ...)"
msgstr ""
"ยอดรวมในการออกใบแจ้งหนี้ในคำสั่งขาย รวมถึงรายการทั้งหมด (บริการ "
"สินค้าที่สามารถเก็บได้ ค่าใช้จ่าย ...)"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_bank_statement_line__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_account_move__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_sale_order__timesheet_total_duration
msgid ""
"Total recorded duration, expressed in the encoding UoM, and rounded to the "
"unit"
msgstr "ระยะเวลาที่บันทึกไว้ทั้งหมด แสดงในการเข้ารหัส UoM และปัดเศษเป็นหน่วย"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_type
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_type
msgid "Track Service"
msgstr "ติดตามบริการ"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.action_timesheet_from_invoice
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_from_sales_order_item
msgid ""
"Track your working hours by projects every day and invoice this time to your"
" customers."
msgstr ""
"ติดตามชั่วโมงทำงานของคุณตามโปรเจ็กต์ทุกวัน "
"และออกใบแจ้งหนี้ตามเวลานี้ให้กับลูกค้าของคุณ"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Trainings"
msgstr "การฝึกอบรม"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__price_unit
msgid "Unit Price"
msgstr "ราคาต่อหน่วย"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Valid work permit for Belgium"
msgstr "ใบอนุญาตทำงานที่ถูกต้องสำหรับเบลเยียม"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Value does not exist in the pricing type"
msgstr "ไม่มีค่าในประเภทการกำหนดราคา"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_invoice_page_inherit
msgid "View Timesheet"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_invoice_page_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_order_portal_content_inherit
msgid "View Timesheets"
msgstr "ดูใบบันทึกเวลา"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_product_timesheet_form
msgid "Warn the salesperson for an upsell when work done exceeds"
msgstr "เตือนพนักงานขายให้ทำยอดขายเพิ่มขึ้นเมื่อมีงานเสร็จเกิน"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "What We Offer"
msgstr "สิ่งที่เราเสนอให้"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "What's great in the job?"
msgstr "งานนี้มีอะไรดี?"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_invoice.py:0
msgid "You can only apply this action from a project."
msgstr "คุณสามารถใช้การดำเนินการนี้ได้เฉพาะจากโปรเจ็กต์เท่านั้น"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid ""
"You cannot link a billable project to a sales order item that comes from an "
"expense or a vendor bill."
msgstr ""
"คุณไม่สามารถเชื่อมโยงโปรเจ็กต์ที่เรียกเก็บเงินได้กับรายการคำสั่งขายที่มาจากค่าใช้จ่ายหรือใบเรียกเก็บเงินของผู้จัดจำหน่าย"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid ""
"You cannot link a billable project to a sales order item that is not a "
"service."
msgstr ""
"คุณไม่สามารถเชื่อมโยงโปรเจ็กต์ที่เรียกเก็บเงินกับรายการคำสั่งขายที่ไม่ใช่บริการได้"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
msgid "You cannot modify timesheets that are already invoiced."
msgstr "คุณไม่สามารถแก้ไขใบบันทึกเวลาที่ออกใบแจ้งหนี้แล้ว"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
msgid "You cannot remove a timesheet that has already been invoiced."
msgstr "คุณไม่สามารถลบใบบันทึกเวลาที่ออกใบแจ้งหนี้ไปแล้วได้"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__billing_type__manually
msgid "billed manually"
msgstr "เรียกเก็บเงินด้วยตนเอง"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/sale_order_line.py:0
msgid "days remaining"
msgstr "วันคงเหลือ"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__billing_type__not_billable
msgid "not billable"
msgstr "ไม่สามารถเรียกเก็บเงินได้"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_product_timesheet_form
msgid "of hours sold."
msgstr "ของจำนวนชั่วโมงที่ขายได้"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/sale_order_line.py:0
msgid "remaining"
msgstr "คงเหลือ"
