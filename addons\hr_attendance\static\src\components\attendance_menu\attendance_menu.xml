<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">

<t t-name="hr_attendance.attendance_menu">
    <t t-if="this.state.isDisplayed">
        <Dropdown position="'bottom-end'" beforeOpen.bind="searchReadEmployee" menuClass="`p-2 pb-3`" state="dropdown">
            <button>
                <i class="fa fa-circle" t-attf-class="text-{{ this.state.checkedIn ? 'success' : 'danger' }}" role="img" aria-label="Attendance"/>
            </button>
            <t t-set-slot="content">
                <div class="o_att_menu_container d-flex flex-column gap-4">
                    <div class="d-flex flex-column gap-3">
                        <div t-if="this.state.checkedIn" class="d-flex flex-wrap gap-3">
                            <div t-if="!this.isFirstAttendance" class="att_container flex-grow-1 flex-shrink-0">
                                <small class="d-block text-muted">Before <t t-esc="this.lastCheckIn"/></small>
                                <div t-esc="this.hoursPreviouslyToday" class="fs-3 text-info text-end"/>
                            </div>
                            <div class="att_container flex-grow-1 flex-shrink-0">
                                <small class="d-block text-muted">Since <t t-esc="this.lastCheckIn"/></small>
                                <div t-esc="this.lastAttendanceWorkedHours" t-attf-class="fs-3 text-info {{ !this.isFirstAttendance ? 'text-end' : '' }}"/>
                            </div>
                        </div>
                        <div t-if="!this.isFirstAttendance"
                            class="att_container d-flex flex-column"
                            t-att-class="this.state.checkedIn ? 'p-3 bg-100 rounded' : ''">
                            <div class="d-flex" t-att-class="this.state.checkedIn ? 'align-items-center justify-content-between' : 'flex-column'">
                                <small class="text-muted">Total today</small>
                                <h2 t-esc="this.hoursToday" class="mb-0 fs-2"/>
                            </div>
                            <button t-on-click="() => this.signInOut()" class="flex-basis-100 mt-3" t-attf-class="btn btn-{{ this.state.checkedIn ? 'warning' : 'success' }}">
                                <span t-if="!this.state.checkedIn">Check in</span>
                                <span t-else="">Check out</span>
                                <i t-attf-class="fa fa-sign-{{ this.state.checkedIn ? 'out' : 'in' }} ms-1"/>
                            </button>
                        </div>
                    </div>
                    <button t-if="this.isFirstAttendance" t-on-click="() => this.signInOut()" t-attf-class="btn btn-{{ this.state.checkedIn ? 'warning' : 'success' }}">
                        <span t-if="!this.state.checkedIn">Check in</span>
                        <span t-else="">Check out</span>
                        <i t-attf-class="fa fa-sign-{{ this.state.checkedIn ? 'out' : 'in' }} ms-1"/>
                    </button>
                </div>
            </t>
        </Dropdown>
    </t>
</t>

</templates>
