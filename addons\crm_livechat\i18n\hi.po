# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_livechat
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Language-Team: Hindi (https://app.transifex.com/odoo/teams/41243/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: crm_livechat
#. odoo-python
#: code:addons/crm_livechat/models/chatbot_script_step.py:0
msgid "%s's New Lead"
msgstr ""

#. module: crm_livechat
#: model:ir.model,name:crm_livechat.model_chatbot_script
msgid "Chatbot Script"
msgstr ""

#. module: crm_livechat
#: model:ir.model,name:crm_livechat.model_chatbot_script_step
msgid "Chatbot Script Step"
msgstr ""

#. module: crm_livechat
#: model:ir.model.fields.selection,name:crm_livechat.selection__chatbot_script_step__step_type__create_lead
msgid "Create Lead"
msgstr ""

#. module: crm_livechat
#. odoo-javascript
#: code:addons/crm_livechat/static/src/core/channel_commands.js:0
msgid "Create a new lead (/lead lead title)"
msgstr ""

#. module: crm_livechat
#. odoo-python
#: code:addons/crm_livechat/models/discuss_channel.py:0
msgid ""
"Create a new lead: %(pre_start)s%(lead_command)s %(i_start)slead "
"title%(i_end)s%(pre_end)s"
msgstr ""

#. module: crm_livechat
#. odoo-python
#: code:addons/crm_livechat/models/discuss_channel.py:0
msgid "Created a new lead: %s"
msgstr ""

#. module: crm_livechat
#: model:ir.model,name:crm_livechat.model_discuss_channel
msgid "Discussion Channel"
msgstr ""

#. module: crm_livechat
#: model:ir.model.fields,field_description:crm_livechat.field_chatbot_script__lead_count
msgid "Generated Lead Count"
msgstr ""

#. module: crm_livechat
#: model:chatbot.script.step,message:crm_livechat.chatbot_script_lead_generation_step_welcome
msgid "Hi there, what brings you to our website today? 👋"
msgstr ""

#. module: crm_livechat
#: model:chatbot.script.step,message:crm_livechat.chatbot_script_lead_generation_step_noone_available
msgid "Hu-ho, it looks like none of our operators are available 🙁"
msgstr ""

#. module: crm_livechat
#: model:chatbot.script,title:crm_livechat.chatbot_script_lead_generation_bot
msgid "Lead Generation Bot"
msgstr ""

#. module: crm_livechat
#: model_terms:ir.ui.view,arch_db:crm_livechat.chatbot_script_view_form
msgid "Leads"
msgstr ""

#. module: crm_livechat
#: model:ir.model.fields,field_description:crm_livechat.field_chatbot_script_step__crm_team_id
msgid "Sales Team"
msgstr ""

#. module: crm_livechat
#: model:ir.model.fields,field_description:crm_livechat.field_chatbot_script_step__step_type
msgid "Step Type"
msgstr ""

#. module: crm_livechat
#: model:chatbot.script.step,message:crm_livechat.chatbot_script_welcome_step_just_looking
msgid "Thank you, you should hear back from us very soon!"
msgstr ""

#. module: crm_livechat
#: model:ir.model.fields,help:crm_livechat.field_chatbot_script_step__crm_team_id
msgid ""
"Used in combination with 'create_lead' step type in order to automatically "
"assign the created lead/opportunity to the defined team"
msgstr ""

#. module: crm_livechat
#: model:chatbot.script.step,message:crm_livechat.chatbot_script_welcome_step_pricing_email
msgid ""
"Would you mind leaving your email address so that we can reach you back?"
msgstr ""
