<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="timesheets_analysis_report_list_inherited" model="ir.ui.view">
        <field name="name">timesheets.analysis.report.list.inherited</field>
        <field name="model">timesheets.analysis.report</field>
        <field name="inherit_id" ref="hr_timesheet.timesheets_analysis_report_list"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='task_id']" position="after">
                <field name="so_line"
                       optional="show"
                       options="{'no_open': True}"
                       placeholder="Non-billable"
                />
                <field name="timesheet_invoice_type" optional="hide"/>
                <field name="timesheet_invoice_id" optional="hide"/>
                <field name="timesheet_revenues" optional="hide" sum="Total"/>
                <field name="margin" optional="hide" sum="Total"/>
            </xpath>
        </field>
    </record>

    <record id="timesheete_analysis_report_form" model="ir.ui.view">
        <field name="name">timesheets.analysis.report.form</field>
        <field name="model">timesheets.analysis.report</field>
        <field name="inherit_id" ref="hr_timesheet.timesheets_analysis_report_form"/>
        <field name="arch" type="xml">
            <xpath expr="//sheet/group/group" position="inside">
                <field name="so_line" widget="so_line_field" placeholder="Non-billable"/>
            </xpath>
        </field>
    </record>

    <record id="timesheets_analysis_report_pivot_inherit" model="ir.ui.view">
        <field name="name">timesheets.analysis.report.pivot</field>
        <field name="model">timesheets.analysis.report</field>
        <field name="inherit_id" ref="hr_timesheet.timesheets_analysis_report_pivot_employee"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='unit_amount']" position="after">
                <field name="billable_time" widget="timesheet_uom"/>
                <field name="non_billable_time" widget="timesheet_uom"/>
            </xpath>
        </field>
    </record>

    <record id="timesheets_analysis_report_graph_inherit" model="ir.ui.view">
        <field name="name">timesheets.analysis.report.graph</field>
        <field name="model">timesheets.analysis.report</field>
        <field name="inherit_id" ref="hr_timesheet.timesheets_analysis_report_pivot_employee"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='unit_amount']" position="after">
                <field name="billable_time" widget="timesheet_uom"/>
                <field name="non_billable_time" widget="timesheet_uom"/>
            </xpath>
        </field>
    </record>

    <!--TO DO: Remove in master and update existing inherit_id-->
    <record id="timesheets_analysis_report_graph_timesheet_grid" model="ir.ui.view">
        <field name="name">timesheets.analysis.report.graph</field>
        <field name="model">timesheets.analysis.report</field>
        <field name="inherit_id" ref="hr_timesheet.timesheets_analysis_report_graph_employee"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='unit_amount']" position="after">
                <field name="billable_time" widget="timesheet_uom"/>
                <field name="non_billable_time" widget="timesheet_uom"/>
            </xpath>
        </field>
    </record>


    <record id="timesheets_analysis_report_pivot_project_inherit" model="ir.ui.view">
        <field name="name">timesheets.analysis.report.pivot.project</field>
        <field name="model">timesheets.analysis.report</field>
        <field name="inherit_id" ref="hr_timesheet.timesheets_analysis_report_pivot_project"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='unit_amount']" position="after">
                <field name="billable_time" widget="timesheet_uom"/>
                <field name="non_billable_time" widget="timesheet_uom"/>
            </xpath>
        </field>
    </record>

    <record id="timesheets_analysis_report_graph_project_inherit" model="ir.ui.view">
        <field name="name">timesheets.analysis.report.graph.project</field>
        <field name="model">timesheets.analysis.report</field>
        <field name="inherit_id" ref="hr_timesheet.timesheets_analysis_report_graph_project"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='unit_amount']" position="after">
                <field name="billable_time" widget="timesheet_uom"/>
                <field name="non_billable_time" widget="timesheet_uom"/>
            </xpath>
        </field>
    </record>

    <record id="timesheets_analysis_report_pivot_task_inherit" model="ir.ui.view">
        <field name="name">timesheets.analysis.report.pivot.task</field>
        <field name="model">timesheets.analysis.report</field>
        <field name="inherit_id" ref="hr_timesheet.timesheets_analysis_report_pivot_task"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='unit_amount']" position="after">
                <field name="billable_time" widget="timesheet_uom"/>
                <field name="non_billable_time" widget="timesheet_uom"/>
            </xpath>
        </field>
    </record>

    <record id="timesheets_analysis_report_graph_task_inherit" model="ir.ui.view">
        <field name="name">timesheets.analysis.report.graph.task</field>
        <field name="model">timesheets.analysis.report</field>
        <field name="inherit_id" ref="hr_timesheet.timesheets_analysis_report_graph_task"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='unit_amount']" position="after">
                <field name="billable_time" widget="timesheet_uom"/>
                <field name="non_billable_time" widget="timesheet_uom"/>
            </xpath>
        </field>
    </record>

    <record id="timesheets_analysis_report_pivot_invoice_type" model="ir.ui.view">
        <field name="name">timesheets.analysis.report.pivot</field>
        <field name="model">timesheets.analysis.report</field>
        <field name="arch" type="xml">
            <pivot string="Timesheets Analysis" sample="1">
                <field name="date" interval="month" type="row"/>
                <field name="timesheet_invoice_type" type="col"/>
                <field name="amount" string="Timesheet Costs"/>
                <field name="unit_amount" type="measure" widget="timesheet_uom"/>
                <field name="billable_time" widget="timesheet_uom"/>
                <field name="non_billable_time" widget="timesheet_uom"/>
            </pivot>
        </field>
    </record>

    <record id="timesheets_analysis_report_graph_invoice_type" model="ir.ui.view">
        <field name="name">timesheets.analysis.report.graph</field>
        <field name="model">timesheets.analysis.report</field>
        <field name="arch" type="xml">
            <graph string="Timesheets" sample="1" js_class="hr_timesheet_graphview">
                <field name="amount" string="Timesheet Costs"/>
                <field name="unit_amount" type="measure" widget="timesheet_uom"/>
                <field name="billable_time" widget="timesheet_uom"/>
                <field name="non_billable_time" widget="timesheet_uom"/>
                <field name="timesheet_invoice_type" type="row"/>
            </graph>
        </field>
    </record>

    <record id="hr_timesheet_report_search_sale_timesheet" model="ir.ui.view">
        <field name="name">timesheets.analysis.report.search</field>
        <field name="model">timesheets.analysis.report</field>
        <field name="inherit_id" ref="sale_timesheet.timesheet_view_search"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <search position="attributes">
                    <attribute name="string">Timesheet Report</attribute>
                </search>
            <xpath expr="//field[@name='order_id']" position="after">
                <field name="so_line" groups="sales_team.group_sale_salesman"/>
            </xpath>
            <xpath expr="//filter[@name='groupby_sale_order_item']" position="before">
                <filter string="Sales Order" name="groupby_sale_order" domain="[]"
                    context="{'group_by': 'order_id'}" groups="sales_team.group_sale_salesman"/>
            </xpath>
        </field>
    </record>

    <record id="timesheet_action_billing_report" model="ir.actions.act_window">
        <field name="name">Timesheets by Billing Type</field>
        <field name="res_model">timesheets.analysis.report</field>
        <field name="path">timesheets-billing</field>
        <field name="domain">[('project_id', '!=', False)]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No data yet!
            </p>
            <p>Review your timesheets by billing type and make sure your time is billable.</p>
        </field>
        <field name="search_view_id" ref="hr_timesheet.hr_timesheet_report_search"/>
        <field name="view_mode">pivot,graph</field>
    </record>

    <record id="timesheet_action_view_report_by_billing_rate_pivot" model="ir.actions.act_window.view">
        <field name="sequence" eval="5"/>
        <field name="view_mode">pivot</field>
        <field name="view_id" ref="timesheets_analysis_report_pivot_invoice_type"/>
        <field name="act_window_id" ref="timesheet_action_billing_report"/>
    </record>

    <record id="timesheet_action_view_report_by_billing_rate_graph" model="ir.actions.act_window.view">
        <field name="sequence" eval="6"/>
        <field name="view_mode">graph</field>
        <field name="view_id" ref="timesheets_analysis_report_graph_invoice_type"/>
        <field name="act_window_id" ref="timesheet_action_billing_report"/>
    </record>

    <record id="timesheet_action_view_report_by_billing_rate_list" model="ir.actions.act_window.view">
        <field name="sequence" eval="10"/>
        <field name="view_mode">list</field>
        <field name="view_id" ref="hr_timesheet.timesheets_analysis_report_list"/>
        <field name="act_window_id" ref="timesheet_action_billing_report"/>
    </record>

    <menuitem id="menu_timesheet_billing_analysis"
            parent="hr_timesheet.menu_timesheets_reports_timesheet"
            action="timesheet_action_billing_report"
            name="By Billing Type"
            sequence="40"/>

</odoo>
