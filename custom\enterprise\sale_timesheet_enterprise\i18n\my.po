# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * timesheet_grid_sale
#
# Translators:
# <AUTHOR> <EMAIL>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:03+0000\n"
"PO-Revision-Date: 2016-09-07 09:57+0000\n"
"Last-Translator: Hein Myat Phone <<EMAIL>>, 2016\n"
"Language-Team: Burmese (https://www.transifex.com/odoo/teams/41243/my/)\n"
"Language: my\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_timesheet_enterprise
#: model:ir.model.fields.selection,name:sale_timesheet_enterprise.selection__res_config_settings__invoiced_timesheet__all
msgid "All recorded timesheets"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model,name:sale_timesheet_enterprise.model_account_analytic_line
msgid "Analytic Account"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model,name:sale_timesheet_enterprise.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.timesheet_view_grid_by_invoice_type
msgid "Day"
msgstr ""

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/components/timesheet_overtime_indication/timesheet_overtime_indication.js:0
msgid "Difference between the number of %s ordered on the sales order item and the number of %s delivered"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,help:sale_timesheet_enterprise.field_project_task__portal_progress
msgid "Display progress of current task."
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.project_sharing_inherit_project_task_view_tree
msgid "Hours Spent"
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.project_sharing_inherit_project_task_view_tree
msgid "Hours Spent on Sub-Tasks"
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.res_config_settings_view_form
msgid "Invoice"
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.res_config_settings_view_form
msgid "Invoicing Policy"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model,name:sale_timesheet_enterprise.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.timesheet_view_grid_by_invoice_type
msgid "Month"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,field_description:sale_timesheet_enterprise.field_project_task__portal_effective_hours
msgid "Portal Effective Hours"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,field_description:sale_timesheet_enterprise.field_project_task__portal_progress
msgid "Portal Progress"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,field_description:sale_timesheet_enterprise.field_project_task__portal_remaining_hours
msgid "Portal Remaining Hours"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,field_description:sale_timesheet_enterprise.field_project_task__portal_subtask_effective_hours
msgid "Portal Subtask Effective Hours"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,field_description:sale_timesheet_enterprise.field_project_task__portal_total_hours_spent
msgid "Portal Total Hours Spent"
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.project_sharing_inherit_project_task_view_tree
msgid "Progress"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model,name:sale_timesheet_enterprise.model_project_project
msgid "Project"
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.project_sharing_inherit_project_task_view_tree
msgid "Remaining Hours"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model,name:sale_timesheet_enterprise.model_sale_order_line
msgid "Sales Order Line"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model,name:sale_timesheet_enterprise.model_project_task
msgid "Task"
msgstr ""

#. module: sale_timesheet_enterprise
#. odoo-python
#: code:addons/sale_timesheet_enterprise/models/project_task.py:0
msgid "This Sale Order Item doesn't have a target value of planned hours. Planned hours :"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,help:sale_timesheet_enterprise.field_project_task__portal_subtask_effective_hours
msgid "Time spent on the sub-tasks (and their own sub-tasks) of this task."
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,help:sale_timesheet_enterprise.field_project_task__portal_effective_hours
msgid "Time spent on this task, excluding its sub-tasks."
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,help:sale_timesheet_enterprise.field_project_task__portal_total_hours_spent
msgid "Time spent on this task, including its sub-tasks."
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.timesheet_view_grid_by_invoice_type
msgid "Timesheets"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,field_description:sale_timesheet_enterprise.field_res_config_settings__invoiced_timesheet
msgid "Timesheets Invoicing"
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.res_config_settings_view_form
msgid "Timesheets taken into account when invoicing your time"
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.project_sharing_inherit_project_task_view_tree
msgid "Total Hours"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,help:sale_timesheet_enterprise.field_project_task__portal_remaining_hours
msgid "Total remaining time, can be re-estimated periodically by the assignee of the task."
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields.selection,name:sale_timesheet_enterprise.selection__res_config_settings__invoiced_timesheet__approved
msgid "Validated timesheets only"
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.timesheet_view_grid_by_invoice_type
msgid "Week"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,help:sale_timesheet_enterprise.field_res_config_settings__invoiced_timesheet
msgid ""
"With the 'all recorded timesheets' option, all timesheets will be invoiced without distinction, even if they haven't been validated. Additionally, all timesheets will be accessible in your customers' portal. \n"
"When you choose the 'validated timesheets only' option, only the validated timesheets will be invoiced and appear in your customers' portal."
msgstr ""
