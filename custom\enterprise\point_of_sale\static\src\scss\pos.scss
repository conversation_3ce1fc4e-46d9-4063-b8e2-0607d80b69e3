/* --- Fonts --- */

@font-face{
    font-family: 'Inconsolata';
    src: url(../fonts/Inconsolata.otf);
}

button {
    transition: all 150ms linear;
}

.btn-light {
    --btn-hover-bg: #{$o-gray-100};
    --btn-hover-border-color: #{$o-gray-100};
}

[type="text"], [type="password"], [type="number"], [type="email"], [type="tel"], textarea, select {
    width: 100%;
    display: block;
    outline: none;
}

.text-large {
    font-size: large;
}

/* --- Display --- */

.z-1000 {
    z-index: 1000;
}

.h-inherit {
    height: inherit;
}

/*  ********* The leftpane contains the order, numpad and paypad ********* */

.product-reminder span {
    animation: 0.7s anim-lineUp ease-out 1;

    @keyframes anim-lineUp {
        0% {
            opacity: 0;
            transform: translateY(80%);
        }
        20% {
            opacity: 0;
        }
        50% {
            opacity: 1;
            transform: translateY(0%);
        }
        100% {
            opacity: 1;
            transform: translateY(0%);
        }
    }
}

.leftpane {
    width: $left-pane-width;
    max-width: $left-pane-width;

    @include media-breakpoint-down(xl) {
        width: $left-pane-width-tablet;
        max-width: $left-pane-width-tablet;
    }
}


@media screen and (max-width: 992px) {
    .order,
    .leftpane {
        max-width: inherit;
        border-right: none;
    }
}

.pos .numpad {
    /*rtl:ignore*/
    direction: ltr;
}
.o_mobile_overscroll {
    overscroll-behavior: none;
}

.pos .button.validation.load-order-button {
    height: calc(var(--btn-height-size) * 2);
}

@media screen and (max-width: 992px) {
    .pos .button.validation.load-order-button {
        height: 70px;
    }
}

/*  ********* The Webkit Scrollbar  ********* */

.pos.big-scrollbars *::-webkit-scrollbar{
    width:  40px;
    height: 40px;
}
.pos.big-scrollbars *::-webkit-scrollbar-track{
    background: $gray-200;
    border-left: none;
}
.pos.big-scrollbars *::-webkit-scrollbar-thumb{
    background: rgb(168,168,168);
    min-height: 40px;
    border-radius: 3px;
}
.pos.big-scrollbars *::-webkit-scrollbar-button{
    width:  40px;
    height: 40px;
    border-radius: 3px;
    background: rgb(210,210,210);
    background-size: cover;
}
.pos.big-scrollbars *::-webkit-scrollbar-button:decrement{
    background-image: url('../img/scroll-up.png');
}
.pos.big-scrollbars *::-webkit-scrollbar-button:increment{
    background-image: url('../img/scroll-down.png');
}

/* Fade */

.o-fade {
    transition: opacity .2s;
}

.o-fade-enter, .o-fade-leave {
    opacity: 0;
}

/*
    We block the top-header when a temp screen is displayed.
    Similar to blocking the whole ui when a popup is displayed.
*/
/* .pos .block-top-header {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height:100%;
    background-color: rgba(0,0,0,0.5);
    z-index:1000;
} */

/* Debug */

.drag-handle {
    /* See o-grab-cursor mixin */
    cursor: url(/web/static/img/openhand.cur), grab;
}

.drag-handle:active {
    cursor: grabbing;
}
.pos .button.validation.with-more-button {
    height: calc(var(--btn-height-size) * 2);
}

// Set all the colors but the "no-color" one

@for $size from 2 through length($o-colors) {
    .o_colorlist_item_color_transparent_#{$size - 1} {
        $-bg: adjust-color(nth($o-colors, $size), $lightness: 28%, $saturation: 15%);
        $-color: adjust-color(nth($o-colors, $size), $lightness: -40%, $saturation: -15%);
        @include o-print-color($-bg, background-color, bg-opacity);
        @include o-print-color($-color, color, text-opacity);
    }
}

.pos-rightheader .status-buttons .btn-secondary{
    background-color: #fff;
    border: 0;
}
// this is to override the property in `web/static/src/webclient/webclient_layout.scss'
body.modal-open {
    position: initial !important;
}
