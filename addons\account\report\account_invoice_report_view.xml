<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data>
    <record id="view_account_invoice_report_pivot" model="ir.ui.view">
         <field name="name">account.invoice.report.pivot</field>
         <field name="model">account.invoice.report</field>
         <field name="arch" type="xml">
             <pivot string="Invoices Analysis" sample="1">
                 <field name="product_categ_id" type="col"/>
                 <field name="invoice_date" type="row"/>
                 <field name="price_subtotal" type="measure"/>
             </pivot>
         </field>
    </record>

    <record id="view_account_invoice_report_graph" model="ir.ui.view">
         <field name="name">account.invoice.report.graph</field>
         <field name="model">account.invoice.report</field>
         <field name="arch" type="xml">
             <graph string="Invoices Analysis" type="line" sample="1">
                 <field name="product_categ_id"/>
                 <field name="price_subtotal" type="measure"/>
             </graph>
         </field>
    </record>

     <record id="account_invoice_report_view_tree" model="ir.ui.view">
         <field name="name">account.invoice.report.view.list</field>
         <field name="model">account.invoice.report</field>
         <field name="arch" type="xml">
             <list string="Invoices Analysis">
                <field name="move_id" string="Invoice Number"/>
                <field name="journal_id" optional="hide"/>
                <field name="partner_id" optional="show"/>
                <field name="country_id" optional="hide"/>
                <field name="invoice_date" optional="show"/>
                <field name="invoice_date_due" optional="show"/>
                <field name="invoice_user_id" optional="hide" widget="many2one_avatar_user"/>
                <field name="product_categ_id" optional="hide"/>
                <field name="product_id" optional="show"/>
                <field name="company_id"  groups="base.group_multi_company"/>
                <field name="price_average" optional="hide" sum="Total"/>
                <field name="quantity" optional="hide" sum="Total"/>
                <field name="price_subtotal_currency" optional="show" sum="Total"/>
                <field name="price_subtotal" optional="show" sum="Total"/>
                <field name="price_total" optional="show" sum="Total"/>
                <field name="price_margin" optional="hide"/>
                <field name="inventory_value" optional="hide" sum="Total"/>
                <field name="state" optional="hide"/>
                <field name="payment_state" optional="hide"/>
                <field name="move_type" optional="hide"/>
             </list>
         </field>
     </record>

    <!-- Custom reports (aka filters) -->
    <record id="filter_invoice_report_salespersons" model="ir.filters">
        <field name="name">By Salespersons</field>
        <field name="model_id">account.invoice.report</field>
        <field name="domain">[]</field>
        <field name="user_id" eval="False"/>
        <field name="context">{'group_by': ['invoice_date:month', 'invoice_user_id']}</field>
    </record>
    <record id="filter_invoice_product" model="ir.filters">
        <field name="name">By Product</field>
        <field name="model_id">account.invoice.report</field>
        <field name="domain">[]</field>
        <field name="user_id" eval="False"/>
        <field name="context">{'group_by': ['invoice_date:month', 'product_id'], 'set_visible':True, 'residual_invisible':True}</field>
    </record>
    <record id="filter_invoice_product_category" model="ir.filters">
        <field name="name">By Product Category</field>
        <field name="model_id">account.invoice.report</field>
        <field name="domain">[]</field>
        <field name="user_id" eval="False"/>
        <field name="context">{'group_by': ['invoice_date:month', 'product_categ_id'], 'residual_invisible':True}</field>
    </record>
    <record id="filter_invoice_refund" model="ir.filters">
        <field name="name">By Credit Note</field>
        <field name="model_id">account.invoice.report</field>
        <field name="domain">[('move_type', '=', 'out_refund')]</field>
        <field name="user_id" eval="False"/>
        <field name="context">{'group_by': ['invoice_date:month', 'invoice_user_id']}</field>
    </record>
    <record id="filter_invoice_country" model="ir.filters">
        <field name="name">By Country</field>
        <field name="model_id">account.invoice.report</field>
        <field name="domain">[]</field>
        <field name="user_id" eval="False"/>
        <field name="context">{'group_by': ['invoice_date:month', 'country_id']}</field>
    </record>

    <record id="view_account_invoice_report_search" model="ir.ui.view">
        <field name="name">account.invoice.report.search</field>
        <field name="model">account.invoice.report</field>
        <field name="arch" type="xml">
            <search string="Invoices Analysis">
                <filter string="My Invoices" name="my_invoice" domain="[('invoice_user_id', '=', uid)]"/>
                <separator/>
                <field name="invoice_date"/>
                <separator/>
                <filter string="To Invoice" name="toinvoice" domain="[('state','=','draft')]" help = "Draft Invoices"/>
                <filter string="Invoiced" name="current" domain="[('state','not in', ('draft','cancel'))]"/>
                <separator/>
                <filter string="Customers" name="customer" domain="['|', ('move_type','=','out_invoice'),('move_type','=','out_refund')]"/>
                <filter string="Vendors" name="supplier" domain="['|', ('move_type','=','in_invoice'),('move_type','=','in_refund')]"/>
                <separator/>
                <filter string="Invoices" name="invoice" domain="['|', ('move_type','=','out_invoice'),('move_type','=','in_invoice')]"/>
                <filter string="Credit Notes" name="creditnote" domain="['|', ('move_type','=','out_refund'),('move_type','=','in_refund')]"/>
                <separator/>
                <filter name="filter_invoice_date" date="invoice_date"/>
                <filter name="invoice_date_due" date="invoice_date_due"/>
                <field name="partner_id" operator="child_of"/>
                <field name="invoice_user_id" />
                <field name="product_id" />
                <field name="product_categ_id" filter_domain="[('product_categ_id', 'child_of', self)]"/>
                <group expand="1" string="Group By">
                    <filter string="Salesperson" name='user' context="{'group_by':'invoice_user_id'}"/>
                    <filter string="Partner" name="partner_id" context="{'group_by':'partner_id','residual_visible':True}"/>
                    <filter string="Product Category" name="category_product" context="{'group_by':'product_categ_id','residual_invisible':True}"/>
                    <filter string="Status" name="status" context="{'group_by':'state'}"/>
                    <filter string="Company" name="company" context="{'group_by':'company_id'}" groups="base.group_multi_company"/>
                    <separator orientation="vertical" />
                    <filter string="Date" name="invoice_date" context="{'group_by':'invoice_date'}"
                            invisible="context.get('invoice_report_view_hide_invoice_date')"/>
                    <filter string="Date" name="group_by_invoice_date_week" context="{'group_by':'invoice_date:week'}"
                            invisible="not context.get('invoice_report_view_hide_invoice_date')"/>
                    <filter string="Due Date" name="duemonth" context="{'group_by':'invoice_date_due:month'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_account_invoice_report_all_supp" model="ir.actions.act_window">
        <field name="name">Invoices Analysis</field>
        <field name="res_model">account.invoice.report</field>
        <field name="view_mode">graph,pivot</field>
        <field name="context">{'search_default_current':1, 'search_default_supplier': 1, 'group_by':['invoice_date:month']}</field>
        <field name="search_view_id" ref="view_account_invoice_report_search"/>
        <field name="help">From this report, you can have an overview of the amount invoiced from your vendors. The search tool can also be used to personalise your Invoices reports and so, match this analysis to your needs.</field>
    </record>
    <record id="action_account_invoice_report_all" model="ir.actions.act_window">
        <field name="name">Invoices Analysis</field>
        <field name="res_model">account.invoice.report</field>
        <field name="view_mode">graph,pivot</field>
        <field name="context">{'search_default_current':1, 'search_default_customer': 1, 'group_by':['invoice_date:month']}</field>
        <field name="search_view_id" ref="view_account_invoice_report_search"/>
        <field name="help">From this report, you can have an overview of the amount invoiced to your customers. The search tool can also be used to personalise your Invoices reports and so, match this analysis to your needs.</field>
    </record>

</data>
</odoo>
