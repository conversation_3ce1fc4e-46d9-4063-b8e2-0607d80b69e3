# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* survey
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2025
# Wil <PERSON>do<PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-07 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_count
msgid "# Questions"
msgstr "Nº de perguntas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__random_questions_count
msgid "# Questions Randomly Picked"
msgstr "Nº de perguntas escolhidas aleatoriamente"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_4
msgid "$100"
msgstr "R$ 100,00"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_1
msgid "$20"
msgstr "R$ 20"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_5
msgid "$200"
msgstr "R$ 200,00"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_6
msgid "$300"
msgstr "R$ 300,00"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_2
msgid "$50"
msgstr "R$ 50,00"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_3
msgid "$80"
msgstr "R$ 80"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "% completed"
msgstr "% concluída"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "%(participant)s just participated in \"%(survey_title)s\"."
msgstr "%(participant)s acabou de participar do \"%(survey_title)s\"."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "%s (copy)"
msgstr "%s (cópia)"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid "%s Votes"
msgstr "%s Votes"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "%s certification passed"
msgstr "Passou na certificação de %s"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "%s challenge certification"
msgstr "Certificação do desafio %s"

#. module: survey
#: model:ir.actions.report,print_report_name:survey.certification_report
msgid "'Certification - %s' % (object.survey_id.display_name)"
msgstr "\"Certificação – %s\" % (object.survey_id.display_name)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "0000000010"
msgstr "0000000010"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug2
msgid "10 kg"
msgstr "10 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug2
msgid "100 years"
msgstr "100 anos"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug3
msgid "1055"
msgstr "1055"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug3
msgid "116 years"
msgstr "116 anos"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug1
msgid "1227"
msgstr "1227"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug4
msgid "127 years"
msgstr "127 anos"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug2
msgid "1324"
msgstr "1324"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug1
msgid "1450 km"
msgstr "1450 km"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug3
msgid "16.2 kg"
msgstr "16,2 kg"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "2023-08-18"
msgstr "18-08-2023"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug2
msgid "3700 km"
msgstr "3700 km"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "403: Forbidden"
msgstr "403: Forbidden"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug4
msgid "47 kg"
msgstr "47 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug1
msgid "5.7 kg"
msgstr "5,7 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug3
msgid "6650 km"
msgstr "6650 km"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug1
msgid "99 years"
msgstr "99 anos"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid ""
"<b>Certificate</b>\n"
"                            <br/>"
msgstr ""
"<b>Certificado</b>\n"
"                            <br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">Completed</span>"
msgstr ""
"<br/>\n"
"                                    <span class=\"text-muted\">Concluído</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">Registered</span>"
msgstr ""
"<br/>\n"
"                                    <span class=\"text-muted\">Registrado</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "<br/> <span>by</span>"
msgstr "<br/> <span>por</span>"

#. module: survey
#: model:mail.template,body_html:survey.mail_template_certification
msgid ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"    <table style=\"width:600px;margin:5px auto;\">\n"
"        <tbody>\n"
"            <tr><td>\n"
"                <!-- We use the logo of the company that created the survey (to handle multi company cases) -->\n"
"                <a href=\"/\"><img t-if=\"not object.survey_id.create_uid.company_id.uses_default_logo\" t-attf-src=\"/logo.png?company={{ object.survey_id.create_uid.company_id.id }}\" style=\"vertical-align:baseline;max-width:100px;\"/></a>\n"
"            </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                    Certification: <t t-out=\"object.survey_id.display_name or ''\">Feedback Form</t>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"    <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"        <tbody>\n"
"            <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p>Dear <span t-out=\"object.partner_id.name or 'participant'\">participant</span></p>\n"
"                <p>\n"
"                    Please find attached your\n"
"                        <strong t-out=\"object.survey_id.display_name or ''\">Furniture Creation</strong>\n"
"                    certification\n"
"                </p>\n"
"                <p>Congratulations for passing the test with a score of <strong t-out=\"object.scoring_percentage\"/>%!</p>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"    <table style=\"width:600px;margin:5px auto;\">\n"
"        <tbody>\n"
"            <tr><td>\n"
"                <!-- Usamos o logo da empresa que criou a pesquisa (para lidar com casos multiempresa) -->\n"
"                <a href=\"/\"><img t-if=\"not object.survey_id.create_uid.company_id.uses_default_logo\" t-attf-src=\"/logo.png?company={{ object.survey_id.create_uid.company_id.id }}\" style=\"vertical-align:baseline;max-width:100px;\"/></a>\n"
"            </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                    Certificado: <t t-out=\"object.survey_id.display_name or ''\">Formulário de feedback</t>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"    <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"        <tbody>\n"
"            <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p>Prezado <span t-out=\"object.partner_id.name or 'participant'\">participante</span></p>\n"
"                <p>\n"
"                    Segue em anexo o seu certificado de\n"
"                        <strong t-out=\"object.survey_id.display_name or ''\">Construção de móveis</strong>\n"
"                    \n"
"                </p>\n"
"                <p> Parabéns por ser aprovado no teste com pontuação de <strong t-out=\"object.scoring_percentage\"/>%!</p>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"</div>\n"
"            "

#. module: survey
#: model:mail.template,body_html:survey.mail_template_user_input_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or 'participant'\">participant</t><br/><br/>\n"
"        <t t-if=\"object.survey_id.certification\">\n"
"            You have been invited to take a new certification.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            We are conducting a survey and your response would be appreciated.\n"
"        </t>\n"
"        </p><div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.get_start_url())\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                <t t-if=\"object.survey_id.certification\">\n"
"                    Start Certification\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    Start Survey\n"
"                </t>\n"
"            </a>\n"
"        </div>\n"
"        <t t-if=\"object.deadline\">\n"
"            Please answer the survey for <t t-out=\"format_date(object.deadline) or ''\">05/05/2021</t>.<br/><br/>\n"
"        </t>\n"
"        <t t-if=\"object.survey_id.certification\">\n"
"            We wish you good luck!\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            Thank you in advance for your participation.\n"
"        </t>\n"
"    \n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Prezado <t t-out=\"object.partner_id.name or 'participant'\">participante</t><br/><br/>\n"
"        <t t-if=\"object.survey_id.certification\">\n"
"            Você foi convidado a prestar outra prova de certificação.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            Estamos realizando uma pesquisa e sua resposta será de grande valor.\n"
"        </t>\n"
"        </p><div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.get_start_url())\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                <t t-if=\"object.survey_id.certification\">\n"
"                    Iniciar certificação\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    Iniciar pesquisa\n"
"                </t>\n"
"            </a>\n"
"        </div>\n"
"        <t t-if=\"object.deadline\">\n"
"            Responda a pesquisa até <t t-out=\"format_date(object.deadline) or ''\">05/05/2021</t>.<br/><br/>\n"
"        </t>\n"
"        <t t-if=\"object.survey_id.certification\">\n"
"            Boa sorte!\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            Agradecemos desde já por sua participação.\n"
"        </t>\n"
"    \n"
"</div>\n"
"            "

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<i class=\"fa fa-bar-chart\"/> Results"
msgstr "<i class=\"fa fa-bar-chart\"/> Resultados"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-check-square-o fa-lg me-2\"/>answer"
msgstr "<i class=\"fa fa-check-square-o fa-lg me-2\"/>resposta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-circle-o  fa-lg me-2\"/>answer"
msgstr "<i class=\"fa fa-circle-o  fa-lg me-2\"/>resposta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-circle-o fa-lg\" role=\"img\" aria-label=\"Not checked\" "
"title=\"Not checked\"/>"
msgstr ""
"<i class=\"fa fa-circle-o fa-lg\" role=\"img\" aria-label=\"Not checked\" "
"title=\"Not checked\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<i class=\"fa fa-close\"/> Close"
msgstr "<i class=\"fa fa-close\"/> Encerrar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-dot-circle-o fa-lg me-2\"/>answer"
msgstr "<i class=\"fa fa-dot-circle-o fa-lg me-2\"/>resposta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-dot-circle-o fa-lg\" role=\"img\" aria-label=\"Checked\" "
"title=\"Checked\"/>"
msgstr ""
"<i class=\"fa fa-dot-circle-o fa-lg\" role=\"img\" aria-label=\"Marcado\" "
"title=\"Marcado\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> It is currently not possible to "
"pass this assessment because no question is configured to give any points."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/> Atualmente, não é possível ser "
"aprovado nessa avaliação pois não há perguntas com pontuação configuradas."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Download certification\" title=\"Download certification\"/>\n"
"                                        Download certification"
msgstr ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Download certification\" title=\"Download certification\"/>\n"
"                                        Baixar certificado"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-square-o fa-lg me-2\"/>answer"
msgstr "<i class=\"fa fa-square-o fa-lg me-2\"/>resposta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
msgid "<i class=\"oi oi-fw oi-arrow-right\"/>Go to Survey"
msgstr "<i class=\"oi oi-fw oi-arrow-right\"/>Ir para pesquisa"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 "
"pt-1\">Avg</span>"
msgstr ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 "
"pt-1\">Méd.</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 "
"pt-1\">Max</span>"
msgstr ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 "
"pt-1\">Máx.</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 "
"pt-1\">Min</span>"
msgstr ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 "
"pt-1\">Mín.</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\" id=\"enter-"
"tooltip\"> or press CTRL+Enter</span>"
msgstr ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\" id=\"enter-"
"tooltip\"> ou pressione CTRL+Enter</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\">\n"
"                            <span id=\"enter-tooltip\">or press Enter</span>\n"
"                        </span>"
msgstr ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\">\n"
"                            <span id=\"enter-tooltip\">ou pressione Enter</span>\n"
"                        </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\">\n"
"                    <span id=\"enter-tooltip\">or press CTRL+Enter</span>\n"
"                </span>"
msgstr ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\">\n"
"                    <span id=\"enter-tooltip\">ou pressione CTRL+Enter</span>\n"
"                </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"mx-1\">-</span>"
msgstr "<span class=\"mx-1\">–</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.res_partner_view_form
msgid ""
"<span class=\"o_stat_text\" invisible=\"certifications_company_count &lt; 2\">Certifications</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"certifications_company_count &gt; 1\">Certification</span>"
msgstr ""
"<span class=\"o_stat_text\" invisible=\"certifications_company_count &lt; 2\">Certificações</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"certifications_company_count &gt; 1\">Certificação</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.res_partner_view_form
msgid ""
"<span class=\"o_stat_text\" invisible=\"certifications_count &lt; 2\">Certifications</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"certifications_count &gt; 1\">Certification</span>"
msgstr ""
"<span class=\"o_stat_text\" invisible=\"certifications_count &lt; 2\">Certificações</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"certifications_count &gt; 1\">Certificação</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid ""
"<span class=\"o_survey_enter fw-bold text-muted ms-2 d-none d-md-inline\">or"
" press Enter</span>"
msgstr ""
"<span class=\"o_survey_enter fw-bold text-muted ms-2 d-none d-md-inline\">ou"
" pressione Enter</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_selection_key
msgid ""
"<span class=\"o_survey_key text-center position-absolute bg-white rounded-"
"start py-0 ps-2\"><span class=\"text-primary text-center text-center w-100 "
"position-relative\">Key</span></span>"
msgstr ""
"<span class=\"o_survey_key text-center position-absolute bg-white rounded-"
"start py-0 ps-2\"><span class=\"text-primary text-center text-center w-100 "
"position-relative\">Chave</span></span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid ""
"<span class=\"o_survey_results_topbar_clear_filters text-primary\">\n"
"                                <i class=\"fa fa-trash me-1\"/>Remove all filters\n"
"                            </span>"
msgstr ""
"<span class=\"o_survey_results_topbar_clear_filters text-primary\">\n"
"                                <i class=\"fa fa-trash me-1\"/>Remover todos os filtros\n"
"                            </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid ""
"<span class=\"o_survey_session_answer_count\">0</span>\n"
"                                     /"
msgstr ""
"<span class=\"o_survey_session_answer_count\">0</span>\n"
"                                     /"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_open
msgid ""
"<span class=\"o_survey_session_navigation_next_label\">Start</span>\n"
"                        <i class=\"oi oi-chevron-right\"/>"
msgstr ""
"<span class=\"o_survey_session_navigation_next_label\">Iniciar</span>\n"
"                        <i class=\"oi oi-chevron-right\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-break text-muted\">Completed</span>"
msgstr "<span class=\"text-break text-muted\">Completed</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-break text-muted\">Registered</span>"
msgstr "<span class=\"text-break text-muted\">Registered</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-break text-muted\">Success</span>"
msgstr "<span class=\"text-break text-muted\">Success</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-muted\">Average Duration</span>"
msgstr "<span class=\"text-muted\">Duração média</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-muted\">Questions</span>"
msgstr "<span class=\"text-muted\">Perguntas</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "<span class=\"text-muted\">Responded</span>"
msgstr "<span class=\"text-muted\">Respondido</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "<span class=\"text-success\">Correct</span>"
msgstr "<span class=\"text-success\">Correto</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "<span class=\"text-warning\">Partial</span>"
msgstr "<span class=\"text-warning\">Parcial</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span invisible=\"not is_scored_question\">Points</span>"
msgstr "<span invisible=\"not is_scored_question\">Pontos</span>"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p3_q1
msgid ""
"<span>\"Red\" is not a category, I know what you are trying to do ;)</span>"
msgstr ""
"<span>\"Vermelho\" não é uma categoria, eu sei o que você está tentando "
"fazer ;)</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "<span>%</span>"
msgstr "<span>%</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>All surveys</span>"
msgstr "<span>Todas as pesquisas</span>"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p3_q6
msgid "<span>Best time to do it, is the right time to do it.</span>"
msgstr "<span>A hora certa é agora.</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>Completed surveys</span>"
msgstr "<span>Pesquisas concluídas</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "<span>Date</span>"
msgstr "<span>Dados</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>Do you like it?</span><br/>"
msgstr "<span>Gostou?</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>Failed only</span>"
msgstr "<span>Somente reprovações</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<span>How many ...?</span><br/>\n"
"                                    <i class=\"fa fa-2x\" role=\"img\" aria-label=\"Numeric\" title=\"Numeric\">123 </i>\n"
"                                    <i class=\"fa fa-2x fa-sort\" role=\"img\" aria-label=\"Numeric\"/>"
msgstr ""
"<span>Quantos?</span><br/>\n"
"                                    <i class=\"fa fa-2x\" role=\"img\" aria-label=\"Numeric\" title=\"Numeric\">123 </i>\n"
"                                    <i class=\"fa fa-2x fa-sort\" role=\"img\" aria-label=\"Numeric\"/>"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p5_q1
msgid ""
"<span>If you don't like us, please try to be as objective as "
"possible.</span>"
msgstr ""
"<span>Se você não gosta da gente, tente ser o mais objetivo possível.</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<span>Name all the animals</span><br/>\n"
"                                    <i class=\"fa fa-align-justify fa-4x\" role=\"img\" aria-label=\"Multiple lines\" title=\"Multiple Lines\"/>"
msgstr ""
"<span>Nomeie todos os animais<br/>\n"
"                                    <i class=\"fa fa-align-justify fa-4x\" role=\"img\" aria-label=\"Multiple lines\" title=\"Multiple Lines\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<span>Name one animal</span><br/>\n"
"                                    <i class=\"fa fa-minus fa-4x\" role=\"img\" aria-label=\"Single Line\" title=\"Single Line\"/>"
msgstr ""
"<span>Nomeie um animal</span><br/>\n"
"                                    <i class=\"fa fa-minus fa-4x\" role=\"img\" aria-label=\"Single Line\" title=\"Single Line\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "<span>Number of attempts left</span>:"
msgstr "<span>Número de tentativas restantes</span>:"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p2_q1
msgid "<span>Our famous Leader!</span>"
msgstr "<span>Nosso famoso líder!</span>"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p3_q3
msgid "<span>Our sales people have an advantage, but you can do it!</span>"
msgstr "<span>Nossa equipe de vendas tem vantagens, mas você consegue!</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>Passed and Failed</span>"
msgstr "<span>Aprovações e Reprovações</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>Passed only</span>"
msgstr "<span>Somente aprovações</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid ""
"<span>This certificate is presented to</span>\n"
"                                <br/>"
msgstr ""
"<span>Este certificado é concedido a</span>\n"
"                                <br/>"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "<span>Try It</span>"
msgstr "<span>Tentar</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<span>Waiting for attendees...</span>"
msgstr "<span>Aguardando os participantes…</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>When does ... start?</span><br/>"
msgstr "<span>Quando … começa?</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>When is Christmas?</span><br/>"
msgstr "<span>Quando é o Natal?</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>Which are yellow?</span><br/>"
msgstr "<span>Quais dos itens são amarelos?</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>Which is yellow?</span><br/>"
msgstr "<span>Qual dos itens é amarelo?</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid ""
"<span>for successfully completing</span>\n"
"                                <br/>"
msgstr ""
"<span>por concluir com êxito</span>\n"
"                                <br/>"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q4
msgid "A \"Citrus\" could give you ..."
msgstr "Um \"Citrus\" pode dar…"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid "A label must be attached to only one question."
msgstr "Um rótulo deve ser anexado a apenas uma pergunta."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_len_max
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_len_min
msgid "A length must be positive!"
msgstr "A duração deve ser positiva."

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_4
msgid "A little bit overpriced"
msgstr "Um pouco caro"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_5
msgid "A lot overpriced"
msgstr "Muito caro"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_session_speed_rating_has_time_limit
msgid ""
"A positive default time limit is required when the session rewards quick "
"answers."
msgstr ""
"Um limite de tempo padrão positivo é necessário quando a sessão recompensa "
"respostas rápidas."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question_answer__answer_score
msgid ""
"A positive score indicates a correct choice; a negative or null score "
"indicates a wrong answer"
msgstr ""
"Pontuações positivas indicam escolhas corretas; pontuações negativas ou "
"nulas indicam respostas erradas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "A problem has occurred"
msgstr "Ocorreu um problema"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "A question can either be skipped or answered, not both."
msgstr "Uma pergunta pode ser ignorada ou respondida, mas não os dois."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"A scored survey needs at least one question that gives points.\n"
"Please check answers and their scores."
msgstr ""
"Uma pesquisa pontuada precisa de pelo menos uma pergunta que gere pontos.\n"
"Verifique as respostas e suas pontuações."

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2
msgid "About our ecommerce"
msgstr "Sobre nosso eCommerce"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1
msgid "About you"
msgstr "Sobre você"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_access_mode
#: model:ir.model.fields,field_description:survey.field_survey_survey__access_mode
msgid "Access Mode"
msgstr "Modo de acesso"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__access_token
msgid "Access Token"
msgstr "Token de acesso"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_access_token_unique
msgid "Access token should be unique"
msgstr "O token de acesso deve ser exclusivo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_needaction
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_needaction
msgid "Action Needed"
msgstr "Requer ação"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__active
msgid "Active"
msgstr "Ativo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_ids
msgid "Activities"
msgstr "Atividades"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_exception_decoration
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoração de atividade excepcional"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_state
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_state
msgid "Activity State"
msgstr "Status da atividade"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_type_icon
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ícone do tipo de atividade"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Add a question"
msgstr "Adicionar uma pergunta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Add a section"
msgstr "Adicionar uma seção"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Add existing contacts..."
msgstr "Adicionar contatos existentes…"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Add some fun to your presentations by sharing questions live"
msgstr "Divirta-se nas apresentações compartilhando as perguntas ao vivo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__emails
msgid "Additional emails"
msgstr "E-mails adicionais"

#. module: survey
#: model:res.groups,name:survey.group_survey_manager
msgid "Administrator"
msgstr "Administrador"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug1
msgid "Africa"
msgstr "África"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q6
msgid ""
"After watching this video, will you swear that you are not going to "
"procrastinate to trim your hedge this year?"
msgstr ""
"Depois de assistir a este vídeo, você promete que não vai procrastinar a "
"poda de sua cerca-viva este ano?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col3
msgid "Agree"
msgstr "Concordar"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scored_date_have_answers
msgid ""
"All \"Is a scored question = True\" and \"Question Type: Date\" questions "
"need an answer"
msgstr ""
"Todas as perguntas \"Is a scored question = True\" e \"Question Type: Data\""
" precisam de resposta"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scored_datetime_have_answers
msgid ""
"All \"Is a scored question = True\" and \"Question Type: Datetime\" "
"questions need an answer"
msgstr ""
"Todas as perguntas \"Is a scored question = True\" e \"Question Type: "
"Datetime\" precisam de resposta"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_selection__all
msgid "All questions"
msgstr "Todas as perguntas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "All surveys"
msgstr "Todas as pesquisas"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_is_time_limited_have_time_limit
msgid "All time-limited questions need a positive time limit"
msgstr ""
"Todas as perguntas com limite de tempo precisam de um limite de tempo "
"positivo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Allow Roaming"
msgstr "Permitir voltar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__allowed_triggering_question_ids
msgid "Allowed Triggering Questions"
msgstr "Perguntas de acionamento permitidas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__allowed_survey_types
msgid "Allowed survey types"
msgstr "Tipos de pesquisas permitidas"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug2
msgid "Amenhotep"
msgstr "Amenhotep"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_user_input_unique_token
msgid "An access token must be unique!"
msgstr "Um token de acesso deve ser exclusivo!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_answer_score
msgid "An answer score for a non-multiple choice question cannot be negative!"
msgstr ""
"A pontuação de resposta das perguntas que não são de múltipla escolha não "
"pode ser negativa."

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p3
msgid "An apple a day keeps the doctor away."
msgstr "Melhor prevenir do que remediar."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_leaderboard
msgid "Anonymous"
msgstr "Anônimo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_tree
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Answer"
msgstr "Resposta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_type
msgid "Answer Type"
msgstr "Tipo de resposta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__deadline
msgid "Answer deadline"
msgstr "Prazo de resposta"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question_answer__value_label
msgid ""
"Answer label as either the value itself if not empty or a letter "
"representing the index of the answer otherwise."
msgstr ""
"Rótulo da resposta como o próprio valor, se não estiver vazio, ou uma letra "
"que representa a resposta, caso contrário."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__user_input_line_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__user_input_line_ids
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Answers"
msgstr "Respostas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_answer_count
msgid "Answers Count"
msgstr "Total de respostas"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__access_mode__public
msgid "Anyone with the link"
msgstr "Quem tiver o link"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "Aparece em"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug1
msgid "Apple Trees"
msgstr "Macieiras"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row1
msgid "Apples"
msgstr "Maçãs"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Archived"
msgstr "Arquivado"

#. module: survey
#: model:survey.question,title:survey.survey_demo_food_preferences_q1
msgid "Are you vegetarian?"
msgstr "Você é vegetariano(a)?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug4
msgid "Art & Culture"
msgstr "Arte e Cultura"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug1
msgid "Arthur B. McDonald"
msgstr "Arthur B. McDonald"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug2
msgid "Asia"
msgstr "Ásia"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__survey_type__assessment
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Assessment"
msgstr "Avaliação"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_attachment_count
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_attachment_count
msgid "Attachment Count"
msgstr "Total de anexos"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__attachment_ids
msgid "Attachments"
msgstr "Anexos"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_number
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Attempt n°"
msgstr "Tentativa nº"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_done_count
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Attempts"
msgstr "Tentativas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_count
msgid "Attempts Count"
msgstr "Total de tentativas"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__nickname
msgid ""
"Attendee nickname, mainly used to identify them in the survey session "
"leaderboard."
msgstr ""
"Apelido do participante, usado principalmente para identificá-lo na tabela "
"de classificação da sessão de pesquisa."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Attendees are answering the question..."
msgstr "Os participantes estão respondendo à pergunta…"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_speed_rating
msgid "Attendees get more points if they answer quickly"
msgstr "Os participantes ganham mais pontos se responderem rapidamente"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__author_id
msgid "Author"
msgstr "Autor"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__certification_mail_template_id
msgid ""
"Automated email sent to the user when they succeed the certification, "
"containing their certification document."
msgstr ""
"E-mail automatizado que contém o certificado e é enviado ao usuário ao ser "
"aprovado na certificação."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug3
msgid "Autumn"
msgstr "Outono"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Average"
msgstr "Média"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_duration_avg
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Average Duration"
msgstr "Duração média"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Average Score"
msgstr "Pontuação média"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__answer_duration_avg
msgid "Average duration of the survey (in hours)"
msgstr "Duração média da pesquisa (em horas)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_score_avg
msgid "Avg Score (%)"
msgstr "Pontuação média (%)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug3
msgid "Avicii"
msgstr "Avicii"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__background_image
#: model:ir.model.fields,field_description:survey.field_survey_survey__background_image
msgid "Background Image"
msgstr "Imagem de fundo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__background_image_url
#: model:ir.model.fields,field_description:survey.field_survey_survey__background_image_url
msgid "Background Url"
msgstr "URL do plano de fundo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "Badge"
msgstr "Medalha"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug3
msgid "Baobab Trees"
msgstr "Árvores de Baobá"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug1
msgid "Bees"
msgstr "Abelhas"

#. module: survey
#: model:survey.question,question_placeholder:survey.vendor_certification_page_3_question_3
msgid "Beware of leap years!"
msgstr "Atenção aos anos bissextos!"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Blue Pen"
msgstr "Caneta azul"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__body_has_template_value
msgid "Body content is the same as the template"
msgstr "O conteúdo do corpo do texto é igual ao do modelo"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug4
msgid "Bricks"
msgstr "Tijolos"

#. module: survey
#: model:survey.question,question_placeholder:survey.survey_feedback_p1_q1
msgid "Brussels"
msgstr "Bruxelas"

#. module: survey
#: model:survey.question,question_placeholder:survey.survey_demo_quiz_p1_q3
msgid "Brussels, Belgium"
msgstr "Bruxelas, Bélgica"

#. module: survey
#: model:survey.survey,title:survey.survey_demo_burger_quiz
msgid "Burger Quiz"
msgstr "Questionário sobre hambúrguer"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "But first, keep listening to the host."
msgstr "Mas, primeiro, ouça o apresentador."

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_3
msgid "Cabinet with Doors"
msgstr "Armário com portas"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_row1
msgid "Cactus"
msgstr "Cacto"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__can_edit_body
msgid "Can Edit Body"
msgstr "Pode editar o corpo do texto"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q3
msgid "Can Humans ever directly see a photon?"
msgstr "Os seres humanos conseguem ver um fóton a olho nu?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_tree
msgid "Certification"
msgstr "Certificação"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_badge_id
msgid "Certification Badge"
msgstr "Medalha de certificação"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_badge_id_dummy
msgid "Certification Badge "
msgstr "Medalha de certificação"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Certification Badge is not configured for the survey %(survey_name)s"
msgstr ""
"A medalha de certificação não está configurada na pesquisa %(survey_name)s"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "Certification Failed"
msgstr "Certificação reprovada"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "Certification n°"
msgstr "Certificação nº"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_report_layout
msgid "Certification template"
msgstr "Modelo de certificado"

#. module: survey
#: model:mail.template,subject:survey.mail_template_certification
msgid "Certification: {{ object.survey_id.display_name }}"
msgstr "Certification: {{ object.survey_id.display_name }}"

#. module: survey
#: model:ir.actions.report,name:survey.certification_report
#: model:ir.model.fields.selection,name:survey.selection__gamification_challenge__challenge_category__certification
msgid "Certifications"
msgstr "Certificações"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_res_partner__certifications_count
#: model:ir.model.fields,field_description:survey.field_res_users__certifications_count
msgid "Certifications Count"
msgstr "Total de certificações"

#. module: survey
#: model:ir.actions.act_window,name:survey.res_partner_action_certifications
msgid "Certifications Succeeded"
msgstr "Certificações aprovadas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Certified"
msgstr "Certificado"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_mail_template_id
msgid "Certified Email Template"
msgstr "Modelo de e-mail do certificado"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_1
msgid "Chair floor protection"
msgstr "Proteção de piso para cadeiras"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Cheating on your neighbors will not help!"
msgstr "Colar dos seus colegas não vai ajudar!"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__is_attempts_limited
#: model:ir.model.fields,help:survey.field_survey_user_input__is_attempts_limited
msgid "Check this option if you want to limit the number of attempts per user"
msgstr ""
"Marque essa opção se você quiser limitar o número de tentativas por usuário"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/boolean_update_flag_field/boolean_update_flag_fields.js:0
msgid "Checkbox updating comparison flag"
msgstr "Caixa de seleção que atualiza o sinalizador de comparação"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug2
msgid "China"
msgstr "China"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Choices"
msgstr "Escolhas"

#. module: survey
#: model_terms:survey.survey,description:survey.survey_demo_burger_quiz
msgid "Choose your favourite subject and show how good you are. Ready?"
msgstr "Escolha seu assunto preferido e mostre o que você sabe. Pronto?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_food_preferences_q3
msgid "Choose your green meal"
msgstr "Escolha sua refeição vegetariana"

#. module: survey
#: model:survey.question,title:survey.survey_demo_food_preferences_q4
msgid "Choose your meal"
msgstr "Escolha sua refeição"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_blue
msgid "Classic Blue"
msgstr "Azul clássico"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_gold
msgid "Classic Gold"
msgstr "Dourado clássico"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_purple
msgid "Classic Purple"
msgstr "Roxo clássico"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row3
msgid "Clementine"
msgstr "Tangerina"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug4
msgid "Cliff Burton"
msgstr "Cliff Burton"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Close"
msgstr "Encerrar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Close Live Session"
msgstr "Encerrar sessão ao vivo"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_1
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Color"
msgstr "Cor"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__color
msgid "Color Index"
msgstr "Índice de cores"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"Combining roaming and \"Scoring with answers after each page\" is not possible; please update the following surveys:\n"
"- %(survey_names)s"
msgstr ""
"Não é possível combinar roaming e \"Pontuação com respostas após cada página\"; atualize as seguintes pesquisas:\n"
"– %(survey_names)s"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_question_form
msgid "Come back once you have added questions to your Surveys."
msgstr "Volte quando tiver adicionado perguntas às suas pesquisas."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_comments
msgid "Comment"
msgstr "Comentário"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comments_message
msgid "Comment Message"
msgstr "Mensagem do comentário"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comment_count_as_answer
msgid "Comment is an answer"
msgstr "Comentários são respostas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_res_partner__certifications_company_count
#: model:ir.model.fields,field_description:survey.field_res_users__certifications_company_count
msgid "Company Certifications Count"
msgstr "Total de certificações da empresa"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__done
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Completed"
msgstr "Concluído"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "Completed surveys"
msgstr "Pesquisas concluídas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Compose Email"
msgstr "Escrever e-mail"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "Computing score requires a question in arguments."
msgstr "O cálculo da pontuação requer uma pergunta nos argumentos."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Conditional display"
msgstr "Exibição condicional"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/survey_question_trigger/survey_question_trigger.js:0
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"Conditional display is not available when questions are randomly picked."
msgstr ""
"A exibição condicional não é disponibilizada se as perguntas são escolhidas "
"aleatoriamente."

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_3
msgid "Conference chair"
msgstr "Cadeira de reuniões"

#. module: survey
#: model_terms:web_tour.tour,rainbow_man_message:survey.survey_tour
msgid "Congratulations! You are now ready to collect feedback like a pro :-)"
msgstr ""
"Parabéns! Agora, você está pronto para reunir feedbacks como um expert :-)"

#. module: survey
#: model_terms:gamification.badge,description:survey.vendor_certification_badge
msgid "Congratulations, you are now official vendor of MyCompany"
msgstr "Parabéns! Agora você é um colaborador oficial da MyCompany"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Congratulations, you have passed the test!"
msgstr "Parabéns, você passou no teste!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Constraints"
msgstr "Restrições"

#. module: survey
#: model:ir.model,name:survey.model_res_partner
#: model:ir.model.fields,field_description:survey.field_survey_user_input__partner_id
msgid "Contact"
msgstr "Contato"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__has_conditional_questions
msgid "Contains conditional questions"
msgstr "Contém perguntas condicionais"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__body
msgid "Contents"
msgstr "Conteúdo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Continue"
msgstr "Continuar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "Continue here"
msgstr "Continuar aqui"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug4
msgid "Cookies"
msgstr "Cookies"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Copied!"
msgstr "Copiado."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug3
msgid "Cornaceae"
msgstr "Cornaceae"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_1
msgid "Corner Desk Right Sit"
msgstr "Mesa de canto (à direita)"

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__is_correct
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_is_correct
msgid "Correct"
msgstr "Correto"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Correct Answer"
msgstr "Resposta correta"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_datetime
msgid "Correct date and time answer for this question."
msgstr "Resposta correta para a data e hora desta pergunta."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_date
msgid "Correct date answer"
msgstr "Resposta correta para a data"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_date
msgid "Correct date answer for this question."
msgstr "Resposta correta para a data desta pergunta."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_datetime
msgid "Correct datetime answer"
msgstr "Resposta correta para data e hora"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_numerical_box
msgid "Correct number answer for this question."
msgstr "Resposta numérica correta para esta pergunta."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_numerical_box
msgid "Correct numerical answer"
msgstr "Resposta numérica correta"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_3
msgid "Correctly priced"
msgstr "Preço certo"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug3
msgid "Cosmic rays"
msgstr "Raios cósmicos"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Create Live Session"
msgstr "Criar sessão ao vivo"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Create a custom survey from scratch"
msgstr "Criar uma pesquisa personalizada do zero"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_question__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__create_date
#: model:ir.model.fields,field_description:survey.field_survey_question__create_date
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__create_date
#: model:ir.model.fields,field_description:survey.field_survey_survey__create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input__create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__create_date
msgid "Created on"
msgstr "Criado em"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Creating test token is not allowed for you."
msgstr "A criação de tokens de teste não é permitida para você."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"Creating token for anybody else than employees is not allowed for internal "
"surveys."
msgstr ""
"Não é permitido criar tokens de pesquisas internas para outras pessoas que "
"não sejam funcionários."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Creating token for closed/archived surveys is not allowed."
msgstr "Não é permitido criar tokens para pesquisas arquivadas/encerradas."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"Creating token for external people is not allowed for surveys requesting "
"authentication."
msgstr ""
"Não é permitida a criação de tokens para pessoas externas em pesquisas que "
"solicitam autenticação."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_id
msgid "Current Question"
msgstr "Pergunta atual"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_start_time
msgid "Current Question Start Time"
msgstr "Hora de início da pergunta atual"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_start_time
msgid "Current Session Start Time"
msgstr "Hora de início da sessão atual"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_time_limited
msgid "Currently only supported for live sessions."
msgstr "No momento, há suporte somente para sessões ao vivo."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__survey_type__custom
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Custom"
msgstr "Personalizado"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid ""
"Customers will receive a new token and be able to completely retake the "
"survey."
msgstr ""
"Os clientes receberão um novo token e poderão refazer toda a pesquisa."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Customers will receive the same token."
msgstr "Os clientes receberão o mesmo token."

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_5
msgid "Customizable Lamp"
msgstr "Lâmpada personalizada"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_time_customized
msgid "Customized speed rewards"
msgstr "Recompensas por velocidade personalizadas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "DEMO_CERTIFIED_NAME"
msgstr "DEMO_CERTIFIED_NAME"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__date
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__date
msgid "Date"
msgstr "Data"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_date
msgid "Date answer"
msgstr "Resposta em data"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__datetime
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__datetime
msgid "Datetime"
msgstr "Data e hora"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_datetime
msgid "Datetime answer"
msgstr "Resposta em data e hora"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__deadline
msgid "Datetime until customer can open the survey and submit answers"
msgstr ""
"Data e hora até que o cliente possa abrir a pesquisa e enviar as respostas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__deadline
msgid "Deadline"
msgstr "Prazo final"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_speed_rating_time_limit
msgid "Default time given to receive additional points for right answers"
msgstr ""
"Tempo padrão dado para receber pontos adicionais por respostas corretas"

#. module: survey
#: model:ir.model.fields,help:survey.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "Defina a visibilidade dos desafios pelos menus"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Delete"
msgstr "Excluir"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__description
#: model:ir.model.fields,field_description:survey.field_survey_survey__description
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Description"
msgstr "Descrição"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_2
msgid "Desk Combination"
msgstr "Combinação de mesas"

#. module: survey
#: model:ir.actions.act_window,name:survey.survey_user_input_line_action
#: model:ir.ui.menu,name:survey.menu_survey_response_line_form
msgid "Detailed Answers"
msgstr "Respostas detalhadas"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col2
msgid "Disagree"
msgstr "Discordar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__display_name
#: model:ir.model.fields,field_description:survey.field_survey_question__display_name
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__display_name
#: model:ir.model.fields,field_description:survey.field_survey_survey__display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input__display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__progression_mode
msgid "Display Progress as"
msgstr "Exibir progresso como"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/survey_question_trigger/survey_question_trigger.js:0
msgid "Displayed if \"%s\"."
msgstr "Exibido se \"%s\"."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Displayed when the answer entered is not valid."
msgstr "Exibido quando a resposta inserida não é válida."

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_1
msgid "Do we sell Acoustic Bloc Screens?"
msgstr "Vendemos telas de isolamento acústico?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q3
msgid "Do you have any other comments, questions, or concerns?"
msgstr "Tem algum outro comentário, pergunta ou preocupação?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_5
msgid "Do you think we have missing products in our catalog? (not rated)"
msgstr "Para você, há algum artigo faltando no nosso catálogo? (Não avaliada)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug2
msgid "Dogs"
msgstr "Cães"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q1
msgid "Dogwood is from which family of trees?"
msgstr "O corniso pertence a qual família de árvores?"

#. module: survey
#: model:survey.question,question_placeholder:survey.survey_demo_quiz_p1_q2
msgid "Don't be shy, be wild!"
msgstr "Não tenha vergonha, se solte!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug1
msgid "Douglas Fir"
msgstr "Douglas Fir"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_4
msgid "Drawer"
msgstr "Gaveta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Duplicate Question"
msgstr "Duplicar pergunta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Edit Survey"
msgstr "Editar pesquisa"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "Edit in backend"
msgstr "Editar no backend"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__email
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Email"
msgstr "E-mail"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "End Live Session"
msgstr "Encerrar sessão ao vivo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__description_done
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "End Message"
msgstr "Mensagem final"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__end_datetime
msgid "End date and time"
msgstr "Data e hora de término"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "End of Survey"
msgstr "Fim da pesquisa"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Enter Session Code"
msgstr "Insira o código da sessão"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__constr_error_msg
msgid "Error message"
msgstr "Mensagem de erro"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug3
msgid "Europe"
msgstr "Europa"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug3
msgid "European Yew"
msgstr "Teixo Europeu"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Exclude Tests"
msgstr "Excluir testes"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_partner_ids
msgid "Existing Partner"
msgstr "Usuário existente"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_emails
msgid "Existing emails"
msgstr "E-mails existentes"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Extremely likely"
msgstr "Extremamente provável"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug2
msgid "Eyjafjallajökull (Iceland)"
msgstr "Eyjafjallajökull (Islândia)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Failed"
msgstr "Reprovado"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "Failed only"
msgstr "Somente reprovações"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_2
msgid "Fanta"
msgstr "Fanta"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#: model:survey.survey,title:survey.survey_feedback
msgid "Feedback Form"
msgstr "Formulário de feedback"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_row2
msgid "Ficus"
msgstr "Fícus"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
#: model_terms:ir.ui.view,arch_db:survey.question_result_text
msgid "Filter surveys"
msgstr "FIltrar pesquisas"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Final Leaderboard"
msgstr "Classificação final"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q4_sug2
msgid "Fish"
msgstr "Peixe"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_follower_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_partner_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (usuários)"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_type_icon
#: model:ir.model.fields,help:survey.field_survey_user_input__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Ícone do Font Awesome. Ex: fa-tasks"

#. module: survey
#: model:survey.survey,title:survey.survey_demo_food_preferences
msgid "Food Preferences"
msgstr "Preferências alimentares"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__text_box
msgid "Free Text"
msgstr "Texto livre"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_text_box
msgid "Free Text answer"
msgstr "Resposta de texto livre"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q4
msgid "From which continent is native the Scots pine (pinus sylvestris)?"
msgstr ""
"De qual continente é originário o pinheiro-silvestre (pinus sylvestris)?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug1
msgid "Fruits"
msgstr "Frutas"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3
msgid "Fruits and vegetables"
msgstr "Frutas e legumes"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "Functional Training"
msgstr "Treinamento funciona"

#. module: survey
#: model:ir.model,name:survey.model_gamification_badge
msgid "Gamification Badge"
msgstr "Medalha de Gamificação"

#. module: survey
#: model:ir.model,name:survey.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "Desafio de Gamificação"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Gather feedbacks from your employees and customers"
msgstr "Reúna feedbacks de seus funcionários e clientes"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug1
msgid "Geography"
msgstr "Geografia"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_give_badge
msgid "Give Badge"
msgstr "Dar medalhas"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q3
msgid "Give the list of all types of wood we sell."
msgstr "Forneça a lista de todos os tipos de madeira que vendemos."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug1
msgid "Good"
msgstr "Bom"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Good luck!"
msgstr "Boa sorte!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug4
msgid "Good value for money"
msgstr "Valor pago"

#. module: survey
#: model_terms:survey.survey,description_done:survey.survey_demo_food_preferences
msgid "Got it!"
msgstr "Entendi!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug2
msgid "Grapefruits"
msgstr "Toranjas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Group By"
msgstr "Agrupar por"

#. module: survey
#: model:ir.model,name:survey.model_ir_http
msgid "HTTP Routing"
msgstr "Roteamento HTTP"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_mode
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Handle existing"
msgstr "Lidar com existentes"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Handle quiz &amp; certifications"
msgstr "Gerir questionários e certificações"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q3_sug1
msgid "Hard"
msgstr "Difícil"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__has_message
#: model:ir.model.fields,field_description:survey.field_survey_user_input__has_message
msgid "Has Message"
msgstr "Tem uma mensagem"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__has_image_only_suggested_answer
msgid "Has image only suggested answer"
msgstr "Tem apenas imagem - resposta sugerida"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_2
msgid "Height"
msgstr "Altura"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Help Participants know what to write"
msgstr "Ajude os participantes a saber o que escrever"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug3
msgid "Hemiunu"
msgstr "Hemiunu"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Here, you can overview all the participations."
msgstr "Aqui você tem uma visão geral de todas as participações"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug1
msgid "High quality"
msgstr "Qualidade alta"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug2
msgid "History"
msgstr "Histórico"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q3
msgid "How frequently do you buy products online?"
msgstr "Com que frequência você faz compras on-line?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "How frequently do you use our products?"
msgstr "Com que frequência você usa nossos produtos?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "How good of a presenter are you? Let's find out!"
msgstr "Você é um bom apresentador? Mostre-nos!"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "How likely are you to recommend the following products to a friend?"
msgstr "Você recomendaria os seguintes produtos para um amigo?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q1
msgid "How long is the White Nile river?"
msgstr "Qual é o comprimento do rio Nilo Branco?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_6
msgid ""
"How many chairs do you think we should aim to sell in a year (not rated)?"
msgstr ""
"Quantas cadeiras você acha que deveríamos almejar vender em um ano? (Não "
"avaliada)"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_1
msgid "How many days is our money-back guarantee?"
msgstr "A nossa garantia de devolução de dinheiro é válida por quantos dias?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "How many orders did you pass during the last 6 months?"
msgstr "Quantos pedidos você fez durante os últimos seis meses?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q4
msgid "How many times did you order products on our website?"
msgstr "Quantas vezes você pediu produtos no nosso site?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_4
msgid "How many versions of the Corner Desk do we have?"
msgstr "Quantas versões da Mesa de canto temos?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q3
msgid "How many years did the 100 years war last?"
msgstr "Quantos anos durou a guerra dos 100 anos?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_1
msgid "How much do we sell our Cable Management Box?"
msgstr "Por quanto vendemos a nossa caixa organizadora de cabos?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q5
msgid "How often should you water those plants"
msgstr "Com que frequência se rega essas plantas?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q4
msgid "How old are you?"
msgstr "Qual é a sua idade?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug4
msgid ""
"I actually don't like thinking. I think people think I like to think a lot. "
"And I don't. I do not like to think at all."
msgstr ""
"Na verdade, não gosto de pensar. Acho que as pessoas acham que gosto de "
"pensar muito. E eu não gosto. Não gosto nem um pouco de pensar."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug2
msgid ""
"I am fascinated by air. If you remove the air from the sky, all the birds "
"would fall to the ground. And all the planes, too."
msgstr ""
"Sou fascinado pelo ar. Se você tirar o ar do céu, todos os pássaros cairão "
"no chão. E todos os aviões também."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row5
msgid "I have added products to my wishlist"
msgstr "Adicionei produtos à minha lista de desejos"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug4
msgid "I have no idea, I'm a dog!"
msgstr "Não faço a menor ideia, eu sou um cachorro!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug3
msgid "I've been noticing gravity since I was very young!"
msgstr "Eu percebo a gravidade desde que era muito jovem."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__id
#: model:ir.model.fields,field_description:survey.field_survey_question__id
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__id
#: model:ir.model.fields,field_description:survey.field_survey_survey__id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__id
msgid "ID"
msgstr "ID"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_exception_icon
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_exception_icon
msgid "Icon"
msgstr "Ícone"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_exception_icon
#: model:ir.model.fields,help:survey.field_survey_user_input__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ícone para indicar uma atividade excepcional."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__access_token
msgid "Identification token"
msgstr "Token de identificação"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__progression_mode
msgid ""
"If Number is selected, it will display the number of questions answered on "
"the total number of question to answer."
msgstr ""
"Se \"Número\" for selecionado, você verá o número de perguntas respondidas "
"em relação ao número total de perguntas."

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_3
msgid ""
"If a customer purchases a 1 year warranty on 6 January 2020, when do we "
"expect the warranty to expire?"
msgstr ""
"Se um cliente comprar uma garantia de um ano em 6 de janeiro de 2020, quando"
" é previsto que a garantia expire?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_2
msgid ""
"If a customer purchases a product on 6 January 2020, what is the latest day "
"we expect to ship it?"
msgstr ""
"Se um cliente comprar um produto no dia 6 de janeiro de 2020, qual é o "
"último dia da previsão de envio dele?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_needaction
#: model:ir.model.fields,help:survey.field_survey_user_input__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Se marcado, há novas mensagens precisando de sua atenção."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_error
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_sms_error
#: model:ir.model.fields,help:survey.field_survey_user_input__message_has_error
#: model:ir.model.fields,help:survey.field_survey_user_input__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Se marcado, há mensagens com erros de entrega."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__save_as_email
msgid ""
"If checked, this option will save the user's answer as its email address."
msgstr ""
"Se marcado, a resposta do usuário será salva como seu endereço de e-mail."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__save_as_nickname
msgid "If checked, this option will save the user's answer as its nickname."
msgstr "Se marcado, a resposta do usuário será salvá como seu apelido."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__users_can_go_back
msgid "If checked, users can go back to previous pages."
msgstr "Se marcado, os usuários podem voltar para páginas anteriores."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__survey_users_login_required
#: model:ir.model.fields,help:survey.field_survey_survey__users_login_required
msgid ""
"If checked, users have to login before answering even with a valid token."
msgstr ""
"Se marcado, os usuários terão que fazer login antes de responder, mesmo com "
"um token válido."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_container
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "If other, please specify:"
msgstr "Se for outro, especifique:"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__questions_selection
msgid ""
"If randomized is selected, add the number of random questions next to the "
"section."
msgstr ""
"Se aleatório for selecionado, adicione o número de perguntas aleatórias ao "
"lado da seção."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__questions_selection
msgid ""
"If randomized is selected, you can configure the number of random questions "
"by section. This mode is ignored in live session."
msgstr ""
"Se o aleatório for selecionado, você poderá configurar o número de perguntas"
" aleatórias por seção. Esse modo é ignorado na sessão ao vivo."

#. module: survey
#: model:survey.question,question_placeholder:survey.vendor_certification_page_1_question_5
msgid "If yes, explain what you think is missing, give examples."
msgstr "Se sim, explique do que você sentiu falta e dê exemplos."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value_image
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Image"
msgstr "Imagem"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value_image_filename
msgid "Image Filename"
msgstr "Nome de arquivo da imagem"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "Image Zoom Dialog"
msgstr "Caixa de diálogo de ampliação da imagem"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug1
msgid "Imhotep"
msgstr "Imhotep"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug6
msgid "Impractical"
msgstr "Impraticável"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__session_state__in_progress
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__in_progress
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "In Progress"
msgstr "Em andamento"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q5
msgid "In the list below, select all the coniferous."
msgstr "Selecione todas as coníferas na lista abaixo."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q2
msgid "In which country did the bonsai technique develop?"
msgstr "Em que país se desenvolveu a técnica do bonsai?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_scored_question
msgid ""
"Include this question as part of quiz scoring. Requires an answer and answer"
" score to be taken into account."
msgstr ""
"Inclua esta pergunta como parte da pontuação do questionário. Requer uma "
"resposta e sua pontuação para ser considerada."

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Incorrect"
msgstr "Incorreto"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug7
msgid "Ineffective"
msgstr "Ineficaz"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_email
msgid "Input must be an email"
msgstr "A entrada deve ser um e-mail"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/integer_update_flag_field/integer_update_flag_fields.js:0
msgid "Integer updating comparison flag"
msgstr "Sinalizador de comparação de atualização de nº inteiro"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__invite_token
msgid "Invite token"
msgstr "Código de convite"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__access_mode__token
msgid "Invited people only"
msgstr "Somente pessoas convidadas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__is_mail_template_editor
msgid "Is Editor"
msgstr "É editor"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_is_follower
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_is_follower
msgid "Is Follower"
msgstr "É um seguidor"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Is a Certification"
msgstr "É uma certificação"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_page
msgid "Is a page?"
msgstr "É uma página?"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__is_session_answer
msgid "Is in a Session"
msgstr "Está em uma sessão"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_placed_before_trigger
msgid "Is misplaced?"
msgstr "Está fora do lugar?"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Is not a Certification"
msgstr "Não é uma certificação"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__is_session_answer
msgid "Is that user input part of a survey session or not."
msgstr "Essa entrada de usuário faz parte de uma sessão de pesquisa?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q3
msgid "Is the wood of a coniferous hard or soft?"
msgstr "A madeira de uma conífera é dura ou macia?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_placed_before_trigger
msgid "Is this question placed before any of its trigger questions?"
msgstr "Esta pergunta está situada após as perguntas que a acionam?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug4
msgid "Istanbul"
msgstr "Istambul"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q1_sug3
msgid "It depends"
msgstr "Depende"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It does not mean anything specific"
msgstr "Não tem nenhum significado específico"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It helps attendees focus on what you are saying"
msgstr "Ajuda os participantes a se concentrarem no que você está dizendo"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It helps attendees remember the content of your presentation"
msgstr "Ajuda os participantes a lembrarem do conteúdo da sua apresentação"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It is a small bit of text, displayed to help participants answer"
msgstr "É um trecho de texto exibido para ajudar os participantes a responder"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It is an option that can be different for each Survey"
msgstr "É uma opção que pode ser diferente em cada pesquisa"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row2
msgid "It is easy to find the product that I want"
msgstr "É fácil encontrar o produto que eu quero"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It is more engaging for your audience"
msgstr "É mais interessante para o público"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It's a Belgian word for \"Management\""
msgstr "É uma palavra belga para \"Gerenciamento\""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug3
msgid "Iznogoud"
msgstr "Iznogoud"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug1
msgid ""
"I’ve never really wanted to go to Japan. Simply because I don’t like eating "
"fish. And I know that’s very popular out there in Africa."
msgstr ""
"Nunca tive vontade de ir ao Japão porque não gosto de comer peixe. E sei que"
" também é muito comum lá na África."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug1
msgid "Japan"
msgstr "Japão"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Join Session"
msgstr "Participar da sessão"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p1_q4
msgid "Just to categorize your answers, don't worry."
msgstr "É só para categorizar suas respostas, não se preocupe."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug2
msgid "Kim Jong-hyun"
msgstr "Kim Jong-hyun"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug1
msgid "Kurt Cobain"
msgstr "Kurt Cobain"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Label"
msgstr "Rótulo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__sequence
msgid "Label Sequence order"
msgstr "Ordem de sequência de rótulo"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__matrix_row_ids
msgid "Labels used for proposed choices: rows of matrix"
msgstr "Rótulos usados para escolhas propostas: linhas da matriz"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__suggested_answer_ids
msgid ""
"Labels used for proposed choices: simple choice, multiple choice and columns"
" of matrix"
msgstr ""
"Rótulos usados para escolhas propostas: escolha única, múltipla escolha e "
"colunas da matriz"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__lang
msgid "Language"
msgstr "Idioma"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_4
msgid "Large Desk"
msgstr "Mesa grande"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_question__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__write_date
#: model:ir.model.fields,field_description:survey.field_survey_question__write_date
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__write_date
#: model:ir.model.fields,field_description:survey.field_survey_survey__write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input__write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__last_displayed_page_id
msgid "Last displayed question/page"
msgstr "Última pergunta/página exibida"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Late Activities"
msgstr "Atividades atrasadas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Launch Session"
msgstr "Iniciar sessão"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Leaderboard"
msgstr "Classificação"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_access_error
msgid "Leave"
msgstr "Sair"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_4
msgid "Legs"
msgstr "Pernas"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug2
msgid "Lemon Trees"
msgstr "Limoeiros"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Let's get started!"
msgstr "Vamos começar!"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Let's give it a spin!"
msgstr "Vamos fazer um tour!"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Let's have a look at your answers!"
msgstr "Vamos conferir suas respostas."

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Let's open the survey you just submitted."
msgstr "Vamos abrir a pesquisa que você acabou de enviar."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Likely"
msgstr "É provável"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Limit Attempts"
msgstr "Limitar tentativas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__is_attempts_limited
#: model:ir.model.fields,field_description:survey.field_survey_user_input__is_attempts_limited
msgid "Limited number of attempts"
msgstr "Número limitado de tentativas"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Live Session"
msgstr "Sessão ao vivo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__session_available
msgid "Live Session available"
msgstr "Sessão ao vivo disponível"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Live Sessions"
msgstr "Sessões ao vivo"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__survey_type__live_session
msgid "Live session"
msgstr "Sessão ao vivo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_available
msgid "Live session available"
msgstr "Sessão ao vivo disponível"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Load a <b>sample Survey</b> to get started quickly."
msgstr "Carregue uma <b>amostra de pesquisa</b> para começar rapidamente."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "Login required"
msgstr "Requer login"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__template_id
msgid "Mail Template"
msgstr "Modelo de e-mail"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__constr_mandatory
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Mandatory Answer"
msgstr "Resposta obrigatória"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__matrix
msgid "Matrix"
msgstr "Matriz"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__matrix_row_ids
msgid "Matrix Rows"
msgstr "Linhas da matriz"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__matrix_subtype
msgid "Matrix Type"
msgstr "Tipo de matriz"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_date
msgid "Max date cannot be smaller than min date!"
msgstr "A data máxima não pode ser anterior à data miníma."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_datetime
msgid "Max datetime cannot be smaller than min datetime!"
msgstr ""
"A data e a hora máximas não podem ser anteriores à data e à hora mínimas."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_length
msgid "Max length cannot be smaller than min length!"
msgstr "O tamanho máximo não pode ser menor do que o tamanho mínimo."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_float
msgid "Max value cannot be smaller than min value!"
msgstr "Valor máximo não pode ser menor do que o valor minímo!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Maximum"
msgstr "Máximo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_date
msgid "Maximum Date"
msgstr "Data máxima"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_datetime
msgid "Maximum Datetime"
msgstr "Data e hora máximas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_length_max
msgid "Maximum Text Length"
msgstr "Tamanho máximo de texto"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_max_obtainable
msgid "Maximum obtainable score"
msgstr "Pontuação méxima obtida"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_float_value
msgid "Maximum value"
msgstr "O valor máximo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "Maybe you were looking for"
msgstr "Talvez você esteja procurando"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_error
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_has_error
msgid "Message Delivery error"
msgstr "Erro na entrega da mensagem"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_ids
msgid "Messages"
msgstr "Mensagens"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Min/Max Limits"
msgstr "Limites mín/máx"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Minimum"
msgstr "Mínimo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_date
msgid "Minimum Date"
msgstr "Data mínima"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_datetime
msgid "Minimum Datetime"
msgstr "Data e hora mínimas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_length_min
msgid "Minimum Text Length"
msgstr "Tamanho mínimo de texto"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_float_value
msgid "Minimum value"
msgstr "Valor mínimo"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_blue
msgid "Modern Blue"
msgstr "Azul moderno"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_gold
msgid "Modern Gold"
msgstr "Dourado moderno"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_purple
msgid "Modern Purple"
msgstr "Roxo moderno"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug3
msgid "Mooses"
msgstr "Alces"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug4
msgid "Mount Elbrus (Russia)"
msgstr "Monte Elbrus (Rússia)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug3
msgid "Mount Etna (Italy - Sicily)"
msgstr "Monte Etna (Itália – Sicília)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug1
msgid "Mount Teide (Spain - Tenerife)"
msgstr "Monte Teide (Espanha – Tenerife)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug4
msgid "Mountain Pine"
msgstr "Pinheiro-da-montanha"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__text_box
msgid "Multiple Lines Text Box"
msgstr "Caixa de texto de várias linhas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Multiple choice with multiple answers"
msgstr "Múltipla escolha com múltiplas respostas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Multiple choice with one answer"
msgstr "Múltipla escolha com uma resposta"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__multiple_choice
msgid "Multiple choice: multiple answers allowed"
msgstr "Múltipla escolha: múltiplas respostas permitidas"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__simple_choice
msgid "Multiple choice: only one answer"
msgstr "Múltipla escolha: apenas uma resposta"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__matrix_subtype__multiple
msgid "Multiple choices per row"
msgstr "Várias opções por linha"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__my_activity_date_deadline
#: model:ir.model.fields,field_description:survey.field_survey_user_input__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Prazo da minha atividade"

#. module: survey
#: model:gamification.badge,name:survey.vendor_certification_badge
msgid "MyCompany Vendor"
msgstr "Fornecedor MyCompany"

#. module: survey
#: model:survey.survey,title:survey.vendor_certification
msgid "MyCompany Vendor Certification"
msgstr "Certificação de fornecedor MyCompany"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Neutral"
msgstr "Neutro"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Never (less than once a month)"
msgstr "Nunca (menos de uma vez por mês)"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__new
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "New"
msgstr "Novo"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug3
msgid "New York"
msgstr "Nova York"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_invite__existing_mode__new
msgid "New invite"
msgstr "Novo convite"

#. module: survey
#: model:mail.message.subtype,description:survey.mt_survey_survey_user_input_completed
msgid "New participation completed."
msgstr "Nova participação concluída."

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Next"
msgstr "Próximo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_calendar_event_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Evento no calendário para a próxima atividade"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_date_deadline
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Prazo da próxima atividade"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_summary
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_summary
msgid "Next Activity Summary"
msgstr "Resumo da próxima atividade"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_type_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo da próxima atividade"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Next Skipped"
msgstr "Próximo ignorado"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__nickname
msgid "Nickname"
msgstr "Apelido"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q1_sug2
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q2_sug2
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug2
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_1_choice_1
msgid "No"
msgstr "Não"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_question_form
msgid "No Questions yet!"
msgstr "Nenhuma pergunta ainda."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "No Survey Found"
msgstr "Nenhuma pergunta encontrada"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_user_input
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "No answers yet!"
msgstr "Nenhuma resposta ainda."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "No attempts left."
msgstr "Não há mais tentativas."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "No question yet, come back later."
msgstr "Nenhuma resposta ainda. Volte mais tarde."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__no_scoring
msgid "No scoring"
msgstr "Sem pontuação"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.survey_question_answer_action
msgid "No survey labels found"
msgstr "Não foram encontrados rótulos de pesquisa"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.survey_user_input_line_action
msgid "No user input lines found"
msgstr "Nenhuma linha opinião do usuário encontrada"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q3_sug2
msgid "No, it's too small for the human eye."
msgstr "Não, é pequeno demais para ser visto a olho nu."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug2
msgid "Norway Spruce"
msgstr "Espruce-da-Noruega"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug2
msgid "Not Good, Not Bad"
msgstr "Mais ou menos"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Not likely at all"
msgstr "Pouco provável"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Now that you are done, submit your form."
msgstr "Agora que está pronto, envie seu formulário."

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Now, use this shortcut to go back to the survey."
msgstr "Agora, use este atalho para voltar à pesquisa."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__progression_mode__number
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__numerical_box
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__scale
msgid "Number"
msgstr "Número"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_needaction_counter
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de ações"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__attempts_limit
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_limit
msgid "Number of attempts"
msgstr "Número de tentativas"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_5
msgid "Number of drawers"
msgstr "Número de gavetas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_error_counter
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_has_error_counter
msgid "Number of errors"
msgstr "Número de erros"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_needaction_counter
#: model:ir.model.fields,help:survey.field_survey_user_input__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensagens que requerem uma ação"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_error_counter
#: model:ir.model.fields,help:survey.field_survey_user_input__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensagens com erro de entrega"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__numerical_box
msgid "Numerical Value"
msgstr "Valor numérico"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_numerical_box
msgid "Numerical answer"
msgstr "Resposta numérica"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Object-Directed Open Organization"
msgstr "Organização aberta orientada a objetos"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
msgid "Occurrence"
msgstr "Ocorrência"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "Odoo"
msgstr "Odoo"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Odoo Certification"
msgstr "Certficação Odoo"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_5
msgid "Office Chair Black"
msgstr "Cadeira de escritório preta"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Often (1-3 times per week)"
msgstr "Frequentemente (1 a 3 vezes por semana)"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"On Survey questions, one can define \"placeholders\". But what are they for?"
msgstr ""
"Em perguntas de pesquisas, é possível definir marcadores de posição. Mas "
"para que isso serve?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug1
msgid "Once a day"
msgstr "Uma vez por dia"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_sug1
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug3
msgid "Once a month"
msgstr "Uma vez por mês"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_sug2
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug2
msgid "Once a week"
msgstr "Uma vez por semana"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug4
msgid "Once a year"
msgstr "Uma vez por ano"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__matrix_subtype__simple
msgid "One choice per row"
msgstr "Uma opção por linha"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "One needs to answer at least half the questions correctly"
msgstr "É necessário responder corretamente a pelo menos metade das perguntas"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "One needs to get 50% of the total score"
msgstr "É necessário alcançar 50% da pontuação total"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__page_per_question
msgid "One page per question"
msgstr "Uma página por pergunta"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__page_per_section
msgid "One page per section"
msgstr "Uma página por seção"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__one_page
msgid "One page with all the questions"
msgstr "Uma página com todas as perguntas"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Only a single question left!"
msgstr "Falta apenas uma pergunta!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
#: model_terms:ir.ui.view,arch_db:survey.question_result_text
msgid "Only show survey results having selected this answer"
msgstr "Mostrar somente resultados de pesquisa com a seleção desta resposta"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Only survey users can manage sessions."
msgstr "Somente os usuários da pesquisa podem gerenciar sessões."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Oops! No survey matches this code."
msgstr "Ops! Nenhuma pesquisa corresponde a este código."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_access_error
msgid ""
"Oopsie! We could not let you open this survey. Make sure you are using the correct link and are allowed to\n"
"                        participate or get in touch with its organizer."
msgstr ""
"Ops! Não foi possível deixar você abrir esta pesquisa. Certifique-se de que está usando o link correto e de que tem permissão para\n"
"                        participar ou entre em contato com o organizador."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Open Session Manager"
msgstr "Abrir gerenciador de sessões"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/question_page/description_page_field.xml:0
msgid "Open section"
msgstr "Abrir sessão"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Optional previous answers required"
msgstr "Necessário respostas opcionais anteriores"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Idioma opcional de tradução (código ISO) para selecionar ao enviar um "
"e-mail. Se não for definido, a versão em inglês será usada. Isso geralmente "
"deve ser uma expressão marcadora de posição que fornece o idioma apropriado,"
" por exemplo, {{ object.partner_id.lang }}."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Options"
msgstr "Opções"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Organizational Development for Operation Officers"
msgstr "Desenvolvimento organizacional para Oficiais de Operações"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid "Other (see comments)"
msgstr "Outros (ver comentários)"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2
msgid "Our Company in a few questions ..."
msgstr "Nossa empresa em algumas perguntas…"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__mail_server_id
msgid "Outgoing mail server"
msgstr "Servidor de envio de e-mail"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Overall Performance"
msgstr "Desempenho geral"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug5
msgid "Overpriced"
msgstr "Caro"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__page_id
msgid "Page"
msgstr "Página"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__page_ids
msgid "Pages"
msgstr "Páginas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__questions_layout
msgid "Pagination"
msgstr "Paginação"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug4
msgid "Papyrus"
msgstr "Papiro"

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Partially"
msgstr "Em parte"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Participant"
msgstr "Participante"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Participants"
msgstr "Participantes"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "Participate to %(survey_name)s"
msgstr "Participar de %(survey_name)s"

#. module: survey
#: model:mail.template,subject:survey.mail_template_user_input_invite
msgid "Participate to {{ object.survey_id.display_name }} survey"
msgstr "Participar da pesquisa {{ object.survey_id.display_name }}"

#. module: survey
#: model:mail.message.subtype,name:survey.mt_survey_survey_user_input_completed
#: model:mail.message.subtype,name:survey.mt_survey_user_input_completed
msgid "Participation completed"
msgstr "Participação concluída"

#. module: survey
#: model:mail.message.subtype,description:survey.mt_survey_user_input_completed
msgid "Participation completed."
msgstr "Participação concluída."

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_user_input
#: model:ir.ui.menu,name:survey.menu_survey_type_form1
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Participations"
msgstr "Participações"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Partner"
msgstr "Usuário"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Passed"
msgstr "Aprovado"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "Passed and Failed"
msgstr "Aprovações e Reprovações"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "Passed only"
msgstr "Somente aprovações"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Pay attention to the host screen until the next question."
msgstr "Preste atenção na tela do apresentador até a próxima pergunta."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__progression_mode__percent
msgid "Percentage left"
msgstr "Porcentagem restante"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Performance by Section"
msgstr "Desempenho por seção"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug3
msgid "Perhaps"
msgstr "Talvez"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug2
msgid "Peter W. Higgs"
msgstr "Peter W. Higgs"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Pick a Badge..."
msgstr "Escolha uma medalha…"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Pick a Style..."
msgstr "Escolha um estilo…"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Pick a Template..."
msgstr "Escolha um modelo…"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p1_q1
msgid "Pick a subject"
msgstr "Escolha um assunto"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__triggering_answer_ids
msgid ""
"Picking any of these answers will trigger this question.\n"
"Leave the field empty if the question should always be displayed."
msgstr ""
"A seleção de qualquer uma dessas respostas acionará esta pergunta.\n"
"Deixe o campo em branco se a pergunta sempre deve ser exibida."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug1
msgid "Pinaceae"
msgstr "Pinaceae"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__question_placeholder
msgid "Placeholder"
msgstr "Marcador de posição"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"Please complete this very short survey to let us know how satisfied your are"
" with our products."
msgstr ""
"Responda a esta breve pesquisa para nos informar sobre a sua satisfação como"
" nossos produtos."

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "Please enter at least one valid recipient."
msgstr "Insira pelo menos um destinatário válido."

#. module: survey
#: model_terms:survey.survey,description:survey.survey_demo_food_preferences
msgid "Please give us your preferences for this event's dinner!"
msgstr "Indique as suas preferências para o jantar desse evento."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid ""
"Please make sure you have at least one question in your survey. You also "
"need at least one section if you chose the \"Page per section\" layout.<br/>"
msgstr ""
"Certifique-se de ter pelo menos uma pergunta no questionário. Se tiver "
"escolhido o layout \"Página por seção\", você também precisa de pelo menos "
"uma seção.<br/>"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3
msgid "Policies"
msgstr "Políticas"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug1
msgid "Pomelos"
msgstr "Pomelos"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug8
msgid "Poor quality"
msgstr "Baixa qualidade"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Practice in front of a mirror"
msgstr "Treinar na frente do espelho"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__predefined_question_ids
msgid "Predefined Questions"
msgstr "Perguntas predefinidas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Preview"
msgstr "Visualizar"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2
msgid "Prices"
msgstr "Preços"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_header
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Print"
msgstr "Imprimir"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_print
msgid "Print Results"
msgstr "Imprimir resultados"

#. module: survey
#: model:ir.actions.server,name:survey.action_survey_print
msgid "Print Survey"
msgstr "Imprimir pesquisa"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1
msgid "Products"
msgstr "Produtos"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Progress bar"
msgstr "Barra de progresso"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__question_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__question_id
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Question"
msgstr "Pergunta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Question & Pages"
msgstr "Perguntas e Páginas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__matrix_question_id
msgid "Question (as matrix row)"
msgstr "Pergunta (como linha da matriz)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_form
msgid "Question Answer Form"
msgstr "Formulário de resposta às perguntas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_answer_count
msgid "Question Answers Count"
msgstr "Total de respostas às perguntas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__questions_selection
#: model:ir.model.fields,field_description:survey.field_survey_survey__questions_selection
msgid "Question Selection"
msgstr "Seleção de pergunta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__question_time_limit_reached
msgid "Question Time Limit Reached"
msgstr "Limite de tempo das perguntas atingido"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__question_type
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__question_type
msgid "Question Type"
msgstr "Tipo de pergunta"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid "Question type should be empty for these pages: %s"
msgstr "O tipo de pergunta deve estar em branco para estas páginas: %s"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_question_form
#: model:ir.model.fields,field_description:survey.field_survey_question__question_ids
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_ids
#: model:ir.ui.menu,name:survey.menu_survey_question_form1
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Questions"
msgstr "Perguntas"

#. module: survey
#: model:ir.ui.menu,name:survey.survey_menu_questions
msgid "Questions & Answers"
msgstr "Perguntas e Respostas"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__triggering_question_ids
msgid ""
"Questions containing the triggering answer(s) to display the current "
"question."
msgstr ""
"Perguntas que contém respostas de acionamento para exibir a pergunta atual."

#. module: survey
#: model:survey.survey,title:survey.survey_demo_quiz
msgid "Quiz about our Company"
msgstr "Questionário sobre nossa empresa"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_success
msgid "Quizz Passed"
msgstr "Aprovado no questionário"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Quizz passed"
msgstr "Aprovado no questionário"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/radio_selection_with_filter/radio_selection_field_with_filter.js:0
msgid "Radio for Selection With Filter"
msgstr "Botão de seleção com filtro"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_selection__random
msgid "Randomized per Section"
msgstr "Em aleatório por seção"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Rarely (1-3 times per month)"
msgstr "Raramente (1 a 3 vezes por mês)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__rating_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__rating_ids
msgid "Ratings"
msgstr "Avaliações"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__session_state__ready
msgid "Ready"
msgstr "Pronto"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Ready to change the way you <b>gather data</b>?"
msgstr "Pronto para mudar o modo como você <b>coleta dados</b>?"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Ready to test? Pick a sample or create one from scratch..."
msgstr "Pronto para começar? Escolha uma amostra ou crie-a do zero…"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__partner_ids
msgid "Recipients"
msgstr "Destinatários"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Red Pen"
msgstr "Caneta vermelha"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_count
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Registered"
msgstr "Registrado"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__render_model
msgid "Rendering Model"
msgstr "Modelo de renderização"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Reopen"
msgstr "Reabrir"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_users_login_required
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_login_required
msgid "Require Login"
msgstr "Requer login"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_success_min
msgid "Required Score (%)"
msgstr "Pontuação necessária (%)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_text
msgid "Resend Comment"
msgstr "Reenviar comentário"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Resend Invitation"
msgstr "Reenviar convite"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_invite__existing_mode__resend
msgid "Resend invite"
msgstr "Reenviar convite"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__user_id
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Responsible"
msgstr "Responsável"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_user_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_user_id
msgid "Responsible User"
msgstr "Usuário responsável"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__restrict_user_ids
msgid "Restricted to"
msgstr "Restrito a"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "Retry"
msgstr "Tentar novamente"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Review your answers"
msgstr "Revisar suas respostas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_speed_rating
msgid "Reward quick answers"
msgstr "Recompensar respostas rápidas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "Rewards for challenges"
msgstr "Recompensas para os desafios"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__matrix_row_id
msgid "Row answer"
msgstr "Linha de resposta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row1"
msgstr "Linha 1"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row2"
msgstr "Linha 2"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row3"
msgstr "Linha 3"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Rows"
msgstr "Linhas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_sms_error
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erro na entrega do SMS"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug4
msgid "Salicaceae"
msgstr "Salicaceae"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__save_as_email
msgid "Save as user email"
msgstr "Salvar como e-mail do usuário"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__save_as_nickname
msgid "Save as user nickname"
msgstr "Salvar como apelido do usuário"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__scale
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Scale"
msgstr "Balança"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_max_label
msgid "Scale Maximum Label"
msgstr "Rótulo de escala máxima"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_max
msgid "Scale Maximum Value"
msgstr "Valor máximo da escala"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Scale Maximum Value (0 to 10)"
msgstr "Valor máximo da escala (0 a 10)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_mid_label
msgid "Scale Middle Label"
msgstr "Rótulo da escala média"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_min_label
msgid "Scale Minimum Label"
msgstr "Rótulo de escala mínima"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_min
msgid "Scale Minimum Value"
msgstr "Valor mínimo da escala"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Scale Minimum Value (0 to 10)"
msgstr "Valor mínimo da escala (0 a 10)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_scale
msgid "Scale value"
msgstr "Valor da escala"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug3
msgid "Sciences"
msgstr "Ciências"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_score
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__answer_score
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_score
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Score"
msgstr "Pontos"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_percentage
msgid "Score (%)"
msgstr "Pontuação (%)"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_score
msgid "Score value for a correct answer to this question."
msgstr "Valor da pontuação de respostas corretas nessa pergunta."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_scored_question
msgid "Scored"
msgstr "Pontuou"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_type
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_type
msgid "Scoring"
msgstr "Pontuação"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scoring_type
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__scoring_type
msgid "Scoring Type"
msgstr "Tipo de pontuação"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_with_answers_after_page
msgid "Scoring with answers after each page"
msgstr "Pontuação com respostas após cada página"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_with_answers
msgid "Scoring with answers at the end"
msgstr "Pontuação com respostas no final"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_without_answers
msgid "Scoring without answers"
msgstr "Pontuação sem respostas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
msgid "Search Label"
msgstr "Buscar rótulo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Search Question"
msgstr "Buscar pergunta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Search Survey User Inputs"
msgstr "Buscar opiniões do usuário da pesquisa"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
msgid "Search User input lines"
msgstr "Buscar linhas de opinião do usuário"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__page_id
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Section"
msgstr "Seção"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_and_page_ids
msgid "Sections and Questions"
msgstr "Seções e Perguntas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "See results"
msgstr "Veja os resultados"

#. module: survey
#: model_terms:survey.survey,description_done:survey.survey_demo_food_preferences
msgid "See you soon!"
msgstr "Até logo!"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_3
msgid "Select all the available customizations for our Customizable Desk"
msgstr ""
"Selecione todas as personalizações disponíveis para a nossa Mesa de "
"escritório personalizável"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_2
msgid "Select all the existing products"
msgstr "Selecione todos os produtos existentes"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_2
msgid "Select all the products that sell for $100 or more"
msgstr "Selecione todos os produtos vendidos por R$ 100 ou mais"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q3
msgid "Select trees that made more than 20K sales this year"
msgstr "Selecione árvores que geraram mais de 20 mil vendas neste ano"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Send"
msgstr "Enviar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__send_email
msgid "Send Email"
msgstr "Enviar e-mail"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Send by Email"
msgstr "Enviar por e-mail"

#. module: survey
#: model:mail.template,description:survey.mail_template_certification
msgid "Sent to participant if they succeeded the certification"
msgstr "Enviado ao participante ao ser aprovado na certificação"

#. module: survey
#: model:mail.template,description:survey.mail_template_user_input_invite
msgid "Sent to participant when you share a survey"
msgstr "Enviado ao participante ao compartilhar a pesquisa"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__sequence
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__question_sequence
msgid "Sequence"
msgstr "Sequência"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_code
msgid "Session Code"
msgstr "Código da sessão"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_link
msgid "Session Link"
msgstr "Link da sessão"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_state
msgid "Session State"
msgstr "Status da sessão"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_session_code_unique
msgid "Session code should be unique"
msgstr "O código da sessão deve ser exclusivo"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug1
msgid "Shanghai"
msgstr "Xangai"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Share"
msgstr "Compartilhar"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Share a Survey"
msgstr "Compartilhar uma pesquisa"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_table_pagination
msgid "Show All"
msgstr "Mostrar tudo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comments_allowed
msgid "Show Comments Field"
msgstr "Mostrar campo de comentários"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Show Correct Answer(s)"
msgstr "Mostrar resposta(s) correta(s)"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Show Final Leaderboard"
msgstr "Mostrar classificação final"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Show Leaderboard"
msgstr "Mostrar tabela de classificação"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Show Results"
msgstr "Mostrar resultados"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_show_leaderboard
msgid "Show Session Leaderboard"
msgstr "Mostrar tabela de classificação da sessão"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Mostrar todos os registros cuja próxima data de ação seja anterior a hoje"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Show them slides with a ton of text they need to read fast"
msgstr ""
"Mostrar slides com um monte de texto que eles têm que ler muito rápido"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__char_box
msgid "Single Line Text Box"
msgstr "Caixa de texto de linha única"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__skipped
#: model_terms:ir.ui.view,arch_db:survey.survey_page_print
msgid "Skipped"
msgstr "Ignorados"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q3_sug2
msgid "Soft"
msgstr "Suave"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "Some emails you just entered are incorrect: %s"
msgstr "Alguns e-mails que você acabou de inserir estão incorretos: %s"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p1
msgid ""
"Some general information about you. It will be used internally for "
"statistics only."
msgstr ""
"Algumas informações gerais sobre você. Elas serão usadas internamente apenas"
" para fins estatísticos."

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p2
msgid "Some questions about our company. Do you really know us?"
msgstr "Algumas perguntas sobre nossa empresa. Você realmente nos conhece?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "Someone just participated in \"%(survey_title)s\"."
msgstr "Alguém acaba de participar de \"%(survey_title)s\"."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_header
msgid "Sorry, no one answered this survey yet."
msgstr "Desculpe, mas ninguém respondeu a esta pesquisa ainda."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Sorry, you have not been fast enough."
msgstr "Desculpe, você não foi rápido o suficiente."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug4
msgid "South America"
msgstr "América do Sul"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug4
msgid "South Korea"
msgstr "Coreia do Sul"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug3
msgid "Space stations"
msgstr "Estações espaciais"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Speak softly so that they need to focus to hear you"
msgstr ""
"Falar baixinho para que eles tenham que se concentrar muito para ouvir"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Speak too fast"
msgstr "Falar bem rápido"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug1
msgid "Spring"
msgstr "Primavera"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#: model:survey.question,title:survey.survey_demo_burger_quiz_p1
msgid "Start"
msgstr "Iniciar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Start Certification"
msgstr "Iniciar certificação"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Start Live Session"
msgstr "Iniciar sessão ao vivo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Start Survey"
msgstr "Iniciar pesquisa"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__start_datetime
msgid "Start date and time"
msgstr "Data e hora de início"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__state
msgid "Status"
msgstr "Status"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_state
#: model:ir.model.fields,help:survey.field_survey_user_input__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status baseado em atividades\n"
"Atrasado: data de vencimento já passou\n"
"Hoje: data da atividade é hoje\n"
"Planejado: atividades futuras."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q4_sug1
msgid "Steak with french fries"
msgstr "Bife com batata frita"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row2
msgid "Strawberries"
msgstr "Morangos"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__subject
msgid "Subject"
msgstr "Assunto"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Subject..."
msgstr "Assunto…"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Submit"
msgstr "Enviar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__success_count
msgid "Success"
msgstr "Sucesso"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__success_ratio
msgid "Success Ratio (%)"
msgstr "Taxa de aprovação (%)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Success rate"
msgstr "Taxa de aprovação"

#. module: survey
#: model:ir.actions.act_window,name:survey.survey_question_answer_action
#: model:ir.ui.menu,name:survey.menu_survey_label_form1
msgid "Suggested Values"
msgstr "Valores sugeridos"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__suggested_answer_id
msgid "Suggested answer"
msgstr "Resposta sugerida"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_answer_value_not_empty
msgid ""
"Suggested answer value must not be empty (a text and/or an image must be "
"provided)."
msgstr ""
"O valor da resposta sugerida não deve estar vazio (um texto e/ou uma imagem "
"devem ser fornecidos)."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value
msgid "Suggested value"
msgstr "Valor sugerido"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__suggestion
msgid "Suggestion"
msgstr "Sugestão"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug2
msgid "Summer"
msgstr "Verão"

#. module: survey
#: model:ir.model,name:survey.model_survey_survey
#: model:ir.model.fields,field_description:survey.field_gamification_badge__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_question__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__survey_id
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__survey_type__survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_activity
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_tree
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Survey"
msgstr "Pesquisa"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_access_error
msgid "Survey Access Error"
msgstr "Erro no acesso à pesquisa"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_view_tree
msgid "Survey Answer Line"
msgstr "Linha de resposta da pesquisa"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_first_submitted
msgid "Survey First Submitted"
msgstr "Primeira pesquisa enviada"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_gamification_badge__survey_ids
msgid "Survey Ids"
msgstr "IDs de pesquisa"

#. module: survey
#: model:ir.model,name:survey.model_survey_invite
msgid "Survey Invitation Wizard"
msgstr "Assistente de convite para pesquisas"

#. module: survey
#: model:ir.model,name:survey.model_survey_question_answer
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_tree
msgid "Survey Label"
msgstr "Rótulo da pesquisa"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Survey Link"
msgstr "Link da pesquisa"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "Survey Participant"
msgstr "Participante da pesquisa"

#. module: survey
#: model:ir.model,name:survey.model_survey_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_tree
msgid "Survey Question"
msgstr "Pergunta da pesquisa"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Survey Time Limit"
msgstr "Limite de tempo da pesquisa"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_time_limit_reached
msgid "Survey Time Limit Reached"
msgstr "Limite de tempo da pesquisa atingido"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__title
msgid "Survey Title"
msgstr "Título da pesquisa"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__survey_type
msgid "Survey Type"
msgstr "Tipo da pesquisa"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_start_url
msgid "Survey URL"
msgstr "URL da pesquisa"

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input
msgid "Survey User Input"
msgstr "Opinião do usuário da pesquisa"

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input_line
msgid "Survey User Input Line"
msgstr "Linha de opinião do usuário da pesquisa"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_tree
msgid "Survey User inputs"
msgstr "Opiniões do usuário da pesquisa"

#. module: survey
#: model:mail.template,name:survey.mail_template_certification
msgid "Survey: Certification Success"
msgstr "Pesquisa: Aprovação na certificação"

#. module: survey
#: model:mail.template,name:survey.mail_template_user_input_invite
msgid "Survey: Invite"
msgstr "Pesquisa: Convite"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_form
#: model:ir.ui.menu,name:survey.menu_survey_form
#: model:ir.ui.menu,name:survey.menu_surveys
msgid "Surveys"
msgstr "Pesquisas"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug3
msgid "Takaaki Kajita"
msgstr "Takaaki Kajita"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "Take Again"
msgstr "Tentar novamente"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Test"
msgstr "Teste"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__test_entry
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Test Entry"
msgstr "Entrada de teste"

#. module: survey
#: model_terms:survey.question,description:survey.vendor_certification_page_3
msgid "Test your knowledge of our policies."
msgstr "Teste seu conhecimento sobre nossas políticas."

#. module: survey
#: model_terms:survey.question,description:survey.vendor_certification_page_2
msgid "Test your knowledge of our prices."
msgstr "Teste seu conhecimento sobre nossos preços."

#. module: survey
#: model_terms:survey.question,description:survey.vendor_certification_page_1
msgid "Test your knowledge of your products!"
msgstr "Teste seu conhecimento sobre seus produtos."

#. module: survey
#: model_terms:survey.survey,description:survey.vendor_certification
msgid "Test your vendor skills!"
msgstr "Teste suas habilidades de vendedor."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Tests Only"
msgstr "Somente testes"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__char_box
msgid "Text"
msgstr "Texto"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_char_box
msgid "Text answer"
msgstr "Resposta de texto"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Thank you for your participation, hope you had a blast!"
msgstr "Agradecemos por sua participação. Esperamos que tenha se divertido!"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Thank you very much for your feedback. We highly value your opinion!"
msgstr ""
"Agradecemos por seu feedback. Sua opinião é muito importante para nós!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Thank you!"
msgstr "Agradecemos!"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Thank you. We will contact you soon."
msgstr "Agradecemos! Entraremos em contato com você em breve."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"The access of the following surveys is restricted. Make sure their responsible still has access to it: \n"
"%(survey_names)s\n"
msgstr ""
"O acesso às pesquisas a seguir é restrito. Certifique-se de que o responsável ainda tenha acesso a elas: \n"
"%(survey_names)s\n"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "The answer must be in the right type"
msgstr "A resposta deve ser do tipo adequado"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: model_terms:ir.ui.view,arch_db:survey.question_container
msgid "The answer you entered is not valid."
msgstr "A resposta que você inseriu não é válida."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_attempts_limit_check
msgid ""
"The attempts limit needs to be a positive number if the survey has a limited"
" number of attempts."
msgstr ""
"O limite de tentativas deve ser um número positivo se a pesquisa tiver um "
"número limitado de tentativas."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_badge_uniq
msgid "The badge for each survey should be unique!"
msgstr "A medalha de cada pesquisa deve ser exclusiva!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row4
msgid "The checkout process is clear and secure"
msgstr "O processo de pagamento é claro e seguro"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_print
msgid "The correct answer was:"
msgstr "A resposta certa era:"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_question_id
msgid "The current question of the survey session."
msgstr "A pergunta atual da sessão de pesquisa."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__description
msgid ""
"The description will be displayed on the home page of the survey. You can "
"use this to give the purpose and guidelines to your candidates before they "
"start it."
msgstr ""
"A descrição será exibida na página inicial da pesquisa. Você pode usá-la "
"para explicar o objetivo e dar instruções aos candidatos antes de iniciarem "
"a pesquisa."

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "The following customers have already received an invite"
msgstr "Os seguintes clientes já receberam um convite"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "The following emails have already received an invite"
msgstr "Os seguintes e-mails já receberam um convite"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid ""
"The following recipients have no user account: %s. You should create user "
"accounts for them or allow external signup in configuration."
msgstr ""
"Os seguintes destinatários não têm conta de usuário: %s. Você deve criar "
"contas de usuário para eles ou permitir a inscrição externa na configuração."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row1
msgid "The new layout and design is fresh and up-to-date"
msgstr "O layout e o design são novos e atualizados"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "The page you were looking for could not be authorized."
msgstr "Não foi possível autorizar a página que você estava procurando."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_scoring_success_min_check
msgid "The percentage of success has to be defined between 0 and 100."
msgstr "A porcentagem de sucesso deve ser definida entre 0 e 100."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_time_limited
msgid "The question is limited in time"
msgstr "A pergunta tem limite de tempo"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scale
msgid ""
"The scale must be a growing non-empty range between 0 and 10 (inclusive)"
msgstr ""
"A escala deve ser um intervalo crescente e não vazio entre 0 e 10 "
"(inclusive)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "The session did not start yet."
msgstr "A sessão ainda não iniciou."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "The session will begin automatically when the host starts."
msgstr "A sessão começará automaticamente quando o apresentador iniciar."

#. module: survey
#. odoo-python
#: code:addons/survey/controllers/main.py:0
msgid "The survey has already started."
msgstr "A pesquisa já começou."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__is_time_limited
msgid "The survey is limited in time"
msgstr "A pesquisa tem limite de tempo"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_question_start_time
msgid ""
"The time at which the current question has started, used to handle the timer"
" for attendees."
msgstr ""
"O horário em que a pergunta atual começou. Usado para controlar o cronômetro"
" dos participantes."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_time_limit_check
msgid ""
"The time limit needs to be a positive number if the survey is time limited."
msgstr ""
"O limite de tempo deve ser um número positivo se a pesquisa tiver limite de "
"tempo."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row3
msgid "The tool to compare the products is useful to make a choice"
msgstr ""
"A ferramenta para comparação é útil para ajudar a escolher os produtos"

#. module: survey
#. odoo-python
#: code:addons/survey/controllers/main.py:0
msgid "The user has not succeeded the certification"
msgstr "O usuário não obteve êxito na certificação"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "There was an error during the validation of the survey."
msgstr "Ocorreu um erro ao validar esta pesquisa."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "They are a default answer, used if the participant skips the question"
msgstr "Resposta padrão usada se o participante ignorar a pergunta."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"They are technical parameters that guarantees the responsiveness of the page"
msgstr "Parâmetros técnicos que asseguram a reatividade da página."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "This answer cannot be overwritten."
msgstr "Esta resposta não pode ser substituída"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/tests/test_survey.py:0
msgid "This answer must be an email address"
msgstr "Essa resposta deve ser um endereço de e-mail"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
msgid "This answer must be an email address."
msgstr "Essa resposta deve ser um endereço de e-mail."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_code
msgid ""
"This code will be used by your attendees to reach your session. Feel free to"
" customize it however you like!"
msgstr ""
"Esse código será usado pelos participantes para acessar sua sessão. Fique à "
"vontade para personalizá-lo!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
msgid "This is a Test Survey Entry."
msgstr "Esta é uma entrada de pesquisa de teste."

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/static/src/js/survey_form.js:0
#: code:addons/survey/tests/test_survey.py:0
msgid "This is not a date"
msgstr "Isso não é uma data"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/tests/test_survey.py:0
msgid "This is not a number"
msgstr "Este não é um número"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__description_done
msgid "This message will be displayed when survey is completed"
msgstr "Esta mensagem será exibida quando a pesquisa estiver concluída"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: model_terms:ir.ui.view,arch_db:survey.question_container
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "This question requires an answer."
msgstr "Esta pergunta requer uma resposta."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_print
msgid "This question was skipped"
msgstr "Esta pergunta foi ignorada"

#. module: survey
#: model_terms:survey.question,description:survey.survey_feedback_p1
msgid ""
"This section is about general information about you. Answering them helps "
"qualifying your answers."
msgstr ""
"Esta seção aborda informações gerais sobre você. Responder a elas ajuda a "
"classificar suas respostas."

#. module: survey
#: model_terms:survey.question,description:survey.survey_feedback_p2
msgid "This section is about our eCommerce experience itself."
msgstr "Esta seção aborda a nossa experiência de eCommerce."

#. module: survey
#: model_terms:survey.survey,description:survey.survey_demo_quiz
msgid ""
"This small quiz will test your knowledge about our Company. Be prepared!"
msgstr ""
"Este breve questionário testará seu conhecimento sobre nossa empresa. "
"Prepare-se!"

#. module: survey
#: model_terms:survey.survey,description:survey.survey_feedback
msgid ""
"This survey allows you to give a feedback about your experience with our products.\n"
"    Filling it helps us improving your experience."
msgstr ""
"Esta pesquisa possibilita que você dê feedback sobre sua experiência com nossos produtos.\n"
"    Respondê-la nos ajuda a melhorar sua experiência."

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid ""
"This survey does not allow external people to participate. You should create"
" user accounts or update survey access mode accordingly."
msgstr ""
"Esta pesquisa não permite a participação de pessoas externas. Você deve "
"criar contas de usuário ou atualizar o modo de acesso à pesquisa "
"adequadamente."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_closed_expired
msgid "This survey is now closed. Thank you for your interest!"
msgstr "Esta pesquisa já foi encerrada. Agradecemos pelo seu interesse."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "This survey is open only to registered people. Please"
msgstr "Esta pesquisa é aberta apenas para pessoas cadastradas."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Time & Scoring"
msgstr "Tempo e Pontuação"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Time Limit"
msgstr "Tempo limite"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Time Limit (seconds)"
msgstr "Limite de tempo (segundos)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__time_limit
msgid "Time limit (minutes)"
msgstr "Limite de tempo (minutos)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__time_limit
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_speed_rating_time_limit
msgid "Time limit (seconds)"
msgstr "Limite de tempo (segundos)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Time limit for this certification:"
msgstr "Limite de tempo dessa certificação:"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Time limit for this survey:"
msgstr "Limite de tempo dessa pesquisa:"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Time limits are only available for Live Sessions."
msgstr "Os limites de tempo estão disponíveis apenas para sessões ao vivo."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__title
msgid "Title"
msgstr "Título"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "To join:"
msgstr "Para participar:"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid ""
"To take this survey, please close all other tabs on <strong class=\"text-"
"danger\"/>."
msgstr ""
"Para responder a esta pesquisa, feche todas as outras abas do <strong "
"class=\"text-danger\"/>."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Today Activities"
msgstr "Atividades de hoje"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug2
msgid "Tokyo"
msgstr "Tóquio"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
msgid "Top User Responses"
msgstr "Melhores respostas de usuários"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_total
msgid "Total Score"
msgstr "Pontuação geral"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col4
msgid "Totally agree"
msgstr "Concordo totalmente"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col1
msgid "Totally disagree"
msgstr "Discordo totalmente"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4
msgid "Trees"
msgstr "Árvores"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__triggering_answer_ids
msgid "Triggering Answers"
msgstr "Respostas de acionamento"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__triggering_question_ids
msgid "Triggering Questions"
msgstr "Perguntas de acionamento"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/survey_question_trigger/survey_question_trigger.js:0
msgid ""
"Triggers based on the following questions will not work because they are positioned after this question:\n"
"\"%s\"."
msgstr ""
"Acionamentos baseados nas perguntas a seguir não funcionarão pois estão posicionados após esta pergunta:\n"
"\"%s\"."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Type"
msgstr "Tipo"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_exception_decoration
#: model:ir.model.fields,help:survey.field_survey_user_input__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo da atividade de exceção registrada."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__suggested_answer_ids
msgid "Types of answers"
msgstr "Tipos de respostas"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug2
msgid "Ulmaceae"
msgstr "Ulmaceae"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "Unable to post message, please configure the sender's email address."
msgstr ""
"Não foi possível publicar mensagem. Configure o endereço de e-mail do "
"remetente."

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Unanswered"
msgstr "Sem resposta"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "Uncategorized"
msgstr "Sem categoria"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_2
msgid "Underpriced"
msgstr "Barato"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Unfortunately, you have failed the test."
msgstr "Infelizmente, você foi reprovado no teste."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug3
msgid "Unique"
msgstr "Único"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Unlikely"
msgstr "improvável"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Upcoming Activities"
msgstr "Próximas atividades"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Use a fun visual support, like a live presentation"
msgstr "Use um suporte visual divertido, como uma apresentação ao vivo"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Use humor and make jokes"
msgstr "Traga humor e faça piadas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Use template"
msgstr "Usar modelo"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Use the breadcrumbs to quickly go back to the dashboard."
msgstr "Usar a navegação hierárquica para voltar rapidamente ao painel."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__description
msgid ""
"Use this field to add additional explanations about your question or to "
"illustrate it with pictures or a video"
msgstr ""
"Use este campo para incluir explicações adicionais sobre sua pergunta ou "
"para ilustrá-la com imagens ou um vídeo"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__random_questions_count
msgid ""
"Used on randomized sections to take X random questions from all the "
"questions of that section."
msgstr ""
"Usado em seções aleatórias para tirar X perguntas aleatórias de todas as "
"perguntas daquela seção."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug2
msgid "Useful"
msgstr "Útil"

#. module: survey
#: model:res.groups,name:survey.group_survey_user
msgid "User"
msgstr "Usuário"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "User Choice"
msgstr "Escolha do usuário"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__user_input_id
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
msgid "User Input"
msgstr "Opinião do usuário"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
#: model_terms:ir.ui.view,arch_db:survey.question_result_text
msgid "User Responses"
msgstr "Respostas de usuários"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_form
msgid "User input line details"
msgstr "Detalhes da linha de opinião do usuário"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__user_input_ids
msgid "User responses"
msgstr "Respostas do usuário"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_can_go_back
msgid "Users can go back"
msgstr "Os usuários podem voltar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_users_can_signup
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_can_signup
msgid "Users can signup"
msgstr "Os usuários podem se inscrever"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_required
msgid "Validate entry"
msgstr "Validar entrada"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/question_page/question_page_one2many_field.js:0
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_error_msg
msgid "Validation Error"
msgstr "Erro de validação"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value_label
msgid "Value Label"
msgstr "Rótulo do valor"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug2
msgid "Vegetables"
msgstr "Vegetais"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q3_sug2
msgid "Vegetarian burger"
msgstr "Hambúrguer vegetariano"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q3_sug1
msgid "Vegetarian pizza"
msgstr "Pizza vegetariana"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_1
msgid "Very underpriced"
msgstr "Barato demais"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug3
msgid "Vietnam"
msgstr "Vietnã"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_open
msgid "Waiting for attendees..."
msgstr "Aguardando os participantes…"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"We have registered your answer! Please wait for the host to go to the next "
"question."
msgstr ""
"Registramos a sua resposta! Aguarde até que o apresentador passe para a "
"próxima pergunta."

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p4
msgid ""
"We like to say that the apple doesn't fall far from the tree, so here are "
"trees."
msgstr ""
"Costumamos dizer que a maçã nunca cai longe da árvore, então aqui estão as "
"árvores."

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p5
msgid "We may be interested by your input."
msgstr "Sua opinião pode ser do nosso interesse."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__website_message_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__website_message_ids
msgid "Website Messages"
msgstr "Mensagens do site"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__website_message_ids
#: model:ir.model.fields,help:survey.field_survey_user_input__website_message_ids
msgid "Website communication history"
msgstr "Histórico de comunicação do site"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"Welcome to this Odoo certification. You will receive 2 random questions out "
"of a pool of 3."
msgstr ""
"Boas-vindas à Certificação do Odoo. Você receberá aleatoriamente duas "
"perguntas de um conjunto de três."

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_5
msgid ""
"What day and time do you think most customers are most likely to call "
"customer service (not rated)?"
msgstr ""
"Quais são os dias e os horários que você considera mais propícios para "
"clientes ligarem para o suporte? (Não avaliada)"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_4
msgid ""
"What day to you think is best for us to start having an annual sale (not "
"rated)?"
msgstr ""
"Em que dia você acha melhor começarmos uma promoção anual? (Não avaliada)"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q2
msgid "What do you think about our new eCommerce?"
msgstr "O que você achou do nosso novo eCommerce?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_3
msgid "What do you think about our prices (not rated)?"
msgstr "O que você achados nossos preços? (Não avaliada)"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p5_q1
msgid "What do you think about this survey?"
msgstr "O que você achou desta pesquisa?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "What does \"ODOO\" stand for?"
msgstr "O que \"ODOO\" quer dizer?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "What does one need to get to pass an Odoo Survey?"
msgstr "O que é necessário para ser aprovado em uma pesquisa da Odoo?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "What is a frequent mistake public speakers do?"
msgstr "Que erro é frequentemente cometido por oradores?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "What is the best way to catch the attention of an audience?"
msgstr "Qual é a melhor forma de prender a atenção do público?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q2
msgid "What is the biggest city in the world?"
msgstr "Qual é a maior cidade do mundo?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q1
msgid "What is your email?"
msgstr "Qual é o seu e-mail?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q2
msgid "What is your nickname?"
msgstr "Qual é o seu apelido?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q2
msgid "What is, approximately, the critical mass of plutonium-239?"
msgstr "Qual é, aproximadamente, a massa crítica do plutônio-239?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q1
msgid "When did Genghis Khan die?"
msgstr "Quando Genghis Khan morreu?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q2
msgid "When did precisely Marc Demo crop its first apple tree?"
msgstr "Quando exatamente Marc Demo cortou sua primeira macieira?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q6
msgid "When do you harvest those fruits"
msgstr "Quando esses frutos são colhidos?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q1
msgid "When is Mitchell Admin born?"
msgstr "Quando Mitchell Admin nasceu?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q2
msgid "When is your date of birth?"
msgstr "Qual é a sua data de nascimento?"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Whenever you pick an answer, Odoo saves it for you."
msgstr "Sempre que selecionar uma opção, o Odoo salvará a sua escolha."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q3
msgid "Where are you from?"
msgstr "De onde você é?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q1
msgid "Where do you live?"
msgstr "Onde você mora?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_show_leaderboard
msgid ""
"Whether or not we want to show the attendees leaderboard for this survey."
msgstr ""
"Se você quer ou não mostrar a tabela de classificação dos participantes "
"dessa pesquisa."

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q1
msgid "Which Musician is not in the 27th Club?"
msgstr "Qual dos músicos não faz parte do Clube dos 27?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q1
msgid "Which category does a tomato belong to"
msgstr "A que categoria pertence o tomate?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q3
msgid "Which is the highest volcano in Europe?"
msgstr "Qual é o vulcão mais alto da Europa?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q1
msgid "Which of the following words would you use to describe our products?"
msgstr ""
"Qual das palavras a seguir você usaria para descrever nossos produtos?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q2
msgid "Which of the following would you use to pollinate"
msgstr "Qual das seguintes opções você usaria para polinizar…"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q2
msgid "Which painting/drawing was not made by Pablo Picasso?"
msgstr "Qual das pinturas/desenhos não foi feito por Pablo Picasso?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q3
msgid "Which quote is from Jean-Claude Van Damme"
msgstr "Qual das citações foi feita por Jean-Claude Van Damme?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1
msgid "Who are you?"
msgstr "Quem é você?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q2
msgid "Who is the architect of the Great Pyramid of Giza?"
msgstr "Quem foi o arquiteto da Grande Pirâmide de Gizé?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q1
msgid ""
"Who received a Nobel prize in Physics for the discovery of neutrino "
"oscillations, which shows that neutrinos have mass?"
msgstr ""
"Quem recebeu o prêmio Nobel de Física pela descoberta das oscilações de "
"neutrinos, que mostra que os neutrinos têm massa?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"Why should you consider making your presentation more fun with a small quiz?"
msgstr ""
"Por que considerar tornar a apresentação mais divertida com um breve "
"questionário?"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_3
msgid "Width"
msgstr "Largura"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug4
msgid "Willard S. Boyle"
msgstr "Willard S. Boyle"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug4
msgid "Winter"
msgstr "Inverno"

#. module: survey
#: model:survey.question,title:survey.survey_demo_food_preferences_q2
msgid "Would you prefer a veggie meal if possible?"
msgstr "Você preferiria uma refeição vegetariana, se possível?"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD\n"
"                                        <i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"
msgstr ""
"AAAA-MM-DD\n"
"                                        <i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD hh:mm:ss\n"
"                                        <i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"
msgstr ""
"AAAA-MM-DD hh:mm:ss\n"
"                                        <i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Yellow Pen"
msgstr "Caneta amarela"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q1_sug1
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q2_sug1
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug1
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_1_choice_2
msgid "Yes"
msgstr "Sim"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q3_sug1
msgid "Yes, that's the only thing a human eye can see."
msgstr "Sim, essa é a única coisa que o olho humano pode ver."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_certification_check
msgid ""
"You can only create certifications for surveys that have a scoring "
"mechanism."
msgstr ""
"Você só pode criar certificados para pesquisas que tenham um mecanismo de "
"pontuação."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_user_input
msgid ""
"You can share your links through different means: email, invite shortcut, "
"live presentation, ..."
msgstr ""
"Você pode compartilhar seus links por diferentes meios: e-mail, atalho de "
"convite, apresentação ao vivo…"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid ""
"You cannot delete questions from surveys \"%(survey_names)s\" while live "
"sessions are in progress."
msgstr ""
"Não é possível excluir perguntas de pesquisas \"%(survey_names)s\" enquanto "
"as sessões ao vivo estiverem em andamento."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"You cannot send an invitation for a \"One page per section\" survey if the "
"survey has no sections."
msgstr ""
"Você não pode enviar convites para uma pesquisa \"Uma página por seção\" se "
"ela não tiver seções."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"You cannot send an invitation for a \"One page per section\" survey if the "
"survey only contains empty sections."
msgstr ""
"Você não pode enviar convites para uma pesquisa \"Uma página por seção\" se "
"ela contiver apenas seções em branco."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "You cannot send an invitation for a survey that has no questions."
msgstr ""
"Você não pode enviar convites para uma pesquisa que não tem perguntas."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "You cannot send invitations for closed surveys."
msgstr "Você não pode enviar convites para pesquisas encerradas."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "You received the badge"
msgstr "Você recebeu a medalha"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "You scored"
msgstr "Você marcou"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p5
msgid "Your feeling"
msgstr "A sua sensação"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"Your responses will help us improve our product range to serve you even "
"better."
msgstr ""
"Suas respostas nos ajudarão a aprimorar nossa gama de produtos para melhor "
"atender você."

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "Zoom in"
msgstr "Mais zoom"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "Zoom out"
msgstr "Menos zoom"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "Zoomed Image"
msgstr "Imagem ampliada"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid "[Question Title]"
msgstr "[Título da pergunta]"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "ans"
msgstr "ans"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "answered"
msgstr "respondeu"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "attempts"
msgstr "tentativas"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "close"
msgstr "encerrar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid ""
"e.g.  'Rick Sanchez' <<EMAIL>>, <EMAIL>"
msgstr "ex.: \"Rick Sanchez\" <<EMAIL>>, <EMAIL>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "e.g. \"Thank you very much for your feedback!\""
msgstr "ex.: \"Agradecemos pelo seu feedback!\""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "e.g. \"The following Survey will help us...\""
msgstr "ex.: \"A pesquisa a seguir nos ajudará a…\""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "e.g. \"What is the...\""
msgstr "ex.: \"Qual é…\""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "e.g. 4812"
msgstr "ex.: 4812"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "e.g. Guidelines, instructions, picture, ... to help attendees answer"
msgstr ""
"ex.: Diretrizes, instruções, imagens, etc. que ajudem os participantes a "
"responder"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "e.g. No one can solve challenges like you do"
msgstr "ex.: Ninguém é capaz de resolver desafios como você."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "e.g. Problem Solver"
msgstr "ex.: Solucionador de problemas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "e.g. Satisfaction Survey"
msgstr "ex.: Pesquisa de satisfação"

#. module: survey
#: model:survey.question,question_placeholder:survey.survey_demo_quiz_p1_q1
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "log in"
msgstr "entrar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "minutes"
msgstr "minutos"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "of"
msgstr "de"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "of achievement"
msgstr "de conquista"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
msgid "or press CTRL+Enter"
msgstr "ou pressione CTRL+Enter"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
msgid "or press Enter"
msgstr "ou pressione Enter"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
msgid "or press ⌘+Enter"
msgstr "ou pressione ⌘+Enter"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "pages"
msgstr "páginas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_closed_expired
msgid "survey expired"
msgstr "a pesquisa expirou"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "survey is empty"
msgstr "a pesquisa está em branco"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "this page"
msgstr "esta página"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "to"
msgstr "até"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"⚠️ This question is positioned before some or all of its triggers and could "
"be skipped."
msgstr ""
"⚠️ Esta pergunta está posicionada antes de algum ou de todos seus "
"acionadores e pode ser ignorada."
