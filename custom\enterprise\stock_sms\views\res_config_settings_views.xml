<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="res_config_settings_view_form_stock" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.delivery.stock</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="stock.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='module_stock_sms']" position="replace">
                <field name="stock_move_sms_validation"/>
            </xpath>
            <xpath expr="//setting[@id='stock_sms']" position="attributes">
                <attribute name="string">SMS Confirmation</attribute>
            </xpath>
            <xpath expr="//setting[@id='stock_sms']" position="inside">
                <div class="row mt16" invisible="not stock_move_sms_validation">
                    <label for="stock_sms_confirmation_template_id" string="SMS Template" class="col-lg-4 o_light_label"/>
                    <field name="stock_sms_confirmation_template_id" class="oe_inline" required="stock_move_sms_validation" context="{'default_model': 'stock.picking'}"/>
                </div>
                <widget name="iap_buy_more_credits" service_name="sms" invisible="not stock_move_sms_validation"/>
            </xpath>
        </field>
    </record>

</odoo>
