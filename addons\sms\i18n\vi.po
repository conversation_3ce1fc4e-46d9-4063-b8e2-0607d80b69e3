# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sms
# 
# Translators:
# <PERSON><PERSON>, 2025
# <PERSON>il <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-07 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_invalid_count
msgid "# Invalid recipients"
msgstr "# <PERSON><PERSON><PERSON><PERSON> nhận không hợp lệ"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_valid_count
msgid "# Valid recipients"
msgstr "# Ng<PERSON>ời nhận hợp lệ"

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid ""
"%(count)s out of the %(total)s selected SMS Text Messages have successfully "
"been resent."
msgstr ""
"%(count)s trong số %(total)s Tin nhắn văn bản SMS được chọn đã được gửi lại "
"thành công."

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_template.py:0
msgid "%s (copy)"
msgstr "%s (sao chép)"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_composer.py:0
msgid "%s invalid recipients"
msgstr "%s người nhận không hợp lệ"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
msgid "******-555-555"
msgstr "******-555-555"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.xml:0
msgid ", fits in"
msgstr ", có chứa"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.iap_account_view_form
msgid ""
"<i class=\"fa fa-warning\"/> An error occurred with your account. Please "
"contact the support for more information..."
msgstr ""
"<i class=\"fa fa-warning\"/> Đã xảy ra lỗi với tài khoản của bạn. Vui lòng "
"liên hệ với bộ phận hỗ trợ để biết thêm thông tin..."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.iap_account_view_form
msgid ""
"<i class=\"fa fa-warning\"/> You cannot send SMS while your account is not "
"registered."
msgstr ""
"<i class=\"fa fa-warning\"/> Bạn không thể gửi tin nhắn SMS khi tài khoản "
"của bạn chưa được đăng ký."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.iap_account_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Register"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Đăng ký"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.iap_account_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Set Sender Name"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Đặt tên người gửi"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid ""
"<span class=\"o_stat_text\">Add</span>\n"
"                                <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">Thêm</span>\n"
"                                <span class=\"o_stat_text\">Tác vụ theo ngữ cảnh</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "<span class=\"o_stat_text\">Preview</span>"
msgstr "<span class=\"o_stat_text\">Xem trước</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid ""
"<span class=\"o_stat_text\">Remove</span>\n"
"                                <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">Gỡ bỏ</span>\n"
"                               <span class=\"o_stat_text\">Tác vụ theo ngữ cảnh</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "<span class=\"text-warning\" invisible=\"not no_record\">No records</span>"
msgstr ""
"<span class=\"text-warning\" invisible=\"not no_record\">Không có bản ghi "
"nào</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.ir_actions_server_view_form
msgid ""
"<span invisible=\"sms_method != 'sms'\">\n"
"                                The message will be sent as an SMS to the recipients of the template\n"
"                                and will not appear in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"sms_method != 'note'\">\n"
"                                The SMS will not be sent, it will only be posted as an\n"
"                                internal note in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"sms_method != 'comment'\">\n"
"                                The SMS will be sent as an SMS to the recipients of the\n"
"                                template and it will also be posted as an internal note\n"
"                                in the messaging history.\n"
"                            </span>"
msgstr ""
"<span invisible=\"sms_method != 'sms'\">\n"
"                                Tin nhắn sẽ được gửi tới người nhận mẫu dưới dạng SMS\n"
"                                và sẽ không xuất hiện trong lịch sử nhắn tin.\n"
"                            </span>\n"
"                            <span invisible=\"sms_method != 'note'\">\n"
"                                Tin nhắn sẽ không được gửi mà chỉ được đăng dưới dạng\n"
"                                ghi chú nội bộ trong lịch sử nhắn tin.\n"
"                            </span>\n"
"                            <span invisible=\"sms_method != 'comment'\">\n"
"                                Tin nhắn sẽ được gửi tới người nhận mẫu dưới dạng SMS\n"
"                                và cũng sẽ được đăng dưới dạng ghi chú nội bộ \n"
"                                trong lịch sử nhắn tin.\n"
"                            </span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "<span> or to specify the country code.</span>"
msgstr "<span> hoặc để xác định mã quốc gia.</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid ""
"<strong>Invalid number:</strong>\n"
"                        <span> make sure to set a country on the </span>"
msgstr ""
"<strong>Số không hợp lệ:</strong>\n"
"                        <span> đảm bảo đã thiết lập quốc gia trên </span>"

#. module: sms
#: model:ir.model.constraint,message:sms.constraint_sms_tracker_sms_uuid_unique
msgid "A record for this UUID already exists"
msgstr "Một bản ghi cho UUID này đã tồn tại"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__account_id
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__account_id
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__account_id
msgid "Account"
msgstr "Tài khoản"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_needaction
msgid "Action Needed"
msgstr "Tác vụ cần thiết"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid ""
"Add a contextual action on the related model to open a sms composer with "
"this template"
msgstr ""
"Thêm một tác vụ theo ngữ cảnh trên mô hình liên quan để mở trình soạn thảo "
"sms bằng mẫu này"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_sms__uuid
msgid "Alternate way to identify a SMS record, used for delivery reports"
msgstr ""
"Cách khác để xác định bản ghi SMS, được sử dụng cho báo cáo gửi tin nhắn"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/core/failure_model_patch.js:0
msgid "An error occurred when sending an SMS"
msgstr "Đã xảy ra lỗi khi gửi SMS"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/core/failure_model_patch.js:0
msgid "An error occurred when sending an SMS on “%(record_name)s”"
msgstr "Có lỗi khi gửi SMS trên “%(record_name)s”"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"An unknown error occurred. Please contact Odoo support if this error "
"persists."
msgstr ""
"Đã xảy ra lỗi không xác định. Vui lòng liên hệ với bộ phận hỗ trợ của Odoo "
"nếu lỗi này vẫn tiếp diễn."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__model_id
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__model_id
msgid "Applies to"
msgstr "Áp dụng cho"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_reset_view_form
msgid ""
"Are you sure you want to reset these sms templates to their original "
"configuration? Changes and translations will be lost."
msgstr ""
"Bạn có chắc chắn muốn đặt lại các mẫu SMS này về cấu hình ban đầu không? Các"
" thay đổi và bản dịch sẽ bị mất."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_attachment_count
msgid "Attachment Count"
msgstr "Số lượng tệp đính kèm"

#. module: sms
#: model:ir.model,name:sms.model_base
msgid "Base"
msgstr "Cơ sở"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_blacklist
msgid "Blacklisted"
msgstr "Đã hạn chế"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "Số điện thoại bị hạn chế là số di động"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "Số điện thoại bị hạn chế là số điện thoại bàn"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__body
#: model:ir.model.fields,field_description:sms.field_sms_template__body
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__body
msgid "Body"
msgstr "Thân"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Buy credits"
msgstr "Mua tín dụng"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "Buy credits."
msgstr "Mua tín dụng. "

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__can_cancel
msgid "Can Cancel"
msgstr "Có thể huỷ"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__can_resend
msgid "Can Resend"
msgstr "Có thể gửi lại"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_code_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_tree
#: model_terms:ir.ui.view,arch_db:sms.sms_template_reset_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Cancel"
msgstr "Hủy"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__canceled
msgid "Cancelled"
msgstr "Đã hủy"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Choose a language:"
msgstr "Chọn ngôn ngữ:"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.ir_actions_server_view_form
msgid "Choose a template..."
msgstr "Chọn một mẫu..."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Choose an example"
msgstr "Chọn một ví dụ"

#. module: sms
#. odoo-python
#: code:addons/sms/models/iap_account.py:0
#: code:addons/sms/wizard/sms_account_code.py:0
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid "Choose your sender name"
msgstr "Chọn tên người gửi"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Close"
msgstr "Đóng"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__composition_mode
msgid "Composition Mode"
msgstr "Chế độ soạn thảo"

#. module: sms
#: model:ir.model,name:sms.model_res_partner
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Contact"
msgstr "Liên hệ"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Content"
msgstr "Nội dung"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_country_not_supported
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_country_not_supported
msgid "Country Not Supported"
msgstr "Quốc gia không được hỗ trợ"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_registration_needed
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_registration_needed
msgid "Country-specific Registration Required"
msgstr "Yêu cầu đăng ký theo quốc gia cụ thể"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "Country-specific registration required."
msgstr "Yêu cầu đăng ký theo quốc gia cụ thể"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_composer__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_sms__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_template__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_tracker__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__create_date
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__create_date
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__create_date
#: model:ir.model.fields,field_description:sms.field_sms_composer__create_date
#: model:ir.model.fields,field_description:sms.field_sms_resend__create_date
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__create_date
#: model:ir.model.fields,field_description:sms.field_sms_sms__create_date
#: model:ir.model.fields,field_description:sms.field_sms_template__create_date
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__create_date
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__create_date
#: model:ir.model.fields,field_description:sms.field_sms_tracker__create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: sms
#: model:iap.service,unit_name:sms.iap_service_sms
msgid "Credits"
msgstr "Tín dụng"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__partner_id
msgid "Customer"
msgstr "Khách hàng"

#. module: sms
#: model:sms.template,name:sms.sms_template_demo_0
msgid "Customer: automated SMS"
msgstr "Khách hàng: SMS tự động"

#. module: sms
#: model:sms.template,body:sms.sms_template_demo_0
msgid "Dear {{ object.display_name }} this is an automated SMS."
msgstr "Xin chào {{ object.display_name }} đây là một SMS tự động."

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__sent
msgid "Delivered"
msgstr "Đã giao"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Discard"
msgstr "Huỷ bỏ"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__display_name
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__display_name
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__display_name
#: model:ir.model.fields,field_description:sms.field_sms_composer__display_name
#: model:ir.model.fields,field_description:sms.field_sms_resend__display_name
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__display_name
#: model:ir.model.fields,field_description:sms.field_sms_sms__display_name
#: model:ir.model.fields,field_description:sms.field_sms_template__display_name
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__display_name
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__display_name
#: model:ir.model.fields,field_description:sms.field_sms_tracker__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: sms
#: model:ir.model,name:sms.model_mail_followers
msgid "Document Followers"
msgstr "Người theo dõi Tài liệu"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_id
msgid "Document ID"
msgstr "ID tài liệu"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_ids
msgid "Document IDs"
msgstr "ID tài liệu"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_model_description
msgid "Document Model Description"
msgstr "Mô tả mô hình tài liệu"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_model
msgid "Document Model Name"
msgstr "Tên Mô hình tài liệu"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_duplicate
msgid "Duplicate"
msgstr "Nhân bản"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Edit Partners"
msgstr "Sửa đối tác"

#. module: sms
#: model:ir.model,name:sms.model_mail_thread
msgid "Email Thread"
msgstr "Luồng email"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/phone_field/phone_field.js:0
msgid "Enable SMS"
msgstr "Bật SMS"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
msgid "Enter a phone number to get an SMS with a verification code."
msgstr "Nhập số điện thoại để nhận tin nhắn SMS chứa mã xác minh."

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__error
msgid "Error"
msgstr "Lỗi"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__failure_type
msgid "Error Message"
msgstr "Thông điệp lỗi"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_expired
msgid "Expired"
msgstr "Đã hết hạn"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__failure_type
msgid "Failure Type"
msgstr "Loại lỗi"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__failure_type
msgid "Failure type"
msgstr "Loại thất bại"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr ""
"Trường được sử dụng để chứa số điện thoại được làm sạch. Giúp tăng tốc tìm "
"kiếm và so sánh. "

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__template_fs
msgid ""
"File from where the template originates. Used to reset broken template."
msgstr "Tệp từ nơi hình thành mẫu. Được sử dụng để thiết lập lại mẫu bị hỏng."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_follower_ids
msgid "Followers"
msgstr "Người theo dõi"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_partner_ids
msgid "Followers (Partners)"
msgstr "Người theo dõi (Đối tác)"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_composer.py:0
msgid "Following numbers are not correctly encoded: %s"
msgstr "Các số sau được nhập chưa chính xác: %s"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__has_insufficient_credit
msgid "Has Insufficient Credit"
msgstr "Không đủ tín dụng"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__has_message
msgid "Has Message"
msgstr "Có tin nhắn"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_mail__has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_message__has_sms_error
msgid "Has SMS error"
msgstr "Có lỗi SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__has_unregistered_account
msgid "Has Unregistered Account"
msgstr "Có tài khoản chưa đăng ký"

#. module: sms
#: model:ir.model,name:sms.model_iap_account
msgid "IAP Account"
msgstr "Tài khoản IAP"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__id
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__id
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__id
#: model:ir.model.fields,field_description:sms.field_sms_composer__id
#: model:ir.model.fields,field_description:sms.field_sms_resend__id
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__id
#: model:ir.model.fields,field_description:sms.field_sms_sms__id
#: model:ir.model.fields,field_description:sms.field_sms_template__id
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__id
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__id
#: model:ir.model.fields,field_description:sms.field_sms_tracker__id
msgid "ID"
msgstr "ID"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Nếu chọn, bạn cần chú ý tới các tin nhắn mới."

#. module: sms
#: model:ir.model.fields,help:sms.field_account_analytic_account__message_has_sms_error
#: model:ir.model.fields,help:sms.field_calendar_event__message_has_sms_error
#: model:ir.model.fields,help:sms.field_crm_team__message_has_sms_error
#: model:ir.model.fields,help:sms.field_crm_team_member__message_has_sms_error
#: model:ir.model.fields,help:sms.field_discuss_channel__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle_log_contract__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle_log_services__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle_model__message_has_sms_error
#: model:ir.model.fields,help:sms.field_gamification_badge__message_has_sms_error
#: model:ir.model.fields,help:sms.field_gamification_challenge__message_has_sms_error
#: model:ir.model.fields,help:sms.field_iap_account__message_has_sms_error
#: model:ir.model.fields,help:sms.field_lunch_supplier__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_blacklist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_blacklist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_cc__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_main_attachment__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_phone__message_has_sms_error
#: model:ir.model.fields,help:sms.field_maintenance_equipment__message_has_sms_error
#: model:ir.model.fields,help:sms.field_maintenance_equipment_category__message_has_sms_error
#: model:ir.model.fields,help:sms.field_maintenance_request__message_has_sms_error
#: model:ir.model.fields,help:sms.field_phone_blacklist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_category__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_pricelist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_product__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_template__message_has_sms_error
#: model:ir.model.fields,help:sms.field_rating_mixin__message_has_sms_error
#: model:ir.model.fields,help:sms.field_res_partner__message_has_error
#: model:ir.model.fields,help:sms.field_res_partner__message_has_sms_error
#: model:ir.model.fields,help:sms.field_res_users__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Nếu chọn, một số tin nhắn sẽ có lỗi gửi."

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr ""
"Nếu số điện thoại được làm sạch nằm trong danh sách hạn chế, thì liên hệ sẽ "
"không nhận được sms hàng loạt từ bất kỳ danh sách nào nữa"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Ignore all"
msgstr "Bỏ qua tất cả"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__outgoing
msgid "In Queue"
msgstr "Đang chờ"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"Cho biết số điện thoại được làm sạch trong danh sách hạn chế có phải là số "
"di động hay không. Giúp phân biệt số nào bị hạn chế             khi có cả "
"trường di động và trường điện thoại bàn trong một mô hình."

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"Cho biết số điện thoại được làm sạch trong danh sách hạn chế có phải là số "
"điện thoại bàn hay không. Giúp phân biệt số nào bị hạn chế             khi "
"có cả trường di động và trường điện thoại bàn trong một mô hình."

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__comment_single_recipient
msgid "Indicates if the SMS composer targets a single specific recipient"
msgstr ""
"Cho biết liệu trình soạn thảo SMS có nhắm mục tiêu đến một người nhận cụ thể"
" hay không"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_credit
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_credit
msgid "Insufficient Credit"
msgstr "Không đủ tín dụng"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_invalid_destination
msgid "Invalid Destination"
msgstr "Địa chỉ đích không hợp lệ"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"Invalid phone number. Please make sure to follow the international format, "
"i.e. a plus sign (+), then country code, city code, and local phone number. "
"For example: ******-555-555"
msgstr ""
"Số điện thoại không hợp lệ. Vui lòng đảm bảo tuân thủ định dạng quốc tế như "
"sau: dấu cộng (+), sau đó là mã quốc gia, mã thành phố và số điện thoại địa "
"phương. Ví dụ: ******-555-555"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_composer.py:0
msgid "Invalid recipient number. Please update it."
msgstr "Số người nhận không hợp lệ. Hãy cập nhật lại."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_is_follower
msgid "Is Follower"
msgstr "Là người theo dõi"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_valid
msgid "Is valid"
msgstr "Hợp lệ"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__mass_keep_log
msgid "Keep a note on document"
msgstr "Ghi chú trên tài liệu"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__lang
msgid "Language"
msgstr "Ngôn ngữ"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_composer__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_sms__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_template__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_tracker__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__write_date
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__write_date
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__write_date
#: model:ir.model.fields,field_description:sms.field_sms_composer__write_date
#: model:ir.model.fields,field_description:sms.field_sms_resend__write_date
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__write_date
#: model:ir.model.fields,field_description:sms.field_sms_sms__write_date
#: model:ir.model.fields,field_description:sms.field_sms_template__write_date
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__write_date
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__write_date
#: model:ir.model.fields,field_description:sms.field_sms_tracker__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: sms
#: model:ir.model,name:sms.model_sms_tracker
msgid "Link SMS to mailing/sms tracking models"
msgstr "Liên kết SMS với các mô hình theo dõi thư/sms"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__mail_message_id
msgid "Mail Message"
msgstr "Nội dung email"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_tracker__mail_notification_id
msgid "Mail Notification"
msgstr "Thông báo thư"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_ir_model__is_mail_thread_sms
msgid "Mail Thread SMS"
msgstr "SMS luồng thư"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__to_delete
msgid "Marked for deletion"
msgstr "Được đánh dấu để xoá"

#. module: sms
#: model:ir.model,name:sms.model_mail_message
#: model:ir.model.fields,field_description:sms.field_sms_composer__body
#: model:ir.model.fields,field_description:sms.field_sms_resend__mail_message_id
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Message"
msgstr "Tin nhắn"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_has_error
msgid "Message Delivery error"
msgstr "Lỗi gửi tin nhắn"

#. module: sms
#: model:ir.model,name:sms.model_mail_notification
msgid "Message Notifications"
msgstr "Thông báo thông điệp"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_ids
msgid "Messages"
msgstr "Tin nhắn"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_number_missing
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_number_missing
msgid "Missing Number"
msgstr "Thiếu số điện thoại"

#. module: sms
#: model:ir.model,name:sms.model_ir_model
msgid "Models"
msgstr "Mô hình"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__name
msgid "Name"
msgstr "Tên"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__no_record
msgid "No Record"
msgstr "Không có dữ liệu"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_not_allowed
msgid "Not Allowed"
msgstr "Được cho phép"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_not_delivered
msgid "Not Delivered"
msgstr "Chưa giao"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__ir_actions_server__sms_method__note
msgid "Note only"
msgstr "Chỉ ghi chú"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid ""
"Note that this is not required, if you don't set a sender name, your SMS "
"will be sent from a short code."
msgstr ""
"Lưu ý rằng điều này không bắt buộc, nếu bạn không đặt tên người gửi, tin "
"nhắn SMS của bạn sẽ được gửi từ một mã ngắn."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__notification_id
msgid "Notification"
msgstr "Thông báo"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "Loại thông báo"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__number
msgid "Number"
msgstr "Số"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__number_field_name
msgid "Number Field"
msgstr "Trường số"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_needaction_counter
msgid "Number of Actions"
msgstr "Số lượng tác vụ"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_has_error_counter
msgid "Number of errors"
msgstr "Số lượng lỗi"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Số tin nhắn cần xử lý"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Số tin nhắn bị gửi lỗi"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__res_ids_count
msgid ""
"Number of recipients that will receive the SMS if sent in mass mode, without"
" applying the Active Domain value"
msgstr ""
"Số người nhận sẽ nhận được SMS nếu được gửi ở chế độ hàng loạt khi không áp "
"dụng giá trị Miền hoạt động"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_optout
msgid "Opted Out"
msgstr "Hủy tham gia"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Ngôn ngữ dịch tuỳ chọn (mã ISO) để chọn khi gửi email. Nếu không đặt, phiên "
"bản tiếng Anh sẽ được sử dụng. Đây luôn là biểu thức phần giữ chỗ cung cấp "
"ngôn ngữ thích hợp, VD: {{ object.partner_id.lang }}."

#. module: sms
#: model:ir.model,name:sms.model_sms_sms
msgid "Outgoing SMS"
msgstr "SMS đi"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__partner_id
msgid "Partner"
msgstr "Đối tác"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "Số điện thoại hạn chế"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__phone_number
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__sms_number
msgid "Phone Number"
msgstr "Số Điện thoại"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__recipient_single_number_itf
msgid ""
"Phone number of the recipient. If changed, it will be recorded on "
"recipient's profile."
msgstr ""
"Số điện thoại của người nhận. Nếu thay đổi sẽ được ghi lại trong hồ sơ người"
" nhận."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_mobile_search
msgid "Phone/Mobile"
msgstr "Điện thoại/di động"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_composer__composition_mode__comment
msgid "Post on a document"
msgstr "Đưa vào tài liệu"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Preview of"
msgstr "Xem trước của"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_reset_view_form
msgid "Proceed"
msgstr "Tiến hành"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__process
msgid "Processing"
msgstr "Đang xử lý"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Put in queue"
msgstr "Đưa vào hàng đợi"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__rating_ids
msgid "Ratings"
msgstr "Đánh giá"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Reason"
msgstr "Lý do"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Recipient"
msgstr "Người nhận"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__partner_name
msgid "Recipient Name"
msgstr "Tên người nhận"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_number_itf
msgid "Recipient Number"
msgstr "Số người nhận"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__recipient_ids
msgid "Recipients"
msgstr "Người nhận"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__numbers
msgid "Recipients (Numbers)"
msgstr "Người nhận (Số)"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_description
msgid "Recipients (Partners)"
msgstr "Người nhận (Đối tác)"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__resource_ref
msgid "Record reference"
msgstr "Tham chiếu bản ghi"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_code_view_form
msgid "Register"
msgstr "Đăng ký"

#. module: sms
#. odoo-python
#: code:addons/sms/models/iap_account.py:0
#: code:addons/sms/wizard/sms_account_phone.py:0
msgid "Register Account"
msgstr "Đăng ký tài khoản"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "Register now."
msgstr "Đăng ký ngay."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_code_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
msgid "Register your SMS account"
msgstr "Đăng ký tài khoản SMS của bạn"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_rejected
msgid "Rejected"
msgstr "Bị từ chối"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__model
msgid "Related Document Model"
msgstr "Mô hình tài liệu liên quan"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Remove the contextual action of the related model"
msgstr "Xóa tác vụ theo ngữ cảnh của mô hình liên quan"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__render_model
msgid "Rendering Model"
msgstr "Kết xuất mô hình"

#. module: sms
#: model:ir.actions.server,name:sms.ir_actions_server_sms_sms_resend
msgid "Resend"
msgstr "Gửi lại"

#. module: sms
#: model:ir.model,name:sms.model_sms_resend_recipient
msgid "Resend Notification"
msgstr "Gửi lại thông báo"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_template_reset_action
msgid "Reset SMS Template"
msgstr "Đặt lại mẫu SMS"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Reset Template"
msgstr "Đặt lại mẫu"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_tree
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Retry"
msgstr "Thử lại"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_button/sms_button.xml:0
#: code:addons/sms/static/src/core/notification_model_patch.js:0
#: model:ir.actions.act_window,name:sms.sms_sms_action
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_id
#: model:ir.model.fields.selection,name:sms.selection__mail_message__message_type__sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__notification_type__sms
#: model:ir.ui.menu,name:sms.sms_sms_menu
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "SMS"
msgstr "SMS"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.xml:0
msgid "SMS ("
msgstr "SMS ("

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__ir_actions_server__sms_method__comment
msgid "SMS (with note)"
msgstr "SMS (kèm ghi chú)"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__ir_actions_server__sms_method__sms
msgid "SMS (without note)"
msgstr "SMS (không kèm ghi chú)"

#. module: sms
#: model:ir.model,name:sms.model_sms_account_phone
msgid "SMS Account Registration Phone Number Wizard"
msgstr "Công cụ đăng ký số điện thoại"

#. module: sms
#: model:ir.model,name:sms.model_sms_account_sender
msgid "SMS Account Sender Name Wizard"
msgstr "Công cụ tên người gửi"

#. module: sms
#: model:ir.model,name:sms.model_sms_account_code
msgid "SMS Account Verification Code Wizard"
msgstr "Công cụ mã xác minh"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_account_analytic_account__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_calendar_event__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_crm_team__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_crm_team_member__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_discuss_channel__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle_log_contract__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle_log_services__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle_model__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_gamification_badge__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_gamification_challenge__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_iap_account__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_lunch_supplier__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_blacklist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_blacklist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_cc__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_main_attachment__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_phone__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_maintenance_equipment__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_maintenance_equipment_category__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_maintenance_request__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_phone_blacklist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_category__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_pricelist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_product__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_template__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_rating_mixin__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_res_partner__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_res_users__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Lỗi gửi SMS"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/messaging_menu/messaging_menu_patch.js:0
msgid "SMS Failure: %(modelName)s"
msgstr "Lỗi SMS: %(modelName)s"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/messaging_menu/messaging_menu_patch.js:0
msgid "SMS Failures"
msgstr "Lỗi SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_id_int
msgid "SMS ID"
msgstr "ID SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_number
msgid "SMS Number"
msgstr "Số SMS"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "SMS Preview"
msgstr "Xem trước SMS"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.xml:0
msgid "SMS Pricing"
msgstr "Giá SMS"

#. module: sms
#: model:ir.model,name:sms.model_sms_resend
msgid "SMS Resend"
msgstr "Gửi lại SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__state
msgid "SMS Status"
msgstr "Trạng thái SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_ir_actions_server__sms_template_id
#: model:ir.model.fields,field_description:sms.field_ir_cron__sms_template_id
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "SMS Template"
msgstr "Mẫu SMS"

#. module: sms
#: model:ir.model,name:sms.model_sms_template_preview
msgid "SMS Template Preview"
msgstr "Xem trước mẫu SMS"

#. module: sms
#: model:ir.model,name:sms.model_sms_template_reset
msgid "SMS Template Reset"
msgstr "Đặt lại mẫu SMS"

#. module: sms
#: model:ir.model,name:sms.model_sms_template
#: model:ir.ui.menu,name:sms.sms_template_menu
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_tree
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_tree
msgid "SMS Templates"
msgstr "Mẫu SMS"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_template_reset.py:0
msgid "SMS Templates have been reset"
msgstr "Mẫu SMS đã được đặt lại"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_tracker_ids
msgid "SMS Trackers"
msgstr "Trình theo dõi SMS"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "SMS content"
msgstr "Nội dung SMS"

#. module: sms
#. odoo-python
#: code:addons/sms/models/ir_actions_server.py:0
msgid "SMS template model of %(action_name)s does not match action model."
msgstr "Mô hình mẫu SMS của %(action_name)s không khớp với mô hình tác vụ."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__sms_tracker_id
msgid "SMS trackers"
msgstr "Trình theo dõi SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_tracker__sms_uuid
msgid "SMS uuid"
msgstr "Uuid SMS"

#. module: sms
#: model:ir.actions.server,name:sms.ir_cron_sms_scheduler_action_ir_actions_server
msgid "SMS: SMS Queue Manager"
msgstr "SMS: Trình quản lý hàng đợi SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_sanitized
#: model:ir.model.fields,field_description:sms.field_sms_composer__sanitized_numbers
msgid "Sanitized Number"
msgstr "Số đã làm sạch "

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_search
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_search
msgid "Search SMS Templates"
msgstr "Tìm kiếm mẫu SMS"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Send & Close"
msgstr "Gửi & đóng"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_tree
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Send Now"
msgstr "Gửi ngay"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_button/sms_button.js:0
#: model:ir.actions.act_window,name:sms.res_partner_act_window_sms_composer_multi
#: model:ir.actions.act_window,name:sms.res_partner_act_window_sms_composer_single
#: model:ir.actions.act_window,name:sms.sms_composer_action_form
#: model:ir.model.fields.selection,name:sms.selection__ir_actions_server__state__sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Send SMS"
msgstr "Gửi SMS"

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_template.py:0
msgid "Send SMS (%s)"
msgstr "Gửi SMS (%s)"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_ir_actions_server__sms_method
#: model:ir.model.fields,field_description:sms.field_ir_cron__sms_method
msgid "Send SMS As"
msgstr "Gửi SMS dưới dạng"

#. module: sms
#: model:ir.model,name:sms.model_sms_composer
msgid "Send SMS Wizard"
msgstr "Công cụ gửi SMS"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_composer__composition_mode__mass
msgid "Send SMS in batch"
msgstr "Gửi SMS theo lô"

#. module: sms
#: model:iap.service,description:sms.iap_service_sms
msgid "Send SMS to your contacts directly from your database."
msgstr "Gửi tin nhắn SMS đến các liên hệ của bạn ngay từ cơ sở dữ liệu."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Send an SMS"
msgstr "Gửi SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__mass_force_send
msgid "Send directly"
msgstr "Gửi trực tiếp"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_composer__composition_mode__numbers
msgid "Send to numbers"
msgstr "Gửi đến số"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
msgid "Send verification code"
msgstr "Gửi mã xác minh"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_iap_account__sender_name
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__sender_name
msgid "Sender Name"
msgstr "Tên người gửi"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_resend_action
msgid "Sending Failures"
msgstr "Lỗi gửi"

#. module: sms
#. odoo-python
#: code:addons/sms/models/ir_actions_server.py:0
msgid "Sending SMS can only be done on a not transient mail.thread model"
msgstr ""

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__pending
msgid "Sent"
msgstr "Đã gửi"

#. module: sms
#: model:ir.model,name:sms.model_ir_actions_server
msgid "Server Action"
msgstr "Tác vụ phía máy chủ"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_server
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_server
msgid "Server Error"
msgstr "Lỗi máy chủ"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid "Set sender name"
msgstr "Đặt tên người gửi"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Set up an account"
msgstr "Thiết lập một tài khoản"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__sidebar_action_id
msgid "Sidebar action"
msgstr "Thanh tác vụ"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__sidebar_action_id
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr ""
"Tác vụ thanh bên để mẫu này khả dụng trong bản ghi của mô hình tài liệu liên"
" quan"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__comment_single_recipient
msgid "Single Mode"
msgstr "Chế độ đơn"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid "Skip for now"
msgstr "Tạm bỏ qua"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__sms_resend_id
msgid "Sms Resend"
msgstr "Gửi lại SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__sms_template_id
msgid "Sms Template"
msgstr "Mẫu SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_number
msgid "Stored Recipient Number"
msgstr "Số người nhận được lưu trữ"

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid "Success"
msgstr "Thành công"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__template_ids
msgid "Template"
msgstr "Mẫu"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__template_fs
msgid "Template Filename"
msgstr "Tên tệp mẫu"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_template_preview_action
msgid "Template Preview"
msgstr "Xem trước Mẫu"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__lang
msgid "Template Preview Language"
msgstr "Ngôn ngữ mẫu xem trước"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_template_action
msgid "Templates"
msgstr "Mẫu"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"The SMS Service is currently unavailable for new users and new accounts "
"registrations are suspended."
msgstr ""
"Dịch vụ SMS hiện không khả dụng đối với người dùng mới và việc đăng ký tài "
"khoản mới đã bị tạm ngừng."

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid "The SMS Text Messages could not be resent."
msgstr "Không thể gửi lại Tin nhắn văn bản SMS."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "The content of the message violates rules applied by our providers."
msgstr ""
"Nội dung tin nhắn vi phạm các quy tắc mà nhà cung cấp của chúng tôi áp dụng."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "The destination country is not supported."
msgstr "Quốc gia đích không được hỗ trợ."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "The number you're trying to reach is not correctly formatted."
msgstr "Số bạn đang cố gắng liên hệ không được định dạng chính xác."

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__model_id
#: model:ir.model.fields,help:sms.field_sms_template_preview__model_id
msgid "The type of document this template can be used with"
msgstr "Kiểu tài liệu mà mẫu này có thể được sử dụng với"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "The verification code is incorrect."
msgstr "Mã xác minh không chính xác"

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid "There are no SMS Text Messages to resend."
msgstr "Không có Tin nhắn văn bản SMS nào để gửi lại."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "This SMS has been removed as the number was already used."
msgstr "SMS này đã bị xóa vì số này đã được sử dụng."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"This account already has an existing sender name and it cannot be changed."
msgstr "Tài khoản này đã có tên người gửi và không thể thay đổi."

#. module: sms
#: model:ir.model.fields,help:sms.field_iap_account__sender_name
msgid "This is the name that will be displayed as the sender of the SMS."
msgstr "Đây là tên sẽ được hiển thị là người gửi tin nhắn SMS."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.res_partner_view_form
msgid ""
"This phone number is blacklisted for SMS Marketing. Click to unblacklist."
msgstr ""
"Số điện thoại này được đưa vào danh sách hạn chế SMS Marketing. Bấm để loại "
"khỏi danh sách hạn chế. "

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "This phone number/account has been banned from our service."
msgstr "Số điện thoại/tài khoản này đã bị cấm sử dụng dịch vụ của chúng tôi."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__resend
msgid "Try Again"
msgstr "Thử lại"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_ir_actions_server__state
#: model:ir.model.fields,field_description:sms.field_ir_cron__state
#: model:ir.model.fields,field_description:sms.field_mail_mail__message_type
#: model:ir.model.fields,field_description:sms.field_mail_message__message_type
msgid "Type"
msgstr "Loại"

#. module: sms
#: model:ir.model.fields,help:sms.field_ir_actions_server__state
#: model:ir.model.fields,help:sms.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Update a Record': update the values of a record\n"
"- 'Create Activity': create an activity (Discuss)\n"
"- 'Send Email': post a message, a note or send an email (Discuss)\n"
"- 'Send SMS': send SMS, log them on documents (SMS)- 'Add/Remove Followers': add or remove followers to a record (Discuss)\n"
"- 'Create Record': create a new record with new values\n"
"- 'Execute Code': a block of Python code that will be executed\n"
"- 'Send Webhook Notification': send a POST request to an external system, also known as a Webhook\n"
"- 'Execute Existing Actions': define an action that triggers several other server actions\n"
msgstr ""
"Loại tác vụ máy chủ. Các giá trị sau đây khả dụng:\n"
"- 'Cập nhật bản ghi': cập nhật giá trị của bản ghi\n"
"- 'Tạo hoạt động': tạo một hoạt động (Thảo luận)\n"
"- 'Gửi email': gửi tin nhắn, ghi chú hoặc email (Thảo luận)\n"
"- 'Gửi SMS': gửi SMS, thêm chúng vào tài liệu (SMS)- 'Thêm/Xóa người theo dõi': thêm hoặc xóa người theo dõi vào/khỏi một bản ghi (Thảo luận)\n"
"- 'Tạo bản ghi': tạo một bản ghi mới với các giá trị mới\n"
"- 'Thực thi mã': một khối mã Python sẽ được thực thi\n"
"- 'Gửi thông báo Webhook': gửi yêu cầu POST tới hệ thống bên ngoài, hay còn gọi là Webhook\n"
"- 'Thực thi tác vụ hiện có': xác định một tác vụ kích hoạt một số tác vụ máy chủ khác\n"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__uuid
msgid "UUID"
msgstr "UUID"

#. module: sms
#: model:ir.model.constraint,message:sms.constraint_sms_sms_uuid_unique
msgid "UUID must be unique"
msgstr "UUID không được trùng lặp"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__unknown
msgid "Unknown error"
msgstr "Lỗi không xác định"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_acc
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_acc
msgid "Unregistered Account"
msgstr "Tài khoản chưa đăng ký"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__template_id
msgid "Use Template"
msgstr "Sử dụng mẫu"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__mass_use_blacklist
msgid "Use blacklist"
msgstr "Sử dụng danh sách hạn chế"

#. module: sms
#: model:ir.model.fields,help:sms.field_mail_mail__message_type
#: model:ir.model.fields,help:sms.field_mail_message__message_type
msgid ""
"Used to categorize message generator\n"
"'email': generated by an incoming email e.g. mailgateway\n"
"'comment': generated by user input e.g. through discuss or composer\n"
"'email_outgoing': generated by a mailing\n"
"'notification': generated by system e.g. tracking messages\n"
"'auto_comment': generated by automated notification mechanism e.g. acknowledgment\n"
"'user_notification': generated for a specific recipient"
msgstr ""
"Được sử dụng để phân loại trình tạo tin nhắn\n"
"'email': được tạo bởi một email đến, VD: cổng thư\n"
"'bình luận': được tạo bởi thông tin người dùng nhập vào, VD: thông qua thảo luận hoặc trình soạn thảo\n"
"'email_outgoing': được tạo bằng cách gửi thư\n"
"'thông báo': được tạo bởi hệ thống, VD: theo dõi tin nhắn\n"
"'auto_comment': được tạo bởi cơ chế thông báo tự động, VD: báo nhận\n"
"'user_notification': được tạo cho một người nhận cụ thể"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__verification_code
msgid "Verification Code"
msgstr "Mã xác minh"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_ids_count
msgid "Visible records count"
msgstr "Số bản ghi có thể thấy"

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid "Warning"
msgstr "Cảnh báo"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "We were not able to find your account in our database."
msgstr ""
"Chúng tôi không tìm thấy tài khoản của bạn trong cơ sở dữ liệu của chúng "
"tôi."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"We were not able to reach you via your phone number. If you have requested "
"multiple codes recently, please retry later."
msgstr ""
"Chúng tôi không thể liên lạc qua số điện thoại của bạn. Nếu gần đây bạn đã "
"yêu cầu nhiều mã, vui lòng thử lại sau."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__website_message_ids
msgid "Website Messages"
msgstr "Thông báo trên trang web"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__website_message_ids
msgid "Website communication history"
msgstr "Lịch sử trao đổi qua trang web"

#. module: sms
#: model:ir.model.fields,help:sms.field_ir_model__is_mail_thread_sms
msgid "Whether this model supports messages and notifications through SMS"
msgstr "Mô hình này có hỗ trợ tin nhắn và thông báo thông qua SMS không"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_sms__to_delete
msgid ""
"Will automatically be deleted, while notifications will not be deleted in "
"any case."
msgstr ""
"Sẽ tự động bị xóa, còn thông báo sẽ không bị xóa trong bất kỳ trường hợp "
"nào."

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_number_format
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_number_format
msgid "Wrong Number Format"
msgstr "Định dạng số sai"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_resend.py:0
msgid "You do not have access to the message and/or related document."
msgstr "Bạn không có quyền truy cập vào tin nhắn và/hoặc tài liệu liên quan."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "You don't have an eligible IAP account."
msgstr "Bạn không có tài khoản IAP đủ điều kiện."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "You don't have enough credits on your IAP account."
msgstr "Bạn không có đủ tín dụng trên tài khoản IAP của mình."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "You tried too many times. Please retry later."
msgstr "Bạn đã thử quá nhiều lần. Vui lòng thử lại sau."

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.js:0
msgid ""
"Your SMS Text Message must include at least one non-whitespace character"
msgstr ""
"Tin nhắn văn bản SMS của bạn phải bao gồm ít nhất một ký tự không phải dấu "
"cách"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_account_code.py:0
msgid "Your SMS account has been successfully registered."
msgstr "Tài khoản SMS của bạn đã được đăng ký thành công."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"Your sender name must be between 3 and 11 characters long and only contain "
"alphanumeric characters."
msgstr ""
"Tên người gửi của bạn phải dài từ 3 đến 11 ký tự và chỉ được chứa các ký tự "
"chữ và số."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid ""
"Your sender name must be between 3 and 11 characters long and only contain alphanumeric characters.\n"
"                        It must fit your company name, and you aren't allowed to modify it once you registered one, choose it carefully."
msgstr ""
"Tên người gửi của bạn phải dài từ 3 đến 11 ký tự và chỉ được chứa các ký tự chữ và số.\n"
"Tên này phải phù hợp với tên công ty của bạn và bạn không được phép sửa đổi tên sau khi đã đăng ký, vậy nên hãy chọn tên thật cẩn thận."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "Your sms account has not been activated yet."
msgstr "Tài khoản SMS của bạn chưa được kích hoạt."

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.xml:0
msgid "characters"
msgstr "ký tự"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "e.g. +1 415 555 0100"
msgstr "VD: +1 415 555 0100"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "e.g. Calendar Reminder"
msgstr "VD: Nhắc nhở lịch"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "e.g. Contact"
msgstr "VD: Liên hệ"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "e.g. en_US or {{ object.partner_id.lang }}"
msgstr "VD: en_US hoặc {{ object.partner_id.lang }}"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "out of"
msgstr "trong số"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid ""
"recipients have an invalid phone number and will not receive this text "
"message."
msgstr ""
"người nhận có số điện thoại không hợp lệ và sẽ không nhận được tin nhắn văn "
"bản này."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "record:"
msgstr "bản ghi:"
