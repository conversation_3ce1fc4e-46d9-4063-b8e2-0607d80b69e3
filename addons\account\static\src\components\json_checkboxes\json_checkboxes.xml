<?xml version="1.0" encoding="UTF-8" ?>
<template>

    <t t-name="account.JsonCheckboxes">
        <div aria-atomic="true">
            <t t-foreach="checkboxes" t-as="checkbox" t-key="checkbox">
                <span class="d-inline-block me-3">
                    <CheckBox
                        id="checkbox"
                        value="checkbox_value['checked']"
                        disabled="checkbox_value['readonly']"
                        onChange="(ev) => this.onChange(checkbox, ev)"
                    >
                        <t t-esc="checkbox_value['label']" />
                    </CheckBox>
                    <i t-if="checkbox_value['question_circle']"
                       class="fa fa-question-circle"
                       role="img"
                       aria-label="Warning"
                       t-att-title="checkbox_value['question_circle']"/>
                </span>
            </t>
        </div>
    </t>

</template>
