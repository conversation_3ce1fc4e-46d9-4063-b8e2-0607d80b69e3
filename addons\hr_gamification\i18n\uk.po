# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_gamification
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: hr_gamification
#: model_terms:ir.actions.act_window,help:hr_gamification.goals_menu_groupby_action2
msgid ""
"A goal is defined by a user and a goal type.\n"
"                    Goals can be created automatically by using challenges."
msgstr ""
"Мета визначається користувачем та типом цілі.\n"
"Цілі можуть створюватись автоматично за допомогою викликів. "

#. module: hr_gamification
#: model:ir.model.fields,help:hr_gamification.field_hr_employee__badge_ids
#: model:ir.model.fields,help:hr_gamification.field_hr_employee_base__badge_ids
#: model:ir.model.fields,help:hr_gamification.field_hr_employee_public__badge_ids
msgid ""
"All employee badges, linked to the employee either directly or through the "
"user"
msgstr ""
"Всі значки співробітників, пов'язані з працівником безпосередньо або через "
"користувача"

#. module: hr_gamification
#: model_terms:ir.actions.act_window,help:hr_gamification.challenge_list_action2
msgid ""
"Assign a list of goals to chosen users to evaluate them.\n"
"                    The challenge can use a period (weekly, monthly...) for automatic creation of goals.\n"
"                    The goals are created for the specified users or member of the group."
msgstr ""
"Призначте список цілей для обраних користувачів для їх оцінки.\n"
"Завдання може використовувати період (щотижня, щомісяця...) для автоматичного створення цілей.\n"
"Цілі створені для вказаних користувачів або учасника групи."

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_res_users__badge_ids
#: model:ir.ui.menu,name:hr_gamification.gamification_badge_menu_hr
msgid "Badges"
msgstr "Значки"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid ""
"Badges are rewards of good work. Give them to people you believe deserve it."
msgstr ""
"Значки - це нагорода за хорошу роботу. Дайте людям, які ви вважаєте "
"заслуговують на це."

#. module: hr_gamification
#: model:ir.model.fields,help:hr_gamification.field_hr_employee__direct_badge_ids
#: model:ir.model.fields,help:hr_gamification.field_hr_employee_base__direct_badge_ids
#: model:ir.model.fields,help:hr_gamification.field_hr_employee_public__direct_badge_ids
msgid "Badges directly linked to the employee"
msgstr "Значки, безпосередньо пов'язані з працівником"

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_hr_employee_base
msgid "Basic Employee"
msgstr "Звичайний користувач"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "Cancel"
msgstr "Скасувати"

#. module: hr_gamification
#: model:ir.actions.act_window,name:hr_gamification.challenge_list_action2
#: model:ir.ui.menu,name:hr_gamification.gamification_challenge_menu_hr
#: model:ir.ui.menu,name:hr_gamification.menu_hr_gamification
msgid "Challenges"
msgstr "Випробування"

#. module: hr_gamification
#: model_terms:ir.actions.act_window,help:hr_gamification.challenge_list_action2
msgid "Create a new challenge"
msgstr "Створити нове випробування"

#. module: hr_gamification
#: model_terms:ir.actions.act_window,help:hr_gamification.goals_menu_groupby_action2
msgid "Create a new goal"
msgstr "Створити нову ціль"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "Describe what they did and why it matters (will be public)"
msgstr "Опишіть, що вони зробили і чому це важливо (буде загальнодоступним)"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee__direct_badge_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_base__direct_badge_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_public__direct_badge_ids
msgid "Direct Badge"
msgstr "Особистий значок"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge_user__employee_id
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge_user_wizard__employee_id
msgid "Employee"
msgstr "Співробітник"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee__badge_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_base__badge_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_public__badge_ids
msgid "Employee Badges"
msgstr "Значки працівників"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee__goal_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_base__goal_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_public__goal_ids
msgid "Employee HR Goals"
msgstr "Цілі персоналу відділу кадрів"

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_gamification_badge
msgid "Gamification Badge"
msgstr "Геміфікація значка"

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_gamification_badge_user
msgid "Gamification User Badge"
msgstr "Геміфікація користувацького значка"

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_gamification_badge_user_wizard
msgid "Gamification User Badge Wizard"
msgstr "Помічник геміфікації користувацького значка"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_res_users__goal_ids
msgid "Goal"
msgstr "Ціль"

#. module: hr_gamification
#: model:ir.actions.act_window,name:hr_gamification.goals_menu_groupby_action2
#: model:ir.ui.menu,name:hr_gamification.gamification_goal_menu_hr
msgid "Goals History"
msgstr "Історія значків"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid "Grant a Badge"
msgstr "Надішліть значок"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid "Grant this employee his first badge"
msgstr "Нагородіть цього співробітника його першим значком"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_badge_form_view
msgid "Granted"
msgstr "Надіслано"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge__granted_employees_count
msgid "Granted Employees Count"
msgstr "Підрахунок виданих співробітникам"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee__has_badges
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_base__has_badges
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_public__has_badges
msgid "Has Badges"
msgstr "Має значок"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid "Received Badges"
msgstr "Отримані значки"

#. module: hr_gamification
#: model:ir.actions.act_window,name:hr_gamification.action_reward_wizard
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "Reward Employee"
msgstr "Нагорода працівника"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "Reward Employee with"
msgstr "Нагорода працівника з"

#. module: hr_gamification
#. odoo-python
#: code:addons/hr_gamification/models/gamification.py:0
msgid "The selected employee does not correspond to the selected user."
msgstr "Вибраний працівник не відповідає вибраному користувачеві."

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_res_users
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge_user_wizard__user_id
msgid "User"
msgstr "Користувач"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "What are you thankful for?"
msgstr "За що ви вдячні?"

#. module: hr_gamification
#. odoo-python
#: code:addons/hr_gamification/wizard/gamification_badge_user_wizard.py:0
msgid "You can not send a badge to yourself."
msgstr "Ви не можете надіслато самому собі значок."

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid "to reward this employee for a good action"
msgstr "винагородити цього працівника за хорошу дію"
