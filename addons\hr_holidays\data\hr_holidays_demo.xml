<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data noupdate="1">

    <record id="base.user_demo" model="res.users">
        <field name="groups_id" eval="[
            (3, ref('hr_holidays.group_hr_holidays_responsible')),
            (3, ref('hr_holidays.group_hr_holidays_user')),
            (3, ref('hr_holidays.group_hr_holidays_manager'))]"/>
    </record>

    <!--Time Off Type-->
    <record id="hr_holiday_status_dv" model="hr.leave.type">
        <field name="name">Parental Leaves</field>
        <field name="requires_allocation">yes</field>
        <field name="employee_requests">no</field>
        <field name="leave_validation_type">both</field>
        <field name="allocation_validation_type">hr</field>
        <field name="responsible_ids" eval="[(4, ref('base.user_admin'))]"/>
        <field name="icon_id" ref="hr_holidays.icon_11"/>
    </record>

    <record id="holiday_status_training" model="hr.leave.type">
        <field name="name">Training Time Off</field>
        <field name="requires_allocation">yes</field>
        <field name="employee_requests">no</field>
        <field name="leave_validation_type">both</field>
        <field name="allocation_validation_type">hr</field>
        <field name="responsible_ids" eval="[(4, ref('base.user_admin'))]"/>
        <field name="icon_id" ref="hr_holidays.icon_26"/>
        <field name="allows_negative" eval="True"/>
        <field name="max_allowed_negative" eval="20"/>
    </record>

    <!-- Accrual Plan -->
    <record id="hr_accrual_plan_1" model="hr.leave.accrual.plan">
        <field name="name">Seniority Plan</field>
    </record>

    <record id="hr_accrual_level_1" model="hr.leave.accrual.level">
        <field name="accrual_plan_id" ref="hr_accrual_plan_1" />
        <field name="start_count">1</field>
        <field name="start_type">day</field>
        <field name="added_value">1</field>
        <field name="frequency">yearly</field>
    </record>
    <record id="hr_accrual_level_2" model="hr.leave.accrual.level">
        <field name="accrual_plan_id" ref="hr_accrual_plan_1" />
        <field name="start_count">4</field>
        <field name="start_type">year</field>
        <field name="added_value">2</field>
        <field name="frequency">yearly</field>
    </record>
    <record id="hr_accrual_level_3" model="hr.leave.accrual.level">
        <field name="accrual_plan_id" ref="hr_accrual_plan_1" />
        <field name="start_count">8</field>
        <field name="start_type">year</field>
        <field name="added_value">3</field>
        <field name="frequency">yearly</field>
    </record>

    <!-- ++++++++++++++++++++++  Mitchell Admin  ++++++++++++++++++++++ -->

    <record id="hr_holidays_allocation_cl" model="hr.leave.allocation">
        <field name="name">Paid Time Off for Mitchell Admin</field>
        <field name="holiday_status_id" ref="holiday_status_cl"/>
        <field name="number_of_days">20</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="state">confirm</field>
        <field name="date_from" eval="time.strftime('%Y-1-1')"/>
        <field name="date_to" eval="time.strftime('%Y-12-31')"/>
    </record>

    <record id="hr_holidays_int_tour" model="hr.leave.allocation">
        <field name="name">International Tour</field>
        <field name="holiday_status_id" ref="holiday_status_comp"/>
        <field name="number_of_days">7</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="state">confirm</field>
        <field name="date_from" eval="time.strftime('%Y-1-1')"/>
        <field name="date_to" eval="time.strftime('%Y-12-31')"/>
    </record>

    <record id="hr_holidays_vc" model="hr.leave.allocation">
        <field name="name">Functional Training</field>
        <field name="holiday_status_id" ref="holiday_status_training"/>
        <field name="number_of_days">7</field>
        <field name="state">confirm</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="date_from" eval="time.strftime('%Y-1-1')"/>
        <field name="date_to" eval="time.strftime('%Y-12-31')"/>
    </record>

    <record id='hr_holidays_cl_allocation' model="hr.leave.allocation">
        <field name="name">Compensation</field>
        <field name="holiday_status_id" ref="holiday_status_comp"/>
        <field name="number_of_days">12</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="state">confirm</field>
        <field name="date_from" eval="time.strftime('%Y-1-1')"/>
        <field name="date_to" eval="time.strftime('%Y-12-31')"/>
    </record>
    <function model="hr.leave.allocation" name="action_validate">
        <value eval="[ref('hr_holidays_allocation_cl'), ref('hr_holidays_int_tour'), ref('hr_holidays_cl_allocation')]"/>
    </function>

    <!-- leave request -->
    <record id="hr_holidays_cl" model="hr.leave">
        <field name="name">Trip with Family</field>
        <field name="holiday_status_id" ref="holiday_status_comp"/>
        <field name="request_date_from" eval="(datetime.today().date() + relativedelta(day=1, weekday=0))"/>
        <field name="request_date_to" eval="(datetime.today().date() + relativedelta(day=1, weekday=0) + relativedelta(weekday=2))"/>
        <field name="employee_id" ref="hr.employee_admin"/>
    </record>

    <record id="hr_holidays_sl" model="hr.leave">
        <field name="name">Doctor Appointment</field>
        <field name="holiday_status_id" ref="holiday_status_sl"/>
        <field name="request_date_from" eval="(datetime.today().date() + relativedelta(day=20, weekday=0))"/>
        <field name="request_date_to" eval="(datetime.today().date() + relativedelta(day=20, weekday=0) + relativedelta(weekday=2))"/>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="state">confirm</field>
    </record>
    <function model="hr.leave" name="action_validate">
        <value eval="ref('hr_holidays.hr_holidays_sl')"/>
    </function>

    <record id="hr.employee_al" model="hr.employee">
        <field name="leave_manager_id" ref="base.user_admin"/>
    </record>
    <record id="hr.employee_mit" model="hr.employee">
        <field name="leave_manager_id" ref="base.user_admin"/>
    </record>
    <record id="hr.employee_qdp" model="hr.employee">
        <field name="leave_manager_id" ref="base.user_admin"/>
    </record>
    <record id="hr.employee_niv" model="hr.employee">
        <field name="leave_manager_id" ref="base.user_admin"/>
    </record>
    <record id="hr.employee_jve" model="hr.employee">
        <field name="leave_manager_id" ref="base.user_admin"/>
    </record>

    <!-- ++++++++++++++++++++++  Ronnie Hart  ++++++++++++++++++++++ -->


    <record id="hr_holidays_allocation_cl_al" model="hr.leave.allocation">
        <field name="name">Paid Time Off for Ronnie Hart</field>
        <field name="holiday_status_id" ref="holiday_status_cl"/>
        <field name="number_of_days">20</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="state">confirm</field>
        <field name="date_from" eval="time.strftime('%Y-1-1')"/>
        <field name="date_to" eval="time.strftime('%Y-12-31')"/>
    </record>

    <record id="hr_holidays_allocation_pl_al" model="hr.leave.allocation">
        <field name="name">Parental Leaves</field>
        <field name="holiday_status_id" ref="hr_holiday_status_dv"/>
        <field name="number_of_days">10</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="state">confirm</field>
        <field name="date_from" eval="time.strftime('%Y-1-1')"/>
        <field name="date_to" eval="time.strftime('%Y-12-31')"/>
    </record>

    <record id="hr_holidays_vc_al" model="hr.leave.allocation">
        <field name="name">Soft Skills Training</field>
        <field name="holiday_status_id" ref="holiday_status_training"/>
        <field name="number_of_days">12</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="state">confirm</field>
        <field name="date_from" eval="time.strftime('%Y-1-1')"/>
        <field name="date_to" eval="time.strftime('%Y-12-31')"/>
    </record>
    <function model="hr.leave.allocation" name="action_validate">
        <value eval="[ref('hr_holidays_allocation_cl_al'), ref('hr_holidays_allocation_pl_al'), ref('hr_holidays_vc_al')]"/>
    </function>

    <!-- leave request -->
    <record id="hr_holidays_cl_al" model="hr.leave">
        <field name="name">Trip with Friends</field>
        <field name="holiday_status_id" ref="holiday_status_cl"/>
        <field name="request_date_from" eval="time.strftime('%Y-%m-14')"/>
        <field name="request_date_to" eval="time.strftime('%Y-%m-20')"/>
        <field name="employee_id" ref="hr.employee_al"/>
    </record>
    <function model="hr.leave" name="action_validate">
        <value eval="ref('hr_holidays.hr_holidays_cl_al')"/>
    </function>

    <record id="hr_holidays_sl_al" model="hr.leave">
        <field name="name">Dentist appointment</field>
        <field name="holiday_status_id" ref="holiday_status_sl"/>
        <field name="request_date_from" eval="(datetime.today().date() + relativedelta(months=1, day=17, weekday=0))"/>
        <field name="request_date_to" eval="(datetime.today().date() + relativedelta(months=1, day=17, weekday=0) + relativedelta(weekday=2))"/>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="state">confirm</field>
    </record>
    <function model="hr.leave" name="action_validate">
        <value eval="ref('hr_holidays.hr_holidays_sl_al')"/>
    </function>

    <!-- ++++++++++++++++++++++  Anita Oliver  ++++++++++++++++++++++ -->

    <record id="hr_holidays_allocation_cl_mit" model="hr.leave.allocation">
        <field name="name">Paid Time Off for Anita Oliver</field>
        <field name="holiday_status_id" ref="holiday_status_cl"/>
        <field name="number_of_days">20</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="state">confirm</field>
        <field name="date_from" eval="time.strftime('%Y-1-1')"/>
        <field name="date_to" eval="time.strftime('%Y-12-31')"/>
    </record>
    <function model="hr.leave.allocation" name="action_validate">
        <value eval="[ref('hr_holidays_allocation_cl_mit')]"/>
    </function>

    <record id="hr_holidays_vc_mit" model="hr.leave.allocation">
        <field name="name">Compliance Training</field>
        <field name="holiday_status_id" ref="holiday_status_training"/>
        <field name="number_of_days">7</field>
        <field name="state">confirm</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date_from" eval="time.strftime('%Y-1-1')"/>
        <field name="date_to" eval="time.strftime('%Y-12-31')"/>
    </record>

    <!-- leave request -->
    <record id="hr_holidays_cl_mit" model="hr.leave">
        <field name="name">Trip to Paris</field>
        <field name="holiday_status_id" ref="holiday_status_cl"/>
        <field name="request_date_from" eval="time.strftime('%Y-%m-22')"/>
        <field name="request_date_to" eval="time.strftime('%Y-%m-28')"/>
        <field name="employee_id" ref="hr.employee_mit"/>
    </record>
    <function model="hr.leave" name="action_validate">
        <value eval="ref('hr_holidays.hr_holidays_cl_mit')"/>
    </function>

    <record id="hr_holidays_cl_mit_2" model="hr.leave">
        <field name="name">Trip</field>
        <field name="holiday_status_id" ref="holiday_status_cl"/>
        <field name="request_date_from" eval="(datetime.today().date() + relativedelta(day=5, weekday=0))"/>
        <field name="request_date_to" eval="(datetime.today().date() + relativedelta(day=5, weekday=0) + relativedelta(weekday=2))"/>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="state">confirm</field>
    </record>
    <function model="hr.leave" name="action_approve">
        <value eval="ref('hr_holidays.hr_holidays_cl_mit_2')"/>
    </function>

    <!-- ++++++++++++++++++++++  Marc Demo  ++++++++++++++++++++++ -->

    <record id="hr_holidays_allocation_cl_qdp" model="hr.leave.allocation">
        <field name="name">Paid Time Off for Marc Demo</field>
        <field name="holiday_status_id" ref="holiday_status_cl"/>
        <field name="number_of_days">20</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="state">confirm</field>
        <field name="date_from" eval="time.strftime('%Y-1-1')"/>
        <field name="date_to" eval="time.strftime('%Y-12-31')"/>
    </record>

    <record id="hr_holidays_vc_qdp" model="hr.leave.allocation">
        <field name="name">Time Management Training</field>
        <field name="holiday_status_id" ref="holiday_status_training"/>
        <field name="number_of_days">7</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="state">confirm</field>
        <field name="date_from" eval="time.strftime('%Y-1-1')"/>
        <field name="date_to" eval="time.strftime('%Y-12-31')"/>
    </record>
    <function model="hr.leave.allocation" name="action_validate">
        <value eval="[ref('hr_holidays.hr_holidays_allocation_cl_qdp'), ref('hr_holidays.hr_holidays_vc_qdp')]"/>
    </function>

    <!-- leave request -->
    <record id="hr_holidays_cl_qdp" model="hr.leave">
        <field name="name">Sick day</field>
        <field name="holiday_status_id" ref="holiday_status_sl"/>
        <field name="request_date_from" eval="(datetime.today().date()+relativedelta(months=1, day=3, weekday=0))"/>
        <field name="request_date_to" eval="(datetime.today().date()+relativedelta(months=1, day=3, weekday=0) + relativedelta(weekday=2))"/>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="state">confirm</field>
    </record>
    <function model="hr.leave" name="action_validate">
        <value eval="ref('hr_holidays.hr_holidays_cl_qdp')"/>
    </function>

    <record id="hr_holidays_sl_qdp" model="hr.leave">
        <field name="name">Sick day</field>
        <field name="holiday_status_id" ref="holiday_status_sl"/>
        <field name="request_date_from" eval="(datetime.today().date() + relativedelta(day=1, weekday=0))"/>
        <field name="request_date_to" eval="(datetime.today().date() + relativedelta(day=1, weekday=0) + relativedelta(days=2))"/>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="state">confirm</field>
    </record>
    <function model="hr.leave" name="action_validate">
        <value eval="ref('hr_holidays.hr_holidays_sl_qdp')"/>
    </function>

    <!-- ++++++++++++++++++++++  Audrey Peterson  ++++++++++++++++++++++ -->

    <record id="hr_holidays_allocation_cl_fpi" model="hr.leave.allocation">
        <field name="name">Paid Time Off for Audrey Peterson</field>
        <field name="holiday_status_id" ref="holiday_status_cl"/>
        <field name="number_of_days">20</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="state">confirm</field>
        <field name="date_from" eval="time.strftime('%Y-1-1')"/>
        <field name="date_to" eval="time.strftime('%Y-12-31')"/>
    </record>
    <function model="hr.leave.allocation" name="action_validate">
        <value eval="[ref('hr_holidays.hr_holidays_allocation_cl_fpi')]"/>
    </function>

    <record id="hr_holidays_vc_fpi" model="hr.leave.allocation">
        <field name="name">Consulting Training</field>
        <field name="holiday_status_id" ref="holiday_status_training"/>
        <field name="number_of_days">7</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="state">confirm</field>
        <field name="date_from" eval="time.strftime('%Y-1-1')"/>
        <field name="date_to" eval="time.strftime('%Y-12-31')"/>
    </record>

    <!-- ++++++++++++++++++++++   Olivia  ++++++++++++++++++++++ -->

    <record id="hr_holidays_allocation_cl_vad" model="hr.leave.allocation">
        <field name="name">Paid Time Off for Olivia</field>
        <field name="holiday_status_id" ref="holiday_status_cl"/>
        <field name="number_of_days">20</field>
        <field name="employee_id" ref="hr.employee_niv"/>
        <field name="state">confirm</field>
        <field name="date_from" eval="time.strftime('%Y-1-1')"/>
        <field name="date_to" eval="time.strftime('%Y-12-31')"/>
    </record>

    <record id="hr_holidays_vc_vad" model="hr.leave.allocation">
        <field name="name">Software Development Training</field>
        <field name="holiday_status_id" ref="holiday_status_training"/>
        <field name="number_of_days">5</field>
        <field name="employee_id" ref="hr.employee_niv"/>
        <field name="state">confirm</field>
        <field name="date_from" eval="time.strftime('%Y-1-1')"/>
        <field name="date_to" eval="time.strftime('%Y-12-31')"/>
    </record>
    <function model="hr.leave.allocation" name="action_validate">
        <value eval="[ref('hr_holidays.hr_holidays_allocation_cl_vad'), ref('hr_holidays.hr_holidays_vc_vad')]"/>
    </function>

    <record id="hr_holidays_cl_vad" model="hr.leave">
        <field name="name">Trip to London</field>
        <field name="holiday_status_id" ref="holiday_status_cl"/>
        <field name="request_date_from" eval="time.strftime('%Y-%m-09')"/>
        <field name="request_date_to" eval="time.strftime('%Y-%m-16')"/>
        <field name="employee_id" ref="hr.employee_niv"/>
        <field name="state">confirm</field>
    </record>
    <function model="hr.leave" name="action_validate">
        <value eval="ref('hr_holidays.hr_holidays_cl_vad')"/>
    </function>

    <record id="hr_holidays_sl_vad" model="hr.leave">
        <field name="name">Doctor Appointment</field>
        <field name="holiday_status_id" ref="holiday_status_sl"/>
        <field name="request_date_from" eval="(datetime.today().date() + relativedelta(day=25, weekday=0))"/>
        <field name="request_date_to" eval="(datetime.today().date() + relativedelta(day=25, weekday=0) + relativedelta(weekday=2))"/>
        <field name="employee_id" ref="hr.employee_niv"/>
        <field name="state">confirm</field>
    </record>
    <function model="hr.leave" name="action_validate">
        <value eval="ref('hr_holidays.hr_holidays_sl_vad')"/>
    </function>

    <!-- ++++++++++++++++++++++   Kim  ++++++++++++++++++++++ -->

    <record id="hr_holidays_allocation_cl_kim" model="hr.leave.allocation">
        <field name="name">Paid Time Off for Kim</field>
        <field name="holiday_status_id" ref="holiday_status_cl"/>
        <field name="number_of_days">20</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="state">confirm</field>
        <field name="date_from" eval="time.strftime('%Y-1-1')"/>
        <field name="date_to" eval="time.strftime('%Y-12-31')"/>
    </record>

    <record id="hr_holidays_vc_kim" model="hr.leave.allocation">
        <field name="name">Onboarding Training</field>
        <field name="holiday_status_id" ref="holiday_status_training"/>
        <field name="number_of_days">5</field>
        <field name="state">confirm</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date_from" eval="time.strftime('%Y-1-1')"/>
        <field name="date_to" eval="time.strftime('%Y-12-31')"/>
    </record>

    <!-- leave request -->
    <record id="hr_holidays_sl_kim" model="hr.leave">
        <field name="name">Dentist appointment</field>
        <field name="holiday_status_id" ref="holiday_status_sl"/>
        <field name="request_date_from" eval="(datetime.today().date() + relativedelta(months=1, day=1, weekday=0))"/>
        <field name="request_date_to" eval="(datetime.today().date() + relativedelta(months=1, day=1, weekday=0))"/>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="state">confirm</field>
    </record>
    <function model="hr.leave" name="action_validate">
        <value eval="ref('hr_holidays.hr_holidays_sl_kim')"/>
    </function>

    <record id="hr_holidays_sl_kim_2" model="hr.leave">
        <field name="name">Second dentist appointment</field>
        <field name="holiday_status_id" ref="holiday_status_sl"/>
        <field name="request_date_from" eval="(datetime.today().date()+relativedelta(months=4, day=1, weekday=2))"/>
        <field name="request_date_to" eval="(datetime.today().date()+relativedelta(months=4, day=1, weekday=2))"/>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="state">confirm</field>
    </record>
    <function model="hr.leave" name="action_validate">
        <value eval="ref('hr_holidays.hr_holidays_sl_kim_2')"/>
    </function>

    <!-- Public time off -->
    <record id="resource_public_time_off_1" model="resource.calendar.leaves">
        <field name="name">Public Time Off</field>
        <field name="company_id" ref="base.main_company"/>
        <field name="calendar_id" ref="resource.resource_calendar_std"/>
        <field name="date_from" eval="time.strftime('%Y-02-13 05:00:00')"/>
        <field name="date_to" eval="time.strftime('%Y-02-13 17:00:00')"/>
    </record>

    <!-- Mandatory day -->
    <record id="hr_leave_mandatory_day_1" model="hr.leave.mandatory.day">
        <field name="name">Company Celebration</field>
        <field name="company_id" ref="base.main_company"/>
        <field name="start_date" eval="(datetime.today() + relativedelta(days=+7)).strftime('%Y-%m-%d 07:00:00')"></field>
        <field name="end_date" eval="(datetime.today() + relativedelta(days=+7)).strftime('%Y-%m-%d 16:00:00')"></field>
        <field name="color">9</field>
    </record>
</data>
</odoo>
