# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_matrix
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid ""
"<small>Switch to the \"else\" condition of this block to view or edit the "
"table.</small>"
msgstr ""
"<small><PERSON><PERSON> ke kondisi \"else\" dari blok ini untuk melihat atau mengedit "
"tabel.</small>"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid "<strong>Product matrix block</strong>"
msgstr "<strong>Blok product matrix</strong>"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_color_1
msgid "Blue"
msgstr "Biru"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid "Cell name"
msgstr "Nama cell"

#. module: product_matrix
#. odoo-javascript
#: code:addons/product_matrix/static/src/xml/product_matrix_dialog.xml:0
msgid "Choose Product Variants"
msgstr "Pilih Varian Produk"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid "Column name"
msgstr "Nama kolom"

#. module: product_matrix
#. odoo-javascript
#: code:addons/product_matrix/static/src/xml/product_matrix_dialog.xml:0
msgid "Confirm"
msgstr "Konfirmasi"

#. module: product_matrix
#. odoo-javascript
#: code:addons/product_matrix/static/src/xml/product_matrix_dialog.xml:0
msgid "Discard"
msgstr "Buang"

#. module: product_matrix
#: model:product.attribute,name:product_matrix.product_attribute_gender
msgid "Gender"
msgstr "Jenis kelamin"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_m
msgid "Men"
msgstr "Pria"

#. module: product_matrix
#: model:product.template,name:product_matrix.matrix_product_template_shirt
msgid "My Company Tshirt (GRID)"
msgstr "Tshirt Perusahaan Saya (GRID)"

#. module: product_matrix
#. odoo-javascript
#: code:addons/product_matrix/static/src/xml/product_matrix.xml:0
msgid "Not available"
msgstr "Tidak tersedia"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_color_2
msgid "Pink"
msgstr "Pink"

#. module: product_matrix
#: model:ir.model,name:product_matrix.model_product_template
msgid "Product"
msgstr "Produk"

#. module: product_matrix
#: model:ir.model,name:product_matrix.model_product_template_attribute_value
msgid "Product Template Attribute Value"
msgstr "Nilai Atribut Templat Produk"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_color_4
msgid "Rainbow"
msgstr "Pelangi"

#. module: product_matrix
#: model:product.template,description_sale:product_matrix.matrix_product_template_shirt
msgid "Show your company love around you =)."
msgstr "Show your company love around you =)."

#. module: product_matrix
#: model:product.attribute,name:product_matrix.product_attribute_size
msgid "Size"
msgstr "Ukuran"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid ""
"The matrix of product variants of this order will be displayed here, if "
"there are any."
msgstr ""
"Matrix varian produk dari pesanan ini akan ditampilkan di sini, bila ada."

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.extra_price
msgid "Variant price"
msgstr "Harga varian"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_w
msgid "Women"
msgstr "Wanita"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_size_xl
msgid "XL"
msgstr "XL"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_size_xs
msgid "XS"
msgstr "XS"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_color_3
msgid "Yellow"
msgstr "Kuning"
