# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_timesheet_enterprise
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# NumerSpiral HBG, 2025
# <PERSON> <<EMAIL>>, 2025
# <PERSON>, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/views/timesheet_leaderboard_dialog/timesheet_leaderboard_dialog.js:0
msgid "%(billableTime)s / %(billable_time_target)s (%(billingRate)s%)"
msgstr ""

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/components/timesheet_leaderboard/timesheet_leaderboard.js:0
msgid "%(currentBillableTimeText)s / %(currentTargetTotalTimeText)s "
msgstr ""

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/views/pivot/timesheet_analysis_pivot_model.js:0
msgid "%(employee_name)s (%(target)sh / month)"
msgstr ""

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/components/timesheet_leaderboard/timesheet_leaderboard.js:0
msgid "%(percentage)s%"
msgstr ""

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/components/timesheet_leaderboard/timesheet_leaderboard.js:0
msgid "%(totalTime)s d"
msgstr ""

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/components/timesheet_leaderboard/timesheet_leaderboard.js:0
msgid "%(totalTime)s days"
msgstr "%(totalTime)s dias"

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/components/timesheet_leaderboard/timesheet_leaderboard.js:0
msgid "%(totalTime)s h"
msgstr ""

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/components/timesheet_leaderboard/timesheet_leaderboard.js:0
msgid "%(totalTime)s hours"
msgstr ""

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/components/timesheet_leaderboard/timesheet_leaderboard.js:0
msgid "(%(percentage)s%)"
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.view_employee_form
msgid ""
"<span class=\"m-14 w-100\" "
"groups=\"hr_timesheet.group_hr_timesheet_approver\">per month</span>"
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.sale_timesheet_enterprise_advance_payment_inv_timesheet_view_form
msgid ""
"<span>\n"
"                            Some employees currently have active timers for the timesheets you are trying to invoice.\n"
"                            Creating the invoice will automatically stop these timers.\n"
"                        </span>"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields.selection,name:sale_timesheet_enterprise.selection__res_config_settings__invoiced_timesheet__all
msgid "All recorded timesheets"
msgstr "Todos os registos de horas registados"

#. module: sale_timesheet_enterprise
#: model:ir.model,name:sale_timesheet_enterprise.model_account_analytic_line
msgid "Analytic Line"
msgstr "Linha Analítica"

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/components/many2one_avatar_rank_field/many2one_avatar_rank_field.xml:0
msgid "Avatar"
msgstr "Avatar"

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,field_description:sale_timesheet_enterprise.field_res_config_settings__timesheet_show_rates
msgid "Billing Rate Indicators"
msgstr ""

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/views/timesheet_leaderboard_dialog/timesheet_leaderboard_dialog.js:0
#: model:ir.model.fields,field_description:sale_timesheet_enterprise.field_res_config_settings__timesheet_show_leaderboard
msgid "Billing Rate Leaderboard"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,field_description:sale_timesheet_enterprise.field_hr_employee__billable_time_target
msgid "Billing Time Target"
msgstr ""

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/components/timesheet_leaderboard/timesheet_leaderboard.xml:0
msgid "Billing:"
msgstr "Faturação:"

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.res_config_settings_view_form
msgid ""
"Boost productivity and competitiveness by displaying a leaderboard based on "
"the billing rates."
msgstr ""

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/views/timesheet_leaderboard_dialog/timesheet_leaderboard_dialog.xml:0
msgid "Close"
msgstr "Encerrar"

#. module: sale_timesheet_enterprise
#: model:ir.model,name:sale_timesheet_enterprise.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: sale_timesheet_enterprise
#: model:ir.model,name:sale_timesheet_enterprise.model_res_config_settings
msgid "Config Settings"
msgstr "Definições de Configuração"

#. module: sale_timesheet_enterprise
#: model:ir.ui.menu,name:sale_timesheet_enterprise.hr_timesheet_menu_configuration_settings
msgid "Configuration"
msgstr "Configuração"

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,field_description:sale_timesheet_enterprise.field_hr_timesheet_tip__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,field_description:sale_timesheet_enterprise.field_hr_timesheet_tip__create_date
msgid "Created on"
msgstr "Criado em"

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.timesheet_view_grid_by_invoice_type
msgid "Day"
msgstr "Dia"

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/components/timesheet_overtime_indication/timesheet_overtime_indication.js:0
msgid ""
"Difference between the allocated %(uom)s (%(allocated_hours)s) on the sales "
"order line and the %(uom)s spent (%(worked_hours)s) on all related projects "
"and tasks"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,field_description:sale_timesheet_enterprise.field_hr_timesheet_tip__display_name
msgid "Display Name"
msgstr "Nome a Exibir"

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,help:sale_timesheet_enterprise.field_project_task__portal_progress
msgid "Display progress of current task."
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model,name:sale_timesheet_enterprise.model_hr_employee
msgid "Employee"
msgstr "Funcionário"

#. module: sale_timesheet_enterprise
#: model:hr.timesheet.tip,name:sale_timesheet_enterprise.timesheet_tip_5
msgid ""
"Even small tasks like answering an email should be recorded and rounded up "
"to a quarter of an hour."
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,field_description:sale_timesheet_enterprise.field_hr_timesheet_tip__id
msgid "ID"
msgstr "Id."

#. module: sale_timesheet_enterprise
#: model:hr.timesheet.tip,name:sale_timesheet_enterprise.timesheet_tip_2
msgid ""
"If you help a colleague on a project, your time should be recorded on this "
"project's timesheet as well."
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.res_config_settings_view_form
msgid "Invoice"
msgstr "Fatura"

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.res_config_settings_view_form
msgid "Invoicing Policy"
msgstr "Política de Faturação"

#. module: sale_timesheet_enterprise
#: model:hr.timesheet.tip,name:sale_timesheet_enterprise.timesheet_tip_3
msgid ""
"It's part of the job to search for information. A customer can't expect you "
"to know everything on the spot. These hours should also be billable."
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model,name:sale_timesheet_enterprise.model_account_move_line
msgid "Journal Item"
msgstr "Item no Diário"

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,field_description:sale_timesheet_enterprise.field_hr_timesheet_tip__write_uid
msgid "Last Updated by"
msgstr "Última Atualização por"

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,field_description:sale_timesheet_enterprise.field_hr_timesheet_tip__write_date
msgid "Last Updated on"
msgstr "Última Atualização em"

#. module: sale_timesheet_enterprise
#. odoo-python
#: code:addons/sale_timesheet_enterprise/models/res_company.py:0
#: model:hr.timesheet.tip,name:sale_timesheet_enterprise.timesheet_tip_4
msgid "Make it a habit to record timesheets every day."
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.timesheet_view_grid_by_invoice_type
msgid "Month"
msgstr "Mês"

#. module: sale_timesheet_enterprise
#: model_terms:ir.actions.act_window,help:sale_timesheet_enterprise.hr_timesheet_tip_action
msgid "Motivate employees to fill timesheets with these tips."
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.actions.act_window,help:sale_timesheet_enterprise.hr_timesheet_tip_action
msgid "No tips found. Let's create one!"
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.project_sharing_inherit_project_task_view_tree
msgid "Progress"
msgstr "Progresso"

#. module: sale_timesheet_enterprise
#: model:ir.model,name:sale_timesheet_enterprise.model_project_project
msgid "Project"
msgstr "Projeto"

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/components/timesheet_leaderboard/timesheet_leaderboard.xml:0
msgid "Record timesheets to determine your billing rate!"
msgstr ""

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/components/timesheet_leaderboard/timesheet_leaderboard.xml:0
msgid "Record timesheets to earn your rank!"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model,name:sale_timesheet_enterprise.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "Fatura de Pagamentos de Adiantamento das Vendas"

#. module: sale_timesheet_enterprise
#: model:ir.model,name:sale_timesheet_enterprise.model_sale_order_line
msgid "Sales Order Line"
msgstr "Linhas da Ordem de Venda"

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.res_config_settings_view_form
msgid ""
"Set a billable time target for your employees. If their current rate falls "
"below their target, it will be highlighted in red."
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.res_config_settings_view_form
msgid "Set employee billable time targets"
msgstr ""

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/views/timesheet_leaderboard_dialog/timesheet_leaderboard_dialog.xml:0
msgid "Show less"
msgstr ""

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/views/timesheet_leaderboard_dialog/timesheet_leaderboard_dialog.xml:0
msgid "Show more"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,help:sale_timesheet_enterprise.field_res_config_settings__timesheet_show_rates
msgid "Show the billing indicators on My Timesheets view"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,help:sale_timesheet_enterprise.field_res_config_settings__timesheet_show_leaderboard
msgid "Show the leaderboard on My Timesheets view"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model,name:sale_timesheet_enterprise.model_project_task
msgid "Task"
msgstr "Tarefa"

#. module: sale_timesheet_enterprise
#: model:ir.model.constraint,message:sale_timesheet_enterprise.constraint_hr_employee_check_billable_time_target
msgid "The billable time target cannot be negative."
msgstr ""

#. module: sale_timesheet_enterprise
#. odoo-python
#: code:addons/sale_timesheet_enterprise/models/project_task.py:0
msgid "This Sale Order Item doesn't have a target value of planned hours."
msgstr ""

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/views/timesheet_leaderboard_dialog/timesheet_leaderboard_dialog.xml:0
msgid "This month"
msgstr "Este mês"

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.project_sharing_inherit_project_task_view_tree
msgid "Time Remaining"
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.project_sharing_inherit_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.timesheet_view_grid_by_invoice_type
msgid "Time Spent"
msgstr "Tempo despendido"

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.project_sharing_inherit_project_task_view_tree
msgid "Time Spent on Sub-tasks"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,help:sale_timesheet_enterprise.field_project_task__portal_subtask_effective_hours
msgid "Time spent on the sub-tasks (and their own sub-tasks) of this task."
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,help:sale_timesheet_enterprise.field_project_task__portal_effective_hours
msgid "Time spent on this task, excluding its sub-tasks."
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,help:sale_timesheet_enterprise.field_project_task__portal_total_hours_spent
msgid "Time spent on this task, including its sub-tasks."
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,field_description:sale_timesheet_enterprise.field_hr_employee__show_billable_time_target
msgid "Timesheet Show Rates"
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.timesheet_view_grid_by_invoice_type
msgid "Timesheets"
msgstr "Folhas de Horas"

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,field_description:sale_timesheet_enterprise.field_res_config_settings__invoiced_timesheet
msgid "Timesheets Invoicing"
msgstr "Faturar horas"

#. module: sale_timesheet_enterprise
#: model:ir.model,name:sale_timesheet_enterprise.model_hr_timesheet_tip
msgid "Timesheets Leaderboard Tip"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:hr.timesheet.tip,name:sale_timesheet_enterprise.timesheet_tip_1
msgid ""
"Timesheets are the lifeblood of our company; they have a direct impact on "
"revenues."
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.res_config_settings_view_form
msgid "Timesheets taken into account when invoicing your time"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,field_description:sale_timesheet_enterprise.field_hr_timesheet_tip__name
msgid "Tip Name"
msgstr ""

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/views/timesheet_leaderboard_dialog/timesheet_leaderboard_dialog.xml:0
msgid "Tip of the day"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.actions.act_window,name:sale_timesheet_enterprise.hr_timesheet_tip_action
#: model:ir.ui.menu,name:sale_timesheet_enterprise.hr_timesheet_menu_configuration_tips
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.hr_timesheet_tip_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.hr_timesheet_tip_view_tree
msgid "Tips"
msgstr "Gorjetas"

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/views/timesheet_leaderboard_dialog/timesheet_leaderboard_dialog.js:0
msgid "Total Time Leaderboard"
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.project_sharing_inherit_project_task_view_tree
msgid "Total Time Spent"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,help:sale_timesheet_enterprise.field_project_task__portal_remaining_hours
msgid ""
"Total remaining time, can be re-estimated periodically by the assignee of "
"the task."
msgstr ""
"Tempo total restante, pode ser re-estimado periodicamente pelo responsável "
"pela tarefa."

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/components/timesheet_leaderboard/timesheet_leaderboard.xml:0
msgid "Total:"
msgstr "Total:"

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/views/timesheet_leaderboard_dialog/timesheet_leaderboard_dialog.js:0
msgid "Total: %(totalTime)s"
msgstr ""

#. module: sale_timesheet_enterprise
#: model:ir.model.fields.selection,name:sale_timesheet_enterprise.selection__res_config_settings__invoiced_timesheet__approved
msgid "Validated timesheets only"
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.timesheet_view_grid_by_invoice_type
msgid "Week"
msgstr "Semana"

#. module: sale_timesheet_enterprise
#: model:ir.model.fields,help:sale_timesheet_enterprise.field_res_config_settings__invoiced_timesheet
msgid ""
"With the 'all recorded timesheets' option, all timesheets will be invoiced without distinction, even if they haven't been validated. Additionally, all timesheets will be accessible in your customers' portal. \n"
"When you choose the 'validated timesheets only' option, only the validated timesheets will be invoiced and appear in your customers' portal."
msgstr ""

#. module: sale_timesheet_enterprise
#: model_terms:ir.ui.view,arch_db:sale_timesheet_enterprise.hr_timesheet_tip_view_tree
msgid "e.g. Make it a habit to record timesheets every day."
msgstr ""

#. module: sale_timesheet_enterprise
#. odoo-javascript
#: code:addons/sale_timesheet_enterprise/static/src/components/timesheet_leaderboard/timesheet_leaderboard.xml:0
msgid "o_timesheet_leaderboard_confetti"
msgstr ""
