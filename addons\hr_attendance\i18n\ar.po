# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_attendance
# 
# Translators:
# Wil Odoo, 2025
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
msgid "\"Check Out\" time cannot be earlier than \"Check In\" time."
msgstr "لا يمكن أن يكون وقت \"تسجيل الخروج\" قبل وقت \"تسجيل الحضور\"."

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
msgid "%(worked_hours)s (%(check_in)s-%(check_out)s)"
msgstr "%(worked_hours)s (%(check_in)s-%(check_out)s)"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "'Documentation'"
msgstr "'التوثيق'"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_hr_attendance_kanban
msgid "<i class=\"fa fa-calendar me-1\" aria-label=\"Period\" role=\"img\" title=\"Period\"/>"
msgstr ""
"<i class=\"fa fa-calendar me-1\" aria-label=\"Period\" role=\"img\" "
"title=\"الفترة \"/>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Installation Manual"
msgstr "<i class=\"fa fa-fw fa-arrow-right\"/>دليل التركيب "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
msgid ""
"<span class=\"fa fa-circle text-success me-1\" role=\"img\" aria-"
"label=\"Available\" title=\"Available\"/>"
msgstr ""
"<span class=\"fa fa-circle text-success me-1\" role=\"img\" aria-"
"label=\"Available\" title=\"متاح \"/>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
msgid ""
"<span class=\"fa fa-circle text-warning me-1\" role=\"img\" aria-label=\"Not available\" title=\"Not available\">\n"
"                                    </span>"
msgstr ""
"<span class=\"fa fa-circle text-warning me-1\" role=\"img\" aria-label=\"Not available\" title=\"غير متاح \">\n"
"                                    </span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span class=\"me-2\">Time Period </span>"
msgstr "<span class=\"me-2\">الفترة الزمنية </span> "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span class=\"me-2\">Time Period</span>"
msgstr "<span class=\"me-2\">الفترة الزمنية</span> "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span class=\"me-2\">Tolerance</span>"
msgstr "<span class=\"me-2\">التسامح</span> "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span class=\"ms-2\">Hours</span>"
msgstr "<span class=\"ms-2\">ساعات</span> "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"<span class=\"ms-2\">Minutes</span>\n"
"                                <br/>\n"
"                                <br/>"
msgstr ""
"<span class=\"ms-2\">دقائق</span>\n"
"                                <br/>\n"
"                                <br/> "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid ""
"<span class=\"o_stat_text\">\n"
"                            This Month\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            هذا الشهر\n"
"                        </span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "<span class=\"o_stat_text\">Extra Hours</span>"
msgstr "<span class=\"o_stat_text\">الساعات الإضافية</span> "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span> Minutes</span>"
msgstr "<span> الدقائق</span> "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span> seconds</span>"
msgstr "<span> ثواني</span> "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__absence_management
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__absence_management
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Absence Management"
msgstr "إدارة الغياب "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_needaction
msgid "Action Needed"
msgstr "إجراء مطلوب"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_management_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Active Employees"
msgstr "الموظفين النشطين "

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_employee_attendance_action_kanban
msgid ""
"Add a few employees to be able to select an employee here and perform his check in / check out.\n"
"                To create employees go to the Employees menu."
msgstr ""
"قم بإضافة بضعة موظفين لتتمكن من اختيار موظف وتسجيل الحضور/الخروج.\n"
"                لإنشاء الموظفين اذهب إلى قائمة الموظفين. "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__adjustment
msgid "Adjustment"
msgstr "التعديلات"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_manager
msgid "Administrator"
msgstr "المدير "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
msgid "All"
msgstr "الكل"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.js:0
msgid "All departments"
msgstr "كافة الأقسام "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Allow Users to Check in/out from Odoo."
msgstr "السماح للمستخدمين بتسجيل الدخول/الخروج من أودو. "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Allow a period of time (around working hours) where extra time will not be "
"counted, in benefit of the company"
msgstr ""
"اسمح بفترة زمنية (ضمن ساعات العمل) لا يُحسَب فيها الوقت الإضافي، لصالح "
"الشركة "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Allow a period of time (around working hours) where extra time will not be "
"deducted, in benefit of the employee"
msgstr ""
"اسمح بفترة زمنية (ضمن ساعات العمل) لا يُختصم فيها الوقت الإضافي، لصالح "
"الموظف "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
msgid "Amount of extra hours"
msgstr "كمية الساعات الإضافية "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree_management
msgid "Approve"
msgstr "موافقة"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Approve Extra Hours"
msgstr "الموافقة على الساعات الإضافية "

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__overtime_status__approved
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_management_view_filter
msgid "Approved"
msgstr "تمت الموافقة "

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_overtime_validation__by_manager
msgid "Approved by Manager"
msgstr "تمت الموافقة من قِبَل المدير "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_management_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Archived Employees"
msgstr "الموظفون المؤرشفون "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "At Work"
msgstr "في العمل "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
#: code:addons/hr_attendance/static/src/views/attendance_helper_view.xml:0
#: model:ir.model,name:hr_attendance.model_hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__attendance_ids
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_tree_inherit_leave
msgid "Attendance"
msgstr "الحاضرين "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_from_systray
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_from_systray
msgid "Attendance From Systray"
msgstr "الحضور من Systray "

#. module: hr_attendance
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_install_kiosk_pwa
msgid "Attendance Kiosk"
msgstr "كشك الحضور "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_kiosk_delay
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_kiosk_delay
msgid "Attendance Kiosk Delay"
msgstr "تأخير كشك تسجيل الحضور "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_kiosk_key
msgid "Attendance Kiosk Key"
msgstr "مفتاح كشك الحضور "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_kiosk_url
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_kiosk_url
msgid "Attendance Kiosk Url"
msgstr "عنوان Url لكشك الحضور "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__attendance_manager_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__attendance_manager_id
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__attendance_manager_id
msgid "Attendance Manager"
msgstr "مدير الحضور "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_kiosk_mode
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_kiosk_mode
msgid "Attendance Mode"
msgstr "وضع الحضور "

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_attendance_overtime
msgid "Attendance Overtime"
msgstr "الوقت الإضافي للحضور "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__attendance_state
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__attendance_state
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__attendance_state
msgid "Attendance Status"
msgstr "حالة الحضور"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Attendance with barcode"
msgstr "تسجيل الحضور باستخدام الباركود "

#. module: hr_attendance
#: model:ir.actions.server,name:hr_attendance.hr_attendance_check_out_cron_ir_actions_server
msgid "Attendance: Automatically check-out employees"
msgstr "الحضور: تسجيل خروج الموظفين تلقائياً "

#. module: hr_attendance
#: model:ir.actions.server,name:hr_attendance.hr_attendance_absence_cron_ir_actions_server
msgid "Attendance: Detect Absences for employees"
msgstr "الحضور: الكشف عن حالات الغياب للموظفين "

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_reporting
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_root
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Attendances"
msgstr "الحضور"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_employee.py:0
#: code:addons/hr_attendance/models/res_users.py:0
msgid "Attendances This Month"
msgstr "الحضور هذا الشهر "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Attendances from Backend"
msgstr "الحضور من الواجهة الخلفية "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__auto_check_out_tolerance
msgid "Auto Check Out Tolerance"
msgstr "التسامح عند تسجيل خروج الموظفين تلقائياً "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__auto_check_out
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__auto_check_out
msgid "Automatic Check Out"
msgstr "تسجيل خروج الموظفين تلقائياً "

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__out_mode__auto_check_out
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Automatic Check-Out"
msgstr "تسجيل خروج الموظفين تلقائياً "

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_overtime_validation__no_validation
msgid "Automatically Approved"
msgstr "تمت الموافقة تلقائياً "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Automatically Check-Out Employees based on their working schedule with an "
"additional tolerance."
msgstr ""
"قم بتسجيل خروج الموظفين تلقائياً بناءً على جداول أعمالهم مع المزيد من "
"التسامح. "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Automatically Checked-Out"
msgstr "تم تسجيل الخروج تلقائياً "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
msgid "Back"
msgstr "العودة "

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_barcode_source__back
msgid "Back Camera"
msgstr "الكاميرا الخلفية "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Badge with Barcode"
msgstr "شارة مع باركود "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Badge with Barcode on Tablet"
msgstr "شارة مع باركود على جهاز لوحي "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_pivot
msgid "Balance"
msgstr "الرصيد"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_kiosk_mode__barcode
msgid "Barcode / RFID"
msgstr "الباركود / رقاقات RFID "

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_kiosk_mode__barcode_manual
msgid "Barcode / RFID and Manual Selection"
msgstr "الباركود / رقاقات RFID والتحديد التلقائي "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_barcode_source
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_barcode_source
msgid "Barcode Source"
msgstr "مصدر الباركود "

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__in_country_name
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__out_country_name
msgid "Based on IP Address"
msgstr "بناء على عنوان IP"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_employee_base
msgid "Basic Employee"
msgstr "الموظف العادي "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
msgid "Before"
msgstr "قبل"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_browser
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "Browser"
msgstr "المتصفح "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Can be converted as Time Off (cfr Time Off configuration)."
msgstr "يمكن تحويلها إلى إجازة (ألقِ نظرة على تهيئة تطبيق الإجازات)."

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
msgid ""
"Cannot create new attendance record for %(empl_name)s, the employee hasn't "
"checked out since %(datetime)s"
msgstr ""
"لا يمكن إنشاء سجل حضور جديد لـ%(empl_name)s, لم يقم الموظف بتسجيل الخروج منذ"
" %(datetime)s "

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
msgid ""
"Cannot create new attendance record for %(empl_name)s, the employee was "
"already checked in on %(datetime)s"
msgstr ""
"لايمكن إنشاء سجل حضور جديد للموظف %(empl_name)s, قام الموظف بتسجيل دخوله "
"بالفعل، في %(datetime)s "

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_employee.py:0
msgid ""
"Cannot perform check out on %(empl_name)s, could not find corresponding "
"check in. Your attendances have probably been modified manually by human "
"resources."
msgstr ""
"لا يمكن تسجيل خروج %(empl_name)s، لم يتم العثور على تسجيل حضورك. قد يكون قد "
"تم تعديل حضورك يدويًا بواسطة الموارد البشرية. "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/check_in_out/check_in_out.xml:0
msgid "Check IN"
msgstr "تسجيل الحضور"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__check_in
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_check_in
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__last_check_in
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__last_check_in
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "Check In"
msgstr "تسجيل الحضور"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/check_in_out/check_in_out.xml:0
msgid "Check OUT"
msgstr "تسجيل الخروج"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__check_out
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_check_out
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__last_check_out
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__last_check_out
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "Check Out"
msgstr "تسجيل الخروج"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
msgid "Check in"
msgstr "تسجيل الحضور"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
msgid "Check out"
msgstr "تسجيل الخروج "

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_employee__attendance_state__checked_in
msgid "Checked in"
msgstr "تم تسجيل الحضور"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
msgid "Checked in at"
msgstr "تم تسجيل الحضور في"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_employee__attendance_state__checked_out
msgid "Checked out"
msgstr "تم تسجيل الخروج"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
msgid "Checked out at"
msgstr "تم تسجيل الخروج في"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Choose how long the greeting message will be displayed."
msgstr "اختر المدة التي ترغب أن تظهر فيها رسالة الترحيب. "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Choose how to record attendances"
msgstr "اختيار الطريقة التي ترغب في تسجيل الحضور بها "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_city
msgid "City"
msgstr "المدينة"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "City (In)"
msgstr "المدينة (الداخل) "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "City (Out)"
msgstr "المدينة (الخارج) "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Close"
msgstr "إغلاق "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__color
msgid "Color"
msgstr "اللون"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_company
msgid "Companies"
msgstr "الشركات"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__company_id
msgid "Company"
msgstr "الشركة "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Company Logo"
msgstr "شعار الشركة "

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_settings
msgid "Configuration"
msgstr "التهيئة "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Connect an RFID reader, and scan a token."
msgstr "قم بتوصيل قارئ RFID، وامسح الرمز. "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_country_name
msgid "Country"
msgstr "الدولة"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Country (In)"
msgstr "الدولة (الداخل) "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Country (Out)"
msgstr "الدولة (الخارج) "

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_employee_attendance_action_kanban
msgid "Create a new employee"
msgstr "إنشاء موظف جديد"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__create_uid
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__create_date
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "Currently Working"
msgstr "يعمل حالياً "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_overtime_search
msgid "Date"
msgstr "التاريخ "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__date
msgid "Day"
msgstr "اليوم"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Define the camera used for the barcode scan."
msgstr "قم بتحديد الكاميرا التي يتم استخدامها لمسح الباركود. "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Define the way the user will be identified by the application."
msgstr "قم بتحديد الطريقة التي سيتم تعريف المستخدم بها من قِبَل التطبيق. "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Departement"
msgstr "القسم "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__department_id
msgid "Department"
msgstr "القسم"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_pivot
msgid "Difference"
msgstr "الفرق"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__hr_attendance_display_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__hr_attendance_display_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__display_extra_hours
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Display Extra Hours"
msgstr "عرض الساعات الإضافية "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Display Extra Hours in Kiosk mode and on User profile."
msgstr "قم بعرض الساعات الإضافية في وضع الكشك في الملف التعريفي للمستخدم. "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Display Extra Hours."
msgstr "عرض الساعات الإضافية. "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__display_name
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Display Time"
msgstr "عرض الوقت "

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
msgid ""
"Do not have access, user cannot edit the attendances that are not his own."
msgstr ""
"لا تلك صلاحيات الوصول. لا يمكن للمستخدم تحرير سجلات الحضور التي ليست له. "

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_employee
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__employee_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__employee_id
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_management_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_overtime_search
msgid "Employee"
msgstr "الموظف"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
msgid "Employee Avatar"
msgstr "الصورة الرمزية للموظف "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_kiosk_use_pin
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_kiosk_use_pin
msgid "Employee PIN Identification"
msgstr "رمز تعريف الموظف "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree_management
msgid "Employee attendances"
msgstr "حضور الموظف"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_employee_attendance_action_kanban
msgid "Employees"
msgstr "الموظفون"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
msgid "Error: could not find corresponding employee."
msgstr "خطأ: تعذًر العثور على الموظف المقابل."

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Errors"
msgstr "الأخطاء "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__expected_hours
msgid "Expected Hours"
msgstr "الساعات المتوقعة "

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_overtime_action
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__validated_overtime_hours
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__duration
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_validated_hours_employee_simple_tree_view
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree_management
msgid "Extra Hours"
msgstr "الساعات الإضافية "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__duration_real
msgid "Extra Hours (Real)"
msgstr "الساعات الإضافية (الحقيقية) "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_overtime_validation
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_overtime_validation
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Extra Hours Validation"
msgstr "تصديق الساعات الإضافية "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
msgid "Extra hours"
msgstr "الساعات الإضافية "

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance_overtime__duration_real
msgid "Extra-hours including the threshold duration"
msgstr "الساعات الإضافية من ضمنها مدة الحد الأقصى "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_follower_ids
msgid "Followers"
msgstr "المتابعين"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء) "

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
msgid "From %s"
msgstr "من %s "

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_barcode_source__front
msgid "Front Camera"
msgstr "الكاميرا الأمامية "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "GPS Coordinates"
msgstr "إحداثيات GPS "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Generate new URL"
msgstr "إنشاء رابط URL جديد "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/card_layout/card_layout.xml:0
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
msgid "Go back"
msgstr "العودة"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
msgid "Goodbye"
msgstr "إلى اللقاء"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_management_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_overtime_search
msgid "Group By"
msgstr "تجميع حسب"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__has_message
msgid "Has Message"
msgstr "يحتوي على رسالة "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "Hours"
msgstr "ساعات "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_last_month
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__hours_last_month
msgid "Hours Last Month"
msgstr "الساعات في الشهر الماضي "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_last_month_display
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__hours_last_month_display
msgid "Hours Last Month Display"
msgstr "عرض ساعات الشهر الماضي "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_previously_today
msgid "Hours Previously Today"
msgstr "الساعات السابقة اليوم "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
msgid "Hours Previously Today:"
msgstr "الساعات السابقة اليوم: "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_today
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__hours_today
msgid "Hours Today"
msgstr "الساعات اليوم "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
msgid "Hours Today:"
msgstr "الساعات اليوم: "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Hr Attendance Search"
msgstr "بحث حضور الموارد البشرية"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__id
msgid "ID"
msgstr "المُعرف"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_ip_address
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "IP Address"
msgstr "عنوان IP"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Identify Manually"
msgstr "التعريف يدوياً "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"If checked, days not covered by an attendance will be visible in the Report."
msgstr "إذا تم تحديدها، ستظهر الأيام التي لا يغطيها الحضور في التقرير."

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__message_needaction
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة عليك رؤيتها. "

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__message_has_error
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Input Mode (In)"
msgstr "وضع الإدخال (الداخل) "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Input Mode (Out)"
msgstr "وضع الإدخال (الخارج) "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Install"
msgstr "تثبيت"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
msgid "Invalid request"
msgstr "طلب غير صالح "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__in_mode__kiosk
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__out_mode__kiosk
msgid "Kiosk"
msgstr "كشك "

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_action_open_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Kiosk Mode"
msgstr "وضع الكشك"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Kiosk Mode Adress"
msgstr "عنوان وضع الكشك "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Kiosk Settings"
msgstr "إعدادات الكشك "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_overtime_search
msgid "Last 3 Months"
msgstr "آخر 3 أشهر"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_attendance_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__last_attendance_id
msgid "Last Attendance"
msgstr "آخر حضور "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_attendance_worked_hours
msgid "Last Attendance Worked Hours"
msgstr "الساعات التي تم عملها في آخر حضور "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__write_uid
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__write_date
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_latitude
msgid "Latitude"
msgstr "خط العرض الجغرافي"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Latitude (In)"
msgstr "خط العرض الجغرافي (الداخل) "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Latitude (Out)"
msgstr "خط العرض الجغرافي (الخارج) "

#. module: hr_attendance
#: model:ir.actions.server,name:hr_attendance.action_load_demo_data
msgid "Load demo data"
msgstr "تحميل البيانات التجريبية"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/views/attendance_helper_view.xml:0
msgid "Load sample data"
msgstr "تحميل البيانات التجريبية "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "Localisation"
msgstr "الأقلمة "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_longitude
msgid "Longitude"
msgstr "خط الطول الجغرافي"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Longitude (In)"
msgstr "خط الطول الجغرافي (الداخل) "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Longitude (Out)"
msgstr "خط الطول الجغرافي (الخارج) "

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_management_action
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_view_attendances_management
msgid "Management"
msgstr "الإدارة"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Manager"
msgstr "المدير "

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__in_mode__manual
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__out_mode__manual
msgid "Manual"
msgstr "يدوي"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Manual Attendance"
msgstr "تسجيل الحضور يدوياً "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Manual Attendance or with barcode"
msgstr "تسجيل الحضور يدوياً أو باستخدام الباركود "

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_kiosk_mode__manual
msgid "Manual Selection"
msgstr "اختيار يدوي"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Manually (optional PIN)"
msgstr "يدوياً (رمز PIN اختياري) "

#. module: hr_attendance
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_greeting_message
msgid "Message"
msgstr "الرسالة"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Method"
msgstr "الطريقة "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_mode
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "Mode"
msgstr "الوضع"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Modes"
msgstr "الأوضاع "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_management_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "My Attendances"
msgstr "حضوري"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_management_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "My Team"
msgstr "فريقي"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "No Badge defined on employees. Set one to test."
msgstr "لا توجد شارة محددة لدى الموظفين. قم بتعيين واحدة لاختبارها."

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__no_validated_overtime_hours
msgid "No Validated Overtime Hours"
msgstr "لا توجد ساعات عمل إضافية تم تصديقها "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/views/attendance_helper_view.xml:0
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_management_action
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_reporting
msgid "No attendance records found"
msgstr "تعذّر إيجاد سجلات حضور"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.js:0
msgid "No employee corresponding to Badge ID '%(barcode)s.'"
msgstr "لا يوجد موظف مقابل لمعرف الشارة '%(barcode)s.' "

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_overtime_action
msgid "No overtime records found"
msgstr "تعذّر إيجاد سجلات ساعات العمل الإضافية "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء "

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "عدد الرسائل التي تتطلب اتخاذ إجراء"

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
msgid "OK"
msgstr "موافق"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Odoo Logo"
msgstr "شعار أودو "

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_officer
msgid "Officer: Manage attendances"
msgstr "المسؤول: إدارة الحضور "

#. module: hr_attendance
#: model:ir.actions.server,name:hr_attendance.open_kiosk_url
msgid "Open Kiosk Url"
msgstr "فتح Url الكشك "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_browser
msgid "Out Browser"
msgstr "متصفح الخارج "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_city
msgid "Out City"
msgstr "المدينة في الخارج "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_country_name
msgid "Out Country Name"
msgstr "اسم الدولة في الخارج "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_ip_address
msgid "Out Ip Address"
msgstr "عنوان Ip في الخارج "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_latitude
msgid "Out Latitude"
msgstr "خطوط العرض في الخارج "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_longitude
msgid "Out Longitude"
msgstr "خطوط الطول في الخارج "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_mode
msgid "Out Mode"
msgstr "الوضع في الخارج "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__overtime_hours
msgid "Over Time"
msgstr "وقت إضافي "

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/res_users.py:0
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__overtime_ids
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_overtime_graph
msgid "Overtime"
msgstr "الوقت الإضافي "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__overtime_status
msgid "Overtime Status"
msgstr "حالة ساعات العمل الإصافية "

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_view_attendances
msgid "Overview"
msgstr "نظرة عامة"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
msgid "Please enter your PIN to"
msgstr "الرجاء إدخال رقمك السري لـ"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
msgid "Please return to the main menu."
msgstr "الرجاء العودة إلى القائمة الرئيسية. "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Powered by"
msgstr "مشغّل بواسطة "

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_employee_public
msgid "Public Employee"
msgstr "موظف في القطاع العام"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "RFID Token with reader"
msgstr "رمز RFID مع القارئ "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "RFID Token with reader on tablet"
msgstr "رمز RFID مع القارئ على الجهاز اللوحي "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__rating_ids
msgid "Ratings"
msgstr "التقييمات "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/views/attendance_helper_view.xml:0
msgid "Ready to track attendances ?"
msgstr "أأنت جاهز لتتبع الحضور؟ "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree_management
msgid "Refuse"
msgstr "رفض"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Refuse Extra Hours"
msgstr "رفض ساعات العمل الإضافية "

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__overtime_status__refused
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_management_view_filter
msgid "Refused"
msgstr "تم الرفض "

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_reporting
msgid "Reporting"
msgstr "إعداد التقارير "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل النصية القصيرة "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Scan your badge"
msgstr "قم بمسح شارتك "

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_barcode_source__scanner
msgid "Scanner"
msgstr "الماسح الضوئي "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
msgid "Search..."
msgstr "بحث..."

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Select on Tablet"
msgstr "التحديد في الجهاز اللوحي "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Set PIN codes in the employee detail form (in HR Settings tab)."
msgstr ""
"قم بتعيين الرموز السرية في استمارة تفاصيل الموظف ( في شريط إعدادات الموارد "
"البشرية). "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Set my badge"
msgstr "إعداد الشارة الخاصة بي "

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.action_hr_attendance_settings
msgid "Settings"
msgstr "الإعدادات"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
msgid "Sign out"
msgstr "تسجيل الخروج"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
msgid "Since"
msgstr "منذ "

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__in_mode__systray
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__out_mode__systray
msgid "Systray"
msgstr "Systray"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__in_mode__technical
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__out_mode__technical
msgid "Technical"
msgstr "تقني"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/views/attendance_helper_view.xml:0
msgid "The attendance records of your employees will be displayed here."
msgstr "سوف يتم عرض سجلات حضور موظفيك هنا. "

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_management_action
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_reporting
msgid "The attendance reporting of your employees will be displayed here."
msgstr "سوف يتم عرض تقارير حضور موظفيك هنا. "

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_overtime_action
msgid "The overtime records of your employees will be displayed here."
msgstr "سوف يتم عرض سجلات ساعات العمل الإضافية لموظفيك هنا. "

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_employee__attendance_manager_id
#: model:ir.model.fields,help:hr_attendance.field_hr_employee_public__attendance_manager_id
#: model:ir.model.fields,help:hr_attendance.field_res_users__attendance_manager_id
msgid ""
"The user set in Attendance will access the attendance of the employee "
"through the dedicated app and will be able to edit them."
msgstr ""
"سيتمكن المستخدم الذي تم تعيينه في الحضور من الوصول إلى حضور الموظف من خلال "
"التطبيق المخصص وسيتمكن من تعديله. "

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance_own_reader
msgid ""
"The user will have access to his own attendances on his user / employee "
"profile"
msgstr ""
"سيتمكن المستخدم من الوصول إلى الحضور الخاص به في ملف تعريف المستخدم / الموظف"
" الخاص به "

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance_officer
msgid ""
"The user will have access to the attendance records and reporting of "
"employees where he's set as an attendance manager"
msgstr ""
"سيتمكن المستخدم من الوصول إلى سجلات الحضور وتقارير الموظفين حيث تم تعيينه "
"كمدير للحضور "

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
msgid ""
"This attendance was automatically checked out because the employee exceeded "
"the allowed time for their scheduled work hours."
msgstr ""
"تم تسجيل خروج هذا الموظف تلقائياً لأن الموظف تجاوز الوقت المسموح به لساعات "
"العمل المحددة له. "

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
msgid ""
"This attendance was automatically created to cover an unjustified absence on"
" that day."
msgstr "تم إنشاء هذا الحضور تلقائياً لتغطية غياب غير مبرر في ذلك اليوم. "

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__overtime_status__to_approve
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_management_view_filter
msgid "To Approve"
msgstr "بانتظار الموافقة "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
msgid "Today"
msgstr "اليوم "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__overtime_company_threshold
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__overtime_company_threshold
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Tolerance Time In Favor Of Company"
msgstr "وقت التسامح لصالح الشركة "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__overtime_employee_threshold
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__overtime_employee_threshold
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Tolerance Time In Favor Of Employee"
msgstr "وقت التسامح لصالح الموظف "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
msgid "Total"
msgstr "الإجمالي "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__total_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__total_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__total_overtime
msgid "Total Overtime"
msgstr "إجمالي ساعات العمل الإضافية "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
msgid "Total today"
msgstr "إجمالي اليوم "

#. module: hr_attendance
#: model:ir.actions.server,name:hr_attendance.action_try_kiosk
msgid "Try kiosk"
msgstr "جرب الكشك "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/views/attendance_helper_view.xml:0
msgid "Try the backend and reporting:"
msgstr "جرب الواجهة الخلفية وإعداد التقارير: "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/views/attendance_helper_view.xml:0
msgid "Try the kiosk"
msgstr "جرب الكشك "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/views/attendance_helper_view.xml:0
msgid "Try the top"
msgstr "جرب الأعلى "

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/controllers/main.py:0
msgid "Unknown"
msgstr "غير معروف"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Use PIN codes (defined on the Employee's profile) to check-in."
msgstr "استخدم PIN كود (المحدد في ملف الموظف التعريفي) للدخول. "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Use this url to access your kiosk mode from any device."
msgstr "استخدم رابط URL هذا للوصول إلى وضع الكشك من أي جهاز. "

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_users
msgid "User"
msgstr "المستخدم"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_own_reader
msgid "User: Read his own attendances"
msgstr "المستخدم: يمكنه قراءة سجلات حضوره  "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "View on Maps"
msgstr "عرض على الخرائط "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع الإلكتروني "

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع الإلكتروني "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
msgid "Welcome"
msgstr "مرحبًا بك"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_employee_simple_tree_view
msgid "Work Hours"
msgstr "ساعات العمل "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree_management
msgid "Worked Extra Hours"
msgstr "ساعات العمل الإضافية التي تم عملها "

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__worked_hours
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_overtime_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_validated_hours_employee_simple_tree_view
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_graph
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Worked Hours"
msgstr "ساعات العمل المقضية "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree_management
msgid "Worked Time"
msgstr "الوقت المقضي في العمل "

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "Worked hours this month"
msgstr "الساعات التي تم عملها هذا الشهر "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.js:0
msgid "Wrong Pin"
msgstr "الرمز السري غير صحيح"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
msgid "You cannot duplicate an attendance."
msgstr "لايمكنك استنساخ الحضور."

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
msgid "You don't have the rights to execute that action."
msgstr "لا تملك صلاحيات كافية لتنفيذ هذا الإجراء."

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.js:0
msgid "Your badge Id is now set, you can scan your badge."
msgstr "معرّف شارتك جاهز الآن ويمكنك مسح الشارة ضوئياً. "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.js:0
msgid "Your badge has already been set."
msgstr "لقد تم إعداد شارتك بالفعل. "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
msgid "check in"
msgstr "تسجيل الحضور"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
msgid "check out"
msgstr "تسجيل الخروج"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "e.g. 1102021021"
msgstr "مثال: 1102021021 "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/views/attendance_helper_view.xml:0
msgid "icon (e.g for work from home)"
msgstr "أيقونة (مثال: للعمل عن بُعد) "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/views/attendance_helper_view.xml:0
msgid "or"
msgstr "أو "

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "with optional PIN code"
msgstr "مع PIN كود اختياري "
