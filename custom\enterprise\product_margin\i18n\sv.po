# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_margin
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <mika<PERSON>.a<PERSON><PERSON>@mariaakerberg.com>, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <and<PERSON>.<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__purchase_num_invoiced
msgid "# Invoiced in Purchase"
msgstr "# Fakturerat i inköp"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__sale_num_invoiced
msgid "# Invoiced in Sale"
msgstr "# Fakturerad i försäljning"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "# Purchased"
msgstr "# Köpta"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Analysis Criteria"
msgstr "Kriterier för analys"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__sale_avg_price
msgid "Avg. Price in Customer Invoices."
msgstr "Genomsnittligt pris i kundfakturor."

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__purchase_avg_price
msgid "Avg. Price in Vendor Bills"
msgstr "Genomsnittligt pris i leverantörsfakturor"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__purchase_avg_price
msgid "Avg. Purchase Unit Price"
msgstr "Genomsnittligt köp enhetspris"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__sale_avg_price
msgid "Avg. Sale Unit Price"
msgstr "Genomsnittlig försäljning enhetspris"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Avg. Unit Price"
msgstr "Genomsnittligt enhetspris"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "Cancel"
msgstr "Avbryt"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Catalog Price"
msgstr "Katalogpris"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__create_uid
msgid "Created by"
msgstr "Skapad av"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__create_date
msgid "Created on"
msgstr "Skapad den"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__display_name
msgid "Display Name"
msgstr "Visningsnamn"

#. module: product_margin
#: model:ir.model.fields.selection,name:product_margin.selection__product_margin__invoice_state__draft_open_paid
#: model:ir.model.fields.selection,name:product_margin.selection__product_product__invoice_state__draft_open_paid
msgid "Draft, Open and Paid"
msgstr "Utkast, öppna och betalda"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__expected_margin
msgid "Expected Margin"
msgstr "Förväntad marginal"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__expected_margin_rate
msgid "Expected Margin (%)"
msgstr "Förväntad marginal (%)"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__sale_expected
msgid "Expected Sale"
msgstr "Förväntad försäljning"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__expected_margin
msgid "Expected Sale - Normal Cost"
msgstr "Förväntad försäljning - normal kostnad"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__sales_gap
msgid "Expected Sale - Turn Over"
msgstr "Förväntad försäljning - omsättning"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__expected_margin_rate
msgid "Expected margin * 100 / Expected Sale"
msgstr "Förväntad marginal * 100 / Förväntad försäljning"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__from_date
msgid "From"
msgstr "Från"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "General Information"
msgstr "Generell information"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__id
msgid "ID"
msgstr "ID"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__invoice_state
#: model:ir.model.fields,field_description:product_margin.field_product_product__invoice_state
msgid "Invoice State"
msgstr "Fakturans status"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__write_uid
msgid "Last Updated by"
msgstr "Senast uppdaterad av"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__write_date
msgid "Last Updated on"
msgstr "Senast uppdaterad den"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__date_from
msgid "Margin Date From"
msgstr "Marginal Datum från"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__date_to
msgid "Margin Date To"
msgstr "Marginal Datum Till"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Margins"
msgstr "Marginaler"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__normal_cost
msgid "Normal Cost"
msgstr "Normal kostnad"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__purchase_gap
msgid "Normal Cost - Total Cost"
msgstr "Normal kostnad - Total kostnad"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "Open Margins"
msgstr "Öppna marginaler"

#. module: product_margin
#: model:ir.model.fields.selection,name:product_margin.selection__product_margin__invoice_state__open_paid
#: model:ir.model.fields.selection,name:product_margin.selection__product_product__invoice_state__open_paid
msgid "Open and Paid"
msgstr "Öppna och betalda"

#. module: product_margin
#: model:ir.model.fields.selection,name:product_margin.selection__product_margin__invoice_state__paid
#: model:ir.model.fields.selection,name:product_margin.selection__product_product__invoice_state__paid
msgid "Paid"
msgstr "Betalad"

#. module: product_margin
#: model:ir.model,name:product_margin.model_product_margin
msgid "Product Margin"
msgstr "Produktmarginal"

#. module: product_margin
#. odoo-python
#: code:addons/product_margin/wizard/product_margin.py:0
#: model:ir.actions.act_window,name:product_margin.product_margin_act_window
#: model:ir.ui.menu,name:product_margin.menu_action_product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_graph
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Product Margins"
msgstr "Produktmarginaler"

#. module: product_margin
#: model:ir.model,name:product_margin.model_product_product
msgid "Product Variant"
msgstr "Produktvariant"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "Properties categories"
msgstr "Properties categories"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__purchase_gap
msgid "Purchase Gap"
msgstr "Skillnad i köp"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Purchases"
msgstr "Inköp"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Sales"
msgstr "Försäljning"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__sales_gap
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Sales Gap"
msgstr "Försäljningsgap"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Standard Price"
msgstr "Standardpris"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__normal_cost
msgid "Sum of Multiplication of Cost price and quantity of Vendor Bills"
msgstr ""
"Summan av multiplikation av självkostnadspris och antal leverantörsfakturor"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__turnover
msgid ""
"Sum of Multiplication of Invoice price and quantity of Customer Invoices"
msgstr "Summan av multiplikationen av fakturapriset och antalet kundfakturor"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__total_cost
msgid "Sum of Multiplication of Invoice price and quantity of Vendor Bills "
msgstr ""
"Summan av multiplikationen av fakturapriset och antalet leverantörsfakturor "

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__sale_expected
msgid ""
"Sum of Multiplication of Sale Catalog price and quantity of Customer "
"Invoices"
msgstr ""
"Summan av multiplikationen av försäljningskatalogens pris och antalet "
"kundfakturor"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__sale_num_invoiced
msgid "Sum of Quantity in Customer Invoices"
msgstr "Summan av antal i kundfakturor"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__purchase_num_invoiced
msgid "Sum of Quantity in Vendor Bills"
msgstr "Summan av antal i leverantörsfakturor"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__to_date
msgid "To"
msgstr "Till"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__total_cost
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Total Cost"
msgstr "Total kostnad"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__total_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Total Margin"
msgstr "Total marginal"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__total_margin_rate
msgid "Total Margin Rate(%)"
msgstr "Total Marginalsats (%)"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__total_margin_rate
msgid "Total margin * 100 / Turnover"
msgstr "Total marginal * 100 / Omsättning"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__turnover
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Turnover"
msgstr "Omsättning"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__total_margin
msgid "Turnover - Total cost"
msgstr "Omsättning - Totalkostnad"
