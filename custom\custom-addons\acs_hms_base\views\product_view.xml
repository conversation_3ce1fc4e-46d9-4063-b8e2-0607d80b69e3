<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="product_template_form_view_inherit" model="ir.ui.view">
        <field name="name">product.template.product.form.drug.details</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">
            <field name="detailed_type" position="after">
                <field name="hospital_product_type" required="1"/>
            </field>
            <xpath expr="//notebook/page[@name='general_information']" position="before">
                <page string="Medicament Details" name="acs_medicament_data" invisible="hospital_product_type != 'medicament'">
                    <group name="acs_medicament_info">
                        <group>
                            <field name="form_id"/>
                            <field name="route_id"/>
                            <label for="dosage"/>
                            <div class="o_row">
                                <field name="dosage"/>
                                <span><field name="dosage_uom_id"/></span>
                            </div>
                            <field name="drug_company_id"/>
                            <field name="product_uom_category_id" invisible="1"/>
                        </group>
                        <group>
                            <field name="active_component_ids" widget="many2many_tags"/>
                            <field name="therapeutic_effect_ids" widget="many2many_tags"/>
                            <field name="adverse_reaction"/>
                        </group>
                    </group>
                    <separator string="Pregnancy" colspan="4" name="pregnancy_warning_separator"/>
                    <group  name="pregnancy_warning">
                        <group>
                            <field name="pregnancy_warning"/>
                        </group>
                        <group>
                            <field name="lactation_warning"/>
                        </group>
                        <field name="pregnancy" placeholder="Pregnancy/Lactation Warning"/>
                    </group>

                    <separator string="Indications" colspan="4"/>
                    <field name="indications" nolabel="1" colspan="4"/>
                    <separator string="Notes" colspan="4"/>
                    <field name="notes" nolabel="1" colspan="4"/>
                </page>
            </xpath>
        </field>
    </record>

    <record id="product_template_search_view_inherit" model="ir.ui.view">
        <field name="name">product.template.product.search</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_search_view"/>
        <field name="arch" type="xml">
            <field name='name' position="after">
                <field name="active_component_ids"/>
                <field name="drug_company_id"/>
            </field>
        </field>
    </record>

    <record id="product_template_action_medicines" model="ir.actions.act_window">
        <field name="name">Medicines</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">product.template</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="view_id" ref="product.product_template_kanban_view"/>
        <field name="domain">[('hospital_product_type','=','medicament')]</field>
    </record>

    <record id="product_template_action_services" model="ir.actions.act_window">
        <field name="name">Services</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">product.template</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="view_id" ref="product.product_template_kanban_view"/>
        <field name="domain">[('type','=','service')]</field>
    </record>

</odoo>