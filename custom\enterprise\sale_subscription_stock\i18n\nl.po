# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_subscription_stock
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-12 08:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_subscription_stock
#. odoo-python
#: code:addons/sale_subscription_stock/models/sale_order.py:0
msgid ""
"A system error prevented the automatic creation of delivery orders for this "
"subscription. To ensure your delivery is processed, please trigger it "
"manually by using the \"Subscription: Generate delivery\" action."
msgstr ""
"Een systeemfout heeft het automatisch aanmaken van leveringsorders voor dit "
"abonnement verhinderd. Om ervoor te zorgen dat de levering wordt verwerkt, "
"moet je deze handmatig activeren met de actie “Abonnement: Genereer "
"levering”."

#. module: sale_subscription_stock
#. odoo-python
#: code:addons/sale_subscription_stock/models/sale_order.py:0
msgid "Delivery creation failed"
msgstr "Aanmaken levering mislukt"

#. module: sale_subscription_stock
#: model:product.template,name:sale_subscription_stock.product_recurring_detergent_product_template
msgid "Detergent (SUB)"
msgstr "Detergent (SUB)"

#. module: sale_subscription_stock
#: model:ir.model.fields,field_description:sale_subscription_stock.field_sale_order__display_recurring_stock_delivery_warning
msgid "Display Recurring Stock Delivery Warning"
msgstr "Waarschuwing voor herhalende levering weergeven"

#. module: sale_subscription_stock
#: model_terms:ir.ui.view,arch_db:sale_subscription_stock.product_template_form_view
msgid ""
"Recurring order with this product will be invoiced at the beginning of the "
"period."
msgstr ""
"Een herhalend order met dit product wordt gefactureerd aan het begin van de "
"periode."

#. module: sale_subscription_stock
#: model:ir.model,name:sale_subscription_stock.model_sale_order
msgid "Sales Order"
msgstr "Verkooporder"

#. module: sale_subscription_stock
#: model:ir.model,name:sale_subscription_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "Verkooporderregel"

#. module: sale_subscription_stock
#: model:ir.model,name:sale_subscription_stock.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr "Voorraadaanvulling rapport"

#. module: sale_subscription_stock
#: model:ir.actions.server,name:sale_subscription_stock.action_compute_price_bom_product
msgid "Subscription: Generate delivery"
msgstr "Abonnement: Genereer levering"

#. module: sale_subscription_stock
#. odoo-javascript
#: code:addons/sale_subscription_stock/static/src/report_stock_forecasted.xml:0
msgid "Subscriptions"
msgstr "Abonnementen"

#. module: sale_subscription_stock
#: model_terms:ir.ui.view,arch_db:sale_subscription_stock.sale_subscription_order_view_form
msgid ""
"The delivery order of the recurring product(s) will be created soon. If another delivery order exists,\n"
"                    recurring product will be added to it automatically."
msgstr ""
"De uitgaande levering van het/de herhalende product(en) wordt binnenkort aangemaakt. Als er een andere uitgaande levering bestaat,\n"
"wordt het herhalende product er automatisch aan toegevoegd."

#. module: sale_subscription_stock
#: model:ir.model,name:sale_subscription_stock.model_stock_picking
msgid "Transfer"
msgstr "Verplaatsing"
