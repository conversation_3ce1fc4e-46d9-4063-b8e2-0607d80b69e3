<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="hr_contract_type_view_tree" model="ir.ui.view">
            <field name="model">hr.contract.type</field>
            <field name="arch" type="xml">
                <list string="Contract Types" editable="bottom">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="code" groups="base.group_no_one"/>
                    <field name="country_id"/>
                </list>
            </field>
        </record>

        <record id="hr_contract_type_view_form" model="ir.ui.view">
            <field name="model">hr.contract.type</field>
            <field name="arch" type="xml">
                <form>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="code" groups="base.group_no_one"/>
                            <field name="country_id"/>
                        </group>
                    </group>
                </form>
            </field>
        </record>

        <record id="hr_contract_type_action" model="ir.actions.act_window">
            <field name="name">Employment Types</field>
            <field name="res_model">hr.contract.type</field>
            <field name="view_mode">list</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new employment type
                </p>
            </field>
        </record>
    </data>
</odoo>
