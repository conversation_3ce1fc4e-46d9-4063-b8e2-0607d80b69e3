<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_partner_view_form" model="ir.ui.view">
        <field name="name">res.partner.view.form.inherit.hr</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <div name="button_box" position="inside">
                <button name="action_open_employees" type="object" class="oe_stat_button" icon="fa-id-card-o" groups="hr.group_hr_user" invisible="employees_count == 0">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value"><field name="employees_count"/></span>
                        <span class="o_stat_text">Employee</span>
                    </div>
                </button>
            </div>
        </field>
    </record>

    <record id="res_partner_view_search" model="ir.ui.view">
        <field name="name">res.partner.search.inherit</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_res_partner_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='inactive']" position="before">
                <filter name="employees" string="Employees" domain="[('employee_ids', '!=', False)]"/>
                <separator/>
            </xpath>
        </field>
    </record>
</odoo>
