# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_subscription_stock
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-12 08:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_subscription_stock
#. odoo-python
#: code:addons/sale_subscription_stock/models/sale_order.py:0
msgid ""
"A system error prevented the automatic creation of delivery orders for this "
"subscription. To ensure your delivery is processed, please trigger it "
"manually by using the \"Subscription: Generate delivery\" action."
msgstr ""
"システムエラーにより、このサブスクリプションの配送オーダが自動的に作成されませんでした。配送が確実に処理されるようにするには、\"サブスクリプション: "
"配送の生成\" アクションを使用して手動でトリガして下さい。"

#. module: sale_subscription_stock
#. odoo-python
#: code:addons/sale_subscription_stock/models/sale_order.py:0
msgid "Delivery creation failed"
msgstr "配送作成に失敗しました"

#. module: sale_subscription_stock
#: model:product.template,name:sale_subscription_stock.product_recurring_detergent_product_template
msgid "Detergent (SUB)"
msgstr "洗剤 (サブ)"

#. module: sale_subscription_stock
#: model:ir.model.fields,field_description:sale_subscription_stock.field_sale_order__display_recurring_stock_delivery_warning
msgid "Display Recurring Stock Delivery Warning"
msgstr "定期在庫配送警告を表示"

#. module: sale_subscription_stock
#: model_terms:ir.ui.view,arch_db:sale_subscription_stock.product_template_form_view
msgid ""
"Recurring order with this product will be invoiced at the beginning of the "
"period."
msgstr "このプロダクトの定期オーダは、期間開始時に請求されます。"

#. module: sale_subscription_stock
#: model:ir.model,name:sale_subscription_stock.model_sale_order
msgid "Sales Order"
msgstr "販売オーダ"

#. module: sale_subscription_stock
#: model:ir.model,name:sale_subscription_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "販売オーダ明細"

#. module: sale_subscription_stock
#: model:ir.model,name:sale_subscription_stock.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr "在庫補充レポート"

#. module: sale_subscription_stock
#: model:ir.actions.server,name:sale_subscription_stock.action_compute_price_bom_product
msgid "Subscription: Generate delivery"
msgstr "サブスクリプション: 配送を作成"

#. module: sale_subscription_stock
#. odoo-javascript
#: code:addons/sale_subscription_stock/static/src/report_stock_forecasted.xml:0
msgid "Subscriptions"
msgstr "サブスクリプション"

#. module: sale_subscription_stock
#: model_terms:ir.ui.view,arch_db:sale_subscription_stock.sale_subscription_order_view_form
msgid ""
"The delivery order of the recurring product(s) will be created soon. If another delivery order exists,\n"
"                    recurring product will be added to it automatically."
msgstr ""
"定期購入プロダクトの配送オーダはまもなく作成されます。別の配送オーダが存在する場合は、\n"
"                    定期的なプロダクトが自動的に追加されます。"

#. module: sale_subscription_stock
#: model:ir.model,name:sale_subscription_stock.model_stock_picking
msgid "Transfer"
msgstr "転送"
