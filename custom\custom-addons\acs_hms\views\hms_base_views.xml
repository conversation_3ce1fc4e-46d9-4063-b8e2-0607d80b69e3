<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <!-- Product -->
    <record id="product_template_form_view_inherit" model="ir.ui.view">
        <field name="name">product.template.product.form.drug.details</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="acs_hms_base.product_template_form_view_inherit"/>
        <field name="arch" type="xml">
            <field name="active_component_ids" position="before">
                <field name="common_dosage_id"/>
            </field>
            <field name="drug_company_id" position="before">
                <field name="manual_prescription_qty"/>
            </field>
            <field name="hospital_product_type" position="after">
                <field name="procedure_time" invisible="detailed_type != 'service'" widget="float_time"/>
                <field name="appointment_invoice_policy" groups="acs_hms_base.group_hms_manager"/>
            </field>
        </field>
    </record>

    <!-- User -->
    <record id="view_res_users_inherit_form" model="ir.ui.view">
        <field name="name">res.users.inherit</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="base.view_users_form"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='access_rights']/group" position="after">
                <group string="Multi Department">
                    <field string="Allowed Departments" name="department_ids" widget="many2many_tags" options="{'no_create': True}" groups="acs_hms.group_allow_multi_department" domain="[('patient_department', '=', True)]"/>
                    <field name="physician_count" invisible="1"/>
                    <field name="patient_count" invisible="1"/>
                </group>
            </xpath>
            <xpath expr="//header" position="inside">
                <button string="Create Physician" type="object" name="action_create_physician" invisible="not id or share or physician_count > 0"/>
                <button string="Create Patient" type="object" name="action_create_patient" invisible="not id or patient_count > 0"/>
            </xpath>
        </field>
    </record>

   <!-- Department -->
   <record id="view_department_form" model="ir.ui.view">
        <field name="name">hr.department.form</field>
        <field name="model">hr.department</field>
        <field name="inherit_id" ref="hr.view_department_form"/>
        <field name="arch" type="xml">
            <field name="parent_id" position="after">
                <field name='patient_department'/>
            </field>
            <xpath expr="//group" position="after">
                <notebook>
                    <page name="info" string="Hospital Information" invisible="not patient_department">
                        <group>
                            <group name="left_data">
                                <field name='department_type'/>
                                <field name='followup_service_id'/>
                                <field name='consultaion_service_id'/>
                            </group>
                            <group name="right_data">
                            </group>
                        </group>
                    </page>
                </notebook>
            </xpath>
        </field>
    </record>

    <!-- Family Disease -->
    <record id="view_patient_family_disease_form" model="ir.ui.view">
        <field name="name">patient.family.disease.form</field>
        <field name="model">hms.patient.family.diseases</field>
        <field name="arch" type="xml">
            <form string="Patient Genetic Family Diseases">
                <sheet>
                    <group>
                        <group>
                            <field name="diseases_ids" widget="many2many_tags"/>
                            <field name="xory"/>
                        </group>
                        <group>
                            <field name="relative"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_patient_family_disease_tree" model="ir.ui.view">
        <field name="name">patient.family.disease.tree</field>
        <field name="model">hms.patient.family.diseases</field>
        <field name="arch" type="xml">
            <tree string="Genetic Family Diseases">
                <field name="diseases_ids" widget="many2many_tags"/>
                <field name="xory"/>
                <field name="relative"/>
            </tree>
        </field>
    </record>

    <!-- Genetic Risk -->
    <record id="view_hms_patient_genetic_risk_form" model="ir.ui.view">
        <field name="name">hms.patient.genetic.risk.form</field>
        <field name="model">hms.patient.genetic.risk</field>
        <field name="arch" type="xml">
            <form string="Patient Genetic Risks">
                <sheet>
                    <group>
                        <field name="disease_gene">
                            <tree string="Disease Gene">
                                <field name="info"/>
                                <field name="name"/>
                                <field name="gene_id"/>
                                <field name="long_name"/>
                                <field name="location"/>
                                <field name="dominance"/>
                                <field name="chromosome"/>
                            </tree>
                            <form string="Disease Gene">
                                <group>
                                    <field name="name"/>
                                    <field name="gene_id"/>
                                    <field name="long_name"/>
                                    <field name="location"/>
                                    <field name="dominance"/>
                                    <field name="chromosome"/>
                                    <newline/>
                                    <field name="info" colspan="4"/>
                                </group>
                            </form>
                        </field>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_hms_patient_genetic_risk_tree" model="ir.ui.view">
        <field name="name">hms.patient.genetic.risk.tree</field>
        <field name="model">hms.patient.genetic.risk</field>
        <field name="arch" type="xml">
            <tree string="Patient Genetic Risks" editable="bottom">
                <field name="disease_gene"/>
            </tree>
        </field>
    </record>

    <!-- Partner -->
    <!-- NOTE: not added any simple view becuase for commsin payment and all we need many features -->
    <record id="view_hms_partner_form" model="ir.ui.view">
        <field name="name">Referring Doctors</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="type">form</field>
        <field name="arch" type="xml">
            <xpath expr="//page" position="after">
                <page string="Hospital Info" name="hms_info">
                    <group>
                        <group>
                            <field name="acs_patient_id"/>
                        </group>
                        <group>
                            <button name="create_patient" string="Create Patient" class="oe_highlight oe_inline" invisible="acs_patient_id" type="object"/>
                        </group>
                    </group>
                    <separator string="Refering Doctor"/>
                    <group>
                        <group>
                            <field name="is_referring_doctor"/>
                        </group>
                        <group>
                            <field name="hospital_name" invisible="not is_referring_doctor"/>
                        </group>
                    </group>
                </page>
            </xpath>
        </field>
    </record>

    <record id="view_res_partner_filter" model="ir.ui.view">
        <field name="name">res.partner.filter</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="acs_hms_base.view_res_partner_filter"/>
        <field name="type">form</field>
        <field name="arch" type="xml">
            <filter name="customer" position="before">
                <filter string="Referring Doctors" name="is_referring_doctor" domain="[('is_referring_doctor','=',True)]"/>
            </filter>
        </field>
    </record>

    <record id="action_referring_doctors" model="ir.actions.act_window">
        <field name="name">Referring Doctors</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.partner</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="context">{'search_default_is_referring_doctor':1, 'default_is_referring_doctor':1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No Record Found
            </p>
            <p>
                Click to add a Referring Doctor.
            </p>
        </field>
    </record>

    <!-- Ethnicity view -->
    <record id="view_acs_ethnicity_tree" model="ir.ui.view">
        <field name="name">acs.ethnicity.tree</field>
        <field name="model">acs.ethnicity</field>
        <field name="arch" type="xml">
            <tree string="ACS Ethnicity">
                <field name="notes"/>
                <field name="code"/>
                <field name="name"/>
            </tree>
        </field>
    </record>

    <record id="view_acs_ethnicity_form" model="ir.ui.view">
        <field name="name">acs.ethnicity.form</field>
        <field name="model">acs.ethnicity</field>
        <field name="arch" type="xml">
            <form string="ACS Ethnicity">
                <sheet>
                    <group>
                        <group>
                            <field name="notes"/>
                            <field name="name"/>
                        </group>
                        <group>
                            <field name="code"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_acs_ethnicity_search" model="ir.ui.view">
        <field name="name">acs.ethnicity.search</field>
        <field name="model">acs.ethnicity</field>
        <field name="arch" type="xml">
            <search string="ACS Ethnicity">
                <field name="notes"/>
                <field name="code"/>
                <field name="name"/>
                <newline/>
                <group expand="0" string="Group By...">
                    <filter string="Notes" name="note_groupby" domain="[]" context="{'group_by':'notes'}"/>
                    <filter string="Code" name="code_groupby" domain="[]" context="{'group_by':'code'}"/>
                    <filter string="Name" name="name_groupby" domain="[]" context="{'group_by':'name'}"/>
                </group>
            </search>
        </field>
    </record>

    <record model="ir.actions.act_window" id="act_open_acs_ethnicity_view">
        <field name="name">Ethnicity</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">acs.ethnicity</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_acs_ethnicity_search"/>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No Record Found
            </p>
            <p>
                Click to add a Ethnicity.
            </p>
        </field>
    </record>

    <!-- Family Relation view -->
    <record id="view_acs_family_relation_tree" model="ir.ui.view">
        <field name="name">acs.family.relation.tree</field>
        <field name="model">acs.family.relation</field>
        <field name="arch" type="xml">
            <tree string="Family Relation" editable="bottom">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="inverse_relation_id"/>
            </tree>
        </field>
    </record>

    <record id="action_acs_family_relation" model="ir.actions.act_window">
        <field name="name">Family Relation</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">acs.family.relation</field>
        <field name="view_mode">tree</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No Record Found
            </p>
            <p>
                Click to add Family Relation.
            </p>
        </field>
    </record>

</odoo>