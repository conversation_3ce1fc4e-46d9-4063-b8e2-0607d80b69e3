<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.point_of_sale</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="95"/>
        <field name="inherit_id" ref="base.res_config_settings_view_form" />
        <field name="arch" type="xml">
            <xpath expr="//form" position="inside">
                <t groups="point_of_sale.group_pos_manager">
                    <field name="is_kiosk_mode" invisible="1"/>
                    <field name="pos_selectable_categ_ids" invisible="1"/>
                    <field name="pos_has_active_session" invisible="1"/>
                    <field name="pos_allowed_pricelist_ids" invisible="1"/>
                    <field name="pos_cash_control" invisible="1"/>
                    <field name="pos_iface_print_via_proxy" invisible="1"/>
                    <field name="pos_company_has_template" invisible="1"/>
                    <field name="group_cash_rounding" invisible="1"/>
                </t>

                <app data-string="Point of sale" string="Point of Sale" name="point_of_sale" groups="point_of_sale.group_pos_manager">
                    <setting type="header" string="Point of Sale">
                        <field name="pos_config_id" options="{'no_open': True, 'no_create': True}" title="Settings on this page will apply to this point of sale."/>
                        <button name="action_pos_config_create_new" type="object" string="+ New Shop" class="btn btn-link"/>
                    </setting>

                    <div class="o_view_nocontent" invisible="pos_config_id">
                        <div class="o_nocontent_help">
                            <p class="o_view_nocontent_empty_folder">No Point of Sale selected</p>
                            <p>Please create/select a Point of Sale above to show the configuration options.</p>
                        </div>
                    </div>
                    <div class="o_notification_alert alert alert-warning mt-1 mb-0" invisible="not pos_has_active_session" role="alert">
                        A session is currently opened for this PoS. Some settings can only be changed after the session is closed.
                        <button class="btn-link" style="padding:0" name="pos_open_ui" type="object" context="{'pos_config_id': pos_config_id}">Click here to close the session</button>
                    </div>
                    <div class="o_notification_alert alert alert-warning" invisible="pos_company_has_template or chart_template" role="alert">
                        There is no Chart of Accounts configured on the company. Please go to the invoicing settings to install a Chart of Accounts.
                    </div>
                    <div invisible="not pos_config_id">
                        <block title="Restaurant Mode" id="restaurant_section">
                            <!-- Wrap the warnings in an o_setting_box so that it doesn't show in the search. -->
                            <setting>
                                <field name="pos_module_pos_restaurant" readonly="pos_has_active_session"/>
                                <div class="content-group" id="warning_text_pos_restaurant" invisible="not pos_module_pos_restaurant">
                                    <div class="text-warning mt16 mb4">
                                        Save this page and come back here to set up the feature.
                                    </div>
                                </div>
                            </setting>
                        </block>
                        <block title="Payment" id="pos_payment_section">
                            <div id="available_payment_terminal" class="o_notification_alert alert alert-warning" role="alert" invisible="not is_kiosk_mode">
                                <span>Please note that the kiosk only works with Adyen &amp; Stripe terminals</span>
                            </div>
                            <setting id="payment_methods_new" string="Payment Methods" help="Payment methods available" documentation="/applications/sales/point_of_sale/payment_methods.html">
                                <field name="pos_payment_method_ids" colspan="4" widget="many2many_tags" readonly="pos_has_active_session" required="pos_company_has_template" options="{'no_create': True}" />
                                <div>
                                    <button name="%(action_payment_methods_tree)d" icon="oi-arrow-right" type="action" string="Payment Methods" class="btn-link"/>
                                </div>
                            </setting>
                            <setting string="Automatically validate order" help="Automatically validates orders paid with a payment terminal.">
                                <field name="pos_auto_validate_terminal_payment"/>
                            </setting>
                            <setting string="Cash Rounding" invisible="is_kiosk_mode" documentation="/applications/sales/point_of_sale/pricing/cash_rounding.html" help="Define the smallest coinage of the currency used to pay by cash">
                                <field name="pos_cash_rounding" readonly="pos_has_active_session"/>
                                <div class="content-group mt16" invisible="not pos_cash_rounding">
                                    <div class="row mt16">
                                        <label string="Rounding Method" for="pos_rounding_method" class="col-lg-3 o_light_label" />
                                        <field name="pos_rounding_method" required="pos_cash_rounding" readonly="pos_has_active_session"/>
                                    </div>
                                    <div class="row mt16">
                                        <div class="col">
                                            <field name="pos_only_round_cash_method" readonly="pos_has_active_session"/>
                                            <label string="Only on cash methods" for="pos_only_round_cash_method" class="o_light_label" />
                                        </div>
                                    </div>
                                </div>
                                <div class="mt8">
                                    <button name="%(account.rounding_list_action)d" icon="oi-arrow-right"
                                            type="action" string="Cash Roundings" class="btn-link"
                                            invisible="not group_cash_rounding"/>
                                </div>
                            </setting>
                            <setting help="Set a maximum difference allowed between the expected and counted money during the closing of the session">
                                <field name="pos_set_maximum_difference" />
                                <div class="content-group mt16" invisible="not pos_set_maximum_difference">
                                    <label for="pos_amount_authorized_diff" string="Authorized Difference" class="fw-normal me-1"/>
                                    <field name="pos_amount_authorized_diff"/>
                                </div>
                            </setting>
                            <setting id="iface_tipproduct" title="This product is used as reference on customer receipts." string="Tips" help="Accept customer tips or convert their change to a tip" documentation="/applications/sales/point_of_sale/restaurant/tips.html">
                                <field name="pos_iface_tipproduct" readonly="pos_has_active_session"/>
                                <div class="content-group" invisible="not pos_iface_tipproduct">
                                    <div class="row mt16" id="tip_product">
                                        <label string="Tip Product" for="pos_tip_product_id" class="col-lg-3 o_light_label "/>
                                        <field name="pos_tip_product_id"/>
                                    </div>
                                </div>
                            </setting>
                        </block>
                        <block title="PoS Interface" id="pos_interface_section">
                            <div class="o_notification_alert alert alert-warning" invisible="pos_company_has_template" role="alert">
                                There is no Chart of Accounts configured on the company. Please go to the invoicing settings to install a Chart of Accounts.
                            </div>
                            <setting id="multiple_employee_session" title="Employees can scan their badge or enter a PIN to log in to a PoS session. These credentials are configurable in the *HR Settings* tab of the employee form." string="Log in with Employees" help="Allow to log and switch between selected Employees" documentation="/applications/sales/point_of_sale/employee_login.html">
                                <field name="pos_module_pos_hr" readonly="pos_has_active_session"/>
                                <div class="content-group mt16" invisible="not pos_module_pos_hr">
                                    <div class="text-warning" id="warning_text_employees">
                                        Save this page and come back here to set up the feature.
                                    </div>
                                </div>
                            </setting>
                            <setting invisible="is_kiosk_mode" help="Improve navigation for imprecise industrial touchscreens">
                                <field name="pos_iface_big_scrollbars"/>
                            </setting>
                            <setting string="Share Open Orders" help="Allow to access each other's active orders" id="is_order_shared" invisible="pos_module_pos_restaurant or is_kiosk_mode">
                                <div class="content-group">
                                    <div class="row mt16">
                                        <label string="Trusted POS" for="pos_trusted_config_ids" class="col-lg-3 o_light_label" />
                                        <field name="pos_trusted_config_ids" widget="many2many_tags" options="{'no_create':'true'}" placeholder="Select PoS to start sharing orders"/>
                                    </div>
                                    <div>
                                        <button name="%(point_of_sale.action_pos_config_tree)d" icon="oi-arrow-right" type="action" string="Point of Sales" class="btn-link"/>
                                    </div>
                                </div>
                            </setting>
                            <setting string="Hide pictures in POS" help="Self-ordering interfaces are not impacted.">
                                <div class="content-group">
                                    <div>
                                        <field name="pos_show_product_images"/>
                                        <label for="pos_show_product_images" string="Show product images" class="fw-normal me-1"/>
                                    </div>
                                    <div>
                                        <field name="pos_show_category_images"/>
                                        <label for="pos_show_category_images" string="Show category images" class="fw-normal me-1"/>
                                    </div>
                                </div>
                            </setting>
                        </block>
                        <block id="product_and_category_block" title="Product &amp; PoS categories">
                            <setting help="Pick which product PoS categories are available">
                                <field name="pos_limit_categories" readonly="pos_has_active_session"/>
                                <div class="content-group mt16" invisible="not pos_limit_categories">
                                    <field name="pos_iface_available_categ_ids" widget="many2many_tags" readonly="pos_has_active_session"/>
                                </div>
                                <div class="content-group mt16" invisible="not pos_limit_categories">
                                    <button name="%(product_pos_category_action)d" icon="oi-arrow-right" type="action" string="PoS Product Categories" class="btn-link"/>
                                </div>
                            </setting>
                            <setting id="margin_and_cost" string="Show margins &amp; costs" help="Show margins &amp; costs on product information">
                                <field name="pos_is_margins_costs_accessible_to_every_user"/>
                            </setting>
                            <setting id="orderlines_seq_cart" string="Sort cart by category" help="Group items in the cart according to their category">
                                <field name="pos_orderlines_sequence_in_cart_by_category"/>
                            </setting>
                        </block>
                        <block title="Accounting" id="pos_accounting_section">
                            <setting id="default_sales_tax_setting" title="This tax is applied to any new product created in the catalog." documentation="/applications/finance/accounting/taxation/taxes/default_taxes.html">
                                <label string="Default Sales Tax" for="sale_tax_id"/>
                                <i class="fa fa-info-circle me-1" title="This setting is common to all PoS." pos-data-toggle="tooltip"/>
                                <div class="text-muted">
                                    Default sales tax for products
                                </div>
                                <div class="content-group mt16">
                                    <field name="sale_tax_id" colspan="4" nolabel="1" domain="[('type_tax_use', 'in', ('sale', 'all'))]"/>
                                </div>
                                <div class="mt8">
                                    <button name="%(account.action_tax_form)d" icon="oi-arrow-right" type="action" string="Taxes" class="btn-link"/>
                                </div>
                            </setting>
                            <setting groups="account.group_account_readonly">
                                <label string="Default Temporary Account" for="account_default_pos_receivable_account_id"/>
                                <i class="fa fa-info-circle me-1" title="This setting is common to all PoS." pos-data-toggle="tooltip"/>
                                <div class="text-muted">
                                    Intermediary account used for unidentified customers.
                                </div>
                                <field name="account_default_pos_receivable_account_id" colspan="4" nolabel="1" domain="[('reconcile', '=', True), ('account_type', '=', 'asset_receivable')]"/>
                            </setting>
                            <setting id="order_edit_tracking" help="Store edited orders in the backend">
                                <field name="pos_order_edit_tracking"/>
                            </setting>
                            <setting id="flexible_taxes" title="Choose a specific fiscal position at the order depending on the kind of customer (tax exempt, onsite vs. takeaway, etc.)." string="Flexible Taxes" help="Use fiscal positions to get different taxes by order" documentation="/applications/sales/point_of_sale/pricing/fiscal_position.html">
                                <field name="pos_tax_regime_selection"/>
                                <div class="content-group mt16" invisible="not pos_tax_regime_selection">
                                    <div class="row">
                                        <label string="Default" for="pos_default_fiscal_position_id" class="col-lg-3 o_light_label"/>
                                        <field name="pos_default_fiscal_position_id"/>
                                    </div>
                                    <div class="row">
                                        <label string="Allowed" for="pos_fiscal_position_ids" class="col-lg-3 o_light_label"/>
                                        <field name="pos_fiscal_position_ids" widget="many2many_tags" options="{'no_create': True}"/>
                                    </div>
                                    <div>
                                        <button name="%(account.action_account_fiscal_position_form)d" icon="oi-arrow-right" type="action" string="Fiscal Positions" class="btn-link"/>
                                    </div>
                                </div>
                            </setting>
                            <setting string="Default Journals" id="pos_default_journals" help="Default journals for orders and invoices">
                                <div class="content-group mt16">
                                    <div class="row" title="Whenever you close a session, one entry is generated in the following accounting journal for all the orders not invoiced. Invoices are recorded in accounting separately.">
                                        <label string="Orders" for="pos_journal_id" class="col-lg-3 o_light_label" options="{'no_open': True, 'no_create': True}"/>
                                        <field name="pos_journal_id" domain="[('company_id', '=', company_id), ('type', 'in', ('general', 'sale'))]" context="{'default_company_id': company_id, 'default_type': 'general'}" required="pos_company_has_template"/>
                                    </div>
                                    <div class="row">
                                        <label string="Invoices" for="pos_invoice_journal_id" class="col-lg-3 o_light_label"/>
                                        <field name="pos_invoice_journal_id"
                                            domain="[('company_id', '=', company_id), ('type', '=', 'sale')]"
                                            required="pos_company_has_template"
                                            context="{'default_company_id': company_id, 'default_type': 'sale'}"/>
                                    </div>
                                </div>
                            </setting>
                            <setting string="Closing Entry by product" id="pos_closeing_entry_by_product" help="Display the breakdown of sales lines by product in the automatically generated closing entry.">
                                <field name="pos_is_closing_entry_by_product"/>
                            </setting>
                            <setting id="pos-avatax" help="Use automatic taxes mapping with Avatax">
                                <field name="pos_module_pos_avatax" />
                            </setting>
                        </block>

                        <block title="Pricing" id="pos_pricing_section">
                            <setting id="multiple_prices_setting" string="Flexible Pricelists" help="Set multiple prices per product, automated discounts, etc." documentation="/applications/sales/point_of_sale/pricing/pricelists.html">
                                <field name="pos_use_pricelist" readonly="pos_has_active_session"/>
                                <div class="content-group" invisible="not pos_use_pricelist">
                                    <div class="row mt16">
                                        <label string="Available" for="pos_available_pricelist_ids" class="col-lg-3 o_light_label"/>
                                        <field name="pos_available_pricelist_ids" widget="many2many_tags" domain="['|',('company_id', '=', company_id),('company_id', '=', False)]" readonly="pos_has_active_session"/>
                                    </div>
                                    <div class="row mt16">
                                        <label string="Default" for="pos_pricelist_id" class="col-lg-3 o_light_label"/>
                                        <field name="pos_pricelist_id" domain="[('id', 'in', pos_allowed_pricelist_ids)]" options="{'no_create': True}"/>
                                    </div>
                                    <div class="mt8">
                                        <button name="%(product.product_pricelist_action2)d" icon="oi-arrow-right" type="action" string="Pricelists" groups="product.group_product_pricelist" class="btn-link"/>
                                    </div>
                                </div>
                            </setting>
                            <setting id="price_control" class="price_control" title="Only users with Manager access rights for PoS app can modify the product prices on orders." string="Price Control" help="Restrict price modification to managers">
                                <field name="pos_restrict_price_control"/>
                            </setting>
                            <setting id="product_prices" string="Product Prices" help="Product prices on receipts">
                                <field name="pos_iface_tax_included" class="o_light_label" widget="radio"/>
                                <div class="content-group">
                                    <widget class="oe-link" name="documentation_link" path="/applications/finance/accounting/taxation/taxes/B2B_B2C.html" label="How to manage tax-included prices" icon="oi oi-fw oi-arrow-right"/>
                                </div>
                            </setting>
                            <setting id="manual_discount" help="Allow cashiers to set a discount per line" documentation="/applications/sales/point_of_sale/pricing/discounts.html">
                                <field name="pos_manual_discount"/>
                            </setting>
                            <setting help="Adds a button to set a global discount" documentation="/applications/sales/point_of_sale/pricing/discounts.html">
                                <field name="pos_module_pos_discount" readonly="pos_has_active_session"/>
                                <div class="content-group mt16" invisible="not pos_module_pos_discount">
                                    <div class="text-warning mb4" id="warning_text_pos_discount" >
                                        Save this page and come back here to set up the feature.
                                    </div>
                                </div>
                            </setting>
                            <setting id="pos_pricer" string="Pricer" title="Pricer tags" help="Display and update your products information through electronic price tags">
                                <field name="module_pos_pricer"/>
                            </setting>
                            <setting id="pos-loyalty" title="Boost your sales with multiple kinds of programs: Coupons, Promotions, Gift Card, Loyalty. Specific conditions can be set (products, customers, minimum purchase amount, period). Rewards can be discounts (% or amount) or free products." string="Promotions, Coupons, Gift Card &amp; Loyalty Program" help="Manage promotion that will grant customers discounts or gifts" documentation="/applications/sales/point_of_sale/pricing/loyalty.html">
                                <field name="module_loyalty"/>
                            </setting>
                        </block>

                        <block title="Bills &amp; Receipts" id="pos_bills_and_receipts_section">
                            <setting help="Add a custom message to header and footer" documentation="/applications/sales/point_of_sale/receipts_invoices.html">
                                <field name="pos_is_header_or_footer"/>
                                <div class="content-group mt16" invisible="not pos_is_header_or_footer">
                                    <div>
                                        <label string="Header" for="pos_receipt_header" class="col-lg-2 o_light_label"/>
                                        <field name="pos_receipt_header" placeholder="e.g. Company Address, Website"/>
                                    </div>
                                    <div>
                                        <label string="Footer" for="pos_receipt_footer" class="col-lg-2 o_light_label"/>
                                        <field name="pos_receipt_footer" placeholder="e.g. Return Policy, Thanks for shopping with us!"/>
                                    </div>
                                </div>
                            </setting>
                            <setting id="auto_printing" help="Print receipts automatically once the payment is registered" documentation="/applications/sales/point_of_sale/receipts_invoices.html">
                                <field name="pos_iface_print_auto"/>
                                <div class="content-group mt16" invisible="not pos_iface_print_auto or not pos_is_posbox and not pos_other_devices">
                                    <div>
                                        <field name="pos_iface_print_skip_screen" class="oe_inline"/><span class="oe_inline"><b>Skip Preview Screen</b></span>
                                    </div>
                                </div>
                            </setting>
                            <setting id="order_reference" groups="base.group_no_one" string="Order Reference" help="Generation of your order references">
                                <field name="pos_sequence_id" readonly="1"/>
                            </setting>
                            <setting id="pos_sms" help="Send Text receipt Using sms">
                                <field name="pos_module_pos_sms"/>
                                <div class="content-group" readonly="pos_has_active_session">
                                    <div class="content-group mt16" invisible="not pos_module_pos_sms">
                                        <div class="text-warning mb4" id="warning_text_pos_sms" >
                                            Save this page and come back here to set up the feature.
                                        </div>
                                    </div>
                                </div>
                            </setting>
                            <setting help="Print information on the receipt to allow the customer to easily access the invoice anytime, from Odoo's portal." documentation="/applications/sales/point_of_sale/receipts_invoices.html">
                                <field name="point_of_sale_use_ticket_qr_code"/>
                                <div class="content-group mt16" invisible="not point_of_sale_use_ticket_qr_code">
                                    <div class="col mt16">
                                        <div class="content-group row">
                                            <label for="point_of_sale_ticket_portal_url_display_mode" class="col-lg-2" string="Print"/>
                                            <field name="point_of_sale_ticket_portal_url_display_mode"/>
                                        </div>
                                    </div>
                                </div>
                            </setting>
                            <setting help="Print basic ticket without prices. Can be used for gifts.">
                                <field name="pos_basic_receipt"/>
                            </setting>
                        </block>

                        <block title="Payment Terminals" help="Those settings are common to all PoS." id="pos_payment_terminals_section">
                            <setting id="adyen_payment_terminal_setting" title="The transactions are processed by Adyen. Set your Adyen credentials on the related payment method." string="Adyen" help="Accept payments with an Adyen payment terminal"
                                     documentation="/applications/sales/point_of_sale/payment_methods/terminals/adyen.html">
                                <field name="module_pos_adyen"/>
                            </setting>
                            <setting id="stripe_payment_terminal_setting" title="The transactions are processed by Stripe. Set your Stripe credentials on the related payment method." string="Stripe" help="Accept payments with a Stripe payment terminal" documentation="applications/sales/point_of_sale/payment_methods/terminals/stripe.html">
                                <field name="module_pos_stripe"/>
                            </setting>
                            <setting title="The transactions are processed by Six. Set the IP address of the terminal on the related payment method." string="Six" documentation="/applications/sales/point_of_sale/payment_methods/terminals/six.html" help="Accept payments with a Six payment terminal">
                                <field name="module_pos_six"/>
                            </setting>
                            <setting title="The transactions are processed by Viva Wallet on terminal or tap on phone." string="Viva Wallet" help="Accept payments with Viva Wallet on a terminal or tap on phone">
                                <field name="module_pos_viva_wallet"/>
                            </setting>
                            <setting title="The transactions are processed by PayTM. Set your PayTM credentials on the related payment method." string="PayTM" help="Accept payments with a PayTM payment terminal">
                                <field name="module_pos_paytm"/>
                            </setting>
                            <setting id="pos_razorpay_setting" title="The transactions are processed by Razorpay. Set your Razorpay credentials on the related payment method." string="Razorpay" help="Accept payments with a Razorpay payment terminal" documentation="/applications/sales/point_of_sale/payment_methods/terminals/razorpay.html">
                                <field name="module_pos_razorpay"/>
                            </setting>
                            <setting title="The transactions are processed by Mercado Pago on terminal" string="Mercado Pago" help="Accept payments with Mercado Pago on a terminal">
                                <field name="module_pos_mercado_pago"/>
                            </setting>
                        </block>

                        <block title="Connected Devices" id="pos_connected_devices_section">
                            <setting id="pos_other_devices" string="ePos Printer" help="Connect device to your PoS without an IoT Box" documentation="/applications/sales/point_of_sale/configuration/epos_ssc.html">
                                <field name="pos_other_devices"/>
                            </setting>
                            <setting id="customer_display" string="Customer Display" help="Show checkout to customers through a second display" documentation="/applications/sales/point_of_sale/shop/customer_display.html" >
                                <div>
                                    <field name="pos_customer_display_type"/>
                                    <div class="mt-1" invisible="pos_customer_display_type == 'none'">
                                        <label for="pos_customer_display_bg_img" />
                                        <field name="pos_customer_display_bg_img" class="w-100" filename="pos_customer_display_bg_img_name"/>
                                    </div>
                                </div>
                            </setting>
                            <setting string="IoT Box" help="Connect devices using an IoT Box">
                                <field name="pos_is_posbox"/>
                                <div class="content-group pos_iot_config" invisible="not pos_is_posbox">
                                    <div class="row">
                                        <label string="IoT Box IP Address" for="pos_proxy_ip" class="col-lg-4 o_light_label"/>
                                        <field name="pos_proxy_ip"/>
                                    </div>
                                    <div class="row iot_barcode_scanner">
                                        <label string="Barcode Scanner/Card Reader" for="pos_iface_scan_via_proxy" class="col-lg-4 o_light_label"/>
                                        <field name="pos_iface_scan_via_proxy"/>
                                    </div>
                                    <div class="row">
                                        <label string="Electronic Scale" for="pos_iface_electronic_scale" class="col-lg-4 o_light_label"/>
                                        <field name="pos_iface_electronic_scale"/>
                                    </div>
                                    <div class="row">
                                        <label string="Receipt Printer" for="pos_iface_print_via_proxy" class="col-lg-4 o_light_label"/>
                                        <field name="pos_iface_print_via_proxy"/>
                                    </div>
                                    <div class="row" invisible="not pos_iface_print_via_proxy">
                                        <label string="Cashdrawer" for="pos_iface_cashdrawer" class="col-lg-4 o_light_label"/>
                                        <field name="pos_iface_cashdrawer"/>
                                    </div>
                                </div>
                            </setting>
                        </block>

                        <block title="Preparation" id="pos_preparation_section">
                            <setting string="Preparation Printers" help="Print orders at the kitchen, at the bar, etc." id="is_order_printer">
                                <field name="pos_is_order_printer"/>
                                <div class="content-group" invisible="not pos_is_order_printer">
                                    <div class="mt16">
                                        <label string="Printers" for="pos_printer_ids" class="o_light_label"/>
                                        <field name="pos_printer_ids" widget="many2many_tags"/>
                                    </div>
                                    <div>
                                        <button name="%(point_of_sale.action_pos_printer_form)d" icon="oi-arrow-right" type="action" string="Printers" class="btn-link"/>
                                    </div>
                                </div>
                            </setting>
                            <setting string="Preparation Display" help="Display orders on the preparation display" id="preparation_display">
                                <field name="module_pos_preparation_display" widget="upgrade_boolean"/>
                            </setting>
                            <setting string="Internal Notes" help="Add internal notes on order lines for the kitchen" id="iface_orderline_notes">
                                <div class="content-group">
                                    <div class="mt16">
                                        <label string="Note Models" for="pos_note_ids" class="o_light_label me-2"/>
                                        <field name="pos_note_ids" widget="many2many_tags" readonly="pos_has_active_session" placeholder="All notes"/>
                                    </div>
                                    <div>
                                        <button name="%(point_of_sale.action_pos_note_model)d" icon="oi-arrow-right" type="action" string="Notes" class="btn-link"/>
                                    </div>
                                </div>
                            </setting>
                        </block>

                        <block title="Inventory" id="pos_inventory_section">
                            <setting title="Operation types show up in the Inventory dashboard." string="Operation Type" help="Used to record product pickings. Products are consumed from its default source location.">
                                <field name="pos_picking_type_id" domain="[('company_id', '=', company_id)]" required="pos_config_id"/>
                            </setting>
                            <setting string="Allow Ship Later" help="Sell products and deliver them later.">
                                <field name="pos_ship_later"/>
                                <div class="content-group mt16" invisible="not pos_ship_later">
                                    <div class="row">
                                        <label for="pos_warehouse_id" string="Warehouse" class="col-lg-3 o_light_label"/>
                                        <field name="pos_warehouse_id" required="pos_ship_later"/>
                                    </div>
                                    <div groups="stock.group_adv_location" class="row">
                                        <label for="pos_route_id" string="Specific route" class="col-lg-3 o_light_label"/>
                                        <field name="pos_route_id"/>
                                    </div>
                                    <div class="row">
                                        <label for="pos_picking_policy" class="col-lg-3 o_light_label"/>
                                        <field name="pos_picking_policy" required="pos_ship_later"/>
                                    </div>
                                </div>
                            </setting>
                            <setting id="barcode_scanner">
                                <span class="o_form_label">Barcodes</span>
                                <i class="fa fa-info-circle me-1" title="This setting is common to all PoS." pos-data-toggle="tooltip"/>
                                <div class="text-muted">
                                    Use barcodes to scan products, customer cards, etc.
                                </div>
                                <div class="content-group mt16 row">
                                    <label for="barcode_nomenclature_id" string="Barcode Nomenclature" class="col-lg-3 o_light_label"/>
                                    <field name="barcode_nomenclature_id"/>
                                </div>
                            </setting>
                            <setting id="update_quantities_stock_setting" groups="base.group_no_one" string="Inventory Management" company_dependent="1" help="Update quantities in stock">
                                <field name="update_stock_quantities" colspan="4" widget="radio"/>
                            </setting>
                        </block>
                    </div>
                </app>
            </xpath>
        </field>
    </record>
</odoo>
