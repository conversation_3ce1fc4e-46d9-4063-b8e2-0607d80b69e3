# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* portal
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <dragan.vukosa<PERSON><PERSON>@gmail.com>, 2022
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-08 06:52+0000\n"
"PO-Revision-Date: 2022-09-22 05:53+0000\n"
"Last-Translator: <PERSON><PERSON> <dragan.vukosavl<PERSON><EMAIL>>, "
"2022\n"
"Language-Team: Serbian (https://app.transifex.com/odoo/teams/41243/sr/)\n"
"Language: sr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "\" to validate your action."
msgstr "\" da potvrdite svoju akciju."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_sidebar.js:0
msgid "%s days overdue"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "1. Enter your password to confirm you own this account"
msgstr ""
"1. Unesite svoju lozinku kako biste potvrdili da vi poseduje ovaj nalog"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid ""
"2. Confirm you want to delete your account by\n"
"                                        copying down your login ("
msgstr ""
"2. Potvrdite da želi da obrišete svoj nalog tako što\n"
"                                        kopirati svoje korisničko ime ("

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.my_account_link
msgid "<i class=\"fa fa-fw fa-id-card-o me-1 small text-primary\"/> My Account"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.user_dropdown
msgid "<i class=\"fa fa-fw fa-sign-out me-1 small text-primary\"/> Logout"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.user_dropdown
msgid "<i class=\"fa fa-fw fa-th me-1 small text-primary\"/> Apps"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.side_content
msgid "<i class=\"fa fa-pencil\"/> Edit information"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
msgid "<i class=\"oi oi-arrow-right me-1\"/>Back to edit mode"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.record_pager
msgid ""
"<i class=\"oi oi-chevron-left\" role=\"img\" aria-label=\"Previous\" "
"title=\"Previous\"/>"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.record_pager
msgid ""
"<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"Next\" "
"title=\"Next\"/>"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid ""
"<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>"
msgstr ""
"<i title=\"Dokumentacija\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "<option value=\"\">Country...</option>"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "<option value=\"\">select...</option>"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid ""
"<small class=\"form-text text-muted\">\n"
"                Company name, VAT Number and country can not be changed once "
"document(s) have been issued for your account.\n"
"                <br/>Please contact us directly for that operation.\n"
"            </small>"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.pager
msgid ""
"<span class=\"fa fa-chevron-left\" role=\"img\" aria-label=\"Previous\" "
"title=\"Previous\"/>"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.pager
msgid ""
"<span class=\"fa fa-chevron-right\" role=\"img\" aria-label=\"Next\" "
"title=\"Next\"/>"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small me-1 navbar-text\">Filter By:</span>"
msgstr "<span class=\"small me-1 navbar-text\">Filtriraj po:</span>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small me-1 navbar-text\">Group By:</span>"
msgstr "<span class=\"small me-1 navbar-text\">Grupiši po:</span>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small me-1 navbar-text\">Sort By:</span>"
msgstr "<span class=\"small me-1 navbar-text\">Sortiraj po:</span>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "<strong>Open </strong>"
msgstr "<strong>Open </strong>"

#. module: portal
#: model:mail.template,body_html:portal.mail_template_data_portal_welcome
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: "
"16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; "
"color: #454748; width: 100%; border-collapse:separate;\"><tr><td "
"align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" "
"style=\"padding: 16px; background-color: white; color: #454748; border-"
"collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" "
"width=\"590\" style=\"min-width: 590px; background-color: white; padding: "
"0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Account</"
"span><br>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-"
"out=\"object.user_id.name or ''\">Marc Demo</span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object."
"user_id.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.user_id."
"company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: "
"80px;\" t-att-alt=\"object.user_id.company_id.name\">\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:"
"rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;"
"min-height:1px;line-height:0; margin:16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" "
"width=\"590\" style=\"min-width: 590px; background-color: white; padding: "
"0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"object.user_id.name or ''\">Marc "
"Demo</t>,<br> <br>\n"
"                        Welcome to <t t-out=\"object.user_id.company_id."
"name\">YourCompany</t>'s Portal!<br><br>\n"
"                        An account has been created for you with the "
"following login: <t t-out=\"object.user_id.login\">demo</t><br><br>\n"
"                        Click on the button below to pick a password and "
"activate your account.\n"
"                        <div style=\"margin: 16px 0px 16px 0px; text-align: "
"center;\">\n"
"                            <a t-att-href=\"object.user_id.signup_url\" "
"style=\"display: inline-block; padding: 10px; text-decoration: none; font-"
"size: 12px; background-color: #875A7B; color: #fff; border-radius: 5px;\">\n"
"                                <strong>Activate Account</strong>\n"
"                            </a>\n"
"                        </div>\n"
"                        <t t-out=\"object.wizard_id.welcome_message or "
"''\">Welcome to our company's portal.</t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:"
"rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;"
"min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" "
"width=\"590\" style=\"min-width: 590px; background-color: white; font-size: "
"11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.user_id.company_id.name or "
"''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: "
"0.7;\">\n"
"                    <t t-out=\"object.user_id.company_id.phone or ''\">+1 "
"************</t>\n"
"                    <t t-if=\"object.user_id.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.user_id."
"company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-"
"out=\"object.user_id.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.user_id.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.user_id."
"company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-"
"out=\"object.user_id.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" "
"style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; "
"padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?"
"utm_source=db&amp;utm_medium=portalinvite\" style=\"color: #875A7B;\">Odoo</"
"a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: portal
#: model:ir.model,name:portal.model_res_users_apikeys_description
msgid "API Key Description"
msgstr ""

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
msgid "API Key Ready"
msgstr "API ključ je spreman"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/signature_form/signature_form.js:0
msgid "Accept & Sign"
msgstr "Prihvati & potpiši"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_warning
#: model:ir.model.fields,field_description:portal.field_portal_share__access_warning
msgid "Access warning"
msgstr "Upozorenje: nemate pravo pristupa"

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Account deleted!"
msgstr "Nalog je obrisan!"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Add a note"
msgstr "Dodaj belešku"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Add attachment"
msgstr "Dodaj prilog"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Add contacts to share the document..."
msgstr "Dodaj kontakte da podeliš dokument..."

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_share__note
msgid "Add extra content to display in the email"
msgstr "Dodajte dopunski sadržaj za prikazivanje u email-u"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_home
msgid "Add, remove or modify your addresses"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Added On"
msgstr "Dodato dana"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_home
msgid "Addresses"
msgstr ""

#. module: portal
#: model:ir.model.fields.selection,name:portal.selection__portal_wizard_user__email_state__exist
msgid "Already Registered"
msgstr "Već registrovan/a"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Are you sure you want to do this?"
msgstr "Da li ste sigurni da to želite da uradite?"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Avatar"
msgstr "Avatar"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Cancel"
msgstr "Otkaži"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Change Password"
msgstr "Promeni lozinku"

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid ""
"Changing VAT number is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"Promena PIB broja nije dozvoljena nakon što je dokument već izdat za vaš "
"nalog. Molimo vas da nas direktno kontaktirate za ovu operativnu aktivnost."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid ""
"Changing company name is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"Promena imena kompanije nije dozvoljena nakon što je dokument već izdat za "
"vaš nalog. Molimo vas da nas direktno kontaktirate za ovu operativnu "
"aktivnost."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid ""
"Changing the country is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
msgid "Check failed"
msgstr "Provera nije uspela"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "City"
msgstr "Grad"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/signature_form/signature_form.xml:0
msgid "Click here to see your document."
msgstr "Klikni ovde da vidiš svoj dokument."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
#: model_terms:ir.ui.view,arch_db:portal.side_content
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Close"
msgstr "Zatvori"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Company Name"
msgstr "Ime kompanije"

#. module: portal
#: model:ir.model,name:portal.model_res_config_settings
msgid "Config Settings"
msgstr "Podešavanje konfiguracije"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_home
msgid "Configure your connection parameters"
msgstr ""

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
msgid "Confirm"
msgstr "Potvrdi"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
msgid "Confirm Password"
msgstr "Potvrdi lozinku"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_home
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Connection &amp; Security"
msgstr ""

#. module: portal
#: model:ir.model,name:portal.model_res_partner
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__partner_id
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
#: model_terms:ir.ui.view,arch_db:portal.side_content
msgid "Contact"
msgstr "Kontakt"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Contact Details"
msgstr "Kontakt podaci"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Contacts"
msgstr "Kontakti"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_composer.js:0
msgid "Could not save file <strong>%s</strong>"
msgstr "Nije moguće sačuvati datoteku <strong>%s</strong>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Country"
msgstr "Zemlja"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__create_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard__create_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__create_uid
msgid "Created by"
msgstr "Kreirao/la"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__create_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard__create_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__create_date
msgid "Created on"
msgstr "Kreiran"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid ""
"Currently available to everyone viewing this document, click to restrict to "
"internal employees."
msgstr ""
"Trenutno dostupan svima koji pregledaju ovaj dokument, kliknite da biste "
"ograničili na interne zaposlene."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid ""
"Currently restricted to internal employees, click to make it available to "
"everyone viewing this document."
msgstr ""
"Trenutno je ograničeno na interne zaposlene, kliknite da biste ga učinili "
"dostupnim svima koji pregledaju ovaj dokument."

#. module: portal
#: model:ir.model.fields,field_description:portal.field_res_config_settings__portal_allow_api_keys
msgid "Customer API Keys"
msgstr "API ključevi korisnika"

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_mixin__access_url
msgid "Customer Portal URL"
msgstr "URK korisničkog portala"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.res_config_settings_view_form
msgid "Customers can generate API Keys"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "Dear"
msgstr "Draga/i"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Delete"
msgstr "Obriši"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Delete Account"
msgstr "Obriši nalog"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Description"
msgstr "Opis"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_breadcrumbs
msgid "Details"
msgstr "Detalji"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Developer API Keys"
msgstr "API ključevi developera"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid ""
"Disable your account, preventing any further login.<br/>\n"
"                                        <b>\n"
"                                            <i class=\"fa fa-exclamation-"
"triangle text-danger\"/>\n"
"                                            This action cannot be undone.\n"
"                                        </b>"
msgstr ""
"Onemogućite svoj nalog, uz zabranu svih budućih prijava.<br/>\n"
"                                        <b>\n"
"                                            <i class=\"fa fa-exclamation-"
"triangle text-danger\"/>\n"
"                                            Ova aktivnost ne može biti "
"opozvana.\n"
"                                        </b>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Discard"
msgstr "Poništi"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__display_name
#: model:ir.model.fields,field_description:portal.field_portal_wizard__display_name
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_sidebar.js:0
msgid "Due in %s days"
msgstr ""

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_sidebar.js:0
msgid "Due today"
msgstr "Dospeva danas"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__email
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Email"
msgstr "E-mail"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Email Address already taken by another user"
msgstr "Email adresa je već zauzeta od strane drugog korisnika"

#. module: portal
#: model:ir.model,name:portal.model_mail_thread
msgid "Email Thread"
msgstr "Email niz"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Enter a description of and purpose for the key."
msgstr "Upišite opis i svrhu ključa."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Forgot password?"
msgstr "Zaboravili ste lozinku?"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Go Back"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Grant Access"
msgstr "Dodeli pristup"

#. module: portal
#: model:ir.model,name:portal.model_portal_wizard
msgid "Grant Portal Access"
msgstr "Odobri pristup Portalu"

#. module: portal
#: model:ir.actions.act_window,name:portal.partner_wizard_action
#: model:ir.actions.server,name:portal.partner_wizard_action_create_and_open
msgid "Grant portal access"
msgstr "Odobri pristup portalu"

#. module: portal
#: model:ir.model,name:portal.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP rutiranje"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid ""
"Here is your new API key, use it instead of a password for RPC access.\n"
"                Your login is still necessary for interactive usage."
msgstr ""
"Ovo je vaš novi API ključ, koristite ga umesto lozinke za RPC pristup.\n"
"                Vaše prijavljivanje je još uvek neophodno za interaktivno "
"korišćenje."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_breadcrumbs
msgid "Home"
msgstr "Početak"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__id
#: model:ir.model.fields,field_description:portal.field_portal_wizard__id
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__id
msgid "ID"
msgstr "ID"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Important:"
msgstr "Važno:"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Internal Note"
msgstr "Interna beleška"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Internal User"
msgstr "Interni korisnik"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Internal notes are only displayed to internal users."
msgstr "Interne beleške se prikazuju samo internim korisnicima."

#. module: portal
#: model:ir.model.fields.selection,name:portal.selection__portal_wizard_user__email_state__ko
msgid "Invalid"
msgstr "Neispravno"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Invalid Email Address"
msgstr "Neispravna email adresa"

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Invalid Email! Please enter a valid email address."
msgstr "Neispravan email! Molimo vas unesite ispravnu email adresu."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Invalid report type: %s"
msgstr "Neispravan tip izveštaja: %s"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__welcome_message
msgid "Invitation Message"
msgstr "Pozivna poruka"

#. module: portal
#: model:mail.template,description:portal.mail_template_data_portal_welcome
msgid "Invitation email to contacts to create a user account"
msgstr "Pozivni email kontaktima da kreiraju korisnički nalog"

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_share.py:0
msgid "Invitation to access %s"
msgstr ""

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__is_internal
msgid "Is Internal"
msgstr "Je interni"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__is_portal
msgid "Is Portal"
msgstr "Je Portal"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid ""
"It is very important that this description be clear\n"
"                and complete,"
msgstr ""
"Veoma je važno da ovaj opis bude jasan\n"
"                i kompletan,"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__write_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard__write_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__write_uid
msgid "Last Updated by"
msgstr "Poslednji ažurirao/la"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__write_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard__write_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__write_date
msgid "Last Updated on"
msgstr "Poslednji put ažurirano dana"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__login_date
msgid "Latest Authentication"
msgstr "Poslednja prijava"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Leave a comment"
msgstr "Ostavi komentar"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__share_link
msgid "Link"
msgstr "Link"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Log out from all devices"
msgstr ""

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
msgid "Log out from all devices?"
msgstr ""

#. module: portal
#: model:ir.model,name:portal.model_mail_message
msgid "Message"
msgstr "Poruka"

#. module: portal
#. odoo-python
#: code:addons/portal/models/mail_thread.py:0
msgid ""
"Model %(model_name)s does not support token signature, as it does not have "
"%(field_name)s field."
msgstr ""
"Model %(model_name)s ne podržava potpis tokena, jer nema %(field_name)s "
"polje ."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Multi company reports are not supported."
msgstr "Izveštaji za više kompanija nisu podržani."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
msgid "My account"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Name"
msgstr "Naziv"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Name your key"
msgstr "Dajte naziv svom ključu"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "New API Key"
msgstr "Novi API ključ"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "New Password:"
msgstr "Nova lozinka:"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Next"
msgstr "Sledeće"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_home
msgid "No Documents to display"
msgstr "Nema dokumenata za prikaz"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__note
msgid "Note"
msgstr "Napomena"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "Odoo Logo"
msgstr "Odoo Logo"

#. module: portal
#. odoo-python
#: code:addons/portal/models/res_users_apikeys_description.py:0
msgid "Only internal and portal users can create API keys"
msgstr "Samo interni i portal korisnici mogu kreirati API ključeve"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Oops! Something went wrong. Try to reload the page and log in."
msgstr ""
"Upss! Nešto je krenulo po zlu. Pokušajte da ponovo učitate stranicu i da se "
"prijavite."

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__partner_ids
msgid "Partners"
msgstr "Partneri"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Password"
msgstr "Lozinka"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Password Updated!"
msgstr "Lozinka je ažurirana!"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Password:"
msgstr "Lozinka:"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Phone"
msgstr "Telefon"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Please enter your password to confirm you own this account"
msgstr ""
"Molimo unesite svoju lozinku da biste potvrdili da ste vlasnik ovog naloga"

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Portal Access Management"
msgstr "Upravljanje pristupa Portalu"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_url
msgid "Portal Access URL"
msgstr "URL pristupa korisničkom portalu"

#. module: portal
#: model:ir.model,name:portal.model_portal_mixin
msgid "Portal Mixin"
msgstr "Portal Mixin"

#. module: portal
#: model:ir.model,name:portal.model_portal_share
msgid "Portal Sharing"
msgstr "Deljenje Portala"

#. module: portal
#: model:ir.model,name:portal.model_portal_wizard_user
msgid "Portal User Config"
msgstr "Podešavanje korisnika Portala"

#. module: portal
#: model:mail.template,name:portal.mail_template_data_portal_welcome
msgid "Portal: User Invite"
msgstr "Portal: Poziv korisniku"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "Powered by"
msgstr "Platformu pokreće"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Previous"
msgstr "Prethodno"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Public"
msgstr ""

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Published on"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid ""
"Put my email and phone in a block list to make sure I'm never contacted again"
msgstr ""
"Stavi moj email i telefon na listu blokiranih kontakata da osiguraš da ne "
"budem kontaktiran/a više nikad"

#. module: portal
#: model:ir.model,name:portal.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Re-Invite"
msgstr "Pozovi ponovo"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__partner_ids
msgid "Recipients"
msgstr "Primaoci"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__resource_ref
msgid "Related Document"
msgstr "Povezani dokument"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__res_id
msgid "Related Document ID"
msgstr "ID povezanog dokumenta"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__res_model
msgid "Related Document Model"
msgstr "Model povezanog dokumenta"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Revoke Access"
msgstr "Opozovi pristup"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Revoke All Sessions"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Save"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Scope"
msgstr "Obim"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "Search"
msgstr "Pronađi"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Security"
msgstr "Bezbednost"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
msgid "Security Control"
msgstr "Kontrola bezbednosti"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_token
msgid "Security Token"
msgstr "Sigurnosni token"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid ""
"Select which contacts should belong to the portal in the list below.\n"
"                        The email address of each selected contact must be "
"valid and unique.\n"
"                        If necessary, you can fix any contact's email "
"address directly in the list."
msgstr ""
"Izaberite koji kontakti iz liste u nastavku treba da pripadaju portalu.\n"
"                        Email adresa svakog kontakta mora biti ispravna i "
"jedinstvena.\n"
"                        Ako je potrebno, možete ispraviti email adresu "
"svakog od kontakata direktno na listi."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Send"
msgstr "Pošalji"

#. module: portal
#: model:ir.actions.act_window,name:portal.portal_share_action
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Share Document"
msgstr "Podeli dokument"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_ir_ui_view__customize_show
msgid "Show As Optional Inherit"
msgstr "Prikaži kao opcionalno nasleđivanje"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.user_sign_in
msgid "Sign in"
msgstr "Prijavi se"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_composer.js:0
msgid ""
"Some fields are required. Please make sure to write a message or attach a "
"document"
msgstr "Neka polja su obavezna. Napišite poruku ili priložite dokument"

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Some required fields are empty."
msgstr "Neka obavezna polja su prazna."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "State / Province"
msgstr "Država / Pokrajina"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__email_state
msgid "Status"
msgstr "Status"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Street"
msgstr "Ulica"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/signature_form/signature_form.xml:0
msgid "Thank You!"
msgstr "Hvala vam!"

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "The attachment %s cannot be removed because it is linked to a message."
msgstr "Prilog %s ne može biti uklonjen jer je povezan sa porukom."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid ""
"The attachment %s cannot be removed because it is not in a pending state."
msgstr "Prilog %s ne može biti uklonjen jer nije u statusu na čekanju."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid ""
"The attachment does not exist or you do not have the rights to access it."
msgstr "Prilog ne postoji ili vi nemate prava da mu pristupite."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "The contact \"%s\" does not have a valid email."
msgstr "Kontakt \"%s\" nema ispravan email."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "The contact \"%s\" has the same email as an existing user"
msgstr "Kontakt \"%s\" ima isti email kao postojeći korisnik"

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "The document does not exist or you do not have the rights to access it."
msgstr "Dokument ne postoji ili vi nemate prava da mu pristupite."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "The key cannot be retrieved later and provides"
msgstr "Ključ se ne može kasnije preuzeti i obezbeđuje"

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "The new password and its confirmation must be identical."
msgstr "Nova lozinka i njena potvrda moraju biti identični."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid ""
"The old password you provided is incorrect, your password was not changed."
msgstr ""
"Stara lozinka koju ste uneli nije ispravna, vaša lozinka nije promenjena."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "The partner \"%s\" already has the portal access."
msgstr "Partner \"%s\" već ima pristup portalu."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "The partner \"%s\" has no portal access or is internal."
msgstr "Partner \"%s\" nema pristup portalu ili je interni korisnik."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid ""
"The template \"Portal: new user\" not found for sending email to the portal "
"user."
msgstr ""
"Šablon \"Portal: novi korisnik\" nije pronađen za slanje email-a novom "
"korisniku portala."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "There are no comments for now."
msgstr "Za sada nema komentara."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "This document does not exist."
msgstr "Ovaj dokument ne postoji."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
msgid "This is a preview of the customer portal."
msgstr "Ovo je pregled korisničkog portala."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid ""
"This partner is linked to an internal User and already has access to the "
"Portal."
msgstr "Partner je povezan sa internim korisnikom i već ima pristup portalu."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "This text is included at the end of the email sent to new portal users."
msgstr ""
"Ovaj tekst je uključen i dodat na kraj email-a poslatog novim korisnicima "
"portala."

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_wizard__welcome_message
msgid "This text is included in the email sent to new users of the portal."
msgstr ""
"Ovaj tekst je uključen u email koji se šalje novim korisnicima portala."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "Toggle filters"
msgstr "Toggle filters"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Type in your password to confirm :"
msgstr ""

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__user_id
msgid "User"
msgstr "Korisnik"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__user_ids
msgid "Users"
msgstr "Korisnici"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "VAT Number"
msgstr "PIB broj"

#. module: portal
#: model:ir.model.fields.selection,name:portal.selection__portal_wizard_user__email_state__ok
msgid "Valid"
msgstr "Važeće"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Valid Email Address"
msgstr "Važeća email adresa"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Verify New Password:"
msgstr "Potvrdi novu lozinku:"

#. module: portal
#: model:ir.model,name:portal.model_ir_ui_view
msgid "View"
msgstr "Pregled"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_account_analytic_account__website_message_ids
#: model:ir.model.fields,field_description:portal.field_calendar_event__website_message_ids
#: model:ir.model.fields,field_description:portal.field_crm_team__website_message_ids
#: model:ir.model.fields,field_description:portal.field_crm_team_member__website_message_ids
#: model:ir.model.fields,field_description:portal.field_discuss_channel__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle_log_contract__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle_log_services__website_message_ids
#: model:ir.model.fields,field_description:portal.field_gamification_badge__website_message_ids
#: model:ir.model.fields,field_description:portal.field_gamification_challenge__website_message_ids
#: model:ir.model.fields,field_description:portal.field_hr_department__website_message_ids
#: model:ir.model.fields,field_description:portal.field_hr_employee__website_message_ids
#: model:ir.model.fields,field_description:portal.field_hr_job__website_message_ids
#: model:ir.model.fields,field_description:portal.field_loyalty_card__website_message_ids
#: model:ir.model.fields,field_description:portal.field_lunch_supplier__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_cc__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_main_attachment__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_phone__website_message_ids
#: model:ir.model.fields,field_description:portal.field_maintenance_equipment__website_message_ids
#: model:ir.model.fields,field_description:portal.field_maintenance_equipment_category__website_message_ids
#: model:ir.model.fields,field_description:portal.field_maintenance_request__website_message_ids
#: model:ir.model.fields,field_description:portal.field_phone_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_pricelist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_product__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_template__website_message_ids
#: model:ir.model.fields,field_description:portal.field_rating_mixin__website_message_ids
#: model:ir.model.fields,field_description:portal.field_res_partner__website_message_ids
#: model:ir.model.fields,field_description:portal.field_res_users__website_message_ids
msgid "Website Messages"
msgstr "Website poruke"

#. module: portal
#: model:ir.model.fields,help:portal.field_account_analytic_account__website_message_ids
#: model:ir.model.fields,help:portal.field_calendar_event__website_message_ids
#: model:ir.model.fields,help:portal.field_crm_team__website_message_ids
#: model:ir.model.fields,help:portal.field_crm_team_member__website_message_ids
#: model:ir.model.fields,help:portal.field_discuss_channel__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle_log_contract__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle_log_services__website_message_ids
#: model:ir.model.fields,help:portal.field_gamification_badge__website_message_ids
#: model:ir.model.fields,help:portal.field_gamification_challenge__website_message_ids
#: model:ir.model.fields,help:portal.field_hr_department__website_message_ids
#: model:ir.model.fields,help:portal.field_hr_employee__website_message_ids
#: model:ir.model.fields,help:portal.field_hr_job__website_message_ids
#: model:ir.model.fields,help:portal.field_loyalty_card__website_message_ids
#: model:ir.model.fields,help:portal.field_lunch_supplier__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_cc__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_main_attachment__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_phone__website_message_ids
#: model:ir.model.fields,help:portal.field_maintenance_equipment__website_message_ids
#: model:ir.model.fields,help:portal.field_maintenance_equipment_category__website_message_ids
#: model:ir.model.fields,help:portal.field_maintenance_request__website_message_ids
#: model:ir.model.fields,help:portal.field_phone_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_product_pricelist__website_message_ids
#: model:ir.model.fields,help:portal.field_product_product__website_message_ids
#: model:ir.model.fields,help:portal.field_product_template__website_message_ids
#: model:ir.model.fields,help:portal.field_rating_mixin__website_message_ids
#: model:ir.model.fields,help:portal.field_res_partner__website_message_ids
#: model:ir.model.fields,help:portal.field_res_users__website_message_ids
msgid "Website communication history"
msgstr "Istorija website komunikacije"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "What's this key for?"
msgstr "Čemu služi ovaj ključ?"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__wizard_id
msgid "Wizard"
msgstr "Čarobnjak"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Write a message..."
msgstr "Napiši poruku..."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Write down your key"
msgstr "Zapišite svoj ključ"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Wrong password."
msgstr "Pogrešna lozinka."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid ""
"You are about to log out from all devices that currently have access to this "
"user's account."
msgstr ""

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "You cannot leave any password empty."
msgstr "Ne možete ostavi nijednu lozinku praznu."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "You must be"
msgstr "Morate biti"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "You should enter \""
msgstr "Treba da unesete \""

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "You should first grant the portal access to the partner \"%s\"."
msgstr "Prvo treba da odobrite pristup portalu za partnera \"%s\"."

#. module: portal
#: model:mail.template,subject:portal.mail_template_data_portal_welcome
msgid "Your account at {{ object.user_id.company_id.name }}"
msgstr "Vaš nalog kod {{ object.user_id.company_id.name }}"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_contact
msgid "Your contact"
msgstr "Vaši kontakt podaci"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Zip / Postal Code"
msgstr "Poštanski broj / PAK"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "avatar"
msgstr "avatar"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "comment"
msgstr "komentar"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "comments"
msgstr "komentari"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "full access"
msgstr "potpuni pristup"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "has invited you to access the following"
msgstr ""

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid ""
"it will be the only way to\n"
"                identify the key once created"
msgstr ""
"to će biti jedini način da\n"
"                identifikuje ključ kada bude kreiran"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "logged in"
msgstr "prijavljeni"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "odoo"
msgstr "odoo"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "password"
msgstr "lozinka"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "to post a comment."
msgstr "da ostavite komentar."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "to your user account, it is very important to store it securely."
msgstr "svom korisničkom nalogu, veoma je važno da ga bezbedno sačuvate."

#~ msgid ""
#~ "<i class=\"fa fa-fw fa-id-card-o me-1 small text-muted\"/> My Account"
#~ msgstr ""
#~ "<i class=\"fa fa-fw fa-id-card-o me-1 small text-muted\"/> Moj nalog"

#~ msgid "<i class=\"fa fa-fw fa-sign-out me-1 small text-muted\"/> Logout"
#~ msgstr "<i class=\"fa fa-fw fa-sign-out me-1 small text-muted\"/> Odjavi se"

#~ msgid "<i class=\"fa fa-fw fa-th me-1 small text-muted\"/> Apps"
#~ msgstr "<i class=\"fa fa-fw fa-th me-1 small text-muted\"/> Apps"

#~ msgid "<i class=\"fa fa-pencil mx-1\"/>Edit Security Settings"
#~ msgstr "<i class=\"fa fa-pencil mx-1\"/>Uredi bezbednosna podešavanja"

#~ msgid "<i class=\"fa fa-pencil\"/> Edit"
#~ msgstr "<i class=\"fa fa-pencil\"/> Uredi"

#~ msgid ""
#~ "<small class=\"form-text text-muted\">Changing company name or VAT number "
#~ "is not allowed once document(s) have been issued for your account. <br/"
#~ ">Please contact us directly for this operation.</small>"
#~ msgstr ""
#~ "<small class=\"form-text text-muted\">Promena naziva kompanije ili PIB "
#~ "broja nije dozvoljena kada su dokumenta već izdata za vaš nalog. <br/>Za "
#~ "ovu operaciju kontaktirajte nas direktno.</small>"

#~ msgid "Account Security"
#~ msgstr "Bezbednost naloga"

#~ msgid "An access token must be provided for each attachment."
#~ msgstr "Pristupni token mora biti dostavljen za svaki prilog."

#~ msgid ""
#~ "Confirm\n"
#~ "                                <span class=\"fa fa-long-arrow-right\"/>"
#~ msgstr ""
#~ "Potvrdi\n"
#~ "                                <span class=\"fa fa-long-arrow-right\"/>"

#~ msgid "Documents"
#~ msgstr "Dokumenta"

#~ msgid "Employees Only"
#~ msgstr "Samo zaposleni"

#~ msgid "Prev"
#~ msgstr "Prethodno"

#~ msgid "Published on %s"
#~ msgstr "Objavljeno dana %s"

#~ msgid ""
#~ "The attachment %s does not exist or you do not have the rights to access "
#~ "it."
#~ msgstr "Prilog %s ne postoji ili vi nemate prava da mu pristupite."

#~ msgid "Visible"
#~ msgstr "Vidljivo"

#~ msgid "You are invited to access %s"
#~ msgstr "Pozvani ste da pristupite %s"

#~ msgid "You have been invited to access the following document:"
#~ msgstr "Pozvani ste da pristupite sledećem dokumentu:"
