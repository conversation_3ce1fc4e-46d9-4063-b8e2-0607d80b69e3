# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery
# 
# Translators:
# <PERSON>, 2024
# NoaFarkash, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# ya<PERSON> terner, 2024
# שהא<PERSON> חוסיין <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# or balmas, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:03+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: or balmas, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: delivery
#. odoo-python
#: code:addons/delivery/wizard/choose_delivery_carrier.py:0
msgid "%(carrier)s Error"
msgstr "%(carrier)s שגיאה"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/sale_order.py:0
msgid ""
"%s\n"
"Free Shipping"
msgstr ""
"%s\n"
"משלוח חינם"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
msgid "%s (copy)"
msgstr "%s (העתק)"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "<i class=\"oi oi-arrow-right me-1\"/>Get rate"
msgstr ""

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
msgid ""
"<p class=\"o_view_nocontent\">\n"
"                    Buy Odoo Enterprise now to get more providers.\n"
"                </p>"
msgstr ""
"<p class=\"o_view_nocontent\">\n"
"                    קנה Odoo Enterprise עכשיו כדי לקבל ספקים נוספים.\n"
"                </p>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"<span class=\"o_stat_text o_warning_text fw-bold\">Test</span>\n"
"                                <span class=\"o_stat_text\">Environment</span>"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "<span class=\"o_stat_text text-danger\">No debug</span>"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "<span class=\"text-success\">Debug requests</span>"
msgstr "<span class=\"text-success\">בקשות לאיתור תקלות</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"<span class=\"text-success\">Production</span>\n"
"                                <span class=\"o_stat_text\">Environment</span>"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.delivery_report_saleorder_document
msgid "<strong>Shipping Description</strong>"
msgstr "<strong>תיאור משלוח</strong>"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__carrier_description
msgid ""
"A description of the delivery method that you want to communicate to your "
"customers on the Sales Order and sales confirmation email.E.g. instructions "
"for customers to follow."
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__integration_level
msgid "Action while validating Delivery Orders"
msgstr "פעולה תוך אימות הזמנות למשלוח"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__active
msgid "Active"
msgstr "פעיל"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "Add"
msgstr "הוסף"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/sale_order.py:0
#: code:addons/delivery/wizard/choose_delivery_carrier.py:0
msgid "Add a shipping method"
msgstr "הוסף שיטת משלוח"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_order_form_with_carrier
msgid "Add shipping"
msgstr "הוסף משלוח"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Additional margin"
msgstr "רווח נוסף"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__amount
msgid "Amount"
msgstr "סכום כולל"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__amount
msgid ""
"Amount of the order to benefit from a free shipping, expressed in the "
"company currency"
msgstr "מסכום זה המשלוח יהיה בחינם, הסכום מבוטא במטבע החברה"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Archived"
msgstr "בארכיון"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Availability"
msgstr "זמינות"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__available_carrier_ids
msgid "Available Carriers"
msgstr "שליחים זמינים"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__delivery_type__base_on_rule
msgid "Based on Rules"
msgstr "מבוסס על כללים"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__can_generate_return
msgid "Can Generate Return"
msgstr "יכול ליצור החזרה"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__carrier_id
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_tree
msgid "Carrier"
msgstr "שליח"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
msgid ""
"Carrier %s cannot have the same tag in both Must Have Tags and Excluded "
"Tags."
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__carrier_description
msgid "Carrier Description"
msgstr "תיאור השילוח"

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "Choose a pick-up point"
msgstr "בחר נקודת איסוף"

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
#: code:addons/delivery/static/src/js/location_selector/map_container/map_container.js:0
msgid "Choose this location"
msgstr "בחר מיקום"

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_schedule/location_schedule.js:0
msgid "Closed"
msgstr "סגור"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__company_id
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__company_id
msgid "Company"
msgstr "חברה"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
msgid "Condition"
msgstr "מצב"

#. module: delivery
#: model:ir.model,name:delivery.model_res_partner
msgid "Contact"
msgstr "איש קשר"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Content"
msgstr "תוכן"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__display_price
msgid "Cost"
msgstr "עלות"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__country_ids
msgid "Countries"
msgstr "ארצות"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__currency_id
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__currency_id
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__currency_id
msgid "Currency"
msgstr "מטבע"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__partner_id
msgid "Customer"
msgstr "לקוח"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__debug_logging
msgid "Debug logging"
msgstr "רישום באגים"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_res_partner__property_delivery_carrier_id
#: model:ir.model.fields,help:delivery.field_res_users__property_delivery_carrier_id
msgid "Default delivery method used in sales orders."
msgstr "שיטת משלוח ברירת מחדל המשמשת בהזמנות לקוח."

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid "Define a new delivery method"
msgstr "הגדר שיטת משלוח חדשה"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Delivery Carrier"
msgstr "שליח"

#. module: delivery
#: model:ir.model,name:delivery.model_choose_delivery_carrier
msgid "Delivery Carrier Selection Wizard"
msgstr "אשף בחירת מוביל המשלוח"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
msgid "Delivery Cost"
msgstr "עלות משלוח"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__delivery_message
#: model:ir.model.fields,field_description:delivery.field_sale_order__delivery_message
msgid "Delivery Message"
msgstr "הודעת משלוח"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__name
#: model:ir.model.fields,field_description:delivery.field_res_partner__property_delivery_carrier_id
#: model:ir.model.fields,field_description:delivery.field_res_users__property_delivery_carrier_id
#: model:ir.model.fields,field_description:delivery.field_sale_order__carrier_id
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Delivery Method"
msgstr "שיטת משלוח"

#. module: delivery
#: model:ir.actions.act_window,name:delivery.action_delivery_carrier_form
#: model:ir.ui.menu,name:delivery.sale_menu_action_delivery_carrier_form
msgid "Delivery Methods"
msgstr "שיטות משלוח"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__delivery_price
msgid "Delivery Price"
msgstr "מחיר משלוח"

#. module: delivery
#: model:ir.model,name:delivery.model_delivery_price_rule
msgid "Delivery Price Rules"
msgstr "כללי מחיר משלוח"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__product_id
msgid "Delivery Product"
msgstr "מוצר משלוח"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__delivery_set
msgid "Delivery Set"
msgstr "משלוח הוגדר"

#. module: delivery
#: model:ir.model,name:delivery.model_delivery_zip_prefix
msgid "Delivery Zip Prefix"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__recompute_delivery_price
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__recompute_delivery_price
msgid "Delivery cost should be recomputed"
msgstr "יש לחשב מחדש את עלות המשלוח"

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_zip_prefix_list
msgid ""
"Delivery zip prefixes are assigned to delivery carriers to restrict\n"
"            which zips it is available to."
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Description"
msgstr "תיאור"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Destination"
msgstr "יעד"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__sequence
msgid "Determine the display order"
msgstr "הגדר את סדר התצוגה"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "Discard"
msgstr "בטל"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid ""
"Each carrier (e.g. UPS) can have several delivery methods (e.g.\n"
"            UPS Express, UPS Standard) with a set of pricing rules attached\n"
"            to each method."
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__prod_environment
msgid "Environment"
msgstr "סביבה"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
msgid "Error: this delivery method is not available for this address."
msgstr "שגיאה: שיטת משלוח זו אינה זמינה עבור כתובת זו."

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
msgid "Error: this delivery method is not available."
msgstr "שגיאה: שיטת משלוח זו אינה זמינה."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__invoice_policy
msgid ""
"Estimated Cost: the customer will be invoiced the estimated cost of the shipping.\n"
"Real Cost: the customer will be invoiced the real cost of the shipping, the cost of theshipping will be updated on the SO after the delivery."
msgstr ""

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__invoice_policy__estimated
msgid "Estimated cost"
msgstr "עלות מוערכת"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__excluded_tag_ids
msgid "Excluded Tags"
msgstr "תגיות שלא נכללות"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_sale_order__carrier_id
msgid "Fill this field if you plan to invoice the shipping based on picking."
msgstr "מלא שדה זה אם בכוונתך לחייב את המשלוח בהסתמך על ליקוט."

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"Filling this form allows you to make the shipping method available according"
" to the content of the order or its destination."
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__fixed_margin
msgid "Fixed Margin"
msgstr "רווח קבוע"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__fixed_price
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__delivery_type__fixed
msgid "Fixed Price"
msgstr "מחיר קבוע"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__free_over
msgid "Free if order amount is above"
msgstr "חינם אם סכום ההזמנה הוא מעל"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__return_label_on_delivery
msgid "Generate Return Label"
msgstr "צור תווית החזרה"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__integration_level__rate
msgid "Get Rate"
msgstr "קבל שער מטבע"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__integration_level__rate_and_ship
msgid "Get Rate and Create Shipment"
msgstr "קבל שער מטבע וצור משלוח"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Group By"
msgstr "קבץ לפי"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__id
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__id
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__id
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__id
msgid "ID"
msgstr "מזהה"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__free_over
msgid ""
"If the order total amount (shipping excluded) is above or equal to this "
"value, the customer benefits from a free shipping"
msgstr ""
"אם הסכום הכולל של ההזמנה (משלוח לא כלול) הוא גבוה או שווה לערך זה, הלקוח "
"יקבל משלוח חינם"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__max_volume
msgid ""
"If the total volume of the order is over this volume, the method won't be "
"available."
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__max_weight
msgid ""
"If the total weight of the order is over this weight, the method won't be "
"available."
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Install more Providers"
msgstr "התקן ספקים נוספים"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__shipping_insurance
msgid "Insurance Percentage"
msgstr "אחוז ביטוח"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__integration_level
msgid "Integration Level"
msgstr "רמת אינטגרציה"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__invoicing_message
msgid "Invoicing Message"
msgstr "הודעת חיוב"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__invoice_policy
msgid "Invoicing Policy"
msgstr "מדיניות חיוב"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__is_delivery
msgid "Is a Delivery"
msgstr "הוא משלוח"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "List view"
msgstr "תצוגת רשימה"

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "Loading..."
msgstr "טוען..."

#. module: delivery
#: model:delivery.carrier,name:delivery.delivery_local_delivery
msgid "Local Delivery"
msgstr "משלוח מקומי"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__debug_logging
msgid "Log requests in order to ease debugging"
msgstr "רישות בקשות כדי להקל על ניפוי באגים"

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_zip_prefix_list
msgid "Manage delivery zip prefixes"
msgstr ""

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "Map view"
msgstr "תצוגת מפה "

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__margin
msgid "Margin"
msgstr "מרווח"

#. module: delivery
#: model:ir.model.constraint,message:delivery.constraint_delivery_carrier_margin_not_under_100_percent
msgid "Margin cannot be lower than -100%"
msgstr "המרווח לא יכול להיות נמוך מ -100%"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Margin on Rate"
msgstr "מרווח על שער"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__max_volume
msgid "Max Volume"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__max_weight
msgid "Max Weight"
msgstr "משקל מקסימלי"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__max_value
msgid "Maximum Value"
msgstr "ערך מקסימלי"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__must_have_tag_ids
msgid "Must Have Tags"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__name
msgid "Name"
msgstr "שם"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
msgid "New Providers"
msgstr "ספקים חדשים"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/sale_order.py:0
msgid "No pick-up points are available for this delivery address."
msgstr "אין נקודות איסוף זמינות עבור כתובת משלוח זו."

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "No result"
msgstr "אין תוצאה"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
msgid "Not available for current order"
msgstr "לא זמין עבור הזמנה זו."

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location/location.js:0
#: code:addons/delivery/static/src/js/location_selector/map_container/map_container.js:0
msgid "Opening hours"
msgstr "שעות פעילות"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__operator
msgid "Operator"
msgstr "מפעיל"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__order_id
msgid "Order"
msgstr "הזמנה"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__pickup_location_data
msgid "Pickup Location Data"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Please select a country before choosing a state or a zip prefix."
msgstr "נא לבחור מדינה לפני בחירת מדינה או מיקוד."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__name
msgid "Prefix"
msgstr "קידומת"

#. module: delivery
#: model:ir.model.constraint,message:delivery.constraint_delivery_zip_prefix_name_uniq
msgid "Prefix already exists!"
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__zip_prefix_ids
msgid ""
"Prefixes of zip codes that this carrier applies to. Note that regular "
"expressions can be used to support countries with varying zip code lengths, "
"i.e. '$' can be added to end of prefix to match the exact zip (e.g. '100$' "
"will only match '100' and not '1000')"
msgstr ""

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__price
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__price
msgid "Price"
msgstr "מחיר"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_tree
msgid "Price Rules"
msgstr "כללי מחירים"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Pricing"
msgstr "תמחור"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__price_rule_ids
msgid "Pricing Rules"
msgstr "כללי תמחור"

#. module: delivery
#: model:ir.model,name:delivery.model_product_category
msgid "Product Category"
msgstr "קטגורית מוצר"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__product_qty
msgid "Product Qty"
msgstr "כמות מוצר"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__delivery_type
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__delivery_type
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Provider"
msgstr "ספק"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__quantity
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__quantity
msgid "Quantity"
msgstr "כמות"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__get_return_label_from_portal
msgid "Return Label Accessible from Customer Portal"
msgstr "תווית החזרה נגישה מפורטל הלקוחות"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__list_base_price
msgid "Sale Base Price"
msgstr "מחיר מכירה בסיסי"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__list_price
msgid "Sale Price"
msgstr "מחיר מכירה"

#. module: delivery
#: model:ir.model,name:delivery.model_sale_order
msgid "Sales Order"
msgstr "הזמנת לקוח"

#. module: delivery
#: model:ir.model,name:delivery.model_sale_order_line
msgid "Sales Order Line"
msgstr "שורת הזמנת לקוח"

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.xml:0
msgid "Search"
msgstr "חיפוש"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__sequence
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__sequence
msgid "Sequence"
msgstr "רצף"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__is_all_service
msgid "Service Product"
msgstr "מוצר שירות"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__prod_environment
msgid "Set to True if your credentials are certified for production."
msgstr "הגדר כ- נכון אם המיומנויות שלך מאושרות כדי לבצע ייצור."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__carrier_id
msgid "Shipping Method"
msgstr "שיטת משלוח"

#. module: delivery
#: model:ir.model,name:delivery.model_delivery_carrier
#: model_terms:ir.ui.view,arch_db:delivery.res_config_settings_view_form
msgid "Shipping Methods"
msgstr "שיטות משלוח"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__shipping_weight
msgid "Shipping Weight"
msgstr "משקל משלוח"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__shipping_insurance
msgid ""
"Shipping insurance is a service which may reimburse senders whose parcels "
"are lost, stolen, and/or damaged in transit."
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"Shipping method details to be included at bottom sales orders and their "
"confirmation emails. E.g. Instructions for customers to follow."
msgstr ""

#. module: delivery
#: model:delivery.carrier,name:delivery.free_delivery_carrier
#: model:product.template,name:delivery.product_product_delivery_product_template
msgid "Standard delivery"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__state_ids
msgid "States"
msgstr "מדינות"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__supports_shipping_insurance
msgid "Supports Shipping Insurance"
msgstr "תומך בביטוח משלוח"

#. module: delivery
#: model:delivery.carrier,name:delivery.delivery_carrier
#: model:product.template,name:delivery.product_product_delivery_poste_product_template
msgid "The Poste"
msgstr "The Poste"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__excluded_tag_ids
msgid ""
"The method is NOT available if at least one product of the order has one of "
"these tags."
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__must_have_tag_ids
msgid ""
"The method is available only if at least one product of the order has one of"
" these tags."
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__get_return_label_from_portal
msgid ""
"The return label can be downloaded by the customer from the customer portal."
msgstr "הלקוח יכול להוריד את תווית ההחזרה מפורטל הלקוחות."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__return_label_on_delivery
msgid "The return label is automatically generated at the delivery."
msgstr "תווית החזרה נוצרת אוטומטית במשלוח."

#. module: delivery
#: model:ir.model.constraint,message:delivery.constraint_delivery_carrier_shipping_insurance_is_percentage
msgid "The shipping insurance must be a percentage between 0 and 100."
msgstr ""

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
msgid "The shipping is free since the order amount exceeds %.2f."
msgstr "המשלוח בחינם מכיוון שסכום ההזמנה גבוה מ  %.2f."

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/map_container/map_container.js:0
msgid "There was an error loading the map"
msgstr ""

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid ""
"These methods allow to automatically compute the delivery price\n"
"            according to your settings; on the sales order (based on the\n"
"            quotation) or the invoice (based on the delivery orders)."
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__fixed_margin
msgid "This fixed amount will be added to the shipping price."
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__tracking_url
msgid ""
"This option adds a link for the customer in the portal to track their "
"package easily. Use <shipmenttrackingnumber> as a placeholder in your URL."
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__margin
msgid "This percentage will be added to the shipping price."
msgstr "אחוז זה יתווסף למחיר המשלוח."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__total_weight
msgid "Total Order Weight"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__tracking_url
msgid "Tracking Link"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "Update"
msgstr "עדכן"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/sale_order.py:0
#: model_terms:ir.ui.view,arch_db:delivery.view_order_form_with_carrier
msgid "Update shipping cost"
msgstr "עדכן עלות משלוח"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__variable
msgid "Variable"
msgstr "משתנה"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__variable_factor
msgid "Variable Factor"
msgstr "מקדם משתנה"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__volume
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__volume
msgid "Volume"
msgstr "נפח"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__volume_uom_name
msgid "Volume unit of measure label"
msgstr "תווית יחידת מידה נפח"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__weight
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__weight
msgid "Weight"
msgstr "משקל"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__wv
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__wv
msgid "Weight * Volume"
msgstr "משקל * נפח"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__weight_uom_name
msgid "Weight Uom Name"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__weight_uom_name
msgid "Weight unit of measure label"
msgstr "תווית יחידת מידה של משקל"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/sale_order.py:0
msgid ""
"You can not update the shipping costs on an order where it was already invoiced!\n"
"\n"
"The following delivery lines (product, invoiced quantity and price) have already been processed:\n"
"\n"
msgstr ""

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/product_category.py:0
msgid ""
"You cannot delete the deliveries product category as it is used on the "
"delivery carriers products."
msgstr ""

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "Your postal code"
msgstr "המיקוד שלך"

#. module: delivery
#: model:ir.actions.act_window,name:delivery.action_delivery_zip_prefix_list
msgid "Zip Prefix"
msgstr "קידומת מיקוד"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__zip_prefix_ids
msgid "Zip Prefixes"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "e.g. UPS Express"
msgstr " למשל UPS Express"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "i.e. https://ekartlogistics.com/shipmenttrack/<shipmenttrackingnumber>"
msgstr ""
