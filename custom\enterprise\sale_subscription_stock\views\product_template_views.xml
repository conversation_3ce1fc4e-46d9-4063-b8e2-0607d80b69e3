<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="product_template_form_view" model="ir.ui.view">
        <field name="name">product.template.form.view.inherit.sale</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">
            <field name="product_tooltip" position="after">
                <label for="product_tooltip" string=""
                    invisible="not recurring_invoice or type != 'consu'"/>
                <div class="fst-italic text-muted"
                    invisible="not recurring_invoice or type != 'consu'">
                    Recurring order with this product will be invoiced at the beginning of the period.
                </div>
            </field>
        </field>

    </record>

</odoo>
