# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_adyen
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON>, 2025
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: pos_adyen
#: model_terms:ir.ui.view,arch_db:pos_adyen.res_config_settings_view_form
msgid "Add tip through payment terminal (Adyen)"
msgstr "Adăugați bacșiș prin intermediul terminalului de plată (Adyen)"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_api_key
msgid "Adyen API key"
msgstr "Cheie API Adyen"

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "Adyen Error"
msgstr "Eroare Adyen"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_latest_response
msgid "Adyen Latest Response"
msgstr "Adyen Ultimul răspuns"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_terminal_identifier
msgid "Adyen Terminal Identifier"
msgstr "Identificatorul terminalului Adyen"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_test_mode
msgid "Adyen Test Mode"
msgstr "Mod de testare Adyen"

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "An unexpected error occurred. Message from Adyen: %s"
msgstr "A apărut o eroare neașteptată. Mesaj de la Adyen: %s"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_config__adyen_ask_customer_for_tip
msgid "Ask Customers For Tip"
msgstr "Întrebați clientul despre bacșiș"

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "Authentication failed. Please check your Adyen credentials."
msgstr "Autentificarea a eșuat. Vă rugăm să verificați credențialele Adyen."

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid ""
"Cancelling the payment failed. Please cancel it manually on the payment "
"terminal."
msgstr ""
"Anularea plății a eșuat. Vă rugăm să o anulați manual pe terminalul de "
"plată."

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "Cannot process transactions with negative amount."
msgstr "Nu se pot procesa tranzacții cu sumă negativă."

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_res_config_settings
msgid "Config Settings"
msgstr "Setări de configurare"

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid ""
"Could not connect to the Odoo server, please check your internet connection "
"and try again."
msgstr ""
"Nu a fost posibilă conectarea la serverul Odoo, vă rugăm să verificați "
"conexiunea la internet și să încercați din nou."

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_event_url
msgid "Event URL"
msgstr ""

#. module: pos_adyen
#. odoo-python
#: code:addons/pos_adyen/models/pos_payment_method.py:0
msgid "Invalid Adyen request"
msgstr "Cerere Adyen invalidă"

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "Message from Adyen: %s"
msgstr "Mesaj de la Adyen: %s"

#. module: pos_adyen
#. odoo-python
#: code:addons/pos_adyen/models/pos_config.py:0
msgid ""
"Please configure a tip product for POS %s to support tipping with Adyen."
msgstr ""
"Vă rugăm să configurați un produs de bacșiș pentru POS %s pentru a permite "
"încasarea bacșișului cu Adyen."

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Configurarea Punctului de Vânzare"

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Metode plată Punct de Vânzare"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_res_config_settings__pos_adyen_ask_customer_for_tip
msgid "Pos Adyen Ask Customer For Tip"
msgstr "Pos Adyen întrebați clientul despre bacșiș"

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_test_mode
msgid "Run transactions in the test environment."
msgstr "Ruleează tranzacții în mediul de testare."

#. module: pos_adyen
#. odoo-python
#: code:addons/pos_adyen/models/pos_payment_method.py:0
msgid ""
"Terminal %(terminal)s is already used in company %(company)s on payment "
"method %(payment_method)s."
msgstr ""
"Terminalul %(terminal)s este deja utilizat în compania %(company)s pentru "
"metoda de plată %(payment_method)s."

#. module: pos_adyen
#. odoo-python
#: code:addons/pos_adyen/models/pos_payment_method.py:0
msgid ""
"Terminal %(terminal)s is already used on payment method %(payment_method)s."
msgstr ""
"Terminalul %(terminal)s este deja folosit în metoda de "
"plată%(payment_method)s."

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_event_url
msgid "This URL needs to be pasted on Adyen's portal terminal settings."
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_api_key
msgid ""
"Used when connecting to Adyen: https://docs.adyen.com/user-management/how-"
"to-get-the-api-key/#description"
msgstr ""
"Utilizat la conectarea la Adyen: https://docs.adyen.com/user-management/how-"
"to-get-the-api-key/#description"

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_terminal_identifier
msgid "[Terminal model]-[Serial number], for example: P400Plus-123456789"
msgstr ""
"[Modelul terminalului]-[Numărul de serie], de exemplu: P400Plus-123456789"
