<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="sms_account_sender_view_form" model="ir.ui.view">
        <field name="name">sms.account.sender.view.form</field>
        <field name="model">sms.account.sender</field>
        <field name="arch" type="xml">
            <form string="Choose your sender name">
                <sheet>
                    <p>
                        Your sender name must be between 3 and 11 characters long and only contain alphanumeric characters.
                        It must fit your company name, and you aren't allowed to modify it once you registered one, choose it carefully.
                    </p>
                    <p>
                        Note that this is not required, if you don't set a sender name, your SMS will be sent from a short code.
                    </p>
                    <group>
                        <field name="sender_name" required="1"/>
                    </group>
                    <footer>
                        <button string="Set sender name" name="action_set_sender_name" type="object" class="btn btn-primary"/>
                        <button string="Skip for now" class="btn-secondary" special="cancel"/>
                    </footer>
                </sheet>
            </form>
        </field>
    </record>
</odoo>
