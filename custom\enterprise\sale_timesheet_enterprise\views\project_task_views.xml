<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="project_task_view_gantt_inherit_sale_timesheet" model="ir.ui.view">
        <field name="name">project.task.gantt.inherit.sale.timesheet</field>
        <field name="model">project.task</field>
        <field name="inherit_id" ref="project_enterprise.project_task_view_gantt"/>
        <field name="arch" type="xml">
            <xpath expr="//gantt" position="attributes">
                <attribute name="progress_bar" separator="," add="sale_line_id"/>
            </xpath>
        </field>
    </record>

    <!-- View for 'Tasks' stat button via Contact form -->
    <record id="project_task_view_form_in_gantt_res_partner" model="ir.ui.view">
        <field name="name">project.task.view.form.gantt.res.partner.inherit.sale_timesheet_enterprise</field>
        <field name="model">project.task</field>
        <field name="inherit_id" ref="project_enterprise.project_task_view_form_in_gantt_res_partner"/>
        <field name="arch" type="xml">
            <xpath expr="//group//field[@name='project_id']" position="attributes">
                <attribute name="domain">[
                    '&amp;',
                        ('allow_billable', '=', True),
                        '&amp;',
                            ('active', '=', True),
                            '|',
                                ('company_id', '=', False),
                                ('company_id', '=?', company_id),
                ]</attribute>
            </xpath>
        </field>
    </record>
</odoo>
