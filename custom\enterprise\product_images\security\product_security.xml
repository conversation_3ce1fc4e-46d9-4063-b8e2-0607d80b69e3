<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <record model="ir.rule" id="product_images_product_fetch_image_wizard_rule">
        <field name="name">
            Product fetch image wizard: access only your own wizard
        </field>
        <field name="model_id" ref="model_product_fetch_image_wizard"/>
        <field name="domain_force">[('create_uid', '=', user.id)]</field>
    </record>

</odoo>
