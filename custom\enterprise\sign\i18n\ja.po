# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sign
# 
# Translators:
# <PERSON>il <PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_partner__signature_count
#: model:ir.model.fields,field_description:sign.field_res_users__signature_count
msgid "# Signatures"
msgstr "# 署名"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "#7898678"
msgstr "#7898678"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid ""
"%(partner)s validated the signature by SMS with the phone number "
"%(phone_number)s."
msgstr "%(partner)s は電話番号 %(phone_number)sのSMSによる署名を検証しました。"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid "%s couldn't sign the document due to an insufficient credit error."
msgstr "%s 不十分な信用のエラーのため、ドキュメントに署名できませんでした。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "%s has been edited and signed"
msgstr "%sが編集され、署名されました。 "

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "%s has been linked to this sign request."
msgstr "%s がこの署名依頼にリンクされています。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "%s has been signed"
msgstr "%s が署名されました"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "%s: missing credits for extra-authentication"
msgstr "%s: 追加認証のためのクレジットが不足しています"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "(UTC)"
msgstr "(UTC)"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
msgid ""
")\n"
"            has refused the document"
msgstr ""
")\n"
"            ドキュメントを拒否しました。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid ""
")\n"
"        has requested your signature on the document"
msgstr ""
")\n"
"        は次のドキュメントへの署名を依頼しています："

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "******1234"
msgstr "******1234"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid ""
"- <em>\n"
"                                            Waiting Signature\n"
"                                        </em>"
msgstr ""
"- <em>\n"
"                                            署名待ち\n"
"                                        </em>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "- <em>Cancelled</em>"
msgstr "- <em>取消済</em>"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "- or -"
msgstr "- または -"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "1001"
msgstr "1001"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "192.168.1.1"
msgstr "192.168.1.1"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "20-03-2000"
msgstr "20-03-2000"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "2023-08-18"
msgstr "2023-08-18"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "2023-08-18 - 12:30:45"
msgstr "2023-08-18 - 12:30:45"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Created by:</b>"
msgstr "<b>作成：</b>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Created on:</b>"
msgstr "<b>作成日：</b>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Creation IP Address:</b>"
msgstr "<b>作成IPアドレス：</b>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Document ID:</b>"
msgstr "<b>ドキュメントID：</b>"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "<b>Drag & drop “Signature”</b> into the bottom of the document."
msgstr "<b>“署名”をドラッグ&ドロップ</b>ドキュメント下方へ"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Gas:</b> 18,700 Pounds of CO² = 18,700*0.4536 = 8482.32 kg"
msgstr "<b>ガス:</b> 18,700パウンドのCO² = 18,700*0.4536 = 8482.32 kg"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Signature:</b>"
msgstr "<b>署名：</b>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<b>Signers:</b>"
msgstr "<b>署名者：</b>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Solid Waste:</b> 1290 Pounds = 1290*0.4536 = 585.14 kg"
msgstr "<b>固形廃棄物:</b> 1290 パウンド = 1290*0.4536 = 585.14 kg"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Total Energy:</b> 27 Millions BTU = 27,000,000*0.0002931 = 7.91 kWh"
msgstr "<b>合計エネルギー:</b> 2千7百万 BTU = 27,000,000*0.0002931 = 7.91 kWh"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Water:</b> 23,400 Gallons = 23,400*3.78541 = 88578.59 L"
msgstr "<b>水:</b> 23,400 Gallons = 23,400*3.78541 = 88578.59 L"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "<b>Wood Use:</b> 4 US Short Tons = 4*907.18474 = 3628.73 kg"
msgstr "<b>木材利用:</b> 4 US Short Tons = 4*907.18474 = 3628.73 kg"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "<br/>(the email access has not been sent)"
msgstr "<br/>(Eメールアクセスは送信されていません)"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid "<i class=\"fa fa-check\"/> A fresh link has just been sent to your inbox!"
msgstr "<i class=\"fa fa-check\"/>新しいリンクがあなたの受信トレイに送信されました！"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<i class=\"fa fa-check\"/> The document's integrity is valid."
msgstr "<i class=\"fa fa-check\"/> ドキュメントの整合性は有効です。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid ""
"<i class=\"fa fa-exclamation-circle\"/> The document's integrity could not "
"be verified."
msgstr "<i class=\"fa fa-exclamation-circle\"/>ドキュメントの整合性が検証できませんでした。 "

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<i class=\"fa fa-globe\"/> View"
msgstr "<i class=\"fa fa-globe\"/> ビュー"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid ""
"<i class=\"fa fa-info-circle\"/> Links sent via email expire after a set "
"delay to increase security."
msgstr ""
"<i class=\"fa fa-info-"
"circle\"/>Eメールで送信されたリンクは、セキュリティを高めるために設定された遅延時間後に失効します。 "

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_terms_conditions_setting_banner
msgid "<i class=\"oi oi-arrow-right me-1\"/>Back to settings"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>管理設定に戻る"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                            Preview"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                            プレビュー"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "<small><i> Cancelled </i></small>"
msgstr "<small><i> 取消済 </i></small>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "<small><i> Waiting Signature </i></small>"
msgstr "<small><i> 署名待ち </i></small>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid ""
"<small>Email Verification: The signatory has confirmed control of their "
"email inbox by clicking on a unique link</small>"
msgstr "<small>電子メール認証： 署名者は、ユニークなリンクをクリックすることで、電子メール受信箱の制御を確認しました。</small>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid ""
"<small>SMS: The signatory has confirmed their control of the phone number "
"using a unique code sent by SMS</small>"
msgstr "<small>SMS: 署名者は、SMSで送信された固有のコードを使用して、電話番号の管理を確認しています</small>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_partner_view_form
msgid "<span class=\"o_stat_text\">Signature Requested</span>"
msgstr "<span class=\"o_stat_text\">署名が依頼されました</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "<span class=\"o_stat_text\">Signed Document</span>"
msgstr "<span class=\"o_stat_text\">署名済ドキュメント</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_share_view_form
msgid ""
"<span class=\"text-muted\" invisible=\"not share_link\">Sharing will create "
"a copy of the file to sign. That file can be reached by the link below. "
"Every public user using the link will generate a document when the Signature"
" is complete. The link is private, only those that receive the link will be "
"able to sign it.</span>"
msgstr ""
"<span class=\"text-muted\" invisible=\"not "
"share_link\">共有することで、署名するためのファイルのコピーが作成されます。そのファイルは、以下のリンクからアクセスできます。リンクを使用するすべてのパブリックユーザは、署名が完了するとドキュメントを生成します。リンクは非公開で、リンクを受け取った人だけが署名することができます。</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<span class=\"text-muted\">Not available</span>"
msgstr "<span class=\"text-muted\">利用不可</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_type_view_form
msgid "<span>(1.0 = full page size)</span>"
msgstr "<span>(1.0 = フルページサイズ)</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_terms_conditions_setting_banner
msgid "<span>This is a preview of your Terms &amp; Conditions.</span>"
msgstr "<span>これは利用規約のプレビューです。</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.cancel_sign_request_item_with_confirmation
msgid ""
"<span>You won't receive any notification for this signature request "
"anymore.</span>"
msgstr "<span>この署名依頼に対する通知はもう届きません。</span>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "<strong>Creation Date:</strong>"
msgstr "<strong>作成日:</strong>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
msgid ""
"<strong>Warning</strong> do not forward this email to other people!<br/>"
msgstr "<strong>警告</strong>このEメールを他の人に転送しないで下さい！ <br/>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid ""
"<strong>Warning</strong> do not forward this email to other people!<br/>\n"
"            They will be able to access this document and sign it as yourself.<br/>\n"
"            <span>Your IP address and localization are associated to your signature to ensure traceability.</span>"
msgstr ""
"<strong>注意</strong> このメールを他の人に転送しないでください！<br/>\n"
"            他人がこのドキュメントにアクセスし、あなたに成り代わって署名するかもしれません。<br/>\n"
"            <span>署名の際のIPアドレスと場所は、トレーサビリティのために関連付けられます </span>"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid ""
"A SMS will be sent to the following phone number. Please update it if it's "
"not relevant."
msgstr "以下の電話番号にSMSが送信されます。関係ない場合は更新して下さい。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.message_signature_link
msgid "A document has been signed and a copy attached to"
msgstr "ドキュメントは署名され、コピーが以下に添付されました:"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"A non-shared sign request's should not have any signer with an empty partner"
msgstr "非共有署名依頼は、空のパートナを持つ署名者を持っていてはいけません。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"A shared sign request should only have one signer with an empty partner"
msgstr "共有署名依頼は、空のパートナーを持つ1人の署名者のみを持つ必要があります。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "A shower uses approximately 65 L of water"
msgstr "1つのシャワーは約65Lの水を使用します。"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "A signature request has been linked to this document: %s"
msgstr "このドキュメントに署名依頼がリンクされました: %s"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "A valid sign request needs at least one sign request item"
msgstr "有効な署名依頼には、少なくとも1つの署名依頼項目が必要です。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "ABCD1234"
msgstr "ABCD1234"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Access Logs"
msgstr "アクセスログ"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__access_warning
msgid "Access warning"
msgstr "アクセス警告"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.portal_my_home_sign
msgid "Access your signed documents"
msgstr "署名済ドキュメントにアクセス"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__access_via_link
msgid "Accessed Through Token"
msgstr "トークンを介してアクセス済"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_mail_activity_type__category
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Action"
msgstr "アクション"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_needaction
msgid "Action Needed"
msgstr "要アクション"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__action
msgid "Action Performed"
msgstr "アクションが実行されました"

#. module: sign
#: model:ir.model.fields,help:sign.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"アクションは、カレンダービューを開くなどの特定の動作をトリガーしたり、ドキュメントのアップロード時に完了として自動的にマークを付けたりする場合があります"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__active
#: model:ir.model.fields,field_description:sign.field_sign_template__active
msgid "Active"
msgstr "有効化"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_ids
msgid "Activities"
msgstr "活動"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "例外活動文字装飾"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
#: model:ir.actions.report,name:sign.action_sign_request_print_logs
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Activity Logs"
msgstr "活動記録"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_state
msgid "Activity State"
msgstr "活動状態"

#. module: sign
#: model:ir.model,name:sign.model_mail_activity_type
msgid "Activity Type"
msgstr "活動タイプ"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_type_icon
msgid "Activity Type Icon"
msgstr "活動種別アイコン"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__sign_log_ids
msgid "Activity logs linked to this request"
msgstr "この依頼に関連した活動記録"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/initials_all_pages_dialog.js:0
msgid "Add Initials"
msgstr "イニシャルを追加"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/initial_all_pages_dialog.xml:0
msgid "Add Once"
msgstr "一度追加"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Add document tags here"
msgstr "ドキュメントタグをここに追加"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/initial_all_pages_dialog.xml:0
msgid "Add to all pages"
msgstr "全てのページに追加"

#. module: sign
#: model:res.groups,name:sign.group_sign_manager
msgid "Administrator"
msgstr "管理者"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.js:0
msgid "Adopt Your Signature"
msgstr "署名を承認する"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__signed
msgid "After Signature"
msgstr "署名後"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
#: model:ir.model.fields,field_description:sign.field_sign_item__alignment
msgid "Alignment"
msgstr "配置"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "All"
msgstr "全て"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_all_request_action
#: model:ir.ui.menu,name:sign.sign_request_documents
msgid "All Documents"
msgstr "全てのドキュメント"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "All signers must have valid email addresses"
msgstr "全ての署名者は有効なEメールアドレスを持っている必要があります"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"Allow signatories to provide their identity using itsme® (available in "
"Belgium and the Netherlands)."
msgstr "itsme®（ベルギーとオランダで利用可能）を使って署名者の身元を提供できるようにする。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"Allow users to define the users or groups which have access to the template."
msgstr "テンプレートにアクセスできるユーザまたはグループを定義する。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "An average computer will consume 750 Wh"
msgstr "平均的なコンピュータは 750 Whを消費します"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Antwrep"
msgstr "アントワープ"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Archive"
msgstr "アーカイブ"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Archived"
msgstr "アーカイブ済"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.cancel_sign_request_item_with_confirmation
msgid "Are you sure you want to cancel the sign request?"
msgstr "署名依頼を本当に取消しますか?"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__start_sign
msgid "At least one signer has signed the document."
msgstr "少なくとも1人の署名者がドキュメントに署名しました。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Attach a file"
msgstr "ファイル添付"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__attachment_id
msgid "Attachment"
msgstr "添付ファイル"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_attachment_count
msgid "Attachment Count"
msgstr "添付数"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__attachment_ids
#: model:ir.model.fields,field_description:sign.field_sign_send_request__attachment_ids
msgid "Attachments"
msgstr "添付"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Authenticate by SMS"
msgstr "SMSによる認証"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__group_ids
msgid "Authorized Groups"
msgstr "承認グループ"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Authorized Groups:"
msgstr "承認グループ:"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__authorized_ids
msgid "Authorized Users"
msgstr "承認ユーザ"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Authorized Users:"
msgstr "承認ユーザ:"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__auto_field
msgid "Auto-fill Partner Field"
msgstr "パートナー項目の自動入力"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_option__available
msgid "Available in new templates"
msgstr "新規テンプレートで利用可能"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Back to %s"
msgstr "%sに戻る"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "Based on"
msgstr "基準"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "Based on various websites, here are our comparisons:"
msgstr "様々なウェブサイトに基づいた比較です:"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__sent
msgid "Before Signature"
msgstr "署名前"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "By"
msgstr "By"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
msgid ""
"By clicking Adopt & Sign, I agree that the chosen signature/initials will be"
" a valid electronic representation of my hand-written signature/initials for"
" all purposes when it is used on documents, including legally binding "
"contracts."
msgstr ""
"承認＆署名をクリックすることにより、私は、選択した署名/イニシャルが、法的拘束力のある契約を含む文書に使用される場合、すべての目的において、私の手書きの署名/イニシャルの有効な電子署名となることに同意します。"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__message_cc
msgid "CC Message"
msgstr "CCメッセージ"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
#: code:addons/sign/static/src/dialogs/public_signer_dialog.xml:0
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.xml:0
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__cancel
#: model_terms:ir.ui.view,arch_db:sign.sign_duplicate_template_with_pdf_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Cancel"
msgstr "キャンセル"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.cancel_sign_request_item_with_confirmation
msgid "Cancel Sign Request"
msgstr "署名依頼を取消"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
msgid "Canceled"
msgstr "取消済"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__canceled
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__canceled
#: model:ir.model.fields.selection,name:sign.selection__sign_request_item__state__canceled
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "Cancelled"
msgstr "取消済"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Carbon Emissions"
msgstr "CO2 排出量"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.js:0
msgid "Center"
msgstr "中央"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Certificate <i class=\"fa fa-download\"/>"
msgstr "証明書 <i class=\"fa fa-download\"/>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Certificate of Completion<br/>"
msgstr "修了証明書<br/>"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__change_authorized
#: model:ir.model.fields,field_description:sign.field_sign_request_item__change_authorized
msgid "Change Authorized"
msgstr "変更を許可"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__checkbox
#: model:sign.item.type,name:sign.sign_item_type_checkbox
msgid "Checkbox"
msgstr "チェックボックス"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_template__is_sharing
msgid "Checked if this template has created a shared document for you"
msgstr "このテンプレートがあなたのために共有ドキュメントを作成したかどうかをチェックします。"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_item_navigator.js:0
msgid "Click to start"
msgstr "クリックしてスタート"

#. module: sign
#. odoo-javascript
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
#: code:addons/sign/static/src/dialogs/thank_you_dialog.js:0
#: code:addons/sign/static/src/js/tours/sign.js:0
#: model:sign.template,redirect_url_text:sign.template_sign_1
#: model:sign.template,redirect_url_text:sign.template_sign_2
#: model:sign.template,redirect_url_text:sign.template_sign_3
#: model:sign.template,redirect_url_text:sign.template_sign_4
#: model:sign.template,redirect_url_text:sign.template_sign_5
#: model:sign.template,redirect_url_text:sign.template_sign_tour
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
#: model_terms:ir.ui.view,arch_db:sign.sign_terms_conditions_setting_banner
msgid "Close"
msgstr "閉じる"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__color
#: model:ir.model.fields,field_description:sign.field_sign_request__color
#: model:ir.model.fields,field_description:sign.field_sign_request_item__color
#: model:ir.model.fields,field_description:sign.field_sign_template__color
msgid "Color"
msgstr "色"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__color
msgid "Color Index"
msgstr "カラーインデクス"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Communication history"
msgstr "通信履歴"

#. module: sign
#: model:ir.model,name:sign.model_res_company
msgid "Companies"
msgstr "会社"

#. module: sign
#: model:sign.item.type,name:sign.sign_item_type_company
#: model:sign.item.type,placeholder:sign.sign_item_type_company
msgid "Company"
msgstr "会社"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__communication_company_id
#: model:ir.model.fields,field_description:sign.field_sign_request_item__communication_company_id
msgid "Company used for communication"
msgstr "通信に利用された会社"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
#: model:ir.model.fields.selection,name:sign.selection__sign_request_item__state__completed
msgid "Completed"
msgstr "完了"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__completed_document
msgid "Completed Document"
msgstr "完了済ドキュメント"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__completed_document_attachment_ids
msgid "Completed Documents"
msgstr "完了済ドキュメント"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__nb_closed
msgid "Completed Signatures"
msgstr "完了済署名"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__completion_date
msgid "Completion Date"
msgstr "完了日"

#. module: sign
#: model:ir.model,name:sign.model_res_config_settings
msgid "Config Settings"
msgstr "コンフィグ設定"

#. module: sign
#: model:ir.ui.menu,name:sign.menu_sign_configuration
msgid "Configuration"
msgstr "設定"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"Configure the field types that can be used to sign documents (placeholder, "
"auto-completion, ...), as well as the values for selection fields in "
"signable documents."
msgstr ""
"文書への署名に使用できるフィールドタイプ (プレースホルダ、オートコンプリート、...) と、署名可能な文書の選択フィールドの値を設定します。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_duplicate_template_with_pdf_view_form
msgid "Confirm"
msgstr "確定"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Congrats, your signature is ready to be submitted!"
msgstr "おめでとうございます。あなたの署名を提出する用意ができました！"

#. module: sign
#: model_terms:web_tour.tour,rainbow_man_message:sign.sign_tour
msgid "Congratulations, you signed your first document!"
msgstr "おめでとうございます。初めてのドキュメントに署名しました！"

#. module: sign
#: model:ir.model,name:sign.model_res_partner
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__partner_id
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Contact"
msgstr "連絡先"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Contacts"
msgstr "連絡先"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Contacts in copy"
msgstr "CC連絡先"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__cc_partner_ids
msgid ""
"Contacts in copy will be notified by email once the document is either fully"
" signed or refused."
msgstr "CC連絡先には、ドキュメントが署名されるか拒否された時に、メールで通知されます。"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__cc_partner_ids
#: model:ir.model.fields,field_description:sign.field_sign_send_request__cc_partner_ids
msgid "Copy to"
msgstr "以下へコピー"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_tag_action
msgid "Create Sign Tags"
msgstr "署名タグを作成"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
msgid "Create date"
msgstr "作成日"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_option__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_role__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_type__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_log__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_request__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_request_item__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_send_request__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_template__create_uid
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__create_uid
msgid "Created by"
msgstr "作成者"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item_option__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item_role__create_date
#: model:ir.model.fields,field_description:sign.field_sign_item_type__create_date
#: model:ir.model.fields,field_description:sign.field_sign_log__create_date
#: model:ir.model.fields,field_description:sign.field_sign_request__create_date
#: model:ir.model.fields,field_description:sign.field_sign_request_item__create_date
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__create_date
#: model:ir.model.fields,field_description:sign.field_sign_send_request__create_date
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__create_date
#: model:ir.model.fields,field_description:sign.field_sign_template__create_date
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__create_date
msgid "Created on"
msgstr "作成日"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__create
msgid "Creation"
msgstr "作成"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Current status of the signature request"
msgstr "署名依頼の現在のステイタス"

#. module: sign
#: model:sign.item.role,name:sign.sign_item_role_customer
msgid "Customer"
msgstr "顧客"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request_item__access_url
msgid "Customer Portal URL"
msgstr "顧客ポータルURL"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
#: model:sign.item.type,name:sign.sign_item_type_date
#: model:sign.item.type,placeholder:sign.sign_item_type_date
msgid "Date"
msgstr "日付"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Date (UTC)"
msgstr "日付 (UTC)"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__default
msgid "Default"
msgstr "デフォルト"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__default_height
msgid "Default Height"
msgstr "デフォルト高さ"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_mail_activity_type__default_sign_template_id
msgid "Default Signature Template"
msgstr "デフォルト署名テンプレート"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__default_width
msgid "Default Width"
msgstr "デフォルト幅"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__sequence
msgid "Default order"
msgstr "デフォルトオーダ"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Delete"
msgstr "削除"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
msgid "Delete sign"
msgstr "署名を削除"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
msgid "Delete sign item"
msgstr "署名項目を削除"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid ""
"Deliver one-time codes by SMS to identify signatories when signing a "
"document."
msgstr "ドキュメントに署名する際に署名者を特定するためのワンタイムコードをSMSで配信する。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo Action"
msgstr "デモアクション"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo IP"
msgstr "IPデモ"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo Latitude"
msgstr "デモ緯度"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo Longitude"
msgstr "デモ経度"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Demo Partner"
msgstr "デモ取引先"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
msgid "Details"
msgstr "詳細"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_users__sign_initials
msgid "Digital Initials"
msgstr "デジタルイニシャル"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_users__sign_initials_frame
msgid "Digital Initials Frame"
msgstr "デジタルイニシャルフレーム"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_users__sign_signature
msgid "Digital Signature"
msgstr "電子署名"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_users__sign_signature_frame
msgid "Digital Signature Frame"
msgstr "デジタル署名フレーム"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_users_view_form
#: model_terms:ir.ui.view,arch_db:sign.view_users_form_simple_modif
msgid "Digital Signatures"
msgstr "電子署名"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_view_form
msgid "Display"
msgstr "表示"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item_option__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item_role__display_name
#: model:ir.model.fields,field_description:sign.field_sign_item_type__display_name
#: model:ir.model.fields,field_description:sign.field_sign_log__display_name
#: model:ir.model.fields,field_description:sign.field_sign_request__display_name
#: model:ir.model.fields,field_description:sign.field_sign_request_item__display_name
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__display_name
#: model:ir.model.fields,field_description:sign.field_sign_send_request__display_name
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__display_name
#: model:ir.model.fields,field_description:sign.field_sign_template__display_name
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__display_name
msgid "Display Name"
msgstr "表示名"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__sign_preview_ready
msgid "Display sign preview button"
msgstr "署名プレビューボタンを表示"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Document"
msgstr "ドキュメント"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Document <i class=\"fa fa-download\"/>"
msgstr "ドキュメント <i class=\"fa fa-download\"/>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Document Details"
msgstr "ドキュメント詳細"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__reference
#: model:ir.model.fields,field_description:sign.field_sign_request_item__reference
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_search
msgid "Document Name"
msgstr "ドキュメント名"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__page
msgid "Document Page"
msgstr "ドキュメントページ"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__template_id
msgid "Document Template"
msgstr "帳票テンプレート"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.js:0
msgid "Document saved as Template."
msgstr "ドキュメントがテンプレートとして保存されました。"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Document to send for signature or to sign yourself."
msgstr "署名のために送付する、または自身で署名するドキュメント。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.portal_my_home_sign
msgid "Document(s) to sign"
msgstr "未署名ドキュメント"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Document/Signer"
msgstr "ドキュメント/署名者"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_request_action
#: model:ir.ui.menu,name:sign.sign_request_menu
msgid "Documents"
msgstr "書類"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
msgid "Download"
msgstr "ダウンロード"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
msgid "Download Certificate"
msgstr "証明書をダウンロード"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Download Document"
msgstr "ドキュメントをダウンロード"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_type_buttons.xml:0
msgid "Drag & Drop a field in the PDF"
msgstr "PDFに項目をドラッグ&ドロップしてください。"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Drag and drop"
msgstr "ドラッグ&ドロップ"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"Draw your most beautiful signature!<br>You can also create one automatically"
" or load a signature from your computer."
msgstr "あなたの最も美しい署名を描いて下さい！<br>また、自動的に署名を作成したり、コンピュータから署名を読み込むこともできます。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Dropdown menu"
msgstr "ドロップダウンメニュー"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "ERROR: Invalid PDF file!"
msgstr "エラー: 無効なPDFファイル！"

#. module: sign
#: model:ir.actions.report,name:sign.sign_report_green_savings_action
msgid "Ecological Savings by using Electronic Signatures"
msgstr "電子署名の利用による環境資源節約"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/signable_sign_request_control_panel.xml:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Edit"
msgstr "編集"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.js:0
msgid "Edit Template"
msgstr "テンプレートを編集"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Edit field types"
msgstr "項目タイプを編集"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Edit selection values"
msgstr "選択値の編集"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Edit template name"
msgstr "テンプレート名を編集"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__signer_email
#: model:sign.item.type,name:sign.sign_item_type_email
#: model:sign.item.type,placeholder:sign.sign_item_type_email
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Email"
msgstr "メール"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__subject
msgid "Email Subject"
msgstr "メール件名"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Email Verification"
msgstr "電子メール認証"

#. module: sign
#: model:sign.item.role,name:sign.sign_item_role_employee
msgid "Employee"
msgstr "従業員"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Energy"
msgstr "エネルギー"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Enter the code received through SMS to complete your signature"
msgstr "SMSを通して受取ったコードを入力して署名を完了させて下さい。"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.js:0
msgid "Error"
msgstr "エラー"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid "Error 404"
msgstr "エラー 404"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Existing sign items are not allowed to be changed"
msgstr "存在する署名アイテムの変更は許可されていません。"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__expired
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__expired
msgid "Expired"
msgstr "契約期間終了済"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Expiring Soon"
msgstr "すぐに失効する"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__auth_method
msgid "Extra Authentication Step"
msgstr "追加認証ステップ"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__favorited_ids
msgid "Favorite of"
msgstr "お気に入り"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__favorited_ids
msgid "Favorited Users"
msgstr "お気に入りユーザ"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__name
#: model:ir.model.fields,field_description:sign.field_sign_item_type__name
msgid "Field Name"
msgstr "フィールド名"

#. module: sign
#: model:ir.ui.menu,name:sign.sign_item_type_menu
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Field Types"
msgstr "項目タイプ"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_type_buttons.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Fields"
msgstr "フィールド"

#. module: sign
#: model:ir.model,name:sign.model_sign_item
msgid "Fields to be sign on Document"
msgstr "ドキュメントで署名されるフィールド"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__datas
msgid "File Content (base64)"
msgstr "ファイルコンテンツ(base64)"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/hooks.js:0
msgid "File Error"
msgstr "ファイルエラー"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__new_pdf
msgid "File name"
msgstr "ファイル名"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__filename
msgid "Filename"
msgstr "ファイル名"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
msgid "Filled by"
msgstr "入力者"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/public_signer_dialog.js:0
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.js:0
msgid "Final Validation"
msgstr "最終検証"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Follow the guide to sign the document."
msgstr "ガイドに従ってドキュメントに署名して下さい。"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_follower_ids
msgid "Followers"
msgstr "フォロワー"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_partner_ids
msgid "Followers (Partners)"
msgstr "フォロワー (取引先)"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesomeのアイコン 例. fa-tasks"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid ""
"For 1000kg of paper usage, with 10% of recycled paper, environmental savings"
" are based on"
msgstr "1000kgの紙を使用する場合、再生紙を10％使用すると、次のような環境資源の節約効果"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_item_role__auth_method
msgid "Force the signatory to identify using a second authentication method"
msgstr "署名者に第二の認証方式による識別を強制する。"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
msgid "Frame"
msgstr "フレーム"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__frame_has_hash
msgid "Frame Has Hash"
msgstr "フレームにハッシュがあります"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__frame_hash
msgid "Frame Hash"
msgstr "フレームハッシュ"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__frame_value
msgid "Frame Value"
msgstr "フレーム値"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__signed
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Fully Signed"
msgstr "署名完了"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Future Activities"
msgstr "今後の活動"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/encrypted_dialog.xml:0
msgid "Generate PDF"
msgstr "PDFを作成"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Geolocation"
msgstr "ジオロケーション"

#. module: sign
#: model:ir.ui.menu,name:sign.sign_report_green_savings
msgid "Green Savings"
msgstr "エコ・レポート"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Green Savings Report"
msgstr "グリーン節約レポート"

#. module: sign
#: model:ir.model,name:sign.model_report_sign_green_savings_report
msgid "Green Savings Report model"
msgstr "グリーン節約レポートモデル"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
msgid "Green Savings Summary"
msgstr "グリーン節約概要"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Group By"
msgstr "グループ化"

#. module: sign
#: model:sign.template.tag,name:sign.sign_template_tag_1
msgid "HR"
msgstr "HR"

#. module: sign
#: model:ir.model,name:sign.model_ir_http
msgid "HTTP Routing"
msgstr "HTTPルーティング"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__has_default_template
msgid "Has Default Template"
msgstr "デフォルトテンプレートがあるか"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__has_message
msgid "Has Message"
msgstr "メッセージあり"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__has_sign_requests
msgid "Has Sign Requests"
msgstr "署名依頼あり"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__height
msgid "Height"
msgstr "高さ"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "Hello"
msgstr "こんにちは"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_item_type__tip
msgid "Hint displayed in the signing hint"
msgstr "署名のヒントで表示されるヒント"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Home"
msgstr "ホーム"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
msgid "How are these results calculated?"
msgstr "これらの結果はどうやって計算されるのでしょう？"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "How do we calculate?"
msgstr "どうやって計算するのでしょう？"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "I've got a total weight, and now?"
msgstr "合計重量が出ました。そして？"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__id
#: model:ir.model.fields,field_description:sign.field_sign_item__id
#: model:ir.model.fields,field_description:sign.field_sign_item_option__id
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__id
#: model:ir.model.fields,field_description:sign.field_sign_item_role__id
#: model:ir.model.fields,field_description:sign.field_sign_item_type__id
#: model:ir.model.fields,field_description:sign.field_sign_log__id
#: model:ir.model.fields,field_description:sign.field_sign_request__id
#: model:ir.model.fields,field_description:sign.field_sign_request_item__id
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__id
#: model:ir.model.fields,field_description:sign.field_sign_send_request__id
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__id
#: model:ir.model.fields,field_description:sign.field_sign_template__id
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__id
msgid "ID"
msgstr "ID"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "IP"
msgstr "IP"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "IP Address"
msgstr "IPアドレス"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__ip
msgid "IP address of the visitor"
msgstr "訪問者のIPアドレス"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__icon
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_exception_icon
msgid "Icon"
msgstr "アイコン"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "例外活動を示すアイコン"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__module_sign_itsme
msgid "Identify with itsme®"
msgstr "itsme®で認証"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__message_needaction
msgid "If checked, new messages require your attention."
msgstr "チェックした場合は、新しいメッセージに注意が必要です。"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_item_role__change_authorized
#: model:ir.model.fields,help:sign.field_sign_request_item__change_authorized
msgid ""
"If checked, recipient of a document with this role can be changed after "
"having sent the request. Useful to replace a signatory who is out of office,"
" etc."
msgstr ""
"チェックした場合、この役割を持つドキュメントの受信者は、依頼を送信した後に変更することができます。休暇中の署名者を交代させる場合などに便利です。"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__message_has_error
#: model:ir.model.fields,help:sign.field_sign_request__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "チェックした場合は、一部のメッセージに配信エラーが発生されました。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid ""
"If you do not want to receive these notifications anymore, you can disable the extra-authentication step in the\n"
"                        <code>\n"
"                            Sign &gt; Configuration &gt; Roles\n"
"                        </code>\n"
"                        menu."
msgstr ""
"これらの通知を受け取りたくない場合は\n"
"                        <code>\n"
"                            署名 &gt; 設定 &gt; 役割\n"
"                        </code>\n"
"                        メニューから追加認証ステップを無効にすることができます。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "If you do not wish to receive future reminders about this document,"
msgstr "もし今後このドキュメントについてのリマインダを受け取りたくない場合は、"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid ""
"If you wish, you can request the document again after buying more credits "
"for the operation."
msgstr "ご希望であれば、操作に必要なクレジットを買い足した後、再度ドキュメントを依頼することができます。"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__in_progress_count
msgid "In Progress Count"
msgstr "進行中数"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "In favorites, remove it"
msgstr "お気に入りで、それを削除"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__log_hash
msgid "Inalterability Hash"
msgstr "変更不可ハッシュ"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
msgid "Include a visual security frame around your signature"
msgstr "署名の周囲に視覚的なセキュリティフレームを含めて下さい。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_view_form
msgid "Information"
msgstr "情報"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__initial
msgid "Initial"
msgstr "イニシャル"

#. module: sign
#: model:sign.item.type,name:sign.sign_item_type_initial
#: model:sign.item.type,placeholder:sign.sign_item_type_initial
msgid "Initials"
msgstr "イニシャル"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Insert your terms & conditions here..."
msgstr "ここに諸条件を定義..."

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__integrity
msgid "Integrity of the Sign request"
msgstr "署名依頼の整合性"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_is_follower
msgid "Is Follower"
msgstr "フォロー中　"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__is_mail_sent
msgid "Is Mail Sent"
msgstr "メール送信済"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__is_sharing
msgid "Is Sharing"
msgstr "共有中"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "Is Signing"
msgstr "署名中"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__is_user_signer
msgid "Is User Signer"
msgstr "ユーザ署名者"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "It's signed!"
msgstr "署名済"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "John Doe"
msgstr "例: ジョン・ドー"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "John Smith"
msgstr "John Smith"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "L of water saved"
msgstr "Lの水削減"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__last_action_date
msgid "Last Action Date"
msgstr "最終アクション日"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_option__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_role__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_item_type__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_log__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_request__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_request_item__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_send_request__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_template__write_uid
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item_option__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item_role__write_date
#: model:ir.model.fields,field_description:sign.field_sign_item_type__write_date
#: model:ir.model.fields,field_description:sign.field_sign_log__write_date
#: model:ir.model.fields,field_description:sign.field_sign_request__write_date
#: model:ir.model.fields,field_description:sign.field_sign_request_item__write_date
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__write_date
#: model:ir.model.fields,field_description:sign.field_sign_send_request__write_date
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__write_date
#: model:ir.model.fields,field_description:sign.field_sign_template__write_date
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__last_reminder
msgid "Last reminder"
msgstr "最終リマインダ"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Late Activities"
msgstr "遅れた活動"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__latitude
#: model:ir.model.fields,field_description:sign.field_sign_request_item__latitude
msgid "Latitude"
msgstr "緯度"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__validity
msgid "Leave empty for requests without expiration."
msgstr "使用期限なしのリクエストの場合は空のままにします。"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.js:0
msgid "Left"
msgstr "左"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Let's <b>prepare & sign</b> our first document."
msgstr " 初めてのドキュメントの<b>準備と署名</b>を行いましょう。"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Let's send the request by email."
msgstr "依頼をEメールで送信しましょう。"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__redirect_url_text
msgid "Link Label"
msgstr "リンクラベル"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__activity_id
msgid "Linked Activity"
msgstr "リンクされた活動"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__reference_doc
msgid "Linked To"
msgstr "以下にリンク:"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__reference_doc
msgid "Linked to"
msgstr "以下にリンク:"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__log_date
msgid "Log Date"
msgstr "記録日"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_log.py:0
msgid "Log history of sign requests cannot be deleted!"
msgstr "署名依頼の記録履歴は削除できません！"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_log.py:0
msgid "Log history of sign requests cannot be modified!"
msgstr "署名依頼の記録履歴は修正できません！"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Logo"
msgstr "ロゴ"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__sign_log_ids
#: model_terms:ir.ui.view,arch_db:sign.sign_log_view_tree
msgid "Logs"
msgstr "ログ"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__longitude
#: model:ir.model.fields,field_description:sign.field_sign_request_item__longitude
msgid "Longitude"
msgstr "経度"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__mail_sent_order
msgid "Mail Sent Order"
msgstr "署名オーダをメール送信"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__update_mail
msgid "Mail Update"
msgstr "メール更新"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "Malformed expression: %(exp)s"
msgstr "不正な表現: %(exp)s"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__group_manage_template_access
#: model:res.groups,name:sign.manage_template_access
msgid "Manage template access"
msgstr "テンプレートアクセスを管理"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
msgid "Mandatory field"
msgstr "必須項目"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__message
msgid "Message"
msgstr "メッセージ"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_has_error
msgid "Message Delivery error"
msgstr "メッセージ配信エラー"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__message_cc
msgid "Message to be sent to contacts in copy of the signed document"
msgstr "署名済ドキュメントのコピーの連絡先に送信されるメッセージ"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__message
msgid "Message to be sent to signers of the specified document"
msgstr "特定のドキュメントの署名者に送られるメッセージ"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_ids
msgid "Messages"
msgstr "メッセージ"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.encrypted_ask_password
msgid "Missing Password"
msgstr "パスワード不明"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__sms_number
msgid "Mobile"
msgstr "モバイル"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Modify Template"
msgstr "テンプレートを変更"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__textarea
#: model:sign.item.type,name:sign.sign_item_type_multiline_text
#: model:sign.item.type,placeholder:sign.sign_item_type_multiline_text
msgid "Multiline Text"
msgstr "複数行テキスト"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_share_view_form
msgid "Multiple Signature Requests"
msgstr "複数署名依頼"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "活動期限"

#. module: sign
#: model:ir.ui.menu,name:sign.sign_request_my_documents
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "My Documents"
msgstr "自分のドキュメント"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_search
msgid "My Favorites"
msgstr "お気に入り"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "My Requests"
msgstr "自分の依頼"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_search
msgid "My Templates"
msgstr "自分のテンプレート"

#. module: sign
#: model:sign.template.tag,name:sign.sign_template_tag_2
msgid "NDA"
msgstr "秘密保持契約(NDA)"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_role__name
#: model:ir.model.fields,field_description:sign.field_sign_template__name
#: model:sign.item.type,name:sign.sign_item_type_name
#: model:sign.item.type,placeholder:sign.sign_item_type_name
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Name"
msgstr "名称"

#. module: sign
#: model:ir.model.constraint,message:sign.constraint_sign_item_role_name_uniq
msgid "Name already exists!"
msgstr "名前はすでに存在します！"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Name for the file"
msgstr "ファイル名"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Name of the file"
msgstr "ファイル名"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Nearly there, keep going!"
msgstr "あと少しです、続けましょう！"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__need_my_signature
msgid "Need My Signature"
msgstr "自分の署名が必要"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/PDF_iframe.js:0
msgid "Need a valid PDF to add signature fields!"
msgstr "署名フィールドを追加するには有効なPDFが必要です。"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "Need to sign documents?"
msgstr "ドキュメントに署名が必要ですか?"

#. module: sign
#: model:ir.actions.act_window,name:sign.action_sign_send_request
msgid "New Signature Request"
msgstr "新規署名依頼"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__new_template
msgid "New Template Name"
msgstr "新規テンプレート名"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "Newest"
msgstr "最新"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "次の活動カレンダーイベント"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "次の活動期限"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_summary
msgid "Next Activity Summary"
msgstr "次の活動概要"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_type_id
msgid "Next Activity Type"
msgstr "次の活動タイプ"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
msgid "Next Document"
msgstr "次のドキュメント"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.xml:0
msgid "Next signatory ("
msgstr "次の署名者("

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "No attachment was provided"
msgstr "添付がありません"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_all_request_action
#: model_terms:ir.actions.act_window,help:sign.sign_request_action
msgid "No document yet"
msgstr "まだドキュメントがありません"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "No specified reason"
msgstr "明確な理由なし"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "No template yet"
msgstr "テンプレートがまだありません"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "None"
msgstr "なし"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Not in favorites, add it"
msgstr "お気に入りにありません、追加して下さい。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Not in favorites, set it"
msgstr "お気に入りにありません、設定して下さい。"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_needaction_counter
msgid "Number of Actions"
msgstr "アクション数"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__num_options
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__num_options
msgid "Number of Radio Button options"
msgstr "ラジオボタンオプション数"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Number of documents in progress for this template."
msgstr "このテンプレートで手続き中のドキュメントの数"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Number of documents signed for this template."
msgstr "このテンプレートで署名されたドキュメントの数"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_has_error_counter
msgid "Number of errors"
msgstr "エラー数"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "アクションを必要とするメッセージの数"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "配信エラーが発生されたメッセージ数"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__num_pages
msgid "Number of pages"
msgstr "ページ数"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "Odoo Sign"
msgstr "Odoo署名"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "One can weighs 15 g"
msgstr "１缶は15g"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "One liter of gas fuel will produce 8.9 kg of CO²"
msgstr "1リットルのガス燃料は8.9 kgのCO²を生み出します。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "One or more selection items have no associated options"
msgstr "1つ以上の選択項目に関連するオプションがありません。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "One uploaded file cannot be read. Is it a valid PDF?"
msgstr "アップロードされたファイルが読み込めません。有効なPDFですか？"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
#: code:addons/sign/controllers/terms.py:0
msgid "Oops"
msgstr "Oops"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
msgid "Operation successful"
msgstr "操作が成功しました"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_option__value
msgid "Option"
msgstr "オプション"

#. module: sign
#: model:ir.model,name:sign.model_sign_item_option
msgid "Option of a selection Field"
msgstr "選択フィールドのオプション"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Optional Message..."
msgstr "任意のメッセージ..."

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_template__redirect_url
msgid "Optional link for redirection after signature"
msgstr "署名後のリダイレクト用オプションリンク"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_template__redirect_url_text
msgid "Optional text to display on the button link"
msgstr "ボタンリンクに表示するオプションテキスト"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Options"
msgstr "オプション"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_duplicate_template_pdf__original_template_id
msgid "Original File"
msgstr "元のファイル"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/encrypted_dialog.js:0
#: model_terms:ir.ui.view,arch_db:sign.encrypted_ask_password
msgid "PDF is encrypted"
msgstr "PDFは暗号化されています"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Paper Savings"
msgstr "紙の節約"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Participants"
msgstr "対象者"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__partner_id
msgid "Partner"
msgstr "取引先"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/encrypted_dialog.js:0
msgid "Password is incorrect."
msgstr "パスワードが間違っています。"

#. module: sign
#: model:sign.item.type,name:sign.sign_item_type_phone
#: model:sign.item.type,placeholder:sign.sign_item_type_phone
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Phone"
msgstr "電話"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Phone Number"
msgstr "電話番号"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
#: model:ir.model.fields,field_description:sign.field_sign_item_type__placeholder
msgid "Placeholder"
msgstr "プレースホルダ"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Please configure senders'(%s) email addresses"
msgstr "送信者'(%s)のEメールアドレスを設定して下さい。"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "Please select recipients for the following roles: %(roles)s"
msgstr "以下の役割のために受信者を選択して下さい: %(roles)s"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__access_url
msgid "Portal Access URL"
msgstr "ポータルアクセスURL"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__posX
msgid "Position X"
msgstr "ポジションX"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__posY
msgid "Position Y"
msgstr "ポジションY"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Preview"
msgstr "プレビュー"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Printed on"
msgstr "印刷日"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__progress
msgid "Progress"
msgstr "進捗"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Public User"
msgstr "パブリックユーザ"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__radio
#: model:sign.item.type,name:sign.sign_item_type_radio
msgid "Radio Buttons"
msgstr "ラジオボタン"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_radio_set__radio_items
msgid "Radio Items"
msgstr "ラジオ項目"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__radio_set_id
msgid "Radio button options"
msgstr "ラジオボタンオプション"

#. module: sign
#: model:ir.model,name:sign.model_sign_item_radio_set
msgid "Radio button set for keeping radio button items together"
msgstr "ラジオボタン項目をまとめるためのラジオボタンセット"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__rating_ids
msgid "Ratings"
msgstr "評価"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Re-send SMS"
msgstr "SMSを再送"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__redirect_url
msgid "Redirect Link"
msgstr "リダイレクトリンク"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.xml:0
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__refuse
msgid "Refuse"
msgstr "否認"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/signable_sign_request_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Refuse Document"
msgstr "ドキュメントを拒否"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.js:0
msgid "Refuse to sign"
msgstr "署名を拒否"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__refused
msgid "Refused Signature"
msgstr "拒否された署名"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__reminder
#: model:ir.model.fields,field_description:sign.field_sign_send_request__reminder
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Reminder"
msgstr "リマインダ"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__reminder_enabled
#: model:ir.model.fields,field_description:sign.field_sign_send_request__reminder_enabled
msgid "Reminder Enabled"
msgstr "リマインダが有効化されました"

#. module: sign
#: model:ir.ui.menu,name:sign.sign_reports
msgid "Reports"
msgstr "レポート"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__request_item_infos
msgid "Request Item Infos"
msgstr "依頼項目情報"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/activity/activity_patch.xml:0
#: code:addons/sign/static/src/backend_components/cog_menu/sign_request_cog_menu.xml:0
#: model:ir.model.fields.selection,name:sign.selection__mail_activity_type__category__sign_request
#: model:mail.activity.type,name:sign.mail_activity_data_signature_request
msgid "Request Signature"
msgstr "署名を依頼"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "Request expiration date must be set in the future."
msgstr "依頼期限日は未来に設定される必要があります。"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__nb_total
msgid "Requested Signatures"
msgstr "依頼された署名"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__required
msgid "Required"
msgstr "必須"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_item_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
msgid "Resend"
msgstr "再送"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
msgid "Resend the invitation"
msgstr "招待メールを再送"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
msgid "Resent!"
msgstr "再送されました！"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/initial_all_pages_dialog.xml:0
#: model:ir.model.fields,field_description:sign.field_sign_item__responsible_id
#: model:ir.model.fields,field_description:sign.field_sign_template__user_id
msgid "Responsible"
msgstr "担当者"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__responsible_count
msgid "Responsible Count"
msgstr "責任者数"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__activity_user_id
msgid "Responsible User"
msgstr "担当ユーザ"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Restore"
msgstr "リストア"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.js:0
msgid "Right"
msgstr "右"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__role_id
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__role_id
msgid "Role"
msgstr "役割"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_role_view_tree
msgid "Role Name"
msgstr "役割名称"

#. module: sign
#: model:ir.ui.menu,name:sign.sign_item_role_menu
msgid "Roles"
msgstr "役割"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_iframe.js:0
msgid "Rotate Clockwise"
msgstr "時計回り"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS配信エラー"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "SMS Sent"
msgstr "SMS送信済"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__sms_token
msgid "SMS Token"
msgstr "SMSトークン"

#. module: sign
#: model:sign.template.tag,name:sign.sign_template_tag_3
msgid "Sales"
msgstr "販売"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__save
msgid "Save"
msgstr "保存"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Save as Template"
msgstr "テンプレートとして保存"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_body.js:0
msgid "Saved"
msgstr "保存しました"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "Search <span class=\"nolabel\"> (in Document)</span>"
msgstr "検索 <span class=\"nolabel\"> (ドキュメント内)</span>"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__access_token
#: model:ir.model.fields,field_description:sign.field_sign_request_item__access_token
msgid "Security Token"
msgstr "セキュリティトークン"

#. module: sign
#: model:sign.item.type,tip:sign.sign_item_type_selection
msgid "Select an option"
msgstr "オプションを選択"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"Select the contact who should sign, according to their role.<br>In this "
"example, select your own contact to sign the document yourself."
msgstr ""
"役割に応じて、署名しなければいけない連絡先を選択して下さい。<br>この例では、あなた自身が文書に署名するために、あなた自身の連絡先を選択します。"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__selection
#: model:sign.item.type,name:sign.sign_item_type_selection
#: model:sign.item.type,placeholder:sign.sign_item_type_selection
msgid "Selection"
msgstr "選択"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__option_ids
msgid "Selection options"
msgstr "選択オプション"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_item_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Send"
msgstr "送信"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Send SMS"
msgstr "SMS配信"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__signer_id
msgid "Send To"
msgstr "以下に送信"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid "Send a new link"
msgstr "新しいリンクを送信"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Send a reminder"
msgstr "リマインダを送信"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
msgid "Send the invitation"
msgstr "招待状を送信"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Sent"
msgstr "送信済"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__nb_wait
msgid "Sent Requests"
msgstr "送信済依頼"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "Sent by"
msgstr "送信者"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_settings_action
#: model:ir.ui.menu,name:sign.sign_item_settings_menu
msgid "Settings"
msgstr "管理設定"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Share"
msgstr "共有"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_share_view_form
msgid "Share & Close"
msgstr "共有して閉じる"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "Share Document by Link"
msgstr "リンクによるドキュメントの共有"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__share_link
msgid "Share Link"
msgstr "リンクをシェア"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Shareable"
msgstr "共有可能"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
#: code:addons/sign/models/sign_template.py:0
#: model:ir.model.fields.selection,name:sign.selection__sign_log__request_state__shared
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__shared
msgid "Shared"
msgstr "共有"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Show all records which has next action date is before today"
msgstr "次のアクションの日付が今日より前のすべてのレコードを表示"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Show standard terms & conditions on signature requests"
msgstr "署名依頼に標準的な利用規約を表示"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.js:0
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
#: model:ir.ui.menu,name:sign.menu_document
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Sign"
msgstr "署名"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__use_sign_terms
msgid "Sign Default Terms & Conditions"
msgstr "デフォルトの利用規約にサインする"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_company__sign_terms
msgid "Sign Default Terms and Conditions"
msgstr "デフォルトの利用規約に署名"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_company__sign_terms_html
msgid "Sign Default Terms and Conditions as a Web page"
msgstr "ウェブページとしてデフォルトの利用規約に署名"

#. module: sign
#: model:ir.model,name:sign.model_sign_duplicate_template_pdf
msgid "Sign Duplicate Template with new PDF"
msgstr "新規PDFで重複テンプレートに署名"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.js:0
msgid "Sign Next Document"
msgstr "次のドキュメントを署名"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.xml:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:sign.sign_request_share_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_tree
msgid "Sign Now"
msgstr "今すぐ署名"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__mail_sent_order
msgid "Sign Order"
msgstr "署名順"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__sign_request_id
msgid "Sign Request"
msgstr "署名依頼"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__sign_request_item_id
msgid "Sign Request Item"
msgstr "署名依頼項目"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request_signer__sign_send_request_id
msgid "Sign Send Request"
msgstr "署名送付依頼"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.res_config_settings_view_form
msgid "Sign Settings"
msgstr "署名の設定"

#. module: sign
#: model:ir.model,name:sign.model_sign_template_tag
msgid "Sign Template Tag"
msgstr "署名テンプレートタグ"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__sign_terms
msgid "Sign Terms & Conditions"
msgstr "利用規約に署名"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_config_settings__sign_terms_html
msgid "Sign Terms & Conditions as a Web page"
msgstr "ウェブページとして利用規約に署名"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_res_company__sign_terms_type
#: model:ir.model.fields,field_description:sign.field_res_config_settings__sign_terms_type
msgid "Sign Terms & Conditions format"
msgstr "利用規約フォーマットに署名"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_name_and_signature_dialog.xml:0
msgid "Sign all"
msgstr "全てに署名"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "Sign document"
msgstr "ドキュメントに署名"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "Sign now"
msgstr "今すぐ署名"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "Sign requests"
msgstr "署名依頼"

#. module: sign
#: model:ir.model,name:sign.model_sign_log
msgid "Sign requests access history"
msgstr "署名依頼アクセス履歴"

#. module: sign
#: model:ir.model,name:sign.model_sign_send_request
msgid "Sign send request"
msgstr "署名送付依頼"

#. module: sign
#: model:ir.model,name:sign.model_sign_send_request_signer
msgid "Sign send request signer"
msgstr "署名送付依頼 署名者"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid ""
"Sign up for Odoo Sign to manage your own documents and signature requests!"
msgstr "Odoo署名に登録すると、自分のドキュメントや署名依頼を管理できます！"

#. module: sign
#: model:ir.actions.server,name:sign.sign_reminder_cron_ir_actions_server
msgid "Sign: Send mail reminder"
msgstr "署名: メールリマインダ送信"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Signatory"
msgstr "署名者"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Signatory's hash:"
msgstr "署名者ハッシュ:"

#. module: sign
#. odoo-javascript
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
#: code:addons/sign/static/src/js/tours/sign.js:0
#: model:ir.model.fields,field_description:sign.field_sign_request_item__signature
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__signature
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__sign
#: model:sign.item.type,name:sign.sign_item_type_signature
#: model:sign.item.type,placeholder:sign.sign_item_type_signature
#: model_terms:ir.ui.view,arch_db:sign._doc_sign
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "Signature"
msgstr "署名"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
msgid "Signature Date"
msgstr "署名日付"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__sign_item_id
msgid "Signature Item"
msgstr "署名項目"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_item_option_action
msgid "Signature Item Options"
msgstr "署名項目オプション"

#. module: sign
#: model:ir.model,name:sign.model_sign_item_role
msgid "Signature Item Party"
msgstr "署名項目主体"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_item_role_action
msgid "Signature Item Role"
msgstr "署名項目役職"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_item_type_action
#: model:ir.model,name:sign.model_sign_item_type
msgid "Signature Item Type"
msgstr "署名項目タイプ"

#. module: sign
#: model:ir.model,name:sign.model_sign_request_item_value
msgid "Signature Item Value"
msgstr "署名項目値"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__sign_item_ids
msgid "Signature Items"
msgstr "署名項目"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/activity/activity_model_patch.js:0
#: code:addons/sign/static/src/backend_components/cog_menu/sign_request_cog_menu.js:0
#: model:ir.model,name:sign.model_sign_request
#: model:ir.model.fields,field_description:sign.field_sign_request_item__sign_request_id
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__sign_request_id
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Signature Request"
msgstr "署名依頼"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "Signature Request - %(file_name)s"
msgstr "署名依頼 - %(file_name)s"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid "Signature Request - %s"
msgstr "署名依頼 - %s"

#. module: sign
#: model:ir.model,name:sign.model_sign_request_item
msgid "Signature Request Item"
msgstr "署名依頼項目"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_request_item_action
msgid "Signature Request Items"
msgstr "署名依頼項目"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__sign_request_item_id
msgid "Signature Request item"
msgstr "署名依頼項目"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__sign_request_ids
msgid "Signature Requests"
msgstr "署名依頼"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__start_sign
msgid "Signature Started"
msgstr "署名開始済"

#. module: sign
#: model:ir.model,name:sign.model_sign_template
msgid "Signature Template"
msgstr "署名テンプレート"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/sign_items.xml:0
msgid "Signature configuration"
msgstr "署名設定"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_send_request.py:0
msgid ""
"Signature requested for template: %(template)s\n"
"Signatories: %(signatories)s"
msgstr ""
"テンプレート用に依頼された署名: %(template)s\n"
"署名者: %(signatories)s"

#. module: sign
#. odoo-python
#: code:addons/sign/models/res_partner.py:0
msgid "Signature(s)"
msgstr "署名"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Signature:"
msgstr "署名："

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.portal_my_home_menu_sign
#: model_terms:ir.ui.view,arch_db:sign.portal_my_home_sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
msgid "Signatures"
msgstr "署名"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.doc_sign
msgid "Signed"
msgstr "署名済"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template__signed_count
msgid "Signed Count"
msgstr "署名済数"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Signed Documents"
msgstr "署名済ドキュメント"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__signed_without_extra_auth
msgid "Signed Without Extra Authentication"
msgstr "追加認証なしで署名済"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "Signed document"
msgstr "署名済ドキュメント"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__signing_date
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
#: model_terms:ir.ui.view,arch_db:sign.signer_status_wrapper
msgid "Signed on"
msgstr "署名日"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__partner_id
msgid "Signer"
msgstr "署名者"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/fields/signer_x2many.js:0
msgid "Signer One 2 Many"
msgstr "署名者1対多"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__request_item_ids
#: model:ir.model.fields,field_description:sign.field_sign_send_request__signer_ids
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "Signers"
msgstr "署名者"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__signers_count
msgid "Signers Count"
msgstr "署名者数"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "Signing Date"
msgstr "署名日付"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Signing Events"
msgstr "署名イベント"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__set_sign_order
msgid "Signing Order"
msgstr "署名順"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"Since you're the one signing this document, you can do it directly within "
"Odoo.<br>External users can use the link provided by email."
msgstr ""
"このドキュメントに署名するのはあなた自身であるため、Odoo内で直接署名することができます。<br>外部ユーザはEメールで送られるリンクを使用することができます。"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid "Some fields have still to be completed"
msgstr "まだ完了していないフィールドがあります。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Some required items are not filled"
msgstr "入力されていない必要項目があります。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Some unauthorised items are filled"
msgstr "いくつかの未認証項目が入力されています。"

#. module: sign
#. odoo-javascript
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_body.js:0
msgid "Somebody is already filling a document which uses this template"
msgstr "誰かがすでにこのテンプレートを使用したドキュメントに入力しています。"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid "Sorry, an error occurred, please try to fill the document again."
msgstr "エラーが発生しました。もう一度入力して下さい。"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.js:0
msgid "Sorry, you cannot refuse this document"
msgstr "このドキュメントを拒否することはできません。"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_send_request__set_sign_order
msgid ""
"Specify the order for each signer. The signature request only gets sent to                                     the next signers in the sequence when all signers from the previous level have                                     signed the document.\n"
"                                    "
msgstr ""
"各署名の順番を指定します。署名署名依頼項目が次の署名者に送られるのは、前のレベルの署名者が　　　　　　　　　　　　全てのドキュメントに署名したときのみです。\n"
"                                    "

#. module: sign
#: model:sign.item.role,name:sign.sign_item_role_default
msgid "Standard"
msgstr "標準"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__state
#: model:ir.model.fields,field_description:sign.field_sign_request_item__state
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "State"
msgstr "状況"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__request_state
msgid "State of the request on action log"
msgstr "アクション記録上の依頼ステータス"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
msgid "Status"
msgstr "状態"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"活動に基づくステータス\n"
"遅延: 期限が既に過ぎています\n"
"今日: 活動日は今日です\n"
"予定: 将来の活動。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Stop Sharing"
msgstr "共有を止める"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_send_request__subject
msgid "Subject"
msgstr "件名"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Summary"
msgstr "概要"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_template_tag__name
msgid "Tag Name"
msgstr "タグ名"

#. module: sign
#: model:ir.model.constraint,message:sign.constraint_sign_template_tag_name_uniq
msgid "Tag name already exists!"
msgstr "タグ名がすでに存在します！"

#. module: sign
#: model:ir.actions.act_window,name:sign.sign_template_tag_action
#: model:ir.model.fields,field_description:sign.field_sign_request__template_tags
#: model:ir.model.fields,field_description:sign.field_sign_template__tag_ids
#: model:ir.ui.menu,name:sign.sign_template_tag_menu
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_template_tag_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_template_tag_view_tree
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_form
msgid "Tags"
msgstr "タグ"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Tags:"
msgstr "タグ:"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_item_type__auto_field
msgid ""
"Technical name of the field on the partner model to auto-complete this "
"signature field at the time of signature."
msgstr "署名時にこの署名欄をオートコンプリートするためのパートナーモデル上のフィールドの技術名。"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__template_id
#: model:ir.model.fields,field_description:sign.field_sign_send_request__template_id
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_kanban
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Template"
msgstr "テンプレート"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/sign_request_control_panel.js:0
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_control_panel.js:0
#: code:addons/sign/static/src/views/hooks.js:0
msgid "Template %s"
msgstr "テンプレート%s"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_top_bar.xml:0
msgid "Template Properties"
msgstr "テンプレートプロパティ"

#. module: sign
#: model:ir.actions.server,name:sign.sign_template_tour_trigger_action
msgid "Template Sample Contract.pdf trigger"
msgstr "テンプレートサンプル契約書.pdfトリガ"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Template or Tag"
msgstr "テンプレートまたはタグ"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
#: model:ir.actions.act_window,name:sign.sign_template_action
#: model:ir.ui.menu,name:sign.sign_template_menu
msgid "Templates"
msgstr "テンプレート"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "Terms &amp; Conditions"
msgstr "諸条件"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__res_company__sign_terms_type__html
msgid "Terms as Web Page"
msgstr "ウェブページとしての規約"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__res_company__sign_terms_type__plain
msgid "Terms in Email"
msgstr "Eメールでの規約"

#. module: sign
#: model:ir.model.fields,help:sign.field_res_company__sign_terms_type
#: model:ir.model.fields,help:sign.field_res_config_settings__sign_terms_type
msgid ""
"Terms in Email - The text will be displayed at the bottom of every signature request email.\n"
"\n"
"        Terms as Web Page - A link will be pasted at the bottom of every signature request email, leading to your content.\n"
"        "
msgstr ""
"Eメールでの規約 - テキストは全ての署名依頼Eメールの下部に表示されます。\n"
"\n"
"ウェブページとしての規約 - 署名依頼メールごとにその末尾に、あなたのコンテンツへのリンクが貼り付けられます。"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_type__item_type__text
#: model:sign.item.type,name:sign.sign_item_type_text
#: model:sign.item.type,placeholder:sign.sign_item_type_text
msgid "Text"
msgstr "テキスト"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.js:0
msgid "Thank You!"
msgstr "ありがとうございます!"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"That's it, all done!<br>The document is signed, and a copy has been sent by "
"email to all participants, along with a traceability report."
msgstr "これで完了です！<br>ドキュメントに署名し、そのコピーをトレーサビリティレポートとともに参加者全員にEメールで送信しました。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid ""
"The Odoo Sign document you are trying to reach does not exist. The signature"
" request might have been deleted or modified."
msgstr "対象のOdoo署名ドキュメントが存在しません。署名依頼が削除されたか変更された可能性があります。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.encrypted_ask_password
msgid "The PDF's password is required to generate the final document."
msgstr "最終ドキュメントを生成するには、PDFのパスワードが必要です。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"The completed document cannot be created because the sign request is not "
"fully signed"
msgstr "署名依頼の署名が完了していないため、完成ドキュメントを作成できません。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "The computation is based on the website"
msgstr "計算はウェブサイトに基づいています。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"The contact of %(role)s has been changed from %(old_partner)s to "
"%(new_partner)s."
msgstr "%(role)sの連絡先が%(old_partner)sから%(new_partner)sへ変更されました。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid "The document"
msgstr "ドキュメント"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The document %s has been fully signed."
msgstr "ドキュメント%sは全て署名されました。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The document (%s) has been rejected by one of the signers"
msgstr "ドキュメント（%s）は署名者の1人によって拒否されました。"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.js:0
msgid "The document has been refused"
msgstr "ドキュメントは否認されました"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The document has been signed by a signer and cannot be edited"
msgstr "ドキュメントは署名者によって署名されたため、編集することはできません。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid ""
"The final document and this completion history have been sent by email "
"on&amp;nbsp;"
msgstr "最終資料とこの完了履歴をメールにて送付した日"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid ""
"The integrity of the document's history cannot be verified. This could mean "
"that signature values or the underlying PDF document may have been modified "
"after the fact."
msgstr ""
"ドキュメント履歴の整合性を検証できません。こ れは、署名値または基礎 となっているPDFドキュメントが事後に変更された可能性があることを意味します。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/res_partner.py:0
msgid ""
"The mail address of %(partner)s has been updated. The request will be "
"automatically resent."
msgstr "%(partner)sのメールアドレスが更新されました。依頼は自動的に再送信されます。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The mail has been sent to contacts in copy: %(contacts)s"
msgstr "メールは連絡先にコピーで送信されました: %(contacts)s"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/terms.py:0
msgid "The requested page is invalid, or doesn't exist anymore."
msgstr "要求されたページは無効であるか、もう存在しません。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "The role %s is required by the Sign application and cannot be deleted."
msgstr "役割%sは署名アプリケーションによって要求され、削除することはできません。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The sign request has not been fully signed"
msgstr "署名依頼の署名が完了していません。"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid "The signature has been canceled by %(partner)s(%(role)s)"
msgstr "%(partner)s(%(role)s) によって署名が取消されました"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The signature has been refused by %(partner)s(%(role)s)"
msgstr " %(partner)s(%(role)s)によって署名が拒否されました。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The signature mail has been sent to: "
msgstr "この署名メールが以下に送信されました:"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request_item__is_mail_sent
msgid "The signature mail has been sent."
msgstr "署名メールが送信されました。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
msgid "The signature request has been cancelled"
msgstr "署名依頼が取消されました"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "The signature request has been edited by: %s."
msgstr "署名依頼が以下によって編集されました:%s"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_action.js:0
msgid "The template doesn't exist anymore."
msgstr "テンプレートはもう存在しません。"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_duplicate_template_with_pdf.py:0
msgid ""
"The template has more pages than the current file, it can't be applied."
msgstr "テンプレートが現在のファイルよりページ数が多いため、適用できません。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid ""
"The total of sheets you saved is based on: the number of sent sign requests "
"x number of sheets in the document x (number of contacts who need to sign + "
"number of contacts in copy if the sign request is signed) = total of pages. "
"We assume that one page weights 0.005 kilograms."
msgstr ""
"保存したシート数の合計は、送信された署名依頼の数×ドキュメント内のシート数 "
"×（署名が必要な連絡先の数＋署名依頼が署名された場合のコピーの連絡先の数）＝ページの合計に基づいています。1ページの重さは0.005キログラムと仮定します。"

#. module: sign
#. odoo-python
#: code:addons/sign/wizard/sign_duplicate_template_with_pdf.py:0
msgid "The uploaded file is not a valid PDF. Please upload a valid PDF file."
msgstr "アップロードされたファイルは有効なPDFではありません。有効なPDFファイルをアップロードして下さい。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_requests
msgid "There are no signatures request."
msgstr "署名依頼がありません。"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "There are other documents waiting for your signature:"
msgstr "あなたの署名待ちのドキュメントが他にもあります:"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"There was an issue downloading your document. Please contact an "
"administrator."
msgstr "ドキュメントのダウンロードに問題が発生しました。管理者にご連絡下さい。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid ""
"There's no reason to panic, <br/>\n"
"                        you can still sign your document in a few clicks!"
msgstr ""
"ご安心下さい。<br/>\n"
"　　　　　数クリックでドキュメントに署名することはまだ可能です！"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/template_alert_dialog/template_alert_dialog.xml:0
msgid "These files cannot be read, they may be corrupted or encrypted."
msgstr "これらのファイルは読み取ることができず、破損していたり暗号化されている可能性があります。"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/template_alert_dialog/template_alert_dialog.xml:0
msgid "They will be removed from the uploaded files"
msgstr "アップロードされたファイルから削除されます。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This function can only be called with sudo."
msgstr "この関数はsudoでのみ呼び出すことができます。"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__reference
#: model:ir.model.fields,help:sign.field_sign_request_item__reference
msgid "This is how the document will be named in the mail"
msgstr "ドキュメントはメール内でこのように名付けられます。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid "This link has expired."
msgstr "このリンクは有効期限切れです。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request cannot be refused"
msgstr "署名依頼は拒否できません"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request cannot be signed"
msgstr "この署名依頼は署名することができません。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request is not valid anymore"
msgstr "この署名依頼はもう有効ではありません。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request item cannot be filled"
msgstr "この署名依頼項目は入力することができません。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request item cannot be refused"
msgstr "この署名依頼項目は拒否することはできません。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "This sign request item cannot be signed"
msgstr "この署名依頼項目は署名することはできません。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid ""
"This will keep all the already completed signature of this request and "
"disable every sent access, are you sure?"
msgstr "これにより、この依頼の完了済署名は全て保持され、送信されたアクセスはすべて無効になります。本当に進めますか？"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item_type__tip
msgid "Tip"
msgstr "ヒント"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_request__state__sent
#: model:ir.model.fields.selection,name:sign.selection__sign_request_item__state__sent
msgid "To Sign"
msgstr "署名未済"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "To produce 1000 kg of wood, we have to cut 12 trees"
msgstr "1000kgの木材を生産するには、12本の木を伐採する必要があります。"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/portal.py:0
msgid "To sign"
msgstr "署名する"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Today Activities"
msgstr "本日の活動"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__transaction_id
msgid "Transaction"
msgstr "取引明細書"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid "Try Odoo Sign"
msgstr "Odoo署名を試しましょう"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Try a sample contract"
msgstr "サンプル契約書を試す"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "Try our sample document"
msgstr "サンプルドキュメントを試しましょう"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid "Try out this sample contract."
msgstr "このサンプル契約書を試してみましょう"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__type_id
#: model:ir.model.fields,field_description:sign.field_sign_item_type__item_type
msgid "Type"
msgstr "タイプ"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/fields/signer_x2many.js:0
msgid "Type a name or email..."
msgstr "名前またはEメールを入力..."

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "記録上の例外活動の種類。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_tag_view_form
msgid "Type tag name here"
msgstr "ここにタグ名を入力して下さい"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "UTC"
msgstr "UTC"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.js:0
msgid "Unable to send the SMS, please contact the sender of the document."
msgstr "SMSを送信できません。ドキュメントの送信者にご連絡下さい。"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid ""
"Unable to sign the document due to missing required data. Please contact an "
"administrator."
msgstr "必要なデータが不足しているため、書類に署名できません。管理者にご連絡下さい。"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_item_role__auth_method__sms
msgid "Unique Code via SMS"
msgstr "SMS経由の一意コード"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__update
msgid "Update"
msgstr "更新"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Upload"
msgstr "アップロード"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/common.xml:0
msgid "Upload PDF"
msgstr "PDFをアップロード"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid "Upload a PDF"
msgstr "PDFをアップロード"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/common.xml:0
msgid "Upload a PDF & Sign"
msgstr "PDFをアップロードして署名"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_all_request_action
#: model_terms:ir.actions.act_window,help:sign.sign_request_action
msgid "Upload a PDF file or use an existing template to begin."
msgstr "PDFファイルをアップロードまたは既存のテンプレートを使用して開始"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "Upload a PDF file to create a reusable template."
msgstr "再利用可能なテンプレートを作成するには、PDFファイルをアップロードします。"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "Upload a PDF file to create your first template"
msgstr "PDFファイルをアップロードして初めてのテンプレートを作成"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/common.xml:0
msgid "Upload a pdf that you want to sign directly"
msgstr "直接署名したいPDFをアップロード"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_view_kanban
msgid "Use Layout"
msgstr "レイアウトを使用"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_template_tag_action
msgid "Use Tags to manage your Sign Templates and Sign Requests"
msgstr "タグを使って署名テンプレートと署名依頼を管理します"

#. module: sign
#: model:ir.actions.act_window,name:sign.action_sign_duplicate_template_with_pdf
msgid "Use the layout of fields on a new PDF"
msgstr "新しいPDFに項目のレイアウトを使用"

#. module: sign
#: model:ir.model,name:sign.model_res_users
#: model:ir.model.fields,field_description:sign.field_sign_log__user_id
#: model:sign.item.role,name:sign.sign_item_role_user
msgid "User"
msgstr "ユーザ"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_log__token
msgid "User token"
msgstr "ユーザートークン"

#. module: sign
#: model:res.groups,name:sign.group_sign_user
msgid "User: Own Templates"
msgstr "ユーザ: 自分のテンプレート"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__validity
#: model:ir.model.fields,field_description:sign.field_sign_send_request__validity
msgid "Valid Until"
msgstr "有効期限日"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_item_custom_popover.xml:0
msgid "Validate"
msgstr "検証"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/public_signer_dialog.xml:0
msgid "Validate & Send"
msgstr "検証と送信"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_request/edit_while_signing_signable_pdf_iframe.js:0
msgid "Validate & the next signatory is “%s”"
msgstr "検証 & 次の署名は “%s”"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign._doc_sign
msgid "Validate &amp; Send Completed Document"
msgstr "検証し、完了ドキュメントを送信する"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Validation Code"
msgstr "検証コード"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request_item__sign_item_value_ids
#: model:ir.model.fields,field_description:sign.field_sign_request_item_value__value
msgid "Value"
msgstr "数値"

#. module: sign
#: model:ir.model.constraint,message:sign.constraint_sign_item_option_value_uniq
msgid "Value already exists!"
msgstr "値は既に存在します！"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "Verify"
msgstr "確認"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "View Document"
msgstr "ドキュメントを見る"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
msgid "View document"
msgstr "ドキュメントを見る"

#. module: sign
#: model:ir.model.fields.selection,name:sign.selection__sign_log__action__open
msgid "View/Download"
msgstr "見る/ダウンロード"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "Viewed/downloaded by"
msgstr "閲覧 / ダウンロードした人"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Waiting for me"
msgstr "自分の署名待ち"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "Waiting for others"
msgstr "他社の署名待ち"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/sign_template/sign_template_action.js:0
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid "Warning"
msgstr "警告"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Waste"
msgstr "ゴミ"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
msgid "Water"
msgstr "水"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid ""
"We can only send reminders in the future - as soon as we find a way to send reminders in the past we'll notify you.\n"
"In the mean time, please make sure to input a positive number of days for the reminder interval."
msgstr ""
"未来にしかリマインドを送信することはできません。過去にリマインダを送信する方法が見つかり次第、お知らせいたします。\n"
"その間に、リマインダの間隔に正の日数を入力して下さい。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid "We couldn't find the signature request you're looking for!"
msgstr "お探しの署名依頼が見つかりませんでした！"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid ""
"We display to you a ratio based on the saved weight versus 1000 kg of paper "
"usage."
msgstr "1000kgの紙使用量に対する節約された重量に基づく比率を表示します。"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.xml:0
msgid "We will send you this document by email once everyone has signed."
msgstr "全員が署名した後、このドキュメントをEメールでお送りします。"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.js:0
msgid ""
"We'll send an email to warn other contacts in copy & signers with the reason"
" you provided."
msgstr "コピー＆署名の他の連絡先に、記入した理由とともに警告メールを送信します。"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__website_message_ids
msgid "Website Messages"
msgstr "ウェブサイトメッセージ"

#. module: sign
#: model:ir.model.fields,help:sign.field_sign_request__website_message_ids
msgid "Website communication history"
msgstr "ウェブサイト通信履歴"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/js/tours/sign.js:0
msgid ""
"Well done, your document is ready!<br>Let's send it to get our first "
"signature."
msgstr "素晴らしい。ドキュメントの用意ができました！<br>送信して、初めての署名を手に入れましょう。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "What about those conversions I see?"
msgstr "これらの表示されている変換はどうでしょう？"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sign_refusal_dialog.xml:0
msgid "Why do you refuse to sign this document?"
msgstr "なぜこのドキュメントに署名するのを拒否するのですか？"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_item__width
msgid "Width"
msgstr "幅"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "Wood"
msgstr "木材"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "Write email or search contact..."
msgstr "Eメールを登録もしくは連絡先を検索..."

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid "Wrong password"
msgstr "間違ったパスワード"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "XYZ123456"
msgstr "XYZ123456"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.deleted_sign_request
msgid ""
"You can contact the person who invited you to sign the document by email for"
" help."
msgstr "署名要求者にメールで連絡することができます。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You can only add new items for the current role"
msgstr "現在の役割にのみ新しい項目を追加できます。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_expired
msgid ""
"You can request a new link to access your document and sign it, it will  be "
"delivered in your inbox right away."
msgstr "ドキュメントにアクセスし、署名するための新しいリンクを依頼することができ、それはあなたの受信トレイにすぐに配信されます。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid ""
"You can't delete a template for which signature requests exist but you can "
"archive it instead."
msgstr "署名依頼が存在するテンプレートを削除することはできませんが、代わりにアーカイブすることはできます。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You cannot reassign this signatory"
msgstr "この署名者を再指定することはできません。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_template.py:0
msgid ""
"You cannot share this document by link, because it has fields to be filled "
"by different roles. Use Send button instead."
msgstr ""
"このドキュメントには異なる役割によって入力されるフィールドがあるため、リンクで共有することはできません。代わりに送信ボタンを使用して下さい。"

#. module: sign
#. odoo-python
#: code:addons/sign/controllers/main.py:0
msgid ""
"You do not have access to these documents, please contact a Sign "
"Administrator."
msgstr "これらのドキュメントにアクセスできない場合は、署名管理者に連絡して下さい。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_refused
msgid "You have refused the document"
msgstr "ドキュメントを拒否しました"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "You have until"
msgstr "以下まで"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You must specify one signer for each role of your sign template"
msgstr "署名テンプレートの各役割に1人の署名者を指定する必要があります。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You need to define a signatory"
msgstr "署名者を定義する必要があります。"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "You should select at least one document to download."
msgstr "ダウンロードするドキュメントを少なくとも1つ選択して下さい。"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.js:0
msgid "You will get the signed document by email."
msgstr "署名済ドキュメントが Eメールで届きます。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.canceled_sign_request_item
msgid "You won't receive any notification for this signature request anymore."
msgstr "この署名依頼に対する通知はもう届きません。"

#. module: sign
#: model_terms:ir.actions.act_window,help:sign.sign_all_request_action
#: model_terms:ir.actions.act_window,help:sign.sign_request_action
#: model_terms:ir.actions.act_window,help:sign.sign_template_action
msgid "You're one click away from automating your signature process!"
msgstr "ワンクリックで署名プロセスの自動化が可能です！"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_portal_my_request
msgid "Your Information"
msgstr "あなたの情報"

#. module: sign
#. odoo-python
#: code:addons/sign/models/sign_request.py:0
msgid "Your confirmation code is %s"
msgstr "確認コードは%s"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/public_signer_dialog.xml:0
msgid "Your email"
msgstr "Eメール"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/encrypted_dialog.xml:0
msgid ""
"Your file is encrypted, PDF's password is required to generate final "
"document. The final document will be encrypted with the same password."
msgstr "ファイルは暗号化され、最終ドキュメントを生成するためにPDFのパスワードが必要です。最終ドキュメントは同じパスワードで暗号化されます。"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/public_signer_dialog.xml:0
msgid "Your name"
msgstr "名前"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/next_direct_sign_dialog.xml:0
msgid "Your signature has been saved. Next signatory is"
msgstr "署名が保存されました。次の署名者は"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid ""
"Your signature was not submitted. Ensure the SMS validation code is correct."
msgstr "署名が送信されませんでした。SMS認証コードが正しいことを確認して下さい。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "and"
msgstr "と"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/backend_components/template_alert_dialog/template_alert_dialog.xml:0
msgid "and the process will continue"
msgstr "そしてプロセスは継続"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "and:"
msgstr "そして:"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid ""
"because you don't have enough credits for this operation.\n"
"                    The signatory was able to finish signing, but was not asked to authenticate fully."
msgstr ""
"この操作に必要なクレジットが不足しているためです。\n"
"                    署名者は署名を終えることができましたが、完全な認証は求められませんでした。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "can"
msgstr "缶分"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "cans"
msgstr "缶"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "click here to cancel it."
msgstr "ここをクリックして取消します。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "days."
msgstr "日数"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "e.g. +1 415 555 0100"
msgstr "例: +1 415 555 0100"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/sms_signer_dialog.xml:0
msgid "e.g. 314159"
msgstr "例: 314159"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_item_role_view_form
msgid "e.g. Employee"
msgstr "例: 従業員"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
msgid "e.g. Non-disclosure agreement"
msgstr "例: 秘密保持契約"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_form
#: model_terms:ir.ui.view,arch_db:sign.sign_send_request_view_form
msgid "every"
msgstr "全ての"

#. module: sign
#: model:sign.item.type,tip:sign.sign_item_type_checkbox
#: model:sign.item.type,tip:sign.sign_item_type_company
#: model:sign.item.type,tip:sign.sign_item_type_date
#: model:sign.item.type,tip:sign.sign_item_type_email
#: model:sign.item.type,tip:sign.sign_item_type_multiline_text
#: model:sign.item.type,tip:sign.sign_item_type_name
#: model:sign.item.type,tip:sign.sign_item_type_phone
#: model:sign.item.type,tip:sign.sign_item_type_radio
#: model:sign.item.type,tip:sign.sign_item_type_text
msgid "fill in"
msgstr "入力してください"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "has been completed and signed by"
msgstr "は完了し、以下によって署名済:"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "has been edited, completed and signed by"
msgstr "以下によって署名済:"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid "has been signed by"
msgstr "以下によって署名済:"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "hour of computer use"
msgstr "パソコン使用時間"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "hours of computer use"
msgstr "パソコン使用時間"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "https://c.environmentalpaper.org/"
msgstr "https://c.environmentalpaper.org/"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "ip"
msgstr "ip"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "is free, forever, with unlimited users - and it's fun to use!"
msgstr "無制限のユーザが無料で無期限で利用でき、しかも楽しく使えます!"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "kWh of energy saved"
msgstr "kWhのエネルギー削減"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "kg of reduced carbon emissions"
msgstr "kgのCO2排出削減"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "kg of waste prevented"
msgstr "kgのゴミ削減"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "kg of wood saved"
msgstr "kgの木材節約"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "liter of car fuel"
msgstr "リットルの自動車燃料"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "liters of car fuel"
msgstr "リットルの自動車燃料"

#. module: sign
#: model:sign.item.type,tip:sign.sign_item_type_initial
msgid "mark it"
msgstr "印をつける"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/components/sign_request/signable_PDF_iframe.js:0
msgid "next"
msgstr "次"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/dialogs/thank_you_dialog.xml:0
msgid "on"
msgstr "on"

#. module: sign
#. odoo-javascript
#: code:addons/sign/static/src/views/helper/sign_action_helper.xml:0
msgid "or"
msgstr "または"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "sheets of paper saved"
msgstr "枚の紙節約"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "shower"
msgstr "シャワー"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "showers"
msgstr "シャワー"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_tree
msgid "sign"
msgstr "サイン"

#. module: sign
#: model:sign.item.type,tip:sign.sign_item_type_signature
msgid "sign it"
msgstr "署名する"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message
msgid "sign.message"
msgstr "sign.message"

#. module: sign
#: model:ir.model.fields,field_description:sign.field_sign_request__message_cc
msgid "sign.message_cc"
msgstr "sign.message_cc"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_view_search
msgid "tags"
msgstr "タグ"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_el
#: model_terms:ir.ui.view,arch_db:sign.green_report_el_pdf
msgid "that's"
msgstr "それは"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_request
msgid "to sign the document."
msgstr "このドキュメントに署名するため"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_request_logs_user
msgid "to:"
msgstr "終了:"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "tree"
msgstr "木"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_container
#: model_terms:ir.ui.view,arch_db:sign.green_report_container_pdf
msgid "trees"
msgstr "木"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_savings_report
msgid "we've got a list of consumption for 1000 kg of paper usage."
msgstr "1000kgの紙の消費量リストがあります。"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_not_enough_credits
msgid "without the requested extra-authentification step ("
msgstr "要求された追加認証ステップなしに ("

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.sign_template_mail_completed
msgid "you"
msgstr "あなた"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_el
#: model_terms:ir.ui.view,arch_db:sign.green_report_el_pdf
msgid "{{green_report_el_title}}"
msgstr "{{green_report_el_title}}"

#. module: sign
#: model_terms:ir.ui.view,arch_db:sign.green_report_el
#: model_terms:ir.ui.view,arch_db:sign.green_report_el_pdf
msgid "{{green_report_el_title}} Summary"
msgstr "{{green_report_el_title}} 概要"
