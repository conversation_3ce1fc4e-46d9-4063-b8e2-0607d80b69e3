<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.SearchBar">
        <div class="pos-search-bar d-flex align-items-center flex-grow-1 rounded">
            <div class="search position-relative h-100 d-flex flex-grow-1">
                <div class="input-group">
                    <input class="form-control form-control-lg bg-view border-light ps-2 ps-lg-5" t-ref="autofocus"
                    t-model="state.searchInput" t-on-keydown="onSearchInputKeydown" t-on-keyup="onSearchInputKeyup" type="text" t-att-placeholder="props.placeholder" />
                    <div t-if="state.showSearchFields and state.searchInput" class="fields">
                        <ul class="py-1 bg-view small">
                            <t t-foreach="searchFieldsList" t-as="fieldName" t-key="fieldName">
                                <li class="ps-5 py-1 text-start"
                                    t-att-class="{ highlight: fieldName == searchFieldsList[state.selectedSearchFieldId] }"
                                    t-on-click="() => this._onClickSearchField(fieldName)">
                                    <span class="field">
                                        <t t-esc="props.config.searchFields.get(fieldName)"></t>
                                    </span>
                                    <span>: </span>
                                    <span class="term text-primary fw-bolder">
                                        <t t-esc="state.searchInput"></t>
                                    </span>
                                </li>
                            </t>
                        </ul>
                    </div>
                    <div t-if="props.config.filter.show"
                        class="filter btn btn-secondary d-flex align-items-center dropdown-toggle"
                        t-on-click.stop="() => { state.showFilterOptions = !state.showFilterOptions }">
                        <span>
                            <t t-esc="props.config.filter.options.get(state.selectedFilter).text" />
                        </span>
                        <ul t-if="state.showFilterOptions" class="options dropdown-menu dropdown-menu-end py-1">
                            <t t-foreach="filterOptionsList" t-as="key" t-key="key">
                                <t t-set="_option" t-value="props.config.filter.options.get(key)" />
                                <li class="dropdown-item" t-on-click="() => this._onSelectFilter(key)" t-att-class="{ 'indented': _option.indented }">
                                    <t t-esc="_option.text"></t>
                                </li>
                            </t>
                        </ul>
                    </div>
                </div>
                <span class="search-icon position-absolute start-0 top-50 translate-middle ms-4 z-3" t-if="!ui.isSmall"><i class="oi oi-search"></i></span>
            </div>
        </div>
    </t>

</templates>
