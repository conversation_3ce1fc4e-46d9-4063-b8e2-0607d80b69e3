<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record id="product_template_form_view" model="ir.ui.view">
            <field name="name">product.template.form.inherit.email.template</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//page[@name='invoicing']//group[@name='accounting']" position="inside">
                    <group name="email_template" string="Automatic Email at Invoice">
                        <field name="email_template_id" string="Email Template" help="Send a product-specific email once the invoice is validated"
                            domain="[('model','=','account.move')]"
                            context="{
                                'form_view_ref':'product_email_template.email_template_form_simplified',
                                'default_model': 'account.move',
                                'default_subject': name,
                                'default_name': name,
                            }"/>
                    </group>
                </xpath>
                <page name="invoicing" position="attributes">
                    <attribute name="groups">account.group_account_invoice,account.group_account_readonly</attribute>
                </page>
            </field>
        </record>
</odoo>
