# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:03+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Man<PERSON>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: delivery
#. odoo-python
#: code:addons/delivery/wizard/choose_delivery_carrier.py:0
msgid "%(carrier)s Error"
msgstr "%(carrier)s foutmelding"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/sale_order.py:0
msgid ""
"%s\n"
"Free Shipping"
msgstr ""
"%s\n"
"Gratis verzending"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
msgid "%s (copy)"
msgstr "%s (kopie)"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "<i class=\"oi oi-arrow-right me-1\"/>Get rate"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>Vraag tarief op"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
msgid ""
"<p class=\"o_view_nocontent\">\n"
"                    Buy Odoo Enterprise now to get more providers.\n"
"                </p>"
msgstr ""
"<p class=\"o_view_nocontent\">\n"
"Koop Odoo Enterprise om meer providers te krijgen\n"
"</p>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"<span class=\"o_stat_text o_warning_text fw-bold\">Test</span>\n"
"                                <span class=\"o_stat_text\">Environment</span>"
msgstr ""
"<span class=\"o_stat_text o_warning_text fw-bold\">Test</span>\n"
"                                <span class=\"o_stat_text\">Omgeving</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "<span class=\"o_stat_text text-danger\">No debug</span>"
msgstr "<span class=\"o_stat_text text-danger\">Geen debug</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "<span class=\"text-success\">Debug requests</span>"
msgstr "<span class=\"text-success\">Debug aanvragen</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"<span class=\"text-success\">Production</span>\n"
"                                <span class=\"o_stat_text\">Environment</span>"
msgstr ""
"<span class=\"text-success\">Productie</span>\n"
"                                <span class=\"o_stat_text\">Omgeving</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.delivery_report_saleorder_document
msgid "<strong>Shipping Description</strong>"
msgstr "<strong>Beschrijving van verzending</strong>"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__carrier_description
msgid ""
"A description of the delivery method that you want to communicate to your "
"customers on the Sales Order and sales confirmation email.E.g. instructions "
"for customers to follow."
msgstr ""
"Een beschrijving van de leveringsmethode die je aan je klanten wilt meedelen"
" op de verkooporder en de e-mail met de verkoopbevestiging. Bijv. "
"instructies die klanten moeten volgen."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__integration_level
msgid "Action while validating Delivery Orders"
msgstr "Actie bij het bevestigen van leveringen"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__active
msgid "Active"
msgstr "Actief"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "Add"
msgstr "Toevoegen"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/sale_order.py:0
#: code:addons/delivery/wizard/choose_delivery_carrier.py:0
msgid "Add a shipping method"
msgstr "Voeg een verzendwijze toe"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_order_form_with_carrier
msgid "Add shipping"
msgstr "Verzending toevoegen"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Additional margin"
msgstr "Extra marge"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__amount
msgid "Amount"
msgstr "Bedrag"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__amount
msgid ""
"Amount of the order to benefit from a free shipping, expressed in the "
"company currency"
msgstr ""
"Bedrag van de order wat kan profiteren van een gratis verzending, uitgedrukt"
" in het bedrijfsvaluta"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Archived"
msgstr "Gearchiveerd"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Availability"
msgstr "Beschikbaarheid"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__available_carrier_ids
msgid "Available Carriers"
msgstr "Beschikbare vervoerders"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__delivery_type__base_on_rule
msgid "Based on Rules"
msgstr "Gebaseerd op regels"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__can_generate_return
msgid "Can Generate Return"
msgstr "Kan retour genereren"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__carrier_id
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_tree
msgid "Carrier"
msgstr "Vervoerder"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
msgid ""
"Carrier %s cannot have the same tag in both Must Have Tags and Excluded "
"Tags."
msgstr ""
"De vervoerder %s kan niet hetzelfde label in zowel verplichte labels als "
"uitgesloten labels hebben."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__carrier_description
msgid "Carrier Description"
msgstr "Vervoerdersomschrijving"

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "Choose a pick-up point"
msgstr "Kies een ophaalpunt"

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
#: code:addons/delivery/static/src/js/location_selector/map_container/map_container.js:0
msgid "Choose this location"
msgstr "Deze locatie kiezen"

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_schedule/location_schedule.js:0
msgid "Closed"
msgstr "Gesloten"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__company_id
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__company_id
msgid "Company"
msgstr "Bedrijf"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
msgid "Condition"
msgstr "Voorwaarde"

#. module: delivery
#: model:ir.model,name:delivery.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Content"
msgstr "Inhoud"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__display_price
msgid "Cost"
msgstr "Kosten"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__country_ids
msgid "Countries"
msgstr "Landen"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__currency_id
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__currency_id
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__partner_id
msgid "Customer"
msgstr "Klant"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__debug_logging
msgid "Debug logging"
msgstr "Fouten log"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_res_partner__property_delivery_carrier_id
#: model:ir.model.fields,help:delivery.field_res_users__property_delivery_carrier_id
msgid "Default delivery method used in sales orders."
msgstr "Standaard verzendwijze gebruikt in verkooporders."

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid "Define a new delivery method"
msgstr "Definieer een nieuwe verzendwijze"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Delivery Carrier"
msgstr "Vervoerder"

#. module: delivery
#: model:ir.model,name:delivery.model_choose_delivery_carrier
msgid "Delivery Carrier Selection Wizard"
msgstr "Vervoerder selectie wizard"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
msgid "Delivery Cost"
msgstr "Verzendkosten"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__delivery_message
#: model:ir.model.fields,field_description:delivery.field_sale_order__delivery_message
msgid "Delivery Message"
msgstr "Afleverbericht"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__name
#: model:ir.model.fields,field_description:delivery.field_res_partner__property_delivery_carrier_id
#: model:ir.model.fields,field_description:delivery.field_res_users__property_delivery_carrier_id
#: model:ir.model.fields,field_description:delivery.field_sale_order__carrier_id
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Delivery Method"
msgstr "Verzendwijze"

#. module: delivery
#: model:ir.actions.act_window,name:delivery.action_delivery_carrier_form
#: model:ir.ui.menu,name:delivery.sale_menu_action_delivery_carrier_form
msgid "Delivery Methods"
msgstr "Verzendmethoden"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__delivery_price
msgid "Delivery Price"
msgstr "Leveringsprijs"

#. module: delivery
#: model:ir.model,name:delivery.model_delivery_price_rule
msgid "Delivery Price Rules"
msgstr "Leveringsprijsregels"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__product_id
msgid "Delivery Product"
msgstr "Verzendwijze product"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__delivery_set
msgid "Delivery Set"
msgstr "Levering set"

#. module: delivery
#: model:ir.model,name:delivery.model_delivery_zip_prefix
msgid "Delivery Zip Prefix"
msgstr "Aflever postcode prefix"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__recompute_delivery_price
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__recompute_delivery_price
msgid "Delivery cost should be recomputed"
msgstr "Leveringskosten moeten opnieuw berekend worden"

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_zip_prefix_list
msgid ""
"Delivery zip prefixes are assigned to delivery carriers to restrict\n"
"            which zips it is available to."
msgstr ""
"Postcodes voor bezorging worden toegewezen aan vervoerders om te beperken\n"
"voor welke postcodes ze beschikbaar zijn."

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Description"
msgstr "Omschrijving"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Destination"
msgstr "Bestemming"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__sequence
msgid "Determine the display order"
msgstr "Bepaal de weergave volgorde"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "Discard"
msgstr "Negeren"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid ""
"Each carrier (e.g. UPS) can have several delivery methods (e.g.\n"
"            UPS Express, UPS Standard) with a set of pricing rules attached\n"
"            to each method."
msgstr ""
"Elke vervoerder (bijv. UPS) kan verschillende verzendmethoden hebben (bijv.\n"
"            UPS Express, UPS Standard) telkens gekoppeld aan een aantal regels\n"
"met prijsformules."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__prod_environment
msgid "Environment"
msgstr "Omgeving"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
msgid "Error: this delivery method is not available for this address."
msgstr "Fout: Deze verzendwijze is niet beschikbaar voor dit adres."

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
msgid "Error: this delivery method is not available."
msgstr "Fout: deze verzendwijze is niet beschikbaar."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__invoice_policy
msgid ""
"Estimated Cost: the customer will be invoiced the estimated cost of the shipping.\n"
"Real Cost: the customer will be invoiced the real cost of the shipping, the cost of theshipping will be updated on the SO after the delivery."
msgstr ""
"Geschatte kosten: de geschatte kosten van de levering zullen aan de klant gefactureerd worden. \n"
"Werkelijke kosten: de werkelijke kosten van de levering zullen aan de klant gefactureerd worden, de verzendkosten worden bijgewerkt op het verkooporder na de levering."

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__invoice_policy__estimated
msgid "Estimated cost"
msgstr "Geschatte kosten"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__excluded_tag_ids
msgid "Excluded Tags"
msgstr "Uitgesloten labels"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_sale_order__carrier_id
msgid "Fill this field if you plan to invoice the shipping based on picking."
msgstr ""
"Vul dit veld in als je transportkosten wilt factureren op basis van een "
"verzamelopdracht."

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"Filling this form allows you to make the shipping method available according"
" to the content of the order or its destination."
msgstr ""
"Vul dit formulier in om de verzendmethode aan te passen naargelang de inhoud"
" van de order of de bestemming."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__fixed_margin
msgid "Fixed Margin"
msgstr "Vaste marge"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__fixed_price
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__delivery_type__fixed
msgid "Fixed Price"
msgstr "Vaste prijs"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__free_over
msgid "Free if order amount is above"
msgstr "Gratis indien het order bedrag meer is dan"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__return_label_on_delivery
msgid "Generate Return Label"
msgstr "Genereer retourlabel"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__integration_level__rate
msgid "Get Rate"
msgstr "Koers ophalen"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__integration_level__rate_and_ship
msgid "Get Rate and Create Shipment"
msgstr "Zoek tarief een maak verzending"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Group By"
msgstr "Groeperen op"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__id
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__id
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__id
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__id
msgid "ID"
msgstr "ID"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__free_over
msgid ""
"If the order total amount (shipping excluded) is above or equal to this "
"value, the customer benefits from a free shipping"
msgstr ""
"Als het totaal bedrag van de order (excl. verzending) groter of gelijk is "
"aan deze waarde, kan de klant profiteren van gratis verzending"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__max_volume
msgid ""
"If the total volume of the order is over this volume, the method won't be "
"available."
msgstr ""
"De methode is niet beschikbaar als het totale volume van de order groter is "
"dan dit volume."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__max_weight
msgid ""
"If the total weight of the order is over this weight, the method won't be "
"available."
msgstr ""
"De methode is niet beschikbaar als het totale gewicht van de order groter is"
" dan dit gewicht."

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Install more Providers"
msgstr "Installeer meer aanbieders"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__shipping_insurance
msgid "Insurance Percentage"
msgstr "Verzekeringspercentage"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__integration_level
msgid "Integration Level"
msgstr "Integratie niveau"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__invoicing_message
msgid "Invoicing Message"
msgstr "Facturatie bericht"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__invoice_policy
msgid "Invoicing Policy"
msgstr "Facturatiebeleid"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__is_delivery
msgid "Is a Delivery"
msgstr "Is een levering"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "List view"
msgstr "Lijstweergave"

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "Loading..."
msgstr "Laden..."

#. module: delivery
#: model:delivery.carrier,name:delivery.delivery_local_delivery
msgid "Local Delivery"
msgstr "Lokale bezorging"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__debug_logging
msgid "Log requests in order to ease debugging"
msgstr "Log aanvragen om gemakkelijker fouten te kunnen traceren"

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_zip_prefix_list
msgid "Manage delivery zip prefixes"
msgstr "Beheer aflever postcode prefixes"

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "Map view"
msgstr "Kaartweergave"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__margin
msgid "Margin"
msgstr "Marge"

#. module: delivery
#: model:ir.model.constraint,message:delivery.constraint_delivery_carrier_margin_not_under_100_percent
msgid "Margin cannot be lower than -100%"
msgstr "Marge kan niet minder zijn dan -100%"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Margin on Rate"
msgstr "Marge op tarief"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__max_volume
msgid "Max Volume"
msgstr "Max Volume"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__max_weight
msgid "Max Weight"
msgstr "Max. gewicht"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__max_value
msgid "Maximum Value"
msgstr "Maximumwaarde"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__must_have_tag_ids
msgid "Must Have Tags"
msgstr "Verplichte labels"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__name
msgid "Name"
msgstr "Naam"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
msgid "New Providers"
msgstr "Nieuwe providers"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/sale_order.py:0
msgid "No pick-up points are available for this delivery address."
msgstr "Er zijn geen afhaalpunten beschikbaar voor dit afleveradres."

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "No result"
msgstr "Geen resultaat"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
msgid "Not available for current order"
msgstr "Niet beschikbaar voor huidige order"

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location/location.js:0
#: code:addons/delivery/static/src/js/location_selector/map_container/map_container.js:0
msgid "Opening hours"
msgstr "Openingsuren"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__operator
msgid "Operator"
msgstr "Operator"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__order_id
msgid "Order"
msgstr "Order"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__pickup_location_data
msgid "Pickup Location Data"
msgstr "Gegevens afhaallocatie"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Please select a country before choosing a state or a zip prefix."
msgstr ""
"Selecteer een land voordat je een staat/provincie of een postcode kiest."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__name
msgid "Prefix"
msgstr "Voorvoegsel"

#. module: delivery
#: model:ir.model.constraint,message:delivery.constraint_delivery_zip_prefix_name_uniq
msgid "Prefix already exists!"
msgstr "Prefix bestaat al!"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__zip_prefix_ids
msgid ""
"Prefixes of zip codes that this carrier applies to. Note that regular "
"expressions can be used to support countries with varying zip code lengths, "
"i.e. '$' can be added to end of prefix to match the exact zip (e.g. '100$' "
"will only match '100' and not '1000')"
msgstr ""
"Voorvoegsels van postcodes waarop deze vervoerder van toepassing is. Merk op"
" dat reguliere expressies kunnen worden gebruikt om landen met verschillende"
" postcodelengtes te ondersteunen, d.w.z. '$' kan worden toegevoegd aan het "
"einde van een voorvoegsel om overeen te komen met de exacte postcode (bijv. "
"'100$' zal alleen overeenkomen met '100' en niet met '1000')"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__price
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__price
msgid "Price"
msgstr "Prijs"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_tree
msgid "Price Rules"
msgstr "Prijsregels"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Pricing"
msgstr "Prijzen"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__price_rule_ids
msgid "Pricing Rules"
msgstr "Prijsregels"

#. module: delivery
#: model:ir.model,name:delivery.model_product_category
msgid "Product Category"
msgstr "Productcategorie"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__product_qty
msgid "Product Qty"
msgstr "Producthoeveelheid"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__delivery_type
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__delivery_type
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Provider"
msgstr "Provider"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__quantity
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__quantity
msgid "Quantity"
msgstr "Aantal"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__get_return_label_from_portal
msgid "Return Label Accessible from Customer Portal"
msgstr "Retourlabel toegankelijk via klantenportaal"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__list_base_price
msgid "Sale Base Price"
msgstr "Basis verkoopprijs"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__list_price
msgid "Sale Price"
msgstr "Verkoopprijs"

#. module: delivery
#: model:ir.model,name:delivery.model_sale_order
msgid "Sales Order"
msgstr "Verkooporder"

#. module: delivery
#: model:ir.model,name:delivery.model_sale_order_line
msgid "Sales Order Line"
msgstr "Verkooporderregel"

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.xml:0
msgid "Search"
msgstr "Zoeken"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__sequence
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__sequence
msgid "Sequence"
msgstr "Reeks"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__is_all_service
msgid "Service Product"
msgstr "Dienst product"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__prod_environment
msgid "Set to True if your credentials are certified for production."
msgstr ""
"Zet op Waar wanneer je inloggegevens gecertificeerd zijn voor productie."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__carrier_id
msgid "Shipping Method"
msgstr "Verzendwijze"

#. module: delivery
#: model:ir.model,name:delivery.model_delivery_carrier
#: model_terms:ir.ui.view,arch_db:delivery.res_config_settings_view_form
msgid "Shipping Methods"
msgstr "Verzendmethoden"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__shipping_weight
msgid "Shipping Weight"
msgstr "Aflevergewicht"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__shipping_insurance
msgid ""
"Shipping insurance is a service which may reimburse senders whose parcels "
"are lost, stolen, and/or damaged in transit."
msgstr ""
"Verzendverzekering is een service die afzenders kan vergoeden wiens "
"pakketten verloren, gestolen en/of beschadigd zijn tijdens het transport."

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"Shipping method details to be included at bottom sales orders and their "
"confirmation emails. E.g. Instructions for customers to follow."
msgstr ""
"Details van de verzendmethode die moeten worden opgenomen aan de onderkant "
"van de verkooporders en de bevestigingsmails. Bijv. Instructies voor klanten"
" om te volgen."

#. module: delivery
#: model:delivery.carrier,name:delivery.free_delivery_carrier
#: model:product.template,name:delivery.product_product_delivery_product_template
msgid "Standard delivery"
msgstr "Standaardlevering"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__state_ids
msgid "States"
msgstr "Staten / Provincies"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__supports_shipping_insurance
msgid "Supports Shipping Insurance"
msgstr "Ondersteunt verzending verzekering"

#. module: delivery
#: model:delivery.carrier,name:delivery.delivery_carrier
#: model:product.template,name:delivery.product_product_delivery_poste_product_template
msgid "The Poste"
msgstr "De post"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__excluded_tag_ids
msgid ""
"The method is NOT available if at least one product of the order has one of "
"these tags."
msgstr ""
"De methode is NIET beschikbaar als er minstens een product van de order over"
" een van deze labels beschikt."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__must_have_tag_ids
msgid ""
"The method is available only if at least one product of the order has one of"
" these tags."
msgstr ""
"De methode is alleen beschikbaar als er minstens een product van de order "
"over een van deze labels beschikt."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__get_return_label_from_portal
msgid ""
"The return label can be downloaded by the customer from the customer portal."
msgstr ""
"Het retourlabel kan door de klant worden gedownload via het klantenportaal."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__return_label_on_delivery
msgid "The return label is automatically generated at the delivery."
msgstr "Het retourlabel is automatisch gegenereerd bij de levering."

#. module: delivery
#: model:ir.model.constraint,message:delivery.constraint_delivery_carrier_shipping_insurance_is_percentage
msgid "The shipping insurance must be a percentage between 0 and 100."
msgstr "De verzendverzekering moet een percentage zijn tussen 0 en 100."

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
msgid "The shipping is free since the order amount exceeds %.2f."
msgstr "De verzendkosten zijn gratis omdat je orderbedrag hoger is dan %.2f."

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/map_container/map_container.js:0
msgid "There was an error loading the map"
msgstr "Er is een fout opgetreden bij het laden van de kaart"

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid ""
"These methods allow to automatically compute the delivery price\n"
"            according to your settings; on the sales order (based on the\n"
"            quotation) or the invoice (based on the delivery orders)."
msgstr ""
"Deze methodes geven je de mogelijkheid om automatisch de leverprijs\n"
"te berekenen volgens je instellingen; op het verkooporder (gebaseerd \n"
"op de offerte) of op de factuur (gebaseerd op de leveringsbonnen)."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__fixed_margin
msgid "This fixed amount will be added to the shipping price."
msgstr "Dit vaste bedrag wordt toegevoegd aan de verzendkosten."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__tracking_url
msgid ""
"This option adds a link for the customer in the portal to track their "
"package easily. Use <shipmenttrackingnumber> as a placeholder in your URL."
msgstr ""
"Deze optie voegt een link toe waarmee de klant zijn pakket gemakkelijk kan "
"volgen in het portaal. Gebruik <shipmenttrackingnumber> als plaatshouder in "
"je URL."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__margin
msgid "This percentage will be added to the shipping price."
msgstr "Dit percentage wordt toegevoegd aan de afleverprijs."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__total_weight
msgid "Total Order Weight"
msgstr "Totaal gewicht van de order"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__tracking_url
msgid "Tracking Link"
msgstr "Traceerlink"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "Update"
msgstr "Bijwerken"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/sale_order.py:0
#: model_terms:ir.ui.view,arch_db:delivery.view_order_form_with_carrier
msgid "Update shipping cost"
msgstr "Verzendkosten bijwerken"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__variable
msgid "Variable"
msgstr "Variabel"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__variable_factor
msgid "Variable Factor"
msgstr "Variabele Factor"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__volume
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__volume
msgid "Volume"
msgstr "Volume"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__volume_uom_name
msgid "Volume unit of measure label"
msgstr "Volume maateenheidslabel"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__weight
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__weight
msgid "Weight"
msgstr "Gewicht"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__wv
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__wv
msgid "Weight * Volume"
msgstr "Gewicht * Volume"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__weight_uom_name
msgid "Weight Uom Name"
msgstr "Naam maateenheid gewicht"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Gewicht maateenheid label"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/sale_order.py:0
msgid ""
"You can not update the shipping costs on an order where it was already invoiced!\n"
"\n"
"The following delivery lines (product, invoiced quantity and price) have already been processed:\n"
"\n"
msgstr ""
"Je kunt de verzendkosten niet bijwerken op een order waar deze al is gefactureerd!\n"
"\n"
"De volgende leveringsregels (product, gefactureerde aantal en prijs) zijn al verwerkt:\n"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/product_category.py:0
msgid ""
"You cannot delete the deliveries product category as it is used on the "
"delivery carriers products."
msgstr ""
"Je kunt de productcategorie leveringen niet verwijderen, aangezien deze "
"wordt gebruikt voor de producten van de vervoerders."

#. module: delivery
#. odoo-javascript
#: code:addons/delivery/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "Your postal code"
msgstr "Je postcode"

#. module: delivery
#: model:ir.actions.act_window,name:delivery.action_delivery_zip_prefix_list
msgid "Zip Prefix"
msgstr "Postcode prefix"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__zip_prefix_ids
msgid "Zip Prefixes"
msgstr "Postcode prefixen"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "e.g. UPS Express"
msgstr "Bijv. UPS Express"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "i.e. https://ekartlogistics.com/shipmenttrack/<shipmenttrackingnumber>"
msgstr ""
"bijv. https://ekartlogistics.com/shipmenttrack/<shipmenttrackingnumber>"
