<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="point_of_sale.MoneyDetailsPopup">
        <Dialog title.translate="Coins/Notes" size="'md'" >
            <t t-set="bills" t-value="Object.keys(state.moneyDetails).sort((a, b) => b - a)"/>
            <div t-attf-style="display: grid; grid-template-rows: repeat(calc({{bills.length}}/{{ui.isSmall ? 1 : 2}}), auto); grid-auto-flow: column;">
                <div t-foreach="bills" t-as="moneyValue" t-key="moneyValue" class="d-flex align-items-center justify-content-center my-1 ">
                    <NumericInput class="'w-50'" tModel="[state.moneyDetails, moneyValue]" min="0"/>
                    <label class="oe_link_icon w-25 text-center" t-att-for="moneyValue">
                        <span class="ms-1" t-esc="env.utils.formatCurrency(_parseFloat(moneyValue))"/>
                    </label>
                </div>
            </div>
            <t t-set-slot="footer" >
                <div class="button highlight btn btn-lg btn-primary" t-on-click="confirm">Confirm</div>
                <h4 class="total-section ms-auto py-2">
                    Total <t t-esc="env.utils.formatCurrency(computeTotal())"/>
                </h4>
            </t>
        </Dialog>
    </t>
</templates>
