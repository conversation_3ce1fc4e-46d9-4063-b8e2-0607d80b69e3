# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_barcodelookup
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "API Key:"
msgstr "Clave API:"

#. module: product_barcodelookup
#: model:ir.model.fields,field_description:product_barcodelookup.field_res_config_settings__barcodelookup_api_key
msgid "API key"
msgstr "Clave API"

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "Barcode Database"
msgstr "Base de datos de código de barras"

#. module: product_barcodelookup
#: model:ir.model.fields,help:product_barcodelookup.field_res_config_settings__barcodelookup_api_key
msgid "Barcode Lookup API Key for create product from barcode."
msgstr ""
"Clave API para búsqueda de código de barras para crear productos a partir de"
" códigos de barras."

#. module: product_barcodelookup
#: model:ir.model,name:product_barcodelookup.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "Create products by scanning using"
msgstr "Cree productos escaneando con"

#. module: product_barcodelookup
#: model:ir.model,name:product_barcodelookup.model_product_template
msgid "Product"
msgstr "Producto"

#. module: product_barcodelookup
#: model:ir.model,name:product_barcodelookup.model_product_product
msgid "Product Variant"
msgstr "Variante de producto"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_8
msgid "age group"
msgstr "clase de edad"

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "barcodelookup.com"
msgstr "barcodelookup.com"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_6
msgid "brand"
msgstr "marca"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_1
msgid "color"
msgstr "color"

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "e.g. d7vctmiv2rwgenebha8bxq7irooudn"
msgstr "p. ej. d7vctmiv2rwgenebha8bxq7irooudn"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_2
msgid "gender"
msgstr "género"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_5
msgid "manufacturer"
msgstr "fabricante"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_3
msgid "material"
msgstr "material"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_4
msgid "pattern"
msgstr "patrón"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_7
msgid "size"
msgstr "tamaño"
