<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="web.SearchableSetting" t-inherit="web.Setting" t-inherit-mode="primary">
        <div t-att-class="classNames" position="attributes">
            <attribute name="t-ref">setting</attribute>
            <attribute name="t-if">visible()</attribute>
        </div>
        <span class="o_form_label" position="replace">
            <span class="o_form_label"><HighlightText originalText="labelString"/></span>
        </span>
        <div class="text-muted" position="replace">
            <div class="text-muted"><HighlightText originalText="props.help"/></div>
        </div>
    </t>
</templates>
