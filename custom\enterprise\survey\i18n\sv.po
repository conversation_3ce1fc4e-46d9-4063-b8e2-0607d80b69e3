# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* survey
# 
# Translators:
# <PERSON><PERSON><PERSON> <mika<PERSON>.<PERSON><PERSON>@vertel.se>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON> <zy<PERSON><PERSON><EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON>ida <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> (sile), 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# Lasse L, 2024
# <PERSON><PERSON><PERSON> <mikael.a<PERSON><PERSON>@mariaakerberg.com>, 2024
# <PERSON> <<EMAIL>>, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-07 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_count
msgid "# Questions"
msgstr "# Frågor"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__random_questions_count
msgid "# Questions Randomly Picked"
msgstr "# Frågor slumpmässigt utvalda"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_4
msgid "$100"
msgstr "$100"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_1
msgid "$20"
msgstr "$20"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_5
msgid "$200"
msgstr "$200"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_6
msgid "$300"
msgstr "$300"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_2
msgid "$50"
msgstr "$50"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_3
msgid "$80"
msgstr "$80"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "% completed"
msgstr "% slutförd"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "%(participant)s just participated in \"%(survey_title)s\"."
msgstr "%(participant)s har precis deltagit i \"%(survey_title)s\"."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "%s (copy)"
msgstr "%s (kopia)"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid "%s Votes"
msgstr "%s Röster"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "%s certification passed"
msgstr "%s av certifieringen avklarad"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "%s challenge certification"
msgstr "%s certifiering av utmaning"

#. module: survey
#: model:ir.actions.report,print_report_name:survey.certification_report
msgid "'Certification - %s' % (object.survey_id.display_name)"
msgstr "\"Certifiering - %s\" % (object.survey_id.display_name)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "0000000010"
msgstr "0000000010"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug2
msgid "10 kg"
msgstr "10 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug2
msgid "100 years"
msgstr "100 år"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug3
msgid "1055"
msgstr "1055"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug3
msgid "116 years"
msgstr "116 år"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug1
msgid "1227"
msgstr "1227"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug4
msgid "127 years"
msgstr "127 år"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug2
msgid "1324"
msgstr "1324"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug1
msgid "1450 km"
msgstr "1450 km"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug3
msgid "16.2 kg"
msgstr "16,2 kg"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "2023-08-18"
msgstr "2023-08-18"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug2
msgid "3700 km"
msgstr "3700 km"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "403: Forbidden"
msgstr "403: Förbjuden"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug4
msgid "47 kg"
msgstr "47 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug1
msgid "5.7 kg"
msgstr "5.7 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug3
msgid "6650 km"
msgstr "6650 km"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug1
msgid "99 years"
msgstr "99 år"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid ""
"<b>Certificate</b>\n"
"                            <br/>"
msgstr ""
"<b>Certifikat</b>\n"
"                            <br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">Completed</span>"
msgstr ""
"<br/>\n"
"                                    <span class=\"text-muted\">Färdigställd</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid ""
"<br/>\n"
"                                    <span class=\"text-muted\">Registered</span>"
msgstr ""
"<br/>\n"
"                                    <span class=\"text-muted\">Registrerad</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "<br/> <span>by</span>"
msgstr "<br/> <span>av</span>"

#. module: survey
#: model:mail.template,body_html:survey.mail_template_certification
msgid ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"    <table style=\"width:600px;margin:5px auto;\">\n"
"        <tbody>\n"
"            <tr><td>\n"
"                <!-- We use the logo of the company that created the survey (to handle multi company cases) -->\n"
"                <a href=\"/\"><img t-if=\"not object.survey_id.create_uid.company_id.uses_default_logo\" t-attf-src=\"/logo.png?company={{ object.survey_id.create_uid.company_id.id }}\" style=\"vertical-align:baseline;max-width:100px;\"/></a>\n"
"            </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                    Certification: <t t-out=\"object.survey_id.display_name or ''\">Feedback Form</t>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"    <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"        <tbody>\n"
"            <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p>Dear <span t-out=\"object.partner_id.name or 'participant'\">participant</span></p>\n"
"                <p>\n"
"                    Please find attached your\n"
"                        <strong t-out=\"object.survey_id.display_name or ''\">Furniture Creation</strong>\n"
"                    certification\n"
"                </p>\n"
"                <p>Congratulations for passing the test with a score of <strong t-out=\"object.scoring_percentage\"/>%!</p>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"    <table style=\"width:600px;margin:5px auto;\">\n"
"        <tbody>\n"
"            <tr><td>\n"
"                <!-- Vi använder logotypen för företaget som skapade undersökningen (för att hantera fler företagsärenden) --> \n"
"                <a href=\"/\"><img t-if=\"not object.survey_id.create_uid.company_id.uses_default_logo\" t-attf-src=\"/logo.png?company={{ object.survey_id.create_uid.company_id.id }}\" style=\"vertical-align:baseline;max-width:100px;\"/></a>\n"
"            </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                    Certifiering: <t t-out=\"object.survey_id.display_name or ''\">Feedback-formulär </t>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"    <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"        <tbody>\n"
"            <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p>Bästa <span t-out=\"object.partner_id.name or 'participant'\">deltagare </span></p>\n"
"                <p>\n"
"                    Se bifogat din \n"
"                        <strong t-out=\"object.survey_id.display_name or ''\">Möbelskapande</strong>\n"
"                    certifiering\n"
"                </p>\n"
"                <p> Grattis till att du klarat provet med poängen med  <strong t-out=\"object.scoring_percentage\"/>%!</p>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"</div>\n"
"            "

#. module: survey
#: model:mail.template,body_html:survey.mail_template_user_input_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or 'participant'\">participant</t><br/><br/>\n"
"        <t t-if=\"object.survey_id.certification\">\n"
"            You have been invited to take a new certification.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            We are conducting a survey and your response would be appreciated.\n"
"        </t>\n"
"        </p><div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.get_start_url())\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                <t t-if=\"object.survey_id.certification\">\n"
"                    Start Certification\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    Start Survey\n"
"                </t>\n"
"            </a>\n"
"        </div>\n"
"        <t t-if=\"object.deadline\">\n"
"            Please answer the survey for <t t-out=\"format_date(object.deadline) or ''\">05/05/2021</t>.<br/><br/>\n"
"        </t>\n"
"        <t t-if=\"object.survey_id.certification\">\n"
"            We wish you good luck!\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            Thank you in advance for your participation.\n"
"        </t>\n"
"    \n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Bästa <t t-out=\"object.partner_id.name or 'participant'\">deltagare</t><br/><br/>\n"
"        <t t-if=\"object.survey_id.certification\">\n"
"            Du har blivit inbjuden att ta en ny certifiering. \n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            Vi genomför en undersökning och ditt svar skulle uppskattas. \n"
"        </t>\n"
"        </p><div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.get_start_url())\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                <t t-if=\"object.survey_id.certification\">\n"
"                    Starta certifiering \n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    Starta undersökning \n"
"                </t>\n"
"            </a>\n"
"        </div>\n"
"        <t t-if=\"object.deadline\">\n"
"            Vänligen svara på enkäten för <t t-out=\"format_date(object.deadline) or ''\">05/05/2021</t>.<br/><br/>\n"
"        </t>\n"
"        <t t-if=\"object.survey_id.certification\">\n"
"            Vi önskar dig lycka till!\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            Tack på förhand för ditt deltagande.\n"
"        </t>\n"
"    \n"
"</div>\n"
"            "

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<i class=\"fa fa-bar-chart\"/> Results"
msgstr "<i class=\"fa fa-bar-chart\"/> Resultat"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-check-square-o fa-lg me-2\"/>answer"
msgstr "<i class=\"fa fa-check-square-o fa-lg me-2\"/>svar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-circle-o  fa-lg me-2\"/>answer"
msgstr "<i class=\"fa fa-circle-o  fa-lg me-2\"/>svar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-circle-o fa-lg\" role=\"img\" aria-label=\"Not checked\" "
"title=\"Not checked\"/>"
msgstr ""
"<i class=\"fa fa-circle-o fa-lg\" role=\"img\" aria-label=\"Inte "
"kontrollerad\" title=\"Inte kontrollerad\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<i class=\"fa fa-close\"/> Close"
msgstr "<i class=\"fa fa-close\"/> Stäng"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-dot-circle-o fa-lg me-2\"/>answer"
msgstr "<i class=\"fa fa-dot-circle-o fa-lg me-2\"/>svar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-dot-circle-o fa-lg\" role=\"img\" aria-label=\"Checked\" "
"title=\"Checked\"/>"
msgstr ""
"<i class=\"fa fa-dot-circle-o fa-lg\" role=\"img\" aria-"
"label=\"Kontrollerad\" title=\"Kontrollerad\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> It is currently not possible to "
"pass this assessment because no question is configured to give any points."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/> Det är för närvarande inte möjligt"
" att klara denna utvärdering eftersom ingen fråga är konfigurerad för att ge"
" poäng."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Download certification\" title=\"Download certification\"/>\n"
"                                        Download certification"
msgstr ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Download certification\" title=\"Download certification\"/>\n"
"                                        Ladda ner certifikat"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr "<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Pilikon\" title=\"Pil\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-square-o fa-lg me-2\"/>answer"
msgstr "<i class=\"fa fa-square-o fa-lg me-2\"/>svar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
msgid "<i class=\"oi oi-fw oi-arrow-right\"/>Go to Survey"
msgstr "<i class=\"oi oi-fw oi-arrow-right\"/>Gå till undersökningen"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 "
"pt-1\">Avg</span>"
msgstr ""
"<span class=\"badge text-bg-sekundär only_left_radius px-2 pt-1\">Avg</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 "
"pt-1\">Max</span>"
msgstr ""
"<span class=\"badge text-bg-sekundär only_left_radius px-2 pt-1\">Max</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid ""
"<span class=\"badge text-bg-secondary only_left_radius px-2 "
"pt-1\">Min</span>"
msgstr ""
"<span class=\"badge text-bg-sekundär only_left_radius px-2 pt-1\">Min</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\" id=\"enter-"
"tooltip\"> or press CTRL+Enter</span>"
msgstr ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\" id=\"enter-"
"tooltip\"> eller tryck på CTRL+Enter</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\">\n"
"                            <span id=\"enter-tooltip\">or press Enter</span>\n"
"                        </span>"
msgstr ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\">\n"
"                            <span id=\"enter-tooltip\">eller tryck Enter</span>\n"
"                        </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\">\n"
"                    <span id=\"enter-tooltip\">or press CTRL+Enter</span>\n"
"                </span>"
msgstr ""
"<span class=\"fw-bold text-muted ms-2 d-none d-md-inline\">\n"
"                    <span id=\"enter-tooltip\">eller tryck CTRL+Enter</span>\n"
"                </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"mx-1\">-</span>"
msgstr "<span class=\"mx-1\">-</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.res_partner_view_form
msgid ""
"<span class=\"o_stat_text\" invisible=\"certifications_company_count &lt; 2\">Certifications</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"certifications_company_count &gt; 1\">Certification</span>"
msgstr ""
"<span class=\"o_stat_text\" invisible=\"certifications_company_count &lt; 2\">Certifieringar</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"certifications_company_count &gt; 1\">Certifiering</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.res_partner_view_form
msgid ""
"<span class=\"o_stat_text\" invisible=\"certifications_count &lt; 2\">Certifications</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"certifications_count &gt; 1\">Certification</span>"
msgstr ""
"<span class=\"o_stat_text\" invisible=\"certifications_count &lt; 2\">Certifieringar</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"certifications_count &gt; 1\">Certifiering</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid ""
"<span class=\"o_survey_enter fw-bold text-muted ms-2 d-none d-md-inline\">or"
" press Enter</span>"
msgstr ""
"<span class=\"o_survey_enter fw-bold text-muted ms-2 d-none d-md-"
"inline\">eller tryck Enter</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_selection_key
msgid ""
"<span class=\"o_survey_key text-center position-absolute bg-white rounded-"
"start py-0 ps-2\"><span class=\"text-primary text-center text-center w-100 "
"position-relative\">Key</span></span>"
msgstr ""
"<span class=\"o_survey_key text-center position-absolute bg-white rounded-"
"start py-0 ps-2\"><span class=\"text-primary text-center text-center w-100 "
"position-relative\">Nyckel</span></span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid ""
"<span class=\"o_survey_results_topbar_clear_filters text-primary\">\n"
"                                <i class=\"fa fa-trash me-1\"/>Remove all filters\n"
"                            </span>"
msgstr ""
"<span class=\"o_survey_results_topbar_clear_filters text-primary\">\n"
"                                <i class=\"fa fa-trash me-1\"/>Ta bort alla filter\n"
"                            </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid ""
"<span class=\"o_survey_session_answer_count\">0</span>\n"
"                                     /"
msgstr ""
"<span class=\"o_survey_session_answer_count\">0</span>\n"
"                                     /"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_open
msgid ""
"<span class=\"o_survey_session_navigation_next_label\">Start</span>\n"
"                        <i class=\"oi oi-chevron-right\"/>"
msgstr ""
"<span class=\"o_survey_session_navigation_next_label\">Starta</span>\n"
"                        <i class=\"oi oi-chevron-right\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-break text-muted\">Completed</span>"
msgstr "<span class=\"text-break text-muted\">Fullbordad</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-break text-muted\">Registered</span>"
msgstr "<span class=\"textbrytning text-muted\">Registrerad</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-break text-muted\">Success</span>"
msgstr "<span class=\"text-break text-muted\">Framgång</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-muted\">Average Duration</span>"
msgstr "<span class=\"text-muted\">Genomsnittlig löptid</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "<span class=\"text-muted\">Questions</span>"
msgstr "<span class=\"text-muted\">Frågor</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "<span class=\"text-muted\">Responded</span>"
msgstr "<span class=\"text-muted\">Svarade</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "<span class=\"text-success\">Correct</span>"
msgstr "<span class=\"text-success\">Rätt</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "<span class=\"text-warning\">Partial</span>"
msgstr "<span class=\"text-warning\">Delvis</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span invisible=\"not is_scored_question\">Points</span>"
msgstr "<span invisible=\"not is_scored_question\">Poäng</span>"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p3_q1
msgid ""
"<span>\"Red\" is not a category, I know what you are trying to do ;)</span>"
msgstr "<span>\"Röd\" är inte en kategori, jag vet vad du försöker göra ;)</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "<span>%</span>"
msgstr "<span>%</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>All surveys</span>"
msgstr "<span>Alla undersökningar</span>"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p3_q6
msgid "<span>Best time to do it, is the right time to do it.</span>"
msgstr "<span>Bästa tiden att göra det, är rätt tid att göra det.</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>Completed surveys</span>"
msgstr "<span>Avslutade undersökningar</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "<span>Date</span>"
msgstr "<span>Datum</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>Do you like it?</span><br/>"
msgstr "<span>Gillar du det? </span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>Failed only</span>"
msgstr "<span>Misslyckades endast</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<span>How many ...?</span><br/>\n"
"                                    <i class=\"fa fa-2x\" role=\"img\" aria-label=\"Numeric\" title=\"Numeric\">123 </i>\n"
"                                    <i class=\"fa fa-2x fa-sort\" role=\"img\" aria-label=\"Numeric\"/>"
msgstr ""
"<span>Hur många ...?</span><br/>\n"
"                                    <i class=\"fa fa-2x\" role=\"img\" aria-label=\"Numeric\" title=\"Numeric\">123 </i>\n"
"                                    <i class=\"fa fa-2x fa-sort\" role=\"img\" aria-label=\"Numeric\"/>"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p5_q1
msgid ""
"<span>If you don't like us, please try to be as objective as "
"possible.</span>"
msgstr ""
"<span>Om du inte gillar oss, försök att vara så objektiv som möjligt.</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<span>Name all the animals</span><br/>\n"
"                                    <i class=\"fa fa-align-justify fa-4x\" role=\"img\" aria-label=\"Multiple lines\" title=\"Multiple Lines\"/>"
msgstr ""
"<span>Namnge alla djur</span><br/>\n"
"                                    <i class=\"fa fa-align-justify fa-4x\" role=\"img\" aria-label=\"Multiple lines\" title=\"Multiple Lines\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<span>Name one animal</span><br/>\n"
"                                    <i class=\"fa fa-minus fa-4x\" role=\"img\" aria-label=\"Single Line\" title=\"Single Line\"/>"
msgstr ""
"<span>Namnge ett djur</span><br/>\n"
"                                    <i class=\"fa fa-minus fa-4x\" role=\"img\" aria-label=\"Single Line\" title=\"Single Line\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "<span>Number of attempts left</span>:"
msgstr "<span>Antal försök kvar</span>:"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p2_q1
msgid "<span>Our famous Leader!</span>"
msgstr "<span>Vår berömda ledare!</span>"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p3_q3
msgid "<span>Our sales people have an advantage, but you can do it!</span>"
msgstr "<span>Våra säljare har en fördel, men du kan göra det!</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>Passed and Failed</span>"
msgstr "<span>Godkänd och underkänd</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "<span>Passed only</span>"
msgstr "<span>Endast godkänt</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid ""
"<span>This certificate is presented to</span>\n"
"                                <br/>"
msgstr ""
"<span>Detta certifikat överlämnas till</span>\n"
"                                <br/>"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "<span>Try It</span>"
msgstr "<span>Prova det</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<span>Waiting for attendees...</span>"
msgstr "<span>Väntar på deltagare...</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>When does ... start?</span><br/>"
msgstr "<span>När börjar … ?</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>When is Christmas?</span><br/>"
msgstr "<span>När är det jul?</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>Which are yellow?</span><br/>"
msgstr "<span>Vilka är gula?</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<span>Which is yellow?</span><br/>"
msgstr "<span>Vilken är gul?</span><br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid ""
"<span>for successfully completing</span>\n"
"                                <br/>"
msgstr ""
"<span>för framgångsrikt genomförande</span>\n"
"                                <br/>"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q4
msgid "A \"Citrus\" could give you ..."
msgstr "En \"Citrus\" kan ge dig ..."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid "A label must be attached to only one question."
msgstr "Endast en fråga kan hålla en etikett."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_len_max
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_len_min
msgid "A length must be positive!"
msgstr "Längden måste vara positiv!"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_4
msgid "A little bit overpriced"
msgstr "Lite för dyrt"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_5
msgid "A lot overpriced"
msgstr "Mycket för dyrt"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_session_speed_rating_has_time_limit
msgid ""
"A positive default time limit is required when the session rewards quick "
"answers."
msgstr "En positiv standardtidsgräns krävs när sessionen belönar snabba svar."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question_answer__answer_score
msgid ""
"A positive score indicates a correct choice; a negative or null score "
"indicates a wrong answer"
msgstr ""
"En positiv poäng anger ett korrekt val; en negativ eller noll poäng anger "
"ett felaktigt svar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "A problem has occurred"
msgstr "Ett problem har uppstått"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "A question can either be skipped or answered, not both."
msgstr "En fråga kan antingen hoppas över eller besvaras, men inte både och."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"A scored survey needs at least one question that gives points.\n"
"Please check answers and their scores."
msgstr ""
"En enkät med poäng behöver minst en fråga som ger poäng.\n"
"Kontrollera svaren och deras poäng."

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2
msgid "About our ecommerce"
msgstr "Om vår e-handel"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1
msgid "About you"
msgstr "Om dig"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_access_mode
#: model:ir.model.fields,field_description:survey.field_survey_survey__access_mode
msgid "Access Mode"
msgstr "Åtkomst"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__access_token
msgid "Access Token"
msgstr "Åtkomsttecken"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_access_token_unique
msgid "Access token should be unique"
msgstr "Åtkomsttoken ska vara unik"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_needaction
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_needaction
msgid "Action Needed"
msgstr "Åtgärd krävs"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__active
msgid "Active"
msgstr "Aktiv"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_ids
msgid "Activities"
msgstr "Aktiviteter"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_exception_decoration
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Aktivitet undantaget dekoration"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_state
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_state
msgid "Activity State"
msgstr "Aktivitetsläge"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_type_icon
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikon för aktivitetstyp"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Add a question"
msgstr "Lägg till en fråga"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Add a section"
msgstr "Lägg till ett avsnitt"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Add existing contacts..."
msgstr "Lägg till befintliga kontakter..."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Add some fun to your presentations by sharing questions live"
msgstr "Gör dina presentationer lite roligare genom att dela frågor live"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__emails
msgid "Additional emails"
msgstr "Ytterligare e-postmeddelanden"

#. module: survey
#: model:res.groups,name:survey.group_survey_manager
msgid "Administrator"
msgstr "Administratör"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug1
msgid "Africa"
msgstr "Afrika"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q6
msgid ""
"After watching this video, will you swear that you are not going to "
"procrastinate to trim your hedge this year?"
msgstr ""
"Efter att ha sett den här videon, kommer du att svära på att du inte kommer "
"att skjuta upp att klippa din häck i år?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col3
msgid "Agree"
msgstr "Instämmer"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scored_date_have_answers
msgid ""
"All \"Is a scored question = True\" and \"Question Type: Date\" questions "
"need an answer"
msgstr ""
"Alla \"Är en poängsatt fråga = Sant\" och \"Frågetyp: Datum\" behöver ett "
"svar"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scored_datetime_have_answers
msgid ""
"All \"Is a scored question = True\" and \"Question Type: Datetime\" "
"questions need an answer"
msgstr ""
"Alla \"Är en poängsatt fråga = Sant\" och \"Frågetyp: Datumtid\" behöver ett"
" svar"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_selection__all
msgid "All questions"
msgstr "Alla frågor"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "All surveys"
msgstr "Alla enkäter"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_is_time_limited_have_time_limit
msgid "All time-limited questions need a positive time limit"
msgstr "Alla tidsbegränsade frågor behöver en positiv tidsgräns"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Allow Roaming"
msgstr "Tillåt roaming"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__allowed_triggering_question_ids
msgid "Allowed Triggering Questions"
msgstr "Tillåtna utlösande frågor"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__allowed_survey_types
msgid "Allowed survey types"
msgstr "Tillåtna undersökningstyper"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug2
msgid "Amenhotep"
msgstr "Amenhotep"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_user_input_unique_token
msgid "An access token must be unique!"
msgstr "En åtkomsttoken måste vara unik!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_answer_score
msgid "An answer score for a non-multiple choice question cannot be negative!"
msgstr ""
"En svarspoäng för en fråga som inte har flera valmöjligheter kan inte vara "
"negativ!"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p3
msgid "An apple a day keeps the doctor away."
msgstr "Ett äpple om dagen håller läkaren borta."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_leaderboard
msgid "Anonymous"
msgstr "Anonym"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_tree
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Answer"
msgstr "Svar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_type
msgid "Answer Type"
msgstr "Typ av svar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__deadline
msgid "Answer deadline"
msgstr "Svarsfrist"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question_answer__value_label
msgid ""
"Answer label as either the value itself if not empty or a letter "
"representing the index of the answer otherwise."
msgstr ""
"Svarsmärkning som antingen värdet självt om det inte är tomt eller en "
"bokstav som representerar indexet för svaret annars."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__user_input_line_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__user_input_line_ids
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Answers"
msgstr "Svar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_answer_count
msgid "Answers Count"
msgstr "Svaren räknas"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__access_mode__public
msgid "Anyone with the link"
msgstr "Alla som har en länk"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "Förekommer i"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug1
msgid "Apple Trees"
msgstr "Äppelträd"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row1
msgid "Apples"
msgstr "Äpplen"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Archived"
msgstr "Arkiverad"

#. module: survey
#: model:survey.question,title:survey.survey_demo_food_preferences_q1
msgid "Are you vegetarian?"
msgstr "Är du vegetarian?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug4
msgid "Art & Culture"
msgstr "Konst & kultur"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug1
msgid "Arthur B. McDonald"
msgstr "Arthur B. McDonald"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug2
msgid "Asia"
msgstr "Asien"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__survey_type__assessment
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Assessment"
msgstr "Bedömning"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_attachment_count
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_attachment_count
msgid "Attachment Count"
msgstr "Antal bilagor"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__attachment_ids
msgid "Attachments"
msgstr "Bilagor"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_number
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Attempt n°"
msgstr "Försök nr"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_done_count
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Attempts"
msgstr "Försök"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_count
msgid "Attempts Count"
msgstr "Antal försök"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__nickname
msgid ""
"Attendee nickname, mainly used to identify them in the survey session "
"leaderboard."
msgstr ""
"Deltagarnas smeknamn, används främst för att identifiera dem i "
"undersökningssessionens topplista."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Attendees are answering the question..."
msgstr "Deltagarna svarar på frågan..."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_speed_rating
msgid "Attendees get more points if they answer quickly"
msgstr "Deltagarna får fler poäng om de svarar snabbt"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__author_id
msgid "Author"
msgstr "Författare"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__certification_mail_template_id
msgid ""
"Automated email sent to the user when they succeed the certification, "
"containing their certification document."
msgstr ""
"Automatiserat e-postmeddelande som skickas till användaren när de har klarat"
" certifieringen, med deras certifieringsdokument."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug3
msgid "Autumn"
msgstr "Höst"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Average"
msgstr "Genomsnitt"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_duration_avg
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Average Duration"
msgstr "Genomsnittlig varaktighet"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Average Score"
msgstr "Genomsnittlig poäng"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__answer_duration_avg
msgid "Average duration of the survey (in hours)"
msgstr "Enkätens genomsnittliga varaktighet (i timmar)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_score_avg
msgid "Avg Score (%)"
msgstr "Genomsnittligt resultat (%)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug3
msgid "Avicii"
msgstr "Avicii"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__background_image
#: model:ir.model.fields,field_description:survey.field_survey_survey__background_image
msgid "Background Image"
msgstr "Bakgrundsbild"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__background_image_url
#: model:ir.model.fields,field_description:survey.field_survey_survey__background_image_url
msgid "Background Url"
msgstr "Bakgrund Url"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "Badge"
msgstr "Utmärkelse"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug3
msgid "Baobab Trees"
msgstr "Baobabträd"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug1
msgid "Bees"
msgstr "Bin"

#. module: survey
#: model:survey.question,question_placeholder:survey.vendor_certification_page_3_question_3
msgid "Beware of leap years!"
msgstr "Se upp för skottår!"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Blue Pen"
msgstr "Blå penna"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__body_has_template_value
msgid "Body content is the same as the template"
msgstr "Brödtexten är detsamma som i mallen"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug4
msgid "Bricks"
msgstr "Brickor"

#. module: survey
#: model:survey.question,question_placeholder:survey.survey_feedback_p1_q1
msgid "Brussels"
msgstr "Bryssel"

#. module: survey
#: model:survey.question,question_placeholder:survey.survey_demo_quiz_p1_q3
msgid "Brussels, Belgium"
msgstr "Bryssel, Belgien"

#. module: survey
#: model:survey.survey,title:survey.survey_demo_burger_quiz
msgid "Burger Quiz"
msgstr "Burgar Quiz"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "But first, keep listening to the host."
msgstr "Men först måste du fortsätta att lyssna på värden."

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_3
msgid "Cabinet with Doors"
msgstr "Skåp med dörrar"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_row1
msgid "Cactus"
msgstr "Kaktus"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__can_edit_body
msgid "Can Edit Body"
msgstr "Kan redigera stycket"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q3
msgid "Can Humans ever directly see a photon?"
msgstr "Kan människor någonsin direkt se en foton?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_tree
msgid "Certification"
msgstr "Certifiering"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_badge_id
msgid "Certification Badge"
msgstr "Certifieringsmärke"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_badge_id_dummy
msgid "Certification Badge "
msgstr "Certifieringsmärke "

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Certification Badge is not configured for the survey %(survey_name)s"
msgstr "Certifieringsmärket är inte konfigurerat för enkäten %(survey_name)s"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "Certification Failed"
msgstr "Certifieringen misslyckades"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "Certification n°"
msgstr "Certifieringsnummer"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_report_layout
msgid "Certification template"
msgstr "Mall för certifiering"

#. module: survey
#: model:mail.template,subject:survey.mail_template_certification
msgid "Certification: {{ object.survey_id.display_name }}"
msgstr "Certifiering: {{ object.survey_id.display_name }}"

#. module: survey
#: model:ir.actions.report,name:survey.certification_report
#: model:ir.model.fields.selection,name:survey.selection__gamification_challenge__challenge_category__certification
msgid "Certifications"
msgstr "Certifieringar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_res_partner__certifications_count
#: model:ir.model.fields,field_description:survey.field_res_users__certifications_count
msgid "Certifications Count"
msgstr "Antal Certifieringar"

#. module: survey
#: model:ir.actions.act_window,name:survey.res_partner_action_certifications
msgid "Certifications Succeeded"
msgstr "Certifieringar som lyckats"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Certified"
msgstr "Certifierad"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_mail_template_id
msgid "Certified Email Template"
msgstr "Mall för certifierad e-post"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_1
msgid "Chair floor protection"
msgstr "Golvskydd för stol"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Cheating on your neighbors will not help!"
msgstr "Att fuskar hjälper dig inte!"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__is_attempts_limited
#: model:ir.model.fields,help:survey.field_survey_user_input__is_attempts_limited
msgid "Check this option if you want to limit the number of attempts per user"
msgstr ""
"Markera det här alternativet om du vill begränsa antalet försök per "
"användare"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/boolean_update_flag_field/boolean_update_flag_fields.js:0
msgid "Checkbox updating comparison flag"
msgstr "Checkbox för uppdatering av jämförelseflagga"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug2
msgid "China"
msgstr "Kina"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Choices"
msgstr "Val"

#. module: survey
#: model_terms:survey.survey,description:survey.survey_demo_burger_quiz
msgid "Choose your favourite subject and show how good you are. Ready?"
msgstr "Välj ditt favoritämne och visa hur duktig du är. Är du redo?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_food_preferences_q3
msgid "Choose your green meal"
msgstr "Välj din gröna måltid"

#. module: survey
#: model:survey.question,title:survey.survey_demo_food_preferences_q4
msgid "Choose your meal"
msgstr "Välj din måltid"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_blue
msgid "Classic Blue"
msgstr "Klassisk Blå"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_gold
msgid "Classic Gold"
msgstr "Klassiskt guld"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_purple
msgid "Classic Purple"
msgstr "Klassisk Lila"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row3
msgid "Clementine"
msgstr "Klementin"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug4
msgid "Cliff Burton"
msgstr "Cliff Burton"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Close"
msgstr "Stäng"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Close Live Session"
msgstr "Stäng live-sessionen"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_1
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Color"
msgstr "Färg"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__color
msgid "Color Index"
msgstr "Färgindex"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"Combining roaming and \"Scoring with answers after each page\" is not possible; please update the following surveys:\n"
"- %(survey_names)s"
msgstr ""
"Att kombinera roaming och \"Poängsättning med svar efter varje sida\" är inte möjligt; vänligen uppdatera följande enkäter:\n"
"- %(survey_names)s"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_question_form
msgid "Come back once you have added questions to your Surveys."
msgstr "Kom tillbaka när du har lagt till frågor i dina enkäter."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_comments
msgid "Comment"
msgstr "Kommentar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comments_message
msgid "Comment Message"
msgstr "Kommentar Meddelande"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comment_count_as_answer
msgid "Comment is an answer"
msgstr "Kommentar är ett svar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_res_partner__certifications_company_count
#: model:ir.model.fields,field_description:survey.field_res_users__certifications_company_count
msgid "Company Certifications Count"
msgstr "Företagets antal certifieringar"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__done
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Completed"
msgstr "Slutförd"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "Completed surveys"
msgstr "Avslutade undersökningar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Compose Email"
msgstr "Skriv e-post"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "Computing score requires a question in arguments."
msgstr "För att beräkna poäng krävs en fråga om argument."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Conditional display"
msgstr "Villkorlig visning"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/survey_question_trigger/survey_question_trigger.js:0
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"Conditional display is not available when questions are randomly picked."
msgstr ""
"Villkorlig visning är inte tillgänglig när frågorna väljs ut slumpmässigt."

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_3
msgid "Conference chair"
msgstr "Konferensstol"

#. module: survey
#: model_terms:web_tour.tour,rainbow_man_message:survey.survey_tour
msgid "Congratulations! You are now ready to collect feedback like a pro :-)"
msgstr "Vi gratulerar! Du är nu redo att samla in feedback som ett proffs :-)"

#. module: survey
#: model_terms:gamification.badge,description:survey.vendor_certification_badge
msgid "Congratulations, you are now official vendor of MyCompany"
msgstr "Grattis, du är nu officiell säljare av MittFöretag"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Congratulations, you have passed the test!"
msgstr "Grattis, du klarade testet!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Constraints"
msgstr "Begränsningar"

#. module: survey
#: model:ir.model,name:survey.model_res_partner
#: model:ir.model.fields,field_description:survey.field_survey_user_input__partner_id
msgid "Contact"
msgstr "Kontakt"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__has_conditional_questions
msgid "Contains conditional questions"
msgstr "Innehåller villkorliga frågor"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__body
msgid "Contents"
msgstr "Innehåll"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Continue"
msgstr "Fortsätt"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "Continue here"
msgstr "Fortsätt här"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug4
msgid "Cookies"
msgstr "Kakor"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Copied!"
msgstr "Kopierad!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug3
msgid "Cornaceae"
msgstr "Kornellväxter"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_1
msgid "Corner Desk Right Sit"
msgstr "Hörnskrivbord höger sitt"

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__is_correct
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_is_correct
msgid "Correct"
msgstr "Korrekt"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Correct Answer"
msgstr "Rätt svar"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_datetime
msgid "Correct date and time answer for this question."
msgstr "Korrekt svar på datum och tid för den här frågan."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_date
msgid "Correct date answer"
msgstr "Rätt datum svar"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_date
msgid "Correct date answer for this question."
msgstr "Rätt datum för den här frågan."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_datetime
msgid "Correct datetime answer"
msgstr "Korrekt svar på datumtid"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_numerical_box
msgid "Correct number answer for this question."
msgstr "Rätt nummer för den här frågan."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_numerical_box
msgid "Correct numerical answer"
msgstr "Korrekt numeriskt svar"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_3
msgid "Correctly priced"
msgstr "Korrekt prissatt"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug3
msgid "Cosmic rays"
msgstr "Kosmiska strålar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Create Live Session"
msgstr "Skapa en livesession"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Create a custom survey from scratch"
msgstr "Skapa en anpassad undersökning från grunden"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_question__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__create_uid
msgid "Created by"
msgstr "Skapad av"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__create_date
#: model:ir.model.fields,field_description:survey.field_survey_question__create_date
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__create_date
#: model:ir.model.fields,field_description:survey.field_survey_survey__create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input__create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__create_date
msgid "Created on"
msgstr "Skapad den"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Creating test token is not allowed for you."
msgstr "Det är inte tillåtet för dig att skapa testpollett."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"Creating token for anybody else than employees is not allowed for internal "
"surveys."
msgstr ""
"Det är inte tillåtet att skapa pollett för andra än anställda för interna "
"enkäter."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Creating token for closed/archived surveys is not allowed."
msgstr ""
"Det är inte tillåtet att skapa token för avslutade/arkiverade enkäter."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"Creating token for external people is not allowed for surveys requesting "
"authentication."
msgstr ""
"Det är inte tillåtet att skapa token för externa personer för enkäter som "
"begär autentisering."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_id
msgid "Current Question"
msgstr "Aktuell fråga"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_start_time
msgid "Current Question Start Time"
msgstr "Aktuell fråga Starttid"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_start_time
msgid "Current Session Start Time"
msgstr "Starttid för den aktuella sessionen"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_time_limited
msgid "Currently only supported for live sessions."
msgstr "För närvarande stöds endast livesessioner."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__survey_type__custom
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Custom"
msgstr "Anpassad"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid ""
"Customers will receive a new token and be able to completely retake the "
"survey."
msgstr "Kunderna får en ny token och kan göra om enkäten helt och hållet."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Customers will receive the same token."
msgstr "Kunderna kommer att få samma symboliska token."

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_5
msgid "Customizable Lamp"
msgstr "Anpassningsbar lampa"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_time_customized
msgid "Customized speed rewards"
msgstr "Anpassade hastighetsbelöningar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "DEMO_CERTIFIED_NAME"
msgstr "DEMO_CERTIFIERAD_NAMN"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__date
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__date
msgid "Date"
msgstr "Datum"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_date
msgid "Date answer"
msgstr "Datum för svar"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__datetime
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__datetime
msgid "Datetime"
msgstr "Datum Tid"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_datetime
msgid "Datetime answer"
msgstr "Datumtid svar"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__deadline
msgid "Datetime until customer can open the survey and submit answers"
msgstr "Datumtid när kunden kan öppna enkäten och skicka in svaren"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__deadline
msgid "Deadline"
msgstr "Tidsfrist"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_speed_rating_time_limit
msgid "Default time given to receive additional points for right answers"
msgstr "Standardtid som ges för att få ytterligare poäng för rätt svar"

#. module: survey
#: model:ir.model.fields,help:survey.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "Definiera utmaningens synlighet genom menyer"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Delete"
msgstr "Radera"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__description
#: model:ir.model.fields,field_description:survey.field_survey_survey__description
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Description"
msgstr "Beskrivning"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_2
msgid "Desk Combination"
msgstr "Skrivbordskombination"

#. module: survey
#: model:ir.actions.act_window,name:survey.survey_user_input_line_action
#: model:ir.ui.menu,name:survey.menu_survey_response_line_form
msgid "Detailed Answers"
msgstr "Detaljerade svar"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col2
msgid "Disagree"
msgstr "Jag avböjer"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__display_name
#: model:ir.model.fields,field_description:survey.field_survey_question__display_name
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__display_name
#: model:ir.model.fields,field_description:survey.field_survey_survey__display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input__display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__display_name
msgid "Display Name"
msgstr "Visningsnamn"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__progression_mode
msgid "Display Progress as"
msgstr "Visa framsteg som"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/survey_question_trigger/survey_question_trigger.js:0
msgid "Displayed if \"%s\"."
msgstr "Visas om \"%s\"."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Displayed when the answer entered is not valid."
msgstr "Visas när det angivna svaret inte är giltigt."

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_1
msgid "Do we sell Acoustic Bloc Screens?"
msgstr "Säljer vi Ljuddämpningsskärmar?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q3
msgid "Do you have any other comments, questions, or concerns?"
msgstr "Har du några andra kommentarer, frågor eller funderingar?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_5
msgid "Do you think we have missing products in our catalog? (not rated)"
msgstr "Tycker du att vi saknar produkter i vår katalog? (inte betygsatt)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug2
msgid "Dogs"
msgstr "Hundar"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q1
msgid "Dogwood is from which family of trees?"
msgstr "Kornell är från vilken familj av träd?"

#. module: survey
#: model:survey.question,question_placeholder:survey.survey_demo_quiz_p1_q2
msgid "Don't be shy, be wild!"
msgstr "Var inte blyg, var vild!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug1
msgid "Douglas Fir"
msgstr "Douglasgran"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_4
msgid "Drawer"
msgstr "Låda"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Duplicate Question"
msgstr "Fråga om duplicering"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Edit Survey"
msgstr "Redigera enkät"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "Edit in backend"
msgstr "Redigera i backend"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__email
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Email"
msgstr "E-post"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "End Live Session"
msgstr "Avsluta direktsänd session"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__description_done
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "End Message"
msgstr "Avsluta meddelandet"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__end_datetime
msgid "End date and time"
msgstr "Slutdatum och -tid"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "End of Survey"
msgstr "Slut på undersökningen"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Enter Session Code"
msgstr "Ange sessionskod"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__constr_error_msg
msgid "Error message"
msgstr "Felmeddelande"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug3
msgid "Europe"
msgstr "Europa"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug3
msgid "European Yew"
msgstr "Europeisk Idegran"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Exclude Tests"
msgstr "Utesluta tester"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_partner_ids
msgid "Existing Partner"
msgstr "Befintlig partner"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_emails
msgid "Existing emails"
msgstr "Befintlig e-post"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Extremely likely"
msgstr "Extremt sannolikt"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug2
msgid "Eyjafjallajökull (Iceland)"
msgstr "Eyjafjallajökull (Island)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Failed"
msgstr "Misslyckades"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "Failed only"
msgstr "Misslyckades endast"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_2
msgid "Fanta"
msgstr "Fanta"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#: model:survey.survey,title:survey.survey_feedback
msgid "Feedback Form"
msgstr "Formulär för återkoppling"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_row2
msgid "Ficus"
msgstr "Ficus"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
#: model_terms:ir.ui.view,arch_db:survey.question_result_text
msgid "Filter surveys"
msgstr "Filtrera undersökningar"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Final Leaderboard"
msgstr "Slutlig topplista"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q4_sug2
msgid "Fish"
msgstr "Fisk"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_follower_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_follower_ids
msgid "Followers"
msgstr "Följare"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_partner_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_partner_ids
msgid "Followers (Partners)"
msgstr "Följare (kontakter)"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_type_icon
#: model:ir.model.fields,help:survey.field_survey_user_input__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font Awesome-ikon t.ex. fa-tasks"

#. module: survey
#: model:survey.survey,title:survey.survey_demo_food_preferences
msgid "Food Preferences"
msgstr "Livsmedelspreferenser"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__text_box
msgid "Free Text"
msgstr "Fri text"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_text_box
msgid "Free Text answer"
msgstr "Fritextsvar"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q4
msgid "From which continent is native the Scots pine (pinus sylvestris)?"
msgstr "Från vilken kontinent kommer den skotska tallen (pinus sylvestris)?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug1
msgid "Fruits"
msgstr "Frukter"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3
msgid "Fruits and vegetables"
msgstr "Frukt och grönsaker"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "Functional Training"
msgstr "Funktionell träning"

#. module: survey
#: model:ir.model,name:survey.model_gamification_badge
msgid "Gamification Badge"
msgstr "Utmärkelse Gamification"

#. module: survey
#: model:ir.model,name:survey.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "Utmaning Gamification"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Gather feedbacks from your employees and customers"
msgstr "Samla in feedback från dina anställda och kunder"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug1
msgid "Geography"
msgstr "Geografi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_give_badge
msgid "Give Badge"
msgstr "Ge Utmärkelse"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q3
msgid "Give the list of all types of wood we sell."
msgstr "Ge en lista över alla träslag som vi säljer."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug1
msgid "Good"
msgstr "Bra"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Good luck!"
msgstr "Lycka till!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug4
msgid "Good value for money"
msgstr "Bra valuta för pengarna"

#. module: survey
#: model_terms:survey.survey,description_done:survey.survey_demo_food_preferences
msgid "Got it!"
msgstr "Jag har den!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug2
msgid "Grapefruits"
msgstr "Grapefrukter"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Group By"
msgstr "Grupp Av"

#. module: survey
#: model:ir.model,name:survey.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-routing"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_mode
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Handle existing"
msgstr "Hantera befintliga"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Handle quiz &amp; certifications"
msgstr "Hantera tester och certifieringar"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q3_sug1
msgid "Hard"
msgstr "Svår"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__has_message
#: model:ir.model.fields,field_description:survey.field_survey_user_input__has_message
msgid "Has Message"
msgstr "Har meddelande"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__has_image_only_suggested_answer
msgid "Has image only suggested answer"
msgstr "Har bild endast föreslagit svar"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_2
msgid "Height"
msgstr "Höjd"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Help Participants know what to write"
msgstr "Hjälp deltagarna att veta vad de ska skriva"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug3
msgid "Hemiunu"
msgstr "Hemiunu"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Here, you can overview all the participations."
msgstr "Här kan du få en överblick över alla deltaganden."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug1
msgid "High quality"
msgstr "Hög kvalitet"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug2
msgid "History"
msgstr "Historia"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q3
msgid "How frequently do you buy products online?"
msgstr "Hur ofta köper du produkter online?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "How frequently do you use our products?"
msgstr "Hur ofta använder du våra produkter?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "How good of a presenter are you? Let's find out!"
msgstr "Hur bra är du på att presentera? Låt oss ta reda på det!"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "How likely are you to recommend the following products to a friend?"
msgstr ""
"Hur troligt är det att du skulle rekommendera följande produkter till en "
"vän?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q1
msgid "How long is the White Nile river?"
msgstr "Hur lång är floden Vita Nilen?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_6
msgid ""
"How many chairs do you think we should aim to sell in a year (not rated)?"
msgstr ""
"Hur många stolar anser du att vi bör sträva efter att sälja på ett år (ej "
"betygsatt)?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_1
msgid "How many days is our money-back guarantee?"
msgstr "Hur många dagar är vår pengarna-tillbaka-garanti?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "How many orders did you pass during the last 6 months?"
msgstr "Hur många order har du skickat under de senaste 6 månaderna?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q4
msgid "How many times did you order products on our website?"
msgstr "Hur många gånger har du beställt produkter på vår webbplats?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_4
msgid "How many versions of the Corner Desk do we have?"
msgstr "Hur många versioner av Corner Desk finns det?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q3
msgid "How many years did the 100 years war last?"
msgstr "Hur många år varade 100-åriga kriget?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_1
msgid "How much do we sell our Cable Management Box?"
msgstr "Hur mycket säljer vi av vår kabelhanteringsbox?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q5
msgid "How often should you water those plants"
msgstr "Hur ofta ska du vattna dessa växter"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q4
msgid "How old are you?"
msgstr "Hur gammal är du?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug4
msgid ""
"I actually don't like thinking. I think people think I like to think a lot. "
"And I don't. I do not like to think at all."
msgstr ""
"Jag tycker faktiskt inte om att tänka. Jag tror att folk tror att jag gillar"
" att tänka mycket. Men det gör jag inte. Jag tycker inte alls om att tänka."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug2
msgid ""
"I am fascinated by air. If you remove the air from the sky, all the birds "
"would fall to the ground. And all the planes, too."
msgstr ""
"Jag är fascinerad av luft. Om man tar bort luften från himlen skulle alla "
"fåglar falla till marken. Alla flygplan också."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row5
msgid "I have added products to my wishlist"
msgstr "Jag har lagt till produkter till min önskelista"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug4
msgid "I have no idea, I'm a dog!"
msgstr "Jag har ingen aning, jag är en hund!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug3
msgid "I've been noticing gravity since I was very young!"
msgstr "Jag har märkt av gravitation sedan jag var väldigt ung!"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__id
#: model:ir.model.fields,field_description:survey.field_survey_question__id
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__id
#: model:ir.model.fields,field_description:survey.field_survey_survey__id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__id
msgid "ID"
msgstr "ID"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_exception_icon
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_exception_icon
#: model:ir.model.fields,help:survey.field_survey_user_input__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon för att indikera en undantagsaktivitet."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__access_token
msgid "Identification token"
msgstr "Identifikationstoken"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__progression_mode
msgid ""
"If Number is selected, it will display the number of questions answered on "
"the total number of question to answer."
msgstr ""
"Om du väljer Antal visas antalet besvarade frågor i förhållande till det "
"totala antalet frågor som ska besvaras."

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_3
msgid ""
"If a customer purchases a 1 year warranty on 6 January 2020, when do we "
"expect the warranty to expire?"
msgstr ""
"Om en kund köper en ettårig garanti den 6 januari 2020, när räknar vi med "
"att garantin löper ut?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_2
msgid ""
"If a customer purchases a product on 6 January 2020, what is the latest day "
"we expect to ship it?"
msgstr ""
"Om en kund köper en produkt den 6 januari 2020, vilken är den senaste dagen "
"vi kan förvänta oss att leverera den?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_needaction
#: model:ir.model.fields,help:survey.field_survey_user_input__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Om markerat, nya meddelanden kräver din uppmärksamhet."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_error
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_sms_error
#: model:ir.model.fields,help:survey.field_survey_user_input__message_has_error
#: model:ir.model.fields,help:survey.field_survey_user_input__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Om markerad, några meddelanden har leveransfel."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__save_as_email
msgid ""
"If checked, this option will save the user's answer as its email address."
msgstr ""
"Om du kryssar i det här alternativet sparas användarens svar som sin "
"e-postadress."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__save_as_nickname
msgid "If checked, this option will save the user's answer as its nickname."
msgstr ""
"Om du kryssar i det här alternativet sparas användarens svar som sitt "
"smeknamn."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__users_can_go_back
msgid "If checked, users can go back to previous pages."
msgstr "Om kryssad, kan användare backa till tidigare sidor."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__survey_users_login_required
#: model:ir.model.fields,help:survey.field_survey_survey__users_login_required
msgid ""
"If checked, users have to login before answering even with a valid token."
msgstr ""
"Om krysset är markerat måste användaren logga in innan han eller hon kan "
"svara även med en giltig token."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_container
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "If other, please specify:"
msgstr "Om annat, vänligen specificera:"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__questions_selection
msgid ""
"If randomized is selected, add the number of random questions next to the "
"section."
msgstr ""
"Om slumpmässigt valts, lägg till antalet slumpmässiga frågor bredvid "
"avsnittet."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__questions_selection
msgid ""
"If randomized is selected, you can configure the number of random questions "
"by section. This mode is ignored in live session."
msgstr ""
"Om slumpmässigt är valt kan du konfigurera antalet slumpmässiga frågor per "
"avsnitt. Det här läget ignoreras vid live-sessioner."

#. module: survey
#: model:survey.question,question_placeholder:survey.vendor_certification_page_1_question_5
msgid "If yes, explain what you think is missing, give examples."
msgstr "Om ja, förklara vad du anser saknas och ge exempel."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value_image
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Image"
msgstr "Bild"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value_image_filename
msgid "Image Filename"
msgstr "Filnamn för bild"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "Image Zoom Dialog"
msgstr "Dialog för bildzoom"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug1
msgid "Imhotep"
msgstr "Imhotep"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug6
msgid "Impractical"
msgstr "Opraktiskt"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__session_state__in_progress
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__in_progress
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "In Progress"
msgstr "Pågående"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q5
msgid "In the list below, select all the coniferous."
msgstr "Välj alla barrträd i listan nedan."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q2
msgid "In which country did the bonsai technique develop?"
msgstr "I vilket land utvecklades bonsai-tekniken?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_scored_question
msgid ""
"Include this question as part of quiz scoring. Requires an answer and answer"
" score to be taken into account."
msgstr ""
"Inkludera denna fråga som en del av poängsättningen av frågesporten. Kräver "
"ett svar och en svarspoäng för att beaktas."

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Incorrect"
msgstr "Inkorrekt"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug7
msgid "Ineffective"
msgstr "Ineffektivt"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_email
msgid "Input must be an email"
msgstr "Inmatninten måste vara en epostadress"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/integer_update_flag_field/integer_update_flag_fields.js:0
msgid "Integer updating comparison flag"
msgstr "Integer uppdatering av jämförelseflagga"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__invite_token
msgid "Invite token"
msgstr "Bjud in token"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__access_mode__token
msgid "Invited people only"
msgstr "Endast inbjudna personer"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__is_mail_template_editor
msgid "Is Editor"
msgstr "Är Redigerare"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_is_follower
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_is_follower
msgid "Is Follower"
msgstr "Är Följare"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Is a Certification"
msgstr "Är en Certifiering"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_page
msgid "Is a page?"
msgstr "Är en sidan?"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__is_session_answer
msgid "Is in a Session"
msgstr "Är i en session"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_placed_before_trigger
msgid "Is misplaced?"
msgstr "Är felplacerad?"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Is not a Certification"
msgstr "Är inte en certifiering"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__is_session_answer
msgid "Is that user input part of a survey session or not."
msgstr "Är denna användarinmatning en del av en enkätsession eller inte."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q3
msgid "Is the wood of a coniferous hard or soft?"
msgstr "Är träet i ett barrträd hårt eller mjukt?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_placed_before_trigger
msgid "Is this question placed before any of its trigger questions?"
msgstr "Är denna fråga placerad före någon av dess triggerfrågor?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug4
msgid "Istanbul"
msgstr "Istanbul"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q1_sug3
msgid "It depends"
msgstr "Det beror på"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It does not mean anything specific"
msgstr "Det betyder inte något specifikt"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It helps attendees focus on what you are saying"
msgstr "Det hjälper deltagarna att fokusera på vad du säger"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It helps attendees remember the content of your presentation"
msgstr "Det hjälper deltagarna att komma ihåg innehållet i din presentation"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It is a small bit of text, displayed to help participants answer"
msgstr ""
"Det är en liten bit text som visas för att hjälpa deltagarna att svara på"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It is an option that can be different for each Survey"
msgstr "Det är ett alternativ som kan vara olika för varje undersökning"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row2
msgid "It is easy to find the product that I want"
msgstr "Det är lätt att hitta den produkt som jag vill ha"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It is more engaging for your audience"
msgstr "Det är mer engagerande för din publik"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "It's a Belgian word for \"Management\""
msgstr "Det är ett belgiskt ord för \"ledning\""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug3
msgid "Iznogoud"
msgstr "Iznogoud"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug1
msgid ""
"I’ve never really wanted to go to Japan. Simply because I don’t like eating "
"fish. And I know that’s very popular out there in Africa."
msgstr ""
"Jag har aldrig riktigt velat åka till Japan. Helt enkelt för att jag inte "
"gillar att äta fisk. Och jag vet att det är väldigt populärt i Afrika."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug1
msgid "Japan"
msgstr "Japan"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Join Session"
msgstr "Gå med i sessionen"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p1_q4
msgid "Just to categorize your answers, don't worry."
msgstr "Bara för att kategorisera dina svar, oroa dig inte."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug2
msgid "Kim Jong-hyun"
msgstr "Kim Jong-hyun"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug1
msgid "Kurt Cobain"
msgstr "Kurt Cobain"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Label"
msgstr "Beskrivning"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__sequence
msgid "Label Sequence order"
msgstr "Etikettordning"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__matrix_row_ids
msgid "Labels used for proposed choices: rows of matrix"
msgstr "Etiketter som används för de föreslagna valen: rader i matrisen"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__suggested_answer_ids
msgid ""
"Labels used for proposed choices: simple choice, multiple choice and columns"
" of matrix"
msgstr ""
"Etiketter som används för föreslagna val: enkelt val, flera val och "
"matriskolumner"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__lang
msgid "Language"
msgstr "Språk"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_4
msgid "Large Desk"
msgstr "Stort skrivbord"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_question__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__write_uid
msgid "Last Updated by"
msgstr "Senast uppdaterad av"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__write_date
#: model:ir.model.fields,field_description:survey.field_survey_question__write_date
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__write_date
#: model:ir.model.fields,field_description:survey.field_survey_survey__write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input__write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__write_date
msgid "Last Updated on"
msgstr "Senast uppdaterad den"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__last_displayed_page_id
msgid "Last displayed question/page"
msgstr "Senast visade fråga/sida"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Late Activities"
msgstr "Sena aktiviteter"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Launch Session"
msgstr "Lanseringssession"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Leaderboard"
msgstr "Resultattavla"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_access_error
msgid "Leave"
msgstr "Lämna"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_4
msgid "Legs"
msgstr "Ben"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug2
msgid "Lemon Trees"
msgstr "Citronträd"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Let's get started!"
msgstr "Nu sätter vi igång!"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Let's give it a spin!"
msgstr "Låt oss prova!"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Let's have a look at your answers!"
msgstr "Låt oss ta en titt på dina svar!"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Let's open the survey you just submitted."
msgstr "Låt oss öppna undersökningen som du just skickade in."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Likely"
msgstr "Sannolikt"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Limit Attempts"
msgstr "Begränsa försök"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__is_attempts_limited
#: model:ir.model.fields,field_description:survey.field_survey_user_input__is_attempts_limited
msgid "Limited number of attempts"
msgstr "Begränsat antal försök"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Live Session"
msgstr "Direktsänd session"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__session_available
msgid "Live Session available"
msgstr "Live-session tillgänglig"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Live Sessions"
msgstr "Live-sessioner"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__survey_type__live_session
msgid "Live session"
msgstr "Direktsänd session"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_available
msgid "Live session available"
msgstr "Live-session tillgänglig"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Load a <b>sample Survey</b> to get started quickly."
msgstr "Ladda en <b>provundersökning</b> för att komma igång snabbt."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "Login required"
msgstr "Inloggning krävs"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__template_id
msgid "Mail Template"
msgstr "E-postmall"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__constr_mandatory
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Mandatory Answer"
msgstr "Obligatoriskt svar"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__matrix
msgid "Matrix"
msgstr "Matris"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__matrix_row_ids
msgid "Matrix Rows"
msgstr "Rader i matrisen"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__matrix_subtype
msgid "Matrix Type"
msgstr "Matristyp"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_date
msgid "Max date cannot be smaller than min date!"
msgstr "Maxdatum kan inte vara mindre än mindatum!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_datetime
msgid "Max datetime cannot be smaller than min datetime!"
msgstr "Max datetime kan inte vara mindre än min datetime!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_length
msgid "Max length cannot be smaller than min length!"
msgstr "Maxlängden kan inte vara mindre än minlängden!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_float
msgid "Max value cannot be smaller than min value!"
msgstr "Maxvärdet kan inte vara mindre än minvärdet!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Maximum"
msgstr "Max"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_date
msgid "Maximum Date"
msgstr "Senaste datum"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_datetime
msgid "Maximum Datetime"
msgstr "Maximal datumtid"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_length_max
msgid "Maximum Text Length"
msgstr "Maximal textlängd"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_max_obtainable
msgid "Maximum obtainable score"
msgstr "Högsta uppnåeliga poäng"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_float_value
msgid "Maximum value"
msgstr "Maximalt värde"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "Maybe you were looking for"
msgstr "Du kanske letade efter"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_error
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_has_error
msgid "Message Delivery error"
msgstr "Meddelande leveransfel"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_ids
msgid "Messages"
msgstr "Meddelanden"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Min/Max Limits"
msgstr "Min/Max-gränser"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Minimum"
msgstr "Minimum"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_date
msgid "Minimum Date"
msgstr "Minsta datum"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_datetime
msgid "Minimum Datetime"
msgstr "Minsta datumtid"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_length_min
msgid "Minimum Text Length"
msgstr "Lägsta textlängd"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_float_value
msgid "Minimum value"
msgstr "Minsta värde"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_blue
msgid "Modern Blue"
msgstr "Modern blå"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_gold
msgid "Modern Gold"
msgstr "Modernt guld"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_purple
msgid "Modern Purple"
msgstr "Modernt lila"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug3
msgid "Mooses"
msgstr "Älgar"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug4
msgid "Mount Elbrus (Russia)"
msgstr "Elbrusberget (Ryssland)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug3
msgid "Mount Etna (Italy - Sicily)"
msgstr "Etna (Italien - Sicilien)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug1
msgid "Mount Teide (Spain - Tenerife)"
msgstr "Teideberget (Spanien - Teneriffa)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug4
msgid "Mountain Pine"
msgstr "Fjälltall"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__text_box
msgid "Multiple Lines Text Box"
msgstr "Flerradig textruta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Multiple choice with multiple answers"
msgstr "Flersvarsval med flera svar tillåtna"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Multiple choice with one answer"
msgstr "Flersvarsval med endast ett svar"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__multiple_choice
msgid "Multiple choice: multiple answers allowed"
msgstr "Flersvarsval: flera svar tillåtna"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__simple_choice
msgid "Multiple choice: only one answer"
msgstr "Flersvarsval: endast ett svar"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__matrix_subtype__multiple
msgid "Multiple choices per row"
msgstr "Flera svar per rad"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__my_activity_date_deadline
#: model:ir.model.fields,field_description:survey.field_survey_user_input__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mina aktiviteters deadline"

#. module: survey
#: model:gamification.badge,name:survey.vendor_certification_badge
msgid "MyCompany Vendor"
msgstr "MittFöretag Leverantör"

#. module: survey
#: model:survey.survey,title:survey.vendor_certification
msgid "MyCompany Vendor Certification"
msgstr "MittFöretag Leverantörscertifiering"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Neutral"
msgstr "Neutral"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Never (less than once a month)"
msgstr "Aldrig (mindre än en gång i månaden)"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__new
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "New"
msgstr "Ny"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug3
msgid "New York"
msgstr "New York"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_invite__existing_mode__new
msgid "New invite"
msgstr "Ny inbjudan"

#. module: survey
#: model:mail.message.subtype,description:survey.mt_survey_survey_user_input_completed
msgid "New participation completed."
msgstr "Nytt deltagande slutfört."

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Next"
msgstr "Nästa"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_calendar_event_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Nästa Kalenderhändelse för aktivitet"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_date_deadline
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Nästa slutdatum för aktivitet"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_summary
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_summary
msgid "Next Activity Summary"
msgstr "Nästa aktivitetssammanställning"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_type_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_type_id
msgid "Next Activity Type"
msgstr "Nästa aktivitetstyp"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Next Skipped"
msgstr "Nästa Skippade"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__nickname
msgid "Nickname"
msgstr "Smeknamn"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q1_sug2
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q2_sug2
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug2
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_1_choice_1
msgid "No"
msgstr "Nej"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_question_form
msgid "No Questions yet!"
msgstr "Inga frågor ännu!"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "No Survey Found"
msgstr "Ingen undersökning hittades"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_user_input
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "No answers yet!"
msgstr "Inga svar ännu!"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "No attempts left."
msgstr "Inga försök kvar."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "No question yet, come back later."
msgstr "Ingen fråga ännu, kom tillbaka senare."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__no_scoring
msgid "No scoring"
msgstr "Inga poäng"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.survey_question_answer_action
msgid "No survey labels found"
msgstr "Inga enkätetiketter hittades"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.survey_user_input_line_action
msgid "No user input lines found"
msgstr "Inga rader för användarinmatning hittades"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q3_sug2
msgid "No, it's too small for the human eye."
msgstr "Nej, den är för liten för det mänskliga ögat."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug2
msgid "Norway Spruce"
msgstr "Norsk gran"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug2
msgid "Not Good, Not Bad"
msgstr "Inte bra, inte dåligt"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Not likely at all"
msgstr "Inte troligt alls"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Now that you are done, submit your form."
msgstr "Nu när du är klar kan du skicka in ditt formulär."

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Now, use this shortcut to go back to the survey."
msgstr "Använd nu denna genväg för att gå tillbaka till undersökningen."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__progression_mode__number
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__numerical_box
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__scale
msgid "Number"
msgstr "Nummer"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_needaction_counter
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_needaction_counter
msgid "Number of Actions"
msgstr "Antal åtgärder"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__attempts_limit
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_limit
msgid "Number of attempts"
msgstr "Antal försök"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_5
msgid "Number of drawers"
msgstr "Antal lådor"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_error_counter
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_has_error_counter
msgid "Number of errors"
msgstr "Antal fel"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_needaction_counter
#: model:ir.model.fields,help:survey.field_survey_user_input__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Antal meddelanden som kräver åtgärd"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_error_counter
#: model:ir.model.fields,help:survey.field_survey_user_input__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Antal meddelanden med leveransfel"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__numerical_box
msgid "Numerical Value"
msgstr "Numeriskt värde"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_numerical_box
msgid "Numerical answer"
msgstr "Numeriskt svar"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Object-Directed Open Organization"
msgstr "Objektstyrd öppen organisation"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
msgid "Occurrence"
msgstr "Förekommande"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "Odoo"
msgstr "Odoo"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Odoo Certification"
msgstr "Odoo-certifiering"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_5
msgid "Office Chair Black"
msgstr "Kontorsstol svart"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Often (1-3 times per week)"
msgstr "Ofta (1-3 gånger per vecka)"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"On Survey questions, one can define \"placeholders\". But what are they for?"
msgstr "I enkätfrågor kan man definiera \"platshållare\". Men vad är de till för?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug1
msgid "Once a day"
msgstr "En gång om dagen"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_sug1
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug3
msgid "Once a month"
msgstr "En gång i månaden"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_sug2
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug2
msgid "Once a week"
msgstr "En gång i veckan"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug4
msgid "Once a year"
msgstr "En gång om året"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__matrix_subtype__simple
msgid "One choice per row"
msgstr "Ett svar per rad"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "One needs to answer at least half the questions correctly"
msgstr "Man måste svara rätt på minst hälften av frågorna"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "One needs to get 50% of the total score"
msgstr "Man måste få 50% of av den totala poängen"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__page_per_question
msgid "One page per question"
msgstr "En sida per fråga"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__page_per_section
msgid "One page per section"
msgstr "En sida per avsnitt"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__one_page
msgid "One page with all the questions"
msgstr "En sida med alla frågor"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Only a single question left!"
msgstr "Bara en enda fråga kvar!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
#: model_terms:ir.ui.view,arch_db:survey.question_result_text
msgid "Only show survey results having selected this answer"
msgstr "Visa endast undersökningsresultat som har valt detta svar"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Only survey users can manage sessions."
msgstr "Endast enkätanvändare kan hantera sessioner."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Oops! No survey matches this code."
msgstr "Hoppsan! Ingen undersökning matchar denna kod."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_access_error
msgid ""
"Oopsie! We could not let you open this survey. Make sure you are using the correct link and are allowed to\n"
"                        participate or get in touch with its organizer."
msgstr ""
"Hoppsan! Vi kunde inte låta dig öppna denna undersökning. Kontrollera att du använder rätt länk och är tillåten att\n"
"                        delta eller ta kontakt med organisatören."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Open Session Manager"
msgstr "Öppna Session Manager"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/question_page/description_page_field.xml:0
msgid "Open section"
msgstr "Öppen sektion"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Optional previous answers required"
msgstr "Valfritt tidigare svar krävs"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Valfritt översättningsspråk (ISO-kod) som ska väljas när du skickar ett "
"e-postmeddelande. Om inget anges används den engelska versionen. Detta bör "
"vanligtvis vara ett platshållaruttryck som anger lämpligt språk, t.ex. {{ "
"object.partner_id.lang }}."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Options"
msgstr "Alternativ"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Organizational Development for Operation Officers"
msgstr "Organisationsutveckling för operativa officerare"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid "Other (see comments)"
msgstr "Annat (se kommentarer)"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2
msgid "Our Company in a few questions ..."
msgstr "Vårt Företag i några frågor ..."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__mail_server_id
msgid "Outgoing mail server"
msgstr "Utgående e-postserver"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Overall Performance"
msgstr "Övergripande prestanda"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug5
msgid "Overpriced"
msgstr "Överpris"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__page_id
msgid "Page"
msgstr "Sida"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__page_ids
msgid "Pages"
msgstr "Sidor"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__questions_layout
msgid "Pagination"
msgstr "Sidindelning"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug4
msgid "Papyrus"
msgstr "Papyrus"

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Partially"
msgstr "Delvis"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Participant"
msgstr "Deltagare"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Participants"
msgstr "Deltagare"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "Participate to %(survey_name)s"
msgstr "Delta i %(survey_name)s"

#. module: survey
#: model:mail.template,subject:survey.mail_template_user_input_invite
msgid "Participate to {{ object.survey_id.display_name }} survey"
msgstr "Delta i {{ object.survey_id.display_name }} enkäten"

#. module: survey
#: model:mail.message.subtype,name:survey.mt_survey_survey_user_input_completed
#: model:mail.message.subtype,name:survey.mt_survey_user_input_completed
msgid "Participation completed"
msgstr "Deltagande slutfört"

#. module: survey
#: model:mail.message.subtype,description:survey.mt_survey_user_input_completed
msgid "Participation completed."
msgstr "Deltagande slutfört."

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_user_input
#: model:ir.ui.menu,name:survey.menu_survey_type_form1
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Participations"
msgstr "Deltagande"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Partner"
msgstr "Partner"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Passed"
msgstr "Godkänd"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "Passed and Failed"
msgstr "Godkänd och underkänd"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_results_filters
msgid "Passed only"
msgstr "Endast godkänt"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Pay attention to the host screen until the next question."
msgstr "Var uppmärksam på värdskärmen fram till nästa fråga."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__progression_mode__percent
msgid "Percentage left"
msgstr "Procent vänster"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Performance by Section"
msgstr "Prestationer per sektion"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug3
msgid "Perhaps"
msgstr "Kanske"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug2
msgid "Peter W. Higgs"
msgstr "Peter W. Higgs"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Pick a Badge..."
msgstr "Välj en bricka..."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Pick a Style..."
msgstr "Välj en stil..."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Pick a Template..."
msgstr "Välj en mall..."

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p1_q1
msgid "Pick a subject"
msgstr "Välj ett ämne"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__triggering_answer_ids
msgid ""
"Picking any of these answers will trigger this question.\n"
"Leave the field empty if the question should always be displayed."
msgstr ""
"Om du väljer något av dessa svar aktiveras denna fråga.\n"
"Lämna fältet tomt om frågan alltid ska visas."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug1
msgid "Pinaceae"
msgstr "Tallväxter"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__question_placeholder
msgid "Placeholder"
msgstr "Platshållare"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"Please complete this very short survey to let us know how satisfied your are"
" with our products."
msgstr ""
"Vänligen fyll i denna mycket korta enkät för att låta oss veta hur nöjda ni "
"är med våra produkter."

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "Please enter at least one valid recipient."
msgstr "Ange minst en giltig mottagare."

#. module: survey
#: model_terms:survey.survey,description:survey.survey_demo_food_preferences
msgid "Please give us your preferences for this event's dinner!"
msgstr "Vänligen meddela oss dina önskemål om middag för detta evenemang!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid ""
"Please make sure you have at least one question in your survey. You also "
"need at least one section if you chose the \"Page per section\" layout.<br/>"
msgstr ""
"Se till att du har minst en fråga i din enkät. Du måste också ha minst ett "
"avsnitt om du har valt layouten \"sida per avsnitt\".<br/>"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3
msgid "Policies"
msgstr "Strategier"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug1
msgid "Pomelos"
msgstr "Pomelos"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug8
msgid "Poor quality"
msgstr "Dålig kvalitet"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Practice in front of a mirror"
msgstr "Öva framför en spegel"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__predefined_question_ids
msgid "Predefined Questions"
msgstr "Fördefinierade frågor"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Preview"
msgstr "Förhandsgranskning"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2
msgid "Prices"
msgstr "Priser"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_header
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Print"
msgstr "Skriv ut"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_print
msgid "Print Results"
msgstr "Skriv ut resultat"

#. module: survey
#: model:ir.actions.server,name:survey.action_survey_print
msgid "Print Survey"
msgstr "Skriv ut enkäten"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1
msgid "Products"
msgstr "Produkter"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Progress bar"
msgstr "Progress bar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__question_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__question_id
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Question"
msgstr "Fråga"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Question & Pages"
msgstr "Frågor och sidor"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__matrix_question_id
msgid "Question (as matrix row)"
msgstr "Fråga (som matrisrad)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_form
msgid "Question Answer Form"
msgstr "Formulär för frågor och svar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_answer_count
msgid "Question Answers Count"
msgstr "Frågor och svar räknas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__questions_selection
#: model:ir.model.fields,field_description:survey.field_survey_survey__questions_selection
msgid "Question Selection"
msgstr "Urval av frågor"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__question_time_limit_reached
msgid "Question Time Limit Reached"
msgstr "Begränsning av frågestunden uppnådd"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__question_type
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__question_type
msgid "Question Type"
msgstr "Fråga Typ"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid "Question type should be empty for these pages: %s"
msgstr "Frågetypen ska vara tom för dessa sidor: %s"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_question_form
#: model:ir.model.fields,field_description:survey.field_survey_question__question_ids
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_ids
#: model:ir.ui.menu,name:survey.menu_survey_question_form1
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Questions"
msgstr "Frågor"

#. module: survey
#: model:ir.ui.menu,name:survey.survey_menu_questions
msgid "Questions & Answers"
msgstr "Frågor och svar"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__triggering_question_ids
msgid ""
"Questions containing the triggering answer(s) to display the current "
"question."
msgstr ""
"Frågor som innehåller det/de utlösande svaret/svaren för att visa den "
"aktuella frågan."

#. module: survey
#: model:survey.survey,title:survey.survey_demo_quiz
msgid "Quiz about our Company"
msgstr "Frågesport om vårt företag"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_success
msgid "Quizz Passed"
msgstr "Quizzen godkänd"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Quizz passed"
msgstr "Quizz godkänd"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/radio_selection_with_filter/radio_selection_field_with_filter.js:0
msgid "Radio for Selection With Filter"
msgstr "Radio för urval med filter"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_selection__random
msgid "Randomized per Section"
msgstr "Randomiserad per sektion"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Rarely (1-3 times per month)"
msgstr "Sällan (1-3 gånger per månad)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__rating_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__rating_ids
msgid "Ratings"
msgstr "Betyg"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__session_state__ready
msgid "Ready"
msgstr "Redo"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Ready to change the way you <b>gather data</b>?"
msgstr "Är du redo att förändra ditt sätt att <b>samla in data</b>?"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Ready to test? Pick a sample or create one from scratch..."
msgstr "Redo att testa? Välj ett exempel eller skapa ett från grunden..."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__partner_ids
msgid "Recipients"
msgstr "Mottagare"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Red Pen"
msgstr "Röd penna"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_count
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Registered"
msgstr "Registrerad"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__render_model
msgid "Rendering Model"
msgstr "Renderingsmodell"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Reopen"
msgstr "Återuppta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_users_login_required
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_login_required
msgid "Require Login"
msgstr "Kräv inloggning"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_success_min
msgid "Required Score (%)"
msgstr "Erforderlig poäng (%)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_text
msgid "Resend Comment"
msgstr "Skicka tillbaka kommentaren"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Resend Invitation"
msgstr "Skicka om inbjudan"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_invite__existing_mode__resend
msgid "Resend invite"
msgstr "Skicka om inbjudan på nytt"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__user_id
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Responsible"
msgstr "Ansvarig"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_user_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__activity_user_id
msgid "Responsible User"
msgstr "Ansvarig användare"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__restrict_user_ids
msgid "Restricted to"
msgstr "Begränsade till"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "Retry"
msgstr "Försök igen"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Review your answers"
msgstr "Granska dina svar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_speed_rating
msgid "Reward quick answers"
msgstr "Belöna snabba svar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "Rewards for challenges"
msgstr "Belöningar för utmaningar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__matrix_row_id
msgid "Row answer"
msgstr "Svar på raden"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row1"
msgstr "Rad 1"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row2"
msgstr "Rad 2"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row3"
msgstr "Rad 3"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Rows"
msgstr "Rader"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_sms_error
#: model:ir.model.fields,field_description:survey.field_survey_user_input__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS leveransfel"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug4
msgid "Salicaceae"
msgstr "Videväxter"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__save_as_email
msgid "Save as user email"
msgstr "Spara som användarens e-postadress"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__save_as_nickname
msgid "Save as user nickname"
msgstr "Spara som användarens smeknamn"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__scale
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Scale"
msgstr "Trappor"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_max_label
msgid "Scale Maximum Label"
msgstr "Skala Max Etikett"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_max
msgid "Scale Maximum Value"
msgstr "Skala Maxvärde"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Scale Maximum Value (0 to 10)"
msgstr "Skala Maxvärde (0 till 10)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_mid_label
msgid "Scale Middle Label"
msgstr "Skala Mitt Etikett"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_min_label
msgid "Scale Minimum Label"
msgstr "Skala Minimum Etikett"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scale_min
msgid "Scale Minimum Value"
msgstr "Skala Minsta värde"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Scale Minimum Value (0 to 10)"
msgstr "Skala Minsta värde (0 till 10)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_scale
msgid "Scale value"
msgstr "Skalvärde"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug3
msgid "Sciences"
msgstr "Vetenskaper"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_score
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__answer_score
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_score
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Score"
msgstr "Poäng"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_percentage
msgid "Score (%)"
msgstr "Poäng (%)"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_score
msgid "Score value for a correct answer to this question."
msgstr "Poängvärde för ett korrekt svar på denna fråga."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_scored_question
msgid "Scored"
msgstr "Uppnådde"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_type
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_type
msgid "Scoring"
msgstr "Poängsättning"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scoring_type
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__scoring_type
msgid "Scoring Type"
msgstr "Typ av poängsättning"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_with_answers_after_page
msgid "Scoring with answers after each page"
msgstr "Poängsättning med svar efter varje sida"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_with_answers
msgid "Scoring with answers at the end"
msgstr "Poängsättning med svar i slutet"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_without_answers
msgid "Scoring without answers"
msgstr "Poäng utan svar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
msgid "Search Label"
msgstr "Sök Etikett"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Search Question"
msgstr "Fråga om sökning"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Search Survey User Inputs"
msgstr "Sökundersökning Användarinput"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
msgid "Search User input lines"
msgstr "Sök Användarens inmatningsrader"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__page_id
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Section"
msgstr "Sektion"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_and_page_ids
msgid "Sections and Questions"
msgstr "Avsnitt och frågor"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "See results"
msgstr "Se resultat"

#. module: survey
#: model_terms:survey.survey,description_done:survey.survey_demo_food_preferences
msgid "See you soon!"
msgstr "Vi ses snart igen!"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_3
msgid "Select all the available customizations for our Customizable Desk"
msgstr ""
"Välj alla tillgängliga anpassningar för vårt anpassningsbara skrivbord"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_2
msgid "Select all the existing products"
msgstr "Välj alla befintliga produkter"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_2
msgid "Select all the products that sell for $100 or more"
msgstr "Välj alla produkter som säljs för 100 dollar eller mer"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q3
msgid "Select trees that made more than 20K sales this year"
msgstr "Välj träd som sålde mer än 20 000 i år"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Send"
msgstr "Skicka"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__send_email
msgid "Send Email"
msgstr "Skicka e-post"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Send by Email"
msgstr "Skicka med e-post"

#. module: survey
#: model:mail.template,description:survey.mail_template_certification
msgid "Sent to participant if they succeeded the certification"
msgstr "Skickas till deltagaren om de klarat certifieringen"

#. module: survey
#: model:mail.template,description:survey.mail_template_user_input_invite
msgid "Sent to participant when you share a survey"
msgstr "Skickas till deltagaren när du delar en undersökning"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__sequence
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__question_sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_code
msgid "Session Code"
msgstr "Sessionskod"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_link
msgid "Session Link"
msgstr "Sessionslänk"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_state
msgid "Session State"
msgstr "Session Status"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_session_code_unique
msgid "Session code should be unique"
msgstr "Sessionskoden ska vara unik"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug1
msgid "Shanghai"
msgstr "Shanghai"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Share"
msgstr "Dela"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "Share a Survey"
msgstr "Dela en undersökning"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_table_pagination
msgid "Show All"
msgstr "Visa alla"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comments_allowed
msgid "Show Comments Field"
msgstr "Visa kommentarsfältet"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Show Correct Answer(s)"
msgstr "Visa korrekta svar"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Show Final Leaderboard"
msgstr "Visa slutlig ledartavla"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Show Leaderboard"
msgstr "Visa ledartavla"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
msgid "Show Results"
msgstr "Resultat av föreställningen"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_show_leaderboard
msgid "Show Session Leaderboard"
msgstr "Visa sessionens topplista"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Show all records which has next action date is before today"
msgstr "Visa alla poster som har nästa händelse före idag"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Show them slides with a ton of text they need to read fast"
msgstr "Visa dem bilder med massor av text som de måste läsa snabbt"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__char_box
msgid "Single Line Text Box"
msgstr "Enradig textruta"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__skipped
#: model_terms:ir.ui.view,arch_db:survey.survey_page_print
msgid "Skipped"
msgstr "Överhoppade"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q3_sug2
msgid "Soft"
msgstr "Mjuk"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "Some emails you just entered are incorrect: %s"
msgstr "Några av de e-postmeddelanden du just angav är felaktiga: %s"

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p1
msgid ""
"Some general information about you. It will be used internally for "
"statistics only."
msgstr ""
"Viss allmän information om dig. Den kommer endast att användas internt för "
"statistik."

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p2
msgid "Some questions about our company. Do you really know us?"
msgstr "Några frågor om vårt företag. Känner du verkligen till oss?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "Someone just participated in \"%(survey_title)s\"."
msgstr "Någon har just deltagit i \"%(survey_title)s\"."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_header
msgid "Sorry, no one answered this survey yet."
msgstr "Tyvärr har ingen svarat på denna enkät ännu."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Sorry, you have not been fast enough."
msgstr "Tyvärr har du inte varit tillräckligt snabb."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug4
msgid "South America"
msgstr "Sydamerika"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug4
msgid "South Korea"
msgstr "Sydkorea"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug3
msgid "Space stations"
msgstr "Rymdstationer"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Speak softly so that they need to focus to hear you"
msgstr "Tala tyst så att de behöver fokusera för att höra dig"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Speak too fast"
msgstr "Tala för fort"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug1
msgid "Spring"
msgstr "Fjädrande"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#: model:survey.question,title:survey.survey_demo_burger_quiz_p1
msgid "Start"
msgstr "Start"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Start Certification"
msgstr "Starta din certifiering"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Start Live Session"
msgstr "Starta direktsänd session"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Start Survey"
msgstr "Starta enkäten"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__start_datetime
msgid "Start date and time"
msgstr "Startdatum och -tid"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__state
msgid "Status"
msgstr "Status"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_state
#: model:ir.model.fields,help:survey.field_survey_user_input__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status baserad på aktiviteter\n"
"Försenade: Leveranstidpunkten har passerat\n"
"Idag: Aktivitetsdatum är idag\n"
"Kommande: Framtida aktiviteter."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q4_sug1
msgid "Steak with french fries"
msgstr "Biff med pommes frites"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row2
msgid "Strawberries"
msgstr "Jordgubbar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__subject
msgid "Subject"
msgstr "Ämne"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Subject..."
msgstr "Ärendemening..."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Submit"
msgstr "Skicka"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__success_count
msgid "Success"
msgstr "Framgång"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__success_ratio
msgid "Success Ratio (%)"
msgstr "Framgångskvot (%)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Success rate"
msgstr "Framgångsgrad"

#. module: survey
#: model:ir.actions.act_window,name:survey.survey_question_answer_action
#: model:ir.ui.menu,name:survey.menu_survey_label_form1
msgid "Suggested Values"
msgstr "Föreslagna värden"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__suggested_answer_id
msgid "Suggested answer"
msgstr "Förslag till svar"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_answer_value_not_empty
msgid ""
"Suggested answer value must not be empty (a text and/or an image must be "
"provided)."
msgstr ""
"Det föreslagna svarsvärdet får inte vara tomt (en text och/eller en bild "
"måste anges)."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value
msgid "Suggested value"
msgstr "Föreslaget värde"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__suggestion
msgid "Suggestion"
msgstr "Förslag"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug2
msgid "Summer"
msgstr "Sommar"

#. module: survey
#: model:ir.model,name:survey.model_survey_survey
#: model:ir.model.fields,field_description:survey.field_gamification_badge__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_question__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__survey_id
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__survey_type__survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_activity
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_tree
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Survey"
msgstr "Enkät"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_access_error
msgid "Survey Access Error"
msgstr "Fel vid åtkomst till undersökningen"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_view_tree
msgid "Survey Answer Line"
msgstr "Svarsrad"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_first_submitted
msgid "Survey First Submitted"
msgstr "Undersökning först inlämnad"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_gamification_badge__survey_ids
msgid "Survey Ids"
msgstr "Undersökningsnummer"

#. module: survey
#: model:ir.model,name:survey.model_survey_invite
msgid "Survey Invitation Wizard"
msgstr "Guiden för inbjudan till enkätundersökningar"

#. module: survey
#: model:ir.model,name:survey.model_survey_question_answer
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_tree
msgid "Survey Label"
msgstr "Undersökningsetikett"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Survey Link"
msgstr "Länk till undersökningen"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "Survey Participant"
msgstr "Deltagare i undersökningen"

#. module: survey
#: model:ir.model,name:survey.model_survey_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_tree
msgid "Survey Question"
msgstr "Enkätfråga"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Survey Time Limit"
msgstr "Tidsgräns för undersökningen"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_time_limit_reached
msgid "Survey Time Limit Reached"
msgstr "Tidsgränsen för undersökningen har uppnåtts"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__title
msgid "Survey Title"
msgstr "Enkättitel"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__survey_type
msgid "Survey Type"
msgstr "Typ av undersökning"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_start_url
msgid "Survey URL"
msgstr "URL för undersökning"

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input
msgid "Survey User Input"
msgstr "Enkät användarsvar"

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input_line
msgid "Survey User Input Line"
msgstr "Linje för användarinmatning i undersökningen"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_tree
msgid "Survey User inputs"
msgstr "Undersökning Användarinput"

#. module: survey
#: model:mail.template,name:survey.mail_template_certification
msgid "Survey: Certification Success"
msgstr "Undersökning: Framgång med certifiering"

#. module: survey
#: model:mail.template,name:survey.mail_template_user_input_invite
msgid "Survey: Invite"
msgstr "Undersökning: Bjud in"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_form
#: model:ir.ui.menu,name:survey.menu_survey_form
#: model:ir.ui.menu,name:survey.menu_surveys
msgid "Surveys"
msgstr "Undersökningar"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug3
msgid "Takaaki Kajita"
msgstr "Takaaki Kajita"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "Take Again"
msgstr "Försök igen"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_kanban
msgid "Test"
msgstr "Testa"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__test_entry
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Test Entry"
msgstr "Testsvar"

#. module: survey
#: model_terms:survey.question,description:survey.vendor_certification_page_3
msgid "Test your knowledge of our policies."
msgstr "Testa dina kunskaper om våra policyer."

#. module: survey
#: model_terms:survey.question,description:survey.vendor_certification_page_2
msgid "Test your knowledge of our prices."
msgstr "Testa din kunskap om våra priser."

#. module: survey
#: model_terms:survey.question,description:survey.vendor_certification_page_1
msgid "Test your knowledge of your products!"
msgstr "Testa din kunskap om dina produkter!"

#. module: survey
#: model_terms:survey.survey,description:survey.vendor_certification
msgid "Test your vendor skills!"
msgstr "Testa dina kunskaper om leverantörer!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Tests Only"
msgstr "Endast tester"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__char_box
msgid "Text"
msgstr "Text"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_char_box
msgid "Text answer"
msgstr "Textsvar"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Thank you for your participation, hope you had a blast!"
msgstr "Tack för ert deltagande, hoppas att ni hade jättekul!"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Thank you very much for your feedback. We highly value your opinion!"
msgstr "Tack så mycket för din feedback. Vi sätter stort värde på din åsikt!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Thank you!"
msgstr "Tack!"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Thank you. We will contact you soon."
msgstr "Tack för att du kom. Vi kommer att kontakta dig inom kort."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"The access of the following surveys is restricted. Make sure their responsible still has access to it: \n"
"%(survey_names)s\n"
msgstr ""
"Tillgången till följande undersökningar är begränsad. Kontrollera att deras ansvariga fortfarande har åtkomst till dem:\n"
"%(survey_names)s\n"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "The answer must be in the right type"
msgstr "Svaret måste vara av rätt typ"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: model_terms:ir.ui.view,arch_db:survey.question_container
msgid "The answer you entered is not valid."
msgstr "Svaret du angav är inte giltig."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_attempts_limit_check
msgid ""
"The attempts limit needs to be a positive number if the survey has a limited"
" number of attempts."
msgstr ""
"Begränsningen av antalet försök måste vara ett positivt tal om enkäten har "
"ett begränsat antal försök."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_badge_uniq
msgid "The badge for each survey should be unique!"
msgstr "Utmärkelsen för varje enkät ska vara unik!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row4
msgid "The checkout process is clear and secure"
msgstr "Utcheckningsprocessen är tydlig och säker"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_print
msgid "The correct answer was:"
msgstr "Det rätta svaret var:"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_question_id
msgid "The current question of the survey session."
msgstr "Den aktuella frågan i enkäten."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__description
msgid ""
"The description will be displayed on the home page of the survey. You can "
"use this to give the purpose and guidelines to your candidates before they "
"start it."
msgstr ""
"Beskrivningen kommer att visas på enkätens startsida. Du kan använda detta "
"för att ge dina kandidater syfte och riktlinjer innan de påbörjar enkäten."

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "The following customers have already received an invite"
msgstr "Följande kunder har redan fått en inbjudan"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "The following emails have already received an invite"
msgstr "Följande e-post har redan fått en inbjudan"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid ""
"The following recipients have no user account: %s. You should create user "
"accounts for them or allow external signup in configuration."
msgstr ""
"Följande mottagare har inget användarkonto: %s. Du bör skapa användarkonton "
"för dem eller tillåta extern registrering i konfigurationen."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row1
msgid "The new layout and design is fresh and up-to-date"
msgstr "Den nya layouten och designen är fräsch och uppdaterad"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "The page you were looking for could not be authorized."
msgstr "Sidan du letade efter kunde inte godkännas."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_scoring_success_min_check
msgid "The percentage of success has to be defined between 0 and 100."
msgstr "Procentandelen för framgång måste definieras mellan 0 och 100."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_time_limited
msgid "The question is limited in time"
msgstr "Frågan är tidsbegränsad"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scale
msgid ""
"The scale must be a growing non-empty range between 0 and 10 (inclusive)"
msgstr ""
"Skalan måste vara ett växande icke-tomt intervall mellan 0 och 10 "
"(inklusive)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "The session did not start yet."
msgstr "Sessionen har inte startat ännu."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "The session will begin automatically when the host starts."
msgstr "Sessionen startar automatiskt när värden startar."

#. module: survey
#. odoo-python
#: code:addons/survey/controllers/main.py:0
msgid "The survey has already started."
msgstr "Enkäten har redan inletts."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__is_time_limited
msgid "The survey is limited in time"
msgstr "Enkäten är tidsbegränsad"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_question_start_time
msgid ""
"The time at which the current question has started, used to handle the timer"
" for attendees."
msgstr ""
"Den tid då den aktuella frågan började, används för att hantera timern för "
"deltagarna."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_time_limit_check
msgid ""
"The time limit needs to be a positive number if the survey is time limited."
msgstr "Tidsgränsen måste vara ett positivt tal om enkäten är tidsbegränsad."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row3
msgid "The tool to compare the products is useful to make a choice"
msgstr ""
"Verktyget för att jämföra produkterna är användbart för att göra ett val"

#. module: survey
#. odoo-python
#: code:addons/survey/controllers/main.py:0
msgid "The user has not succeeded the certification"
msgstr "Användaren har inte klarat certifieringen"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "There was an error during the validation of the survey."
msgstr "Ett fel uppstod vid validering av enkäten."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "They are a default answer, used if the participant skips the question"
msgstr "De är ett standardsvar som används om deltagaren hoppar över frågan"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"They are technical parameters that guarantees the responsiveness of the page"
msgstr "De är tekniska parametrar som garanterar sidans responsivitet"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "This answer cannot be overwritten."
msgstr "Detta svar kan inte skrivas över."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/tests/test_survey.py:0
msgid "This answer must be an email address"
msgstr "Svaret måste vara en e-postadress"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
msgid "This answer must be an email address."
msgstr "Svaret måste vara en e-postadress."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_code
msgid ""
"This code will be used by your attendees to reach your session. Feel free to"
" customize it however you like!"
msgstr ""
"Denna kod kommer att användas av deltagarna för att komma till din session. "
"Du får gärna anpassa den som du vill!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
msgid "This is a Test Survey Entry."
msgstr "Detta är en Test Survey Entry."

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/static/src/js/survey_form.js:0
#: code:addons/survey/tests/test_survey.py:0
msgid "This is not a date"
msgstr "Detta är inte ett datum"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/tests/test_survey.py:0
msgid "This is not a number"
msgstr "Detta är inte en siffra"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__description_done
msgid "This message will be displayed when survey is completed"
msgstr "Detta meddelande visas när enkäten är avslutad"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
#: model_terms:ir.ui.view,arch_db:survey.question_container
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "This question requires an answer."
msgstr "Obligatorisk fråga."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_print
msgid "This question was skipped"
msgstr "Denna fråga hoppades över"

#. module: survey
#: model_terms:survey.question,description:survey.survey_feedback_p1
msgid ""
"This section is about general information about you. Answering them helps "
"qualifying your answers."
msgstr ""
"Detta avsnitt handlar om allmän information om dig. Genom att besvara dem "
"kan du kvalificera dina svar."

#. module: survey
#: model_terms:survey.question,description:survey.survey_feedback_p2
msgid "This section is about our eCommerce experience itself."
msgstr "Det här avsnittet handlar om vår e-handelsupplevelse i sig."

#. module: survey
#: model_terms:survey.survey,description:survey.survey_demo_quiz
msgid ""
"This small quiz will test your knowledge about our Company. Be prepared!"
msgstr ""
"Detta lilla quiz kommer att testa dina kunskaper om vårt företag. Var "
"beredd!"

#. module: survey
#: model_terms:survey.survey,description:survey.survey_feedback
msgid ""
"This survey allows you to give a feedback about your experience with our products.\n"
"    Filling it helps us improving your experience."
msgstr ""
"Denna enkät ger dig möjlighet att ge feedback om din erfarenhet av våra produkter.\n"
"    Genom att fylla i den hjälper du oss att förbättra din upplevelse."

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid ""
"This survey does not allow external people to participate. You should create"
" user accounts or update survey access mode accordingly."
msgstr ""
"Denna enkät tillåter inte att externa personer deltar. Du bör skapa "
"användarkonton eller uppdatera tillträdesläget för undersökningen i enlighet"
" med detta."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_closed_expired
msgid "This survey is now closed. Thank you for your interest!"
msgstr "Denna undersökning är nu avslutad. Tack för ditt intresse!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "This survey is open only to registered people. Please"
msgstr "Denna enkät är endast öppen för registrerade personer. Vänligen"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Time & Scoring"
msgstr "Tid och poängräkning"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "Time Limit"
msgstr "Tidsgräns"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Time Limit (seconds)"
msgstr "Tidsgräns (sekunder)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__time_limit
msgid "Time limit (minutes)"
msgstr "Tidsgräns (minuter)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__time_limit
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_speed_rating_time_limit
msgid "Time limit (seconds)"
msgstr "Tidsgräns (sekunder)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Time limit for this certification:"
msgstr "Tidsgräns för denna certifiering:"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Time limit for this survey:"
msgstr "Tidsgräns för denna undersökning:"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Time limits are only available for Live Sessions."
msgstr "Tidsgränser är endast tillgängliga för livesessioner."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__title
msgid "Title"
msgstr "Titel"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "To join:"
msgstr "För att delta:"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid ""
"To take this survey, please close all other tabs on <strong class=\"text-"
"danger\"/>."
msgstr ""
"För att fylla i denna enkät, vänligen stäng alla andra tabbar på  <strong "
"class=\"text-danger\"/>."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Today Activities"
msgstr "Välj ledighetstyp"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug2
msgid "Tokyo"
msgstr "Tokyo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
msgid "Top User Responses"
msgstr "Bästa svaren från användarna"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_total
msgid "Total Score"
msgstr "Totalt antal poäng"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col4
msgid "Totally agree"
msgstr "Håller helt och hållet med"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col1
msgid "Totally disagree"
msgstr "Håller inte alls med"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4
msgid "Trees"
msgstr "Träd"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__triggering_answer_ids
msgid "Triggering Answers"
msgstr "Utlösande svar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__triggering_question_ids
msgid "Triggering Questions"
msgstr "Utlösande frågor"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/views/widgets/survey_question_trigger/survey_question_trigger.js:0
msgid ""
"Triggers based on the following questions will not work because they are positioned after this question:\n"
"\"%s\"."
msgstr ""
"Triggers som baseras på följande frågor kommer inte att fungera eftersom de är placerade efter denna fråga:\n"
"\"%s\"."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Type"
msgstr "Typ"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_exception_decoration
#: model:ir.model.fields,help:survey.field_survey_user_input__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ av undantagsaktivitet som har registrerats."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__suggested_answer_ids
msgid "Types of answers"
msgstr "Svarstyper"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug2
msgid "Ulmaceae"
msgstr "Almväxter"

#. module: survey
#. odoo-python
#: code:addons/survey/wizard/survey_invite.py:0
msgid "Unable to post message, please configure the sender's email address."
msgstr ""
"Det går inte att skicka meddelandet, vänligen konfigurera avsändarens "
"e-postadress."

#. module: survey
#. odoo-javascript
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
msgid "Unanswered"
msgstr "Obesvarade"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_user_input.py:0
msgid "Uncategorized"
msgstr "Okategoriserad"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_2
msgid "Underpriced"
msgstr "Underpris"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Unfortunately, you have failed the test."
msgstr "Tyvärr, du klarade inte testet."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug3
msgid "Unique"
msgstr "Unik"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Unlikely"
msgstr "Osannolikt"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Upcoming Activities"
msgstr "Kommande aktiviteter"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Use a fun visual support, like a live presentation"
msgstr "Använd ett roligt visuellt stöd, som en livepresentation"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Use humor and make jokes"
msgstr "Använda humor och skämta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Use template"
msgstr "Använd mall"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Use the breadcrumbs to quickly go back to the dashboard."
msgstr ""
"Använd brödsmulorna för att snabbt gå tillbaka till instrumentpanelen."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__description
msgid ""
"Use this field to add additional explanations about your question or to "
"illustrate it with pictures or a video"
msgstr ""
"Använd det här fältet för att lägga till ytterligare förklaringar till din "
"fråga eller för att illustrera den med bilder eller en video"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__random_questions_count
msgid ""
"Used on randomized sections to take X random questions from all the "
"questions of that section."
msgstr ""
"Används i slumpmässiga avsnitt för att ta X slumpmässiga frågor från alla "
"frågor i det avsnittet."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug2
msgid "Useful"
msgstr "Användbar"

#. module: survey
#: model:res.groups,name:survey.group_survey_user
msgid "User"
msgstr "Användare"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "User Choice"
msgstr "Användarens val"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__user_input_id
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
msgid "User Input"
msgstr "Användarinput"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date_or_datetime
#: model_terms:ir.ui.view,arch_db:survey.question_result_text
msgid "User Responses"
msgstr "Användarsvar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_form
msgid "User input line details"
msgstr "Information om användarens inmatningslinje"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__user_input_ids
msgid "User responses"
msgstr "Användarsvar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_can_go_back
msgid "Users can go back"
msgstr "Användare kan backa"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_users_can_signup
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_can_signup
msgid "Users can signup"
msgstr "Användare kan registrera sig"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_required
msgid "Validate entry"
msgstr "Bekräfta inmatningen"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/question_page/question_page_one2many_field.js:0
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_error_msg
msgid "Validation Error"
msgstr "Verifieringsfel"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value_label
msgid "Value Label"
msgstr "Värdeetikett"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug2
msgid "Vegetables"
msgstr "Grönsaker"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q3_sug2
msgid "Vegetarian burger"
msgstr "Vegetarisk hamburgare"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q3_sug1
msgid "Vegetarian pizza"
msgstr "Vegetarisk pizza"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_1
msgid "Very underpriced"
msgstr "Mycket underpris"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug3
msgid "Vietnam"
msgstr "Vietnam"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_open
msgid "Waiting for attendees..."
msgstr "Väntar på deltagare..."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"We have registered your answer! Please wait for the host to go to the next "
"question."
msgstr ""
"Vi har registrerat ditt svar! Vänligen vänta på att värden går vidare till "
"nästa fråga."

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p4
msgid ""
"We like to say that the apple doesn't fall far from the tree, so here are "
"trees."
msgstr ""
"Vi brukar säga att äpplet inte faller långt från trädet, så här är träden."

#. module: survey
#: model_terms:survey.question,description:survey.survey_demo_quiz_p5
msgid "We may be interested by your input."
msgstr "Vi kan vara intresserade av din input."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__website_message_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__website_message_ids
msgid "Website Messages"
msgstr "Webbplatsmeddelanden"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__website_message_ids
#: model:ir.model.fields,help:survey.field_survey_user_input__website_message_ids
msgid "Website communication history"
msgstr "Webbplatsens kommunikationshistorik"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"Welcome to this Odoo certification. You will receive 2 random questions out "
"of a pool of 3."
msgstr ""
"Välkommen till denna Odoo-certifiering. Du kommer att få 2 slumpmässiga "
"frågor från en pool av 3."

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_5
msgid ""
"What day and time do you think most customers are most likely to call "
"customer service (not rated)?"
msgstr ""
"Vilken dag och tid tror du att de flesta kunder är mest benägna att ringa "
"till kundtjänsten (ej betygsatt)?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_4
msgid ""
"What day to you think is best for us to start having an annual sale (not "
"rated)?"
msgstr ""
"Vilken dag tycker du är bäst att vi ska börja en årlig utförsäljning (ej "
"betygsatt)?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q2
msgid "What do you think about our new eCommerce?"
msgstr "Vad tycker du om vår nya e-handel?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_3
msgid "What do you think about our prices (not rated)?"
msgstr "Vad tycker du om våra priser (ej betygsatt)?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p5_q1
msgid "What do you think about this survey?"
msgstr "Vad tycker du om den här undersökningen?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "What does \"ODOO\" stand for?"
msgstr "Vad står \"ODOO\" för?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "What does one need to get to pass an Odoo Survey?"
msgstr "Vad behöver man för att klara en Odoo undersökning?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "What is a frequent mistake public speakers do?"
msgstr "Vad är ett vanligt misstag som offentliga talare gör?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "What is the best way to catch the attention of an audience?"
msgstr "Vilket är det bästa sättet att fånga en åhörares uppmärksamhet?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q2
msgid "What is the biggest city in the world?"
msgstr "Vilken är den största staden i världen?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q1
msgid "What is your email?"
msgstr "Vad är din e-postadress?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q2
msgid "What is your nickname?"
msgstr "Vad är ditt smeknamn?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q2
msgid "What is, approximately, the critical mass of plutonium-239?"
msgstr "Vad är ungefär den kritiska massan för plutonium-239?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q1
msgid "When did Genghis Khan die?"
msgstr "När dog Djingis Khan?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q2
msgid "When did precisely Marc Demo crop its first apple tree?"
msgstr "När odlade just Marc Demo sitt första äppelträd?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q6
msgid "When do you harvest those fruits"
msgstr "När skördar du dessa frukter"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q1
msgid "When is Mitchell Admin born?"
msgstr "När föddes Mitchell Admin?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q2
msgid "When is your date of birth?"
msgstr "När är ditt födelsedatum?"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/tours/survey_tour.js:0
msgid "Whenever you pick an answer, Odoo saves it for you."
msgstr "När du väljer ett svar sparar Odoo det åt dig."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q3
msgid "Where are you from?"
msgstr "Var kommer du ifrån?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q1
msgid "Where do you live?"
msgstr "Var bor du?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_show_leaderboard
msgid ""
"Whether or not we want to show the attendees leaderboard for this survey."
msgstr "Om vi vill visa deltagarnas topplista för den här enkäten eller inte."

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q1
msgid "Which Musician is not in the 27th Club?"
msgstr "Vilken musiker är inte med i den 27:e klubben?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q1
msgid "Which category does a tomato belong to"
msgstr "Vilken kategori tillhör en tomat"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q3
msgid "Which is the highest volcano in Europe?"
msgstr "Vilken är den högsta vulkanen i Europa?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q1
msgid "Which of the following words would you use to describe our products?"
msgstr ""
"Vilket av följande ord skulle du använda för att beskriva våra produkter?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q2
msgid "Which of the following would you use to pollinate"
msgstr "Vilka av följande skulle du använda för att pollinera"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q2
msgid "Which painting/drawing was not made by Pablo Picasso?"
msgstr "Vilken målning/teckning gjordes inte av Pablo Picasso?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q3
msgid "Which quote is from Jean-Claude Van Damme"
msgstr "Vilket citat är från Jean-Claude Van Damme"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1
msgid "Who are you?"
msgstr "Vem är du?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q2
msgid "Who is the architect of the Great Pyramid of Giza?"
msgstr "Vem är arkitekten bakom den stora pyramiden i Giza?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q1
msgid ""
"Who received a Nobel prize in Physics for the discovery of neutrino "
"oscillations, which shows that neutrinos have mass?"
msgstr ""
"Vem fick Nobelpriset i fysik för upptäckten av neutrinoscillationer, som "
"visar att neutriner har massa?"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"Why should you consider making your presentation more fun with a small quiz?"
msgstr ""
"Varför ska du överväga att göra din presentation roligare med ett litet "
"quiz?"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_3
msgid "Width"
msgstr "Bredd"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug4
msgid "Willard S. Boyle"
msgstr "Willard S. Boyle"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug4
msgid "Winter"
msgstr "Vinter"

#. module: survey
#: model:survey.question,title:survey.survey_demo_food_preferences_q2
msgid "Would you prefer a veggie meal if possible?"
msgstr "Skulle du föredra en vegetarisk måltid om möjligt?"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD\n"
"                                        <i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"
msgstr ""
"ÅÅÅÅ-MM-DD\n"
"                                       <i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD hh:mm:ss\n"
"                                        <i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"
msgstr ""
"ÅÅÅÅ-MM-DD hh:mm:ss\n"
"                                       <i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid "Yellow Pen"
msgstr "Gul penna"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q1_sug1
#: model:survey.question.answer,value:survey.survey_demo_food_preferences_q2_sug1
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug1
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_1_choice_2
msgid "Yes"
msgstr "Ja"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q3_sug1
msgid "Yes, that's the only thing a human eye can see."
msgstr "Ja, det är det enda som ett mänskligt öga kan se."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_certification_check
msgid ""
"You can only create certifications for surveys that have a scoring "
"mechanism."
msgstr ""
"Du kan bara skapa certifieringar för enkäter som har en "
"poängsättningsmekanism."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_user_input
msgid ""
"You can share your links through different means: email, invite shortcut, "
"live presentation, ..."
msgstr ""
"Du kan dela dina länkar på olika sätt: e-post, genväg, live-presentation, "
"..."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid ""
"You cannot delete questions from surveys \"%(survey_names)s\" while live "
"sessions are in progress."
msgstr ""
"Du kan inte ta bort frågor från enkäter \"%(survey_names)s\" medan live-"
"sessioner pågår."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"You cannot send an invitation for a \"One page per section\" survey if the "
"survey has no sections."
msgstr ""
"Du kan inte skicka en inbjudan till en enkät med \"en sida per avsnitt\" om "
"enkäten inte har några avsnitt."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid ""
"You cannot send an invitation for a \"One page per section\" survey if the "
"survey only contains empty sections."
msgstr ""
"Du kan inte skicka en inbjudan till en enkät med \"En sida per avsnitt\" om "
"enkäten endast innehåller tomma avsnitt."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "You cannot send an invitation for a survey that has no questions."
msgstr ""
"Du kan inte skicka en inbjudan till en enkät som inte innehåller några "
"frågor."

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey.py:0
msgid "You cannot send invitations for closed surveys."
msgstr "Du kan inte skicka inbjudningar till stängda enkäter."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "You received the badge"
msgstr "Du fick märket"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "You scored"
msgstr "Du uppnådde"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p5
msgid "Your feeling"
msgstr "Din känsla"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_survey_template.py:0
msgid ""
"Your responses will help us improve our product range to serve you even "
"better."
msgstr ""
"Dina svar kommer att hjälpa oss att förbättra vårt produktsortiment så att "
"vi kan hjälpa dig ännu bättre."

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "Zoom in"
msgstr "Zooma in"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "Zoom out"
msgstr "Zooma ut"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "Zoomed Image"
msgstr "In zoomad bild"

#. module: survey
#. odoo-python
#: code:addons/survey/models/survey_question.py:0
msgid "[Question Title]"
msgstr "[Frågans titel]"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "ans"
msgstr "svar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "answered"
msgstr "besvarade"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "attempts"
msgstr "försök"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/xml/survey_image_zoomer_templates.xml:0
msgid "close"
msgstr "stäng"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid ""
"e.g.  'Rick Sanchez' <<EMAIL>>, <EMAIL>"
msgstr ""
"t.ex. \"Rick Sanchez\" <<EMAIL>>, <EMAIL>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "e.g. \"Thank you very much for your feedback!\""
msgstr "t.ex. \"Tack så mycket för din feedback!\""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "e.g. \"The following Survey will help us...\""
msgstr "t.ex. \"Följande undersökning kommer att hjälpa oss...\""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "e.g. \"What is the...\""
msgstr "t.ex. \"Vad är...\""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "e.g. 4812"
msgstr "t.ex. 4812"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "e.g. Guidelines, instructions, picture, ... to help attendees answer"
msgstr ""
"t.ex. riktlinjer, instruktioner, bilder, ... för att hjälpa deltagarna att "
"svara"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "e.g. No one can solve challenges like you do"
msgstr "t.ex. Ingen kan lösa utmaningar på samma sätt som du"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "e.g. Problem Solver"
msgstr "t.ex. Problemlösare"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "e.g. Satisfaction Survey"
msgstr "t.ex. Nöjdhetsundersökning"

#. module: survey
#: model:survey.question,question_placeholder:survey.survey_demo_quiz_p1_q1
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "log in"
msgstr "logga in"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "minutes"
msgstr "minuter"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "of"
msgstr "av"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_general
msgid "of achievement"
msgstr "av prestationer"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
msgid "or press CTRL+Enter"
msgstr "eller tryck på CTRL+Enter"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
msgid "or press Enter"
msgstr "eller tryck på Enter"

#. module: survey
#. odoo-javascript
#: code:addons/survey/static/src/js/survey_form.js:0
msgid "or press ⌘+Enter"
msgstr "eller tryck på ⌘+Enter"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "pages"
msgstr "sidor"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_closed_expired
msgid "survey expired"
msgstr "enkäten har avslutats"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "survey is empty"
msgstr "undersökningen är tom"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "this page"
msgstr "den här sidan"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_form
msgid "to"
msgstr "till"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"⚠️ This question is positioned before some or all of its triggers and could "
"be skipped."
msgstr ""
"⚠️ Denna fråga är placerad före några eller alla av dess triggers och kan "
"hoppas över."
