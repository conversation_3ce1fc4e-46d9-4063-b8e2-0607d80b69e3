<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.CashMoveReceipt">
        <div class="pos-receipt">
            <ReceiptHeader data="props.headerData" />
            <div class="pos-receipt-center-align">
                CASH
                <t t-esc="props.translatedType.toUpperCase()" />
            </div>
            <br />
            <div>
                AMOUNT
                <span t-esc="props.formattedAmount" class="pos-receipt-right-align" />
            </div>
            <div>
                REASON
                <span t-esc="props.reason" class="pos-receipt-right-align" />
            </div>
            <br />
            <div class="pos-receipt-order-data">
                <div><t t-esc="props.date" /></div>
            </div>
        </div>
    </t>

</templates>
