<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="iap_service_snailmail" model="iap.service">
            <field name="name">Snail Mail</field>
            <field name="technical_name">snailmail</field>
            <field name="description">Send your customer invoices and follow-up reports by post, worldwide.</field>
            <field name="unit_name">Stamps</field>
            <field name="integer_balance">True</field>
        </record>
    </data>
</odoo>
