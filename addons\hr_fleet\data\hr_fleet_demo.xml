<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="fleet.vehicle_1" model="fleet.vehicle">
        <field name="driver_employee_id" ref="hr.employee_qdp"/>
    </record>
    <record id="fleet.vehicle_2" model="fleet.vehicle">
        <field name="driver_employee_id" ref="hr.employee_hne"/>
    </record>
    <record id="fleet.vehicle_3" model="fleet.vehicle">
        <field name="driver_employee_id" ref="hr.employee_fpi"/>
    </record>
    <record id="fleet.vehicle_4" model="fleet.vehicle">
        <field name="driver_employee_id" ref="hr.employee_jog"/>
    </record>
    <record id="fleet.vehicle_5" model="fleet.vehicle">
        <field name="driver_employee_id" ref="hr.employee_jep"/>
    </record>

    <!-- recompute driver_employee_id as we assigned a proper work_contact_id to some employees -->
    <function model="fleet.vehicle.assignation.log" name="_compute_driver_employee_id">
        <value model="fleet.vehicle.assignation.log" eval="obj().search([('driver_employee_id', '=', False), ('driver_id', '!=', False)]).ids"/>
    </function>

    <record id="onboarding_fleet_training" model="mail.activity.plan.template">
        <field name="summary">Fleet Training</field>
        <field name="responsible_type">fleet_manager</field>
        <field name="plan_id" ref="hr.onboarding_plan"/>
    </record>

</odoo>
