# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_barcodelookup
# 
# Translators:
# <PERSON><PERSON><PERSON> <alexand<PERSON>@gnugr.org>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <george_taras<PERSON><PERSON>@yahoo.com>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Greek (https://app.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "API Key:"
msgstr "Κλειδί API:"

#. module: product_barcodelookup
#: model:ir.model.fields,field_description:product_barcodelookup.field_res_config_settings__barcodelookup_api_key
msgid "API key"
msgstr "Κλειδί API"

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "Barcode Database"
msgstr "Βάση Δεδομένων Barcode"

#. module: product_barcodelookup
#: model:ir.model.fields,help:product_barcodelookup.field_res_config_settings__barcodelookup_api_key
msgid "Barcode Lookup API Key for create product from barcode."
msgstr "Κλειδί Barcode Lookup API για δημιουργία είδους από barcode."

#. module: product_barcodelookup
#: model:ir.model,name:product_barcodelookup.model_res_config_settings
msgid "Config Settings"
msgstr "Ρυθμίσεις διαμόρφωσης"

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "Create products by scanning using"
msgstr "Δημιουργία προϊόντων με σάρωση χρησιμοποιώντας"

#. module: product_barcodelookup
#: model:ir.model,name:product_barcodelookup.model_product_template
msgid "Product"
msgstr "Είδος"

#. module: product_barcodelookup
#: model:ir.model,name:product_barcodelookup.model_product_product
msgid "Product Variant"
msgstr "Μεταβλητή Είδους"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_8
msgid "age group"
msgstr "ηλικιακή ομάδα"

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "barcodelookup.com"
msgstr "barcodelookup.com"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_6
msgid "brand"
msgstr "μάρκα"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_1
msgid "color"
msgstr "χρώμα"

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "e.g. d7vctmiv2rwgenebha8bxq7irooudn"
msgstr "π.χ. d7vctmiv2rwgenebha8bxq7irooudn"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_2
msgid "gender"
msgstr "φύλο"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_5
msgid "manufacturer"
msgstr "κατασκευαστής"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_3
msgid "material"
msgstr "υλικό "

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_4
msgid "pattern"
msgstr "μοτίβο"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_7
msgid "size"
msgstr "μέγεθος"
