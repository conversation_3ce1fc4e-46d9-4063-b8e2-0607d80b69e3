# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_timesheet
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# Leaanika Randmets, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Anna, 2024
# <PERSON><PERSON><PERSON>, 2024
# JanaAvalah, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:04+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: JanaAvalah, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/sale_order.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    No activities found. Let's start a new one!\n"
"                </p><p>\n"
"                    Track your working hours by projects every day and invoice this time to your customers.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                   Tegevusi ei leitud. Loome ühe!\n"
"                </p><p>\n"
"                    Jälgi oma logitud aega igapäevaselt, logitud read lähevad kliendi arvele!\n"
"                </p>\n"
"            "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/account_move.py:0
#: code:addons/sale_timesheet/models/project_project.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Record timesheets\n"
"                </p><p>\n"
"                    You can register and track your workings hours by project every\n"
"                    day. Every time spent on a project will become a cost and can be re-invoiced to\n"
"                    customers if required.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Registreeri tööaja arvestusleht\n"
"                </p><p>\n"
"                    Saate registreerida ja jälgida oma töötunde projektipõhiselt\n"
"                    iga päev. Iga projektile kulutatud aeg muutub kuluks ja seda saab klientidele vajadusel\n"
"                    uuesti arveldada.\n"
"                </p>\n"
"            "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
msgid ""
"'%(missing_plan_names)s' analytic plan(s) required on the analytic "
"distribution of the sale order item '%(so_line_name)s' linked to the "
"timesheet."
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_sale_page
msgid "- Timesheet product"
msgstr "Ajaarvestuslehe toode"

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "1 Onsite Interview"
msgstr "1 Kohapealne intervjuu"

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "1 Phone Call"
msgstr "1 Telefonikõne"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "12 days / year, including <br>6 of your choice."
msgstr "12 päeva aastas, sealhulgas <br> 6 teie valikul."

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "2 open days"
msgstr "2 lahtiste uste päeva"

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "4 Days after Interview"
msgstr "4 päeva pärast invervjuud"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "<b>Daily Cost: </b>"
msgstr "<b>Päevakulu: </b>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "<b>Unit Price: </b>"
msgstr "<b>Ühiku hind: </b>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid ""
"<i class=\"fa fa-lightbulb-o\"/>\n"
"                        <span>\n"
"                            Define the rate at which an employee's time is billed based on their expertise, skills, or experience.\n"
"                            To bill the same service at a different rate, create separate sales order items.\n"
"                        </span>"
msgstr ""

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "<small><b>READ</b></small>"
msgstr "<small><b>LOE</b></small>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_form_inherit
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning\" title=\"The sales "
"order associated with this timesheet entry has been cancelled.\" "
"invisible=\"sale_order_state != 'cancel'\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning\" title=\"The sales "
"order associated with this timesheet entry has been cancelled.\" "
"invisible=\"sale_order_state != 'cancel'\"/>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_form_inherit
msgid "<span class=\"o_stat_text\">Invoice</span>"
msgstr "<span class=\"o_stat_text\">Arve</span>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.account_invoice_view_form_inherit_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
msgid "<span class=\"o_stat_text\">Recorded</span>"
msgstr "<span class=\"o_stat_text\">Kirja pandud</span>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_form_inherit
msgid "<span class=\"o_stat_text\">Sales Order</span>"
msgstr "<span class=\"o_stat_text\">Müügitellimus</span>"

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "<span class=\"text-muted small\">Days to get an Offer</span>"
msgstr "<span class=\"text-muted small\">Päevad pakkumise saamiseks</span>"

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "<span class=\"text-muted small\">Process</span>"
msgstr "<span class=\"text-muted small\">Protsess</span>"

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "<span class=\"text-muted small\">Time to Answer</span>"
msgstr "<span class=\"text-muted small\">Aega vastamiseks</span>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Amount Due:</strong>"
msgstr "<strong>Kuulub tasumisele:</strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Invoiced:</strong>"
msgstr "<strong>Arveldatud:</strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Invoices:</strong>"
msgstr "<strong>Arved:</strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Sales Order:</strong>"
msgstr "<strong>Müügitellimus:</strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_timesheet_table_inherit
msgid "<strong>Time Remaining on SO: </strong>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "<u>Profitability</u>"
msgstr "<u>Kasumlikkus</u>"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "A full-time position <br>Attractive salary package."
msgstr "Täiskohaga töökoht <br>Atraktiivne palgapakett."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"Vastavalt toote konfiguratsioonile saab tarnitud koguse automaatselt arvutada mehhanismi abil:\n"
"- Käsitsi: kogus määratakse reale käsitsi\n"
"- Analüütiliste kulude alusel: kogus on postitatud kuludest tulenevate koguste summa\n"
"- Ajaarvestus: kogus on müügireaga seotud ülesannetele logitud tundide summa.\n"
"- Laoliikumised: kogus tuleb kinnitatud korjetest\n"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Achieve monthly sales objectives"
msgstr "Saavuta igakuised müügieesmärgid"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Additional languages"
msgstr "Keeled"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Administrative Work"
msgstr "Administratiivne töö"

#. module: sale_timesheet
#: model:account.analytic.account,name:sale_timesheet.account_analytic_account_project_support
#: model:project.project,name:sale_timesheet.project_support
msgid "After-Sales Services"
msgstr "Müügijärgsed teenused"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__allocated_hours
msgid "Allocated Time"
msgstr "Jaotatud aeg"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__amount_to_invoice
msgid "Amount to invoice"
msgstr "Arve summa"

#. module: sale_timesheet
#: model:ir.model.constraint,message:sale_timesheet.constraint_project_sale_line_employee_map_uniqueness_employee
msgid ""
"An employee cannot be selected more than once in the mapping. Please remove "
"duplicate(s) and try again."
msgstr ""
"Töötajat ei saa kaardistamisel valida rohkem kui üks kord. Eemaldage "
"duplikaat(id) ja proovige uuesti."

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analüütiline rida"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__analytic_line_ids
msgid "Analytic lines"
msgstr "Analüütilised read"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid ""
"As an employee of our company, you will <b>collaborate with each department\n"
"                        to create and deploy disruptive products.</b> Come work at a growing company\n"
"                        that offers great benefits with opportunities to moving forward and learn\n"
"                        alongside accomplished leaders. We're seeking an experienced and outstanding\n"
"                        member of staff.\n"
"                        <br><br>\n"
"                        This position is both <b>creative and rigorous</b> by nature you need to think\n"
"                        outside the box. We expect the candidate to be proactive and have a \"get it done\"\n"
"                        spirit. To be successful, you will have solid solving problem skills."
msgstr ""
"Meie ettevõtte töötajana teete <b>koostööd iga osakonnaga\n"
"                         toodete loomiseks ja juurutamiseks.</b> Tulge tööle kasvavasse ettevõttesse\n"
"                         mis pakub suuri eeliseid koos võimalusega edasi liikuda ja õppida\n"
"                         kõrvuti edukate juhtidega. Otsime kogenud ja silmapaistvat inimest\n"
"                         meeskonnaliiget.\\\n"
"                         <br><br>\n"
"                         See ametikoht on oma olemuselt nii <b>loov kui ka range</b>, peate mõtlema\n"
"                         väljaspool kasti. Ootame kandidaadilt proaktiivset suhtumist ja \"teen ära\"\n"
"                         suhtumist. Edu saavutamiseks tulevad kasuks head probleemide lahendamise oskused."

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Autonomy"
msgstr "Autonoomia"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Bachelor Degree or Higher"
msgstr "Bakalaureusekraad või kõrgem"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product_template.py:0
msgid "Based on Timesheets"
msgstr "Tööaja arvestuslehtedel põhinev"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__allow_billable
msgid "Billable"
msgstr "Arveldatav"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__billable_time
msgid "Billable Time"
msgstr "Arveldatav aeg"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__timesheet_invoice_type
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__timesheet_invoice_type
msgid "Billable Type"
msgstr "Arvelduse tüüp"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Billed"
msgstr "Arveldatud"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_manual
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_manual
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed Manually"
msgstr "Arveldatud manuaalselt"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed at a Fixed Price"
msgstr "Fikseeritud hinnaga arveldus"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_fixed
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_fixed
msgid "Billed at a Fixed price"
msgstr "Fikseeritud hinnaga arveldus"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_milestones
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_milestones
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed on Milestones"
msgstr "Arveid esitatakse vahe-eesmärkide kohta"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_time
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_time
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed on Timesheets"
msgstr "Arveldatakse ajaarvestuslehelt"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Billing"
msgstr "Arveldus"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__billing_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billing Type"
msgstr "Arvelduse tüüp"

#. module: sale_timesheet
#: model:ir.ui.menu,name:sale_timesheet.menu_timesheet_billing_analysis
msgid "By Billing Type"
msgstr "Arveldustüübi järgi"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__sale_order_id
msgid "Choose the Sales Order to invoice"
msgstr "Valige arveldamiseks müügitellimus"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__commercial_partner_id
msgid "Commercial Partner"
msgstr "Äripartner"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_res_config_settings
msgid "Config Settings"
msgstr "Seadistused"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Configure your services"
msgstr "Konfigureerige oma teenused"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__cost
msgid "Cost"
msgstr "Kulu"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Costs"
msgstr "Kulud"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.project_project_action_multi_create_invoice
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Create Invoice"
msgstr "Loo arve"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_invoice
msgid "Create Invoice from project"
msgstr "Loo projektist arve"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Create Sales Order from Project"
msgstr "Looge projektist müügitellimus"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Create content that will help our users on a daily basis"
msgstr "Looge sisu, mis aitab meie kasutajaid igapäevaselt"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__create_uid
msgid "Created by"
msgstr "Loonud"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__create_date
msgid "Created on"
msgstr "Loodud"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__currency_id
msgid "Currency"
msgstr "Valuuta"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__partner_id
msgid "Customer"
msgstr "Klient"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_order_timesheet_product_template
msgid "Customer Care (Prepaid Hours)"
msgstr "Klienditeenindus (ettemakstud tunnid)"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_kanban_inherit_sale_timesheet
msgid "Customer Ratings"
msgstr "Kliendi hinnangud"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Customer Relationship"
msgstr "Kliendisuhted"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Days Ordered,"
msgstr "Tellitud päevad,"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Days Remaining)"
msgstr "päevi järele)"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Discard"
msgstr "Loobu"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Discover our products."
msgstr "Tutvu meie toodetega."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__display_name
msgid "Display Name"
msgstr "Kuvatav nimi"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "Draft Invoice"
msgstr "Arve mustand"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid ""
"Each employee has a chance to see the impact of his work.\n"
"                    You can make a real contribution to the success of the company.\n"
"                    <br>\n"
"                    Several activities are often organized all over the year, such as weekly\n"
"                    sports sessions, team building events, monthly drink, and much more"
msgstr ""
"Igal töötajal on võimalus näha oma töö mõju.\n"
"                         Saate anda tõelise panuse ettevõtte edusse.\n"
"                         <br>\n"
"                         Sageli korraldatakse aastaringselt mitmeid tegevusi, näiteks kord nädalas\n"
"                         sporditunnid, meeskonnaüritused ja palju muud"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Eat &amp; Drink"
msgstr "Söök ja jook"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_product_elevator_installation_product_template
msgid "Elevator Installation"
msgstr "Lifti paigaldus"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_hr_employee
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__employee_id
msgid "Employee"
msgstr "Töötaja"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__employee_rate
msgid "Employee rate"
msgstr "Töötajate määr"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__date_end_invoice_timesheet
msgid "End Date"
msgstr "Lõppkuupäev"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Expand your knowledge of various business industries"
msgstr "Laienda oma teadmisi erinevates ärivaldkondades"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Expected"
msgstr "Eeldatav"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Experience in writing online content"
msgstr "Kogemus veebisisu kirjutamisel"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Fixed price services"
msgstr "Fikseeritud hinnaga teenused"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Fruit, coffee and <br>snacks provided."
msgstr "Pakutakse puuvilju, kohvi ja suupisteid."

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_manual_product_template
msgid "Furniture Delivery (Manual)"
msgstr "Mööbli kohaletoimetamine (käsitsi)"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Google Adwords experience"
msgstr "Google Adwords'i kogemus"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Great team of smart people, in a friendly and open culture"
msgstr "Toetav, sõbralik ja professionaalne meeskond."

#. module: sale_timesheet
#: model:hr.job,name:sale_timesheet.job_labour
msgid "Handyman"
msgstr "Käsitööline"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__has_multi_sol
msgid "Has Multi Sol"
msgstr "Has Multi Sol"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Highly creative and autonomous"
msgstr "Väga loominguline ja autonoomne"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__display_cost
msgid "Hourly Cost"
msgstr "Tunnihind"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Hours Ordered,"
msgstr "Tellitud tunnid,"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Hours Remaining)"
msgstr "tunde järele)"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__id
msgid "ID"
msgstr "ID"

#. module: sale_timesheet
#: model:hr.job,name:sale_timesheet.job_interior_designer
msgid "Interior Designer"
msgstr "Sisekujundaja"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_product_interior_designing_product_template
msgid "Interior Designing"
msgstr "Sisekujundus"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__timesheet_invoice_id
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__timesheet_invoice_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.report_timesheet_account_move
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Invoice"
msgstr "Arve"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_res_config_settings__invoice_policy
msgid "Invoice Policy"
msgstr "Arvelduse meetod"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product_template.py:0
msgid "Invoice based on timesheets (delivered quantity)."
msgstr "Arve põhineb tööaja arvestuslehtedelt (tarnitud kogused). "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_analytic_line__timesheet_invoice_id
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__timesheet_invoice_id
msgid "Invoice created from the timesheet"
msgstr "Arve on loodud ajaarvestuse põhjal"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Invoiced"
msgstr "Arveldatud"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Invoices"
msgstr "Arved"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Invoicing"
msgstr "Raamatupidamine"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__is_project_map_empty
msgid "Is Project map empty"
msgstr "Kas projekti kaart on tühi"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__is_so_line_edited
msgid "Is Sales Order Item Manually Edited"
msgstr "Kas müügitellimuse kirje on käsitsi muudetud"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_move
msgid "Journal Entry"
msgstr "Andmiku kanne"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_move_line
msgid "Journal Item"
msgstr "Andmiku kanderida"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_timesheet_2_product_template
msgid "Junior Architect (Invoice on Timesheets)"
msgstr "Noorem arhitekt (arveldata ajaarvestuslehel)"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_milestones_product_template
msgid "Kitchen Assembly (Milestones)"
msgstr "Köögimööbli paigaldus (eesmärgid)"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__write_uid
msgid "Last Updated by"
msgstr "Viimati uuendatud"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__write_date
msgid "Last Updated on"
msgstr "Viimati uuendatud"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Lead the entire sales cycle"
msgstr "Juhi kogu müügitsüklit"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__service_type
#: model:ir.model.fields,help:sale_timesheet.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"Manuaalselt määratud kogused tellimusel: Arve esitatakse manuaalselt sisestatud koguse põhjal, ilma analüütilist kontot loomata.\n"
"Tööajatabelid lepingul: Arve esitatakse seotud tööajatabelis jälgitud tundide põhjal.\n"
"Loo ülesanne ja jälgi töötunde: Loo ülesanne müügitellimuse kinnitamisel ja jälgi töötunde."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__margin
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Margin"
msgstr "Marginaal"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Master demos of our software"
msgstr "Tarkvara demod"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Materials"
msgstr "Materjalid"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Tarnitud koguste uuendamise meetod"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Milestone services"
msgstr "Mitme etapiga teenused"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Must Have"
msgstr "Peab olema"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Negotiate and contract"
msgstr "Läbirääkimised ja lepingud"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Nice to have"
msgstr "Hea kui oleks"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "No Invoice"
msgstr "Arve puudub"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.action_timesheet_from_invoice
msgid "No activities found"
msgstr "Ühtegi tegevust ei leitud"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_from_sales_order_item
msgid "No activities found. Let's start a new one!"
msgstr "Tegevusi ei leitud. Loome uue!"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_billing_report
msgid "No data yet!"
msgstr "Andmed puuduvad!"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "No dumb managers, no stupid tools to use, no rigid working hours"
msgstr "Ei mingeid rumalaid juhte ega tööriistu ega jäika tööaega"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid ""
"No waste of time in enterprise processes, real responsibilities and autonomy"
msgstr "Ei raisata aega ettevõtte protsessides, kohustustes ja autonoomias"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__non_billable
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__non_billable
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Non-Billable"
msgstr "Pole arveldatav"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_form_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_tree_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_task_view_form_inherit_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheete_analysis_report_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_list_inherited
msgid "Non-billable"
msgstr "Mittearveldatav"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__non_billable_time
msgid "Non-billable Time"
msgstr "Mittearveldatav aeg"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Not Billed"
msgstr "Arveldamata"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__timesheet_revenues
msgid "Number of hours spent multiplied by the unit price per hour/day."
msgstr "Kulutatud tundide arv korrutatud ühikuhinnaga tunnis/päevas."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__billable_time
msgid "Number of hours/days linked to a SOL."
msgstr "SOLiga seotud tundide/päevade arv."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__non_billable_time
msgid "Number of hours/days not linked to a SOL."
msgstr "Tundide/päevade arv, mis ei ole seotud SOLiga."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_advance_payment_inv__date_end_invoice_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_advance_payment_inv__date_start_invoice_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_advance_payment_inv_timesheet_view_form
msgid ""
"Only timesheets not yet invoiced (and validated, if applicable) from this "
"period will be invoiced. If the period is not indicated, all timesheets not "
"yet invoiced (and validated, if applicable) will be invoiced without "
"distinction."
msgstr ""
"Arvele pannakse ainult need töölehed, mille kohta ei ole veel arveid "
"esitatud (ja vajaduse korral kinnitatud). Kui ajavahemikku ei ole märgitud, "
"esitatakse kõik veel arvele võtmata (ja vajaduse korral kinnitamata) "
"tööajatabelid, ilma et neid eristataks."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Operation not supported"
msgstr "Toiming pole toetatud"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__order_id
msgid "Order Reference"
msgstr "Tellimuse viide"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__other_costs
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__other_costs
msgid "Other costs"
msgstr "Muud kulud"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__other_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__other_revenues
msgid "Other revenues"
msgstr "Muud tulud"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Our Product"
msgstr "Meie tooted"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Passion for software products"
msgstr "Kirg tarkvaratoodete vastu"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__service_upsell_threshold
#: model:ir.model.fields,help:sale_timesheet.field_product_template__service_upsell_threshold
msgid ""
"Percentage of time delivered compared to the prepaid amount that must be "
"reached for the upselling opportunity activity to be triggered."
msgstr ""
"Protsentuaalselt tarnitud aeg võrreldes ettemakstud summaga, mis peab olema "
"saavutatud, et käivitada lisamüügivõimaluse tegevus."

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Perfect written English"
msgstr "Väga hea inglise keel kirjas"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Perks"
msgstr "Hüved"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Personal Evolution"
msgstr "Isiklik areng:"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Play any sport with colleagues, <br>the bill is covered."
msgstr ""
"Tegelege kolleegidega mis tahes spordialaga, sest kulud on juba kaetud."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__pricing_type
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__pricing_type
msgid "Pricing"
msgstr "Hinnastamine"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_template
msgid "Product"
msgstr "Toode"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_product
msgid "Product Variant"
msgstr "Toote variatsioon"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_project
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__project_id
msgid "Project"
msgstr "Projekt"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_sale_line_employee_map
msgid "Project Sales line, employee mapping"
msgstr "Projekti müügivoog, töötajate kaardistamine"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__project_template_id
msgid "Project Template"
msgstr "Projekti mall"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_update
msgid "Project Update"
msgstr "Projekti värskendus"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__fixed_rate
msgid "Project rate"
msgstr "Projekti määr"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_invoice__project_id
msgid "Project to make billable"
msgstr "Projekt arveldamiseks"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Qualify the customer needs"
msgstr "Täpsustage kliendi vajadused"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Quotation"
msgstr "Pakkumine"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Real responsibilities and challenges in a fast evolving company"
msgstr "Uued ja huvitavad väljakutsed kiiresti arenevas ettevõttes"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__remaining_hours_available
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__remaining_hours_available
msgid "Remaining Hours Available"
msgstr "Alles jäänud saadaval tunde"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Responsibilities"
msgstr "Kohustused"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Revenues"
msgstr "Käibed"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_billing_report
msgid ""
"Review your timesheets by billing type and make sure your time is billable."
msgstr ""
"Vaadake oma tööajatabelid arve liikide kaupa läbi ja veenduge, et Teie aeg "
"on arveldatav."

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_product_roofing_product_template
msgid "Roofing"
msgstr "Katusetööd"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_sale_page
msgid "S0001"
msgstr "S0001"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "Müügi ettemaksuarve"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
#: model:ir.model,name:sale_timesheet.model_sale_order
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__sale_order_id
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__order_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_report_search_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Sales Order"
msgstr "Müügitellimus"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__so_line
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__sale_line_id
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__so_line
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_timesheet_table_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.report_timesheet_sale_order
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Sales Order Item"
msgstr "Müügiartikkel"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_order_line
msgid "Sales Order Line"
msgstr "Müügitellimuse rida"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_kanban_inherit_sale_timesheet_so_button
msgid "Sales Orders"
msgstr "Müügitellimused"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__sale_line_employee_ids
msgid ""
"Sales order item that will be selected by default on the timesheets of the corresponding employee. It bypasses the sales order item defined on the project and the task, and can be modified on each timesheet entry if necessary. In other words, it defines the rate at which an employee's time is billed based on their expertise, skills or experience, for instance.\n"
"If you would like to bill the same service at a different rate, you need to create two separate sales order items as each sales order item can only have a single unit price at a time.\n"
"You can also define the hourly company cost of your employees for their timesheets on this project specifically. It will bypass the timesheet cost set on the employee."
msgstr ""
"Sales order item that will be selected by default on the timesheets of the corresponding employee. It bypasses the sales order item defined on the project and the task, and can be modified on each timesheet entry if necessary. In other words, it defines the rate at which an employee's time is billed based on their expertise, skills or experience, for instance.\n"
"If you would like to bill the same service at a different rate, you need to create two separate sales order items as each sales order item can only have a single unit price at a time.\n"
"You can also define the hourly company cost of your employees for their timesheets on this project specifically. It will bypass the timesheet cost set on the employee."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_analytic_line__so_line
msgid ""
"Sales order item to which the time spent will be added in order to be "
"invoiced to your customer. Remove the sales order item for the timesheet "
"entry to be non-billable."
msgstr ""
"Müügitellimuse artikkel, millele lisatakse kulutatud aeg tellimusele. "
"Eemaldage müügitellimuse artikkel, et tööaja kirje ei oleks arveldatav."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr "Müügitellimus, millega ülesanne on seotud."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Search in Invoice"
msgstr "Otsi arvelt"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Search in Sales Order"
msgstr "Otsi müügitellimusest"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Sell services and invoice time spent"
msgstr "Müü teenuseid ja koosta ajakulu põhjal arveid"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_timesheet_1_product_template
msgid "Senior Architect (Invoice on Timesheets)"
msgstr "Vanemarhitekt (arveldata ajaarvestuslehel)"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__service_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__service_revenues
msgid "Service Revenues"
msgstr "Teenuse tulud"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.time_product_product_template
msgid "Service on Timesheets"
msgstr "Teenus ajaarvestuslehel"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__timesheet_product_id
#: model:ir.model.fields,help:sale_timesheet.field_project_task__timesheet_product_id
msgid ""
"Service that will be used by default when invoicing the time spent on a "
"task. It can be modified on each task individually by selecting a specific "
"sales order item."
msgstr ""
"Teenus, mida kasutatakse vaikimisi ülesandele kulutatud aja arvete "
"esitamisel. Seda saab muuta iga ülesande puhul eraldi, valides konkreetse "
"müügitellimuse kirje."

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.product_template_action_default_services
#: model:project.project,label_tasks:sale_timesheet.project_support
msgid "Services"
msgstr "Teenused"

#. module: sale_timesheet
#: model:hr.job,name:sale_timesheet.job_engineer
msgid "Site Manager"
msgstr "Objekti juht"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_product_solar_installation_product_template
msgid "Solar Panel Installation"
msgstr "Päikesepaneelide paigaldus"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Sport Activity"
msgstr "Sportlik tegevus"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__date_start_invoice_timesheet
msgid "Start Date"
msgstr "Alguskuupäev"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__sale_order_state
msgid "Status"
msgstr "Olek"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Strong analytical skills"
msgstr "Tugev analüüsivõime"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_task
msgid "Task"
msgstr "Ülesanne"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__task_rate
msgid "Task rate"
msgstr "Ülesande määr"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "Ülesannete analüüs"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Technical Expertise"
msgstr "Tehniline kogemus"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product_product.py:0
#: code:addons/sale_timesheet/models/product_template.py:0
msgid ""
"The %s product is required by the Timesheets app and cannot be archived nor "
"deleted."
msgstr ""
"Rakendus Timesheets vajab toodet %s ja seda ei saa arhiveerida ega "
"kustutada."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product_template.py:0
msgid ""
"The %s product is required by the Timesheets app and cannot be linked to a "
"company."
msgstr ""

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_invoice.py:0
msgid "The selected Sales Order should contain something to invoice."
msgstr "Valitud müügitellimus peaks sisaldama midagi arveldamiseks."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__pricing_type
#: model:ir.model.fields,help:sale_timesheet.field_project_task__pricing_type
msgid ""
"The task rate is perfect if you would like to bill different services to "
"different customers at different rates. The fixed rate is perfect if you "
"bill a service at a fixed rate per hour or day worked regardless of the "
"employee who performed it. The employee rate is preferable if your employees"
" deliver the same service at a different rate. For instance, junior and "
"senior consultants would deliver the same service (= consultancy), but at a "
"different rate because of their level of seniority."
msgstr ""
"Ülesannete määr sobib ideaalselt, kui soovite eri klientidele erinevaid "
"teenuseid erinevate määradega arvele võtta. Fikseeritud määr sobib "
"ideaalselt, kui Te arvete teenuse eest kindla hinnaga töötatud tunni või "
"päeva kohta, olenemata sellest, milline töötaja seda tegi. Töötaja määr on "
"eelistatav, kui Teie töötajad osutavad sama teenust erineva hinnaga. Näiteks"
" nooremad ja vanemad konsultandid osutavad sama teenust (= konsultatsioon), "
"kuid nende staažist tulenevalt erineva tariifiga."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_sale_line_employee_map__cost
msgid ""
"This cost overrides the employee's default employee hourly wage in "
"employee's HR Settings"
msgstr ""
"See kulu tühistab töötaja vaikimisi määratud tunnitasu töötaja HR seadetes."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_upsell_threshold
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_upsell_threshold
msgid "Threshold"
msgstr "Piirmäär"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Time Billing"
msgstr "Aja arveldamine"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__remaining_hours_so
#: model:ir.model.fields,field_description:sale_timesheet.field_report_project_task_user__remaining_hours_so
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__remaining_hours
msgid "Time Remaining on SO"
msgstr "Järelejäänud aeg müügitellimusel"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_graph_employee_per_date
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
msgid "Time Spent"
msgstr "Kulutatud aeg"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Time-based services"
msgstr "Ajapõhised teenused"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_plan
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_plan_pivot
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_graph_employee_per_date
msgid "Timesheet"
msgstr "Tööaja ajaarvestusleht"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Timesheet Activities"
msgstr "Tööaja arvestuslehe tegevused"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_graph_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_pivot_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_graph_employee_per_date
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
msgid "Timesheet Costs"
msgstr "Tunnihind (kulu)"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__timesheet_product_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__timesheet_product_id
msgid "Timesheet Product"
msgstr "Ajaarvestuslehe toode"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_report_search_sale_timesheet
msgid "Timesheet Report"
msgstr "Tööaja arvestuslehtede aruanne"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__timesheet_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__timesheet_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__timesheet_revenues
msgid "Timesheet Revenues"
msgstr "Tööaja arvestuslehe tulud"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_total_duration
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_total_duration
msgid "Timesheet Total Duration"
msgstr "Ajaarvestuslehe kogukestus"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/account_move.py:0
#: model:ir.actions.act_window,name:sale_timesheet.action_timesheet_from_invoice
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_sales_order
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_sales_order_item
#: model:ir.actions.report,name:sale_timesheet.timesheet_report_account_move
#: model:ir.actions.report,name:sale_timesheet.timesheet_report_sale_order
#: model:ir.model.fields.selection,name:sale_timesheet.selection__sale_order_line__qty_delivered_method__timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_graph_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
msgid "Timesheets"
msgstr "Tööaja arvestusleht"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Billed Manually)"
msgstr "Tööaja arvestuslehed (Arveldatud manuaalselt)"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Billed on Milestones)"
msgstr "Tööaja arvestuslehed (arveldatud vahe-eesmärkidena)"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Billed on Timesheets)"
msgstr "Tööaja arvestuslehed (arveldatud tööaja arvestuslehelt)"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Fixed Price)"
msgstr "Tööaja arvestuslehed (Fikseeritud hind)"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Non-Billable)"
msgstr "Tööaja arvestuslehed (Mittearveldatav)"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_pivot_invoice_type
msgid "Timesheets Analysis"
msgstr "Tööaja arvestuslehe analüüsid"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_timesheets_analysis_report
msgid "Timesheets Analysis Report"
msgstr "Tööaja arvestuslehe analüüsi aruanne"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_advance_payment_inv_timesheet_view_form
msgid "Timesheets Period"
msgstr "Ajalogimise ridade periood"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_billing_report
msgid "Timesheets by Billing Type"
msgstr "Ajaarvestus arveldustüüpide järgi"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_sale_page
msgid "Timesheets for the"
msgstr "Ajaarvestusleht "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets of %s"
msgstr "Ajaarvestusleht %s"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_type__timesheet
msgid "Timesheets on project (one fare per SO/Project)"
msgstr "Timesheets on project (one fare per SO/Project)"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets revenues"
msgstr "Tööaja arvestuslehtede tulud"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__margin
msgid "Timesheets revenues minus the costs"
msgstr "Tööaja arvestuslehe tulud miinus kulud"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Timesheets taken into account when invoicing your time"
msgstr "Arvete esitamisel võetakse kulunud tööaeg arvesse"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_res_config_settings__invoice_policy
msgid "Timesheets taken when invoicing time spent"
msgstr "Timesheets taken when invoicing time spent"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Timesheets without a sales order item are reported as"
msgstr "Tööaja arvestuslehed ilma müügitellimuse artiklita märgitakse kui "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "To Bill"
msgstr "Arveldada"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "To Invoice"
msgstr "Arveks teha"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_list_inherited
msgid "Total"
msgstr "Kokku"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_invoice__amount_to_invoice
msgid ""
"Total amount to invoice on the sales order, including all items (services, "
"storables, expenses, ...)"
msgstr ""
"Müügitellimusel esitatava arve kogusumma, sealhulgas kõik kirjed (teenused, "
"laovarud, kulud, ...)."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_bank_statement_line__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_account_move__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_sale_order__timesheet_total_duration
msgid ""
"Total recorded duration, expressed in the encoding UoM, and rounded to the "
"unit"
msgstr ""
"Salvestatud kogukestus, mis on väljendatud kodeeringus UoM ja ümardatud "
"ühikuni."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_type
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_type
msgid "Track Service"
msgstr "Jälgimisteenus"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.action_timesheet_from_invoice
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_from_sales_order_item
msgid ""
"Track your working hours by projects every day and invoice this time to your"
" customers."
msgstr ""
"Jälgige igapäevaselt oma projektipõhist tööaega ning arveldage see aeg "
"kliendile edasi."

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Trainings"
msgstr "Koolitused"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__price_unit
msgid "Unit Price"
msgstr "Ühikhind"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Valid work permit for Belgium"
msgstr "Kehtiv tööluba Belgias"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Value does not exist in the pricing type"
msgstr "Väärtus ei ole hinnakujundustüübis olemas"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_invoice_page_inherit
msgid "View Timesheet"
msgstr "Vaata tööaja arvestuslehti"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_invoice_page_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_order_portal_content_inherit
msgid "View Timesheets"
msgstr "Vaata ajaarvestuslehti"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_product_timesheet_form
msgid "Warn the salesperson for an upsell when work done exceeds"
msgstr "Hoiata müügiesindajat siis, kui tehtud töö ületab"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "What We Offer"
msgstr "Mida me pakume"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "What's great in the job?"
msgstr "Mis on töös head?"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_invoice.py:0
msgid "You can only apply this action from a project."
msgstr "Seda toimingut saate rakendada ainult projektist."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid ""
"You cannot link a billable project to a sales order item that comes from an "
"expense or a vendor bill."
msgstr ""
"Te ei saa siduda arveldatavat projekti müügitellimuse kirjega, mis pärineb "
"kuludest või müüja arvelt."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid ""
"You cannot link a billable project to a sales order item that is not a "
"service."
msgstr ""
"Te ei saa siduda arveldatavat projekti müügitellimuse kirjega, mis ei ole "
"teenus."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
msgid "You cannot modify timesheets that are already invoiced."
msgstr ""
"Te ei saa muuta tööaja arvestustabeleid, mille kohta on juba arveid "
"esitatud."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
msgid "You cannot remove a timesheet that has already been invoiced."
msgstr "Te ei saa eemaldada juba arveldatud tööajalogimise rida."

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__billing_type__manually
msgid "billed manually"
msgstr "arveldatud manuaalselt"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/sale_order_line.py:0
msgid "days remaining"
msgstr "päevi järele"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__billing_type__not_billable
msgid "not billable"
msgstr "ei ole arveldatav"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_product_timesheet_form
msgid "of hours sold."
msgstr "müüdud tundidest."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/sale_order_line.py:0
msgid "remaining"
msgstr "alles jäänud"
