# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_margin
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__purchase_num_invoiced
msgid "# Invoiced in Purchase"
msgstr "# Abgerechnet durch Einkauf"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__sale_num_invoiced
msgid "# Invoiced in Sale"
msgstr "# Abgerechnet durch Verkauf"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "# Purchased"
msgstr "# Gekauft"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Analysis Criteria"
msgstr "Analysekriterien"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__sale_avg_price
msgid "Avg. Price in Customer Invoices."
msgstr "Durchschnittspreis in Kundenrechnungen"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__purchase_avg_price
msgid "Avg. Price in Vendor Bills"
msgstr "Durchschnittspreis in Eingangsrechnungen "

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__purchase_avg_price
msgid "Avg. Purchase Unit Price"
msgstr "Durchschn. Einzelpreis im Einkauf"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__sale_avg_price
msgid "Avg. Sale Unit Price"
msgstr "Durchschn. Einzelpreis im Verkauf"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Avg. Unit Price"
msgstr "Durchschn. Einzelpreis"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "Cancel"
msgstr "Abbrechen"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Catalog Price"
msgstr "Katalogpreis"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: product_margin
#: model:ir.model.fields.selection,name:product_margin.selection__product_margin__invoice_state__draft_open_paid
#: model:ir.model.fields.selection,name:product_margin.selection__product_product__invoice_state__draft_open_paid
msgid "Draft, Open and Paid"
msgstr "Entwurf, offen und bezahlt"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__expected_margin
msgid "Expected Margin"
msgstr "Erwartete Marge"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__expected_margin_rate
msgid "Expected Margin (%)"
msgstr "Erwartete Marge (%)"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__sale_expected
msgid "Expected Sale"
msgstr "Erwarteter Verkauf"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__expected_margin
msgid "Expected Sale - Normal Cost"
msgstr "Erwarteter Verkauf - Normalkosten"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__sales_gap
msgid "Expected Sale - Turn Over"
msgstr "Erwarteter Verkauf - Umsatz"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__expected_margin_rate
msgid "Expected margin * 100 / Expected Sale"
msgstr "Erwartete Marge * 100 / Erwartete Verkäufe"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__from_date
msgid "From"
msgstr "Von"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "General Information"
msgstr "Allgemeine Informationen"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__id
msgid "ID"
msgstr "ID"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__invoice_state
#: model:ir.model.fields,field_description:product_margin.field_product_product__invoice_state
msgid "Invoice State"
msgstr "Rechnungsstatus"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__date_from
msgid "Margin Date From"
msgstr "Marge datiert ab"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__date_to
msgid "Margin Date To"
msgstr "Marge datiert bis"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Margins"
msgstr "Margen"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__normal_cost
msgid "Normal Cost"
msgstr "Normalkosten"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__purchase_gap
msgid "Normal Cost - Total Cost"
msgstr "Normalkosten - Gesamtkosten"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "Open Margins"
msgstr "Offene Margen"

#. module: product_margin
#: model:ir.model.fields.selection,name:product_margin.selection__product_margin__invoice_state__open_paid
#: model:ir.model.fields.selection,name:product_margin.selection__product_product__invoice_state__open_paid
msgid "Open and Paid"
msgstr "Offen und bezahlt"

#. module: product_margin
#: model:ir.model.fields.selection,name:product_margin.selection__product_margin__invoice_state__paid
#: model:ir.model.fields.selection,name:product_margin.selection__product_product__invoice_state__paid
msgid "Paid"
msgstr "Bezahlt"

#. module: product_margin
#: model:ir.model,name:product_margin.model_product_margin
msgid "Product Margin"
msgstr "Produktmarge"

#. module: product_margin
#. odoo-python
#: code:addons/product_margin/wizard/product_margin.py:0
#: model:ir.actions.act_window,name:product_margin.product_margin_act_window
#: model:ir.ui.menu,name:product_margin.menu_action_product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_graph
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Product Margins"
msgstr "Produktmargen"

#. module: product_margin
#: model:ir.model,name:product_margin.model_product_product
msgid "Product Variant"
msgstr "Produktvariante"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "Properties categories"
msgstr "Kategorien von Eigenschaften"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__purchase_gap
msgid "Purchase Gap"
msgstr "Einkaufslücke"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Purchases"
msgstr "Einkäufe"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Sales"
msgstr "Verkäufe"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__sales_gap
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Sales Gap"
msgstr "Verkaufslücke"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Standard Price"
msgstr "Standardpreis"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__normal_cost
msgid "Sum of Multiplication of Cost price and quantity of Vendor Bills"
msgstr ""
"Summe der Multiplikation von Standardpreis und Anzahl Lieferantenrechnungen"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__turnover
msgid ""
"Sum of Multiplication of Invoice price and quantity of Customer Invoices"
msgstr ""
"Summe der Multiplikation von Rechnungspreis mit Anzahl Kundenrechnungen"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__total_cost
msgid "Sum of Multiplication of Invoice price and quantity of Vendor Bills "
msgstr ""
"Summe der Multiplikation von Rechnungspreis und Anzahl Lieferantenrechnungen"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__sale_expected
msgid ""
"Sum of Multiplication of Sale Catalog price and quantity of Customer "
"Invoices"
msgstr "Summe der Multiplikation von Listenpreis und Anzahl Kundenrechnungen"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__sale_num_invoiced
msgid "Sum of Quantity in Customer Invoices"
msgstr "Summe der Menge in Kundenrechnungen"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__purchase_num_invoiced
msgid "Sum of Quantity in Vendor Bills"
msgstr "Summe der Menge in Lieferantenrechnungen"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__to_date
msgid "To"
msgstr "Bis"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__total_cost
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Total Cost"
msgstr "Gesamtkosten"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__total_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Total Margin"
msgstr "Gesamtmarge"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__total_margin_rate
msgid "Total Margin Rate(%)"
msgstr "Gesamtmarge (%)"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__total_margin_rate
msgid "Total margin * 100 / Turnover"
msgstr "Gesamtmarge * 100 / Umsatz"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__turnover
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Turnover"
msgstr "Umsatz"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__total_margin
msgid "Turnover - Total cost"
msgstr "Umsatz - Gesamtkosten"
