# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sms
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <ka<PERSON><PERSON>l<PERSON>@emsystems.fi>, 2024
# <PERSON> <miu<PERSON><PERSON>@gmail.com>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>nst<PERSON> A<PERSON>rant<PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> Trigaux, 2024
# <AUTHOR> <EMAIL>, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-07 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_invalid_count
msgid "# Invalid recipients"
msgstr "# Virheellisiä vastaanottajia"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_valid_count
msgid "# Valid recipients"
msgstr "# Kelvolliset vastaanottajat"

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid ""
"%(count)s out of the %(total)s selected SMS Text Messages have successfully "
"been resent."
msgstr ""
"%(count)s valituista %(total)s tekstiviesteistä on lähetetty uudelleen."

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_template.py:0
msgid "%s (copy)"
msgstr "%s (kopio)"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_composer.py:0
msgid "%s invalid recipients"
msgstr "%s virheelliset vastaanottajat"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
msgid "******-555-555"
msgstr "******-555-555"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.xml:0
msgid ", fits in"
msgstr ", sopii"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.iap_account_view_form
msgid ""
"<i class=\"fa fa-warning\"/> An error occurred with your account. Please "
"contact the support for more information..."
msgstr ""
"<i class=\"fa fa-warning\"/> Tililläsi tapahtui virhe. Ota yhteyttä "
"asiakaspalveluun saadaksesi lisätietoja..."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.iap_account_view_form
msgid ""
"<i class=\"fa fa-warning\"/> You cannot send SMS while your account is not "
"registered."
msgstr ""
"<i class=\"fa fa-warning\"/> Et voi lähettää tekstiviestejä, jos tilisi ei "
"ole rekisteröity."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.iap_account_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Register"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Rekisteröi"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.iap_account_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Set Sender Name"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Aseta lähettäjän nimi"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid ""
"<span class=\"o_stat_text\">Add</span>\n"
"                                <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">Lisää</span>\n"
"                                <span class=\"o_stat_text\">Kontekstin mukainen toiminto</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "<span class=\"o_stat_text\">Preview</span>"
msgstr "<span class=\"o_stat_text\">Esikatselu</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid ""
"<span class=\"o_stat_text\">Remove</span>\n"
"                                <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">Poista</span>\n"
"                                <span class=\"o_stat_text\">Kontekstin mukainen toiminto</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "<span class=\"text-warning\" invisible=\"not no_record\">No records</span>"
msgstr "<span class=\"text-warning\" invisible=\"not no_record\">Ei tietueita</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.ir_actions_server_view_form
msgid ""
"<span invisible=\"sms_method != 'sms'\">\n"
"                                The message will be sent as an SMS to the recipients of the template\n"
"                                and will not appear in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"sms_method != 'note'\">\n"
"                                The SMS will not be sent, it will only be posted as an\n"
"                                internal note in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"sms_method != 'comment'\">\n"
"                                The SMS will be sent as an SMS to the recipients of the\n"
"                                template and it will also be posted as an internal note\n"
"                                in the messaging history.\n"
"                            </span>"
msgstr ""
"<span invisible=\"sms_method != 'sms'\">\n"
"                                Viesti lähetetään tekstiviestinä mallin vastaanottajille\n"
"                                eikä se näy viestihistoriassa.\n"
"                            </span>\n"
"                            <span invisible=\"sms_method != 'note'\">\n"
"                                Tekstiviestiä ei lähetetä, vaan se vain kirjataan muistiin\n"
"                                sisäinen huomautus viestihistoriaan.\n"
"                            </span>\n"
"                            <span invisible=\"sms_method != 'comment'\">\n"
"                                Tekstiviesti lähetetään tekstiviestinä tekstiviestin vastaanottajille\n"
"                                mallin vastaanottajille, ja se kirjataan myös sisäisenä muistiinpanona\n"
"                                viestihistoriaan.\n"
"                            </span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "<span> or to specify the country code.</span>"
msgstr "<span> tai määrittää maakoodi.</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid ""
"<strong>Invalid number:</strong>\n"
"                        <span> make sure to set a country on the </span>"
msgstr ""
"<strong>Väärä numero:</strong>\n"
"                        <span> varmista, että asetat maan </span>"

#. module: sms
#: model:ir.model.constraint,message:sms.constraint_sms_tracker_sms_uuid_unique
msgid "A record for this UUID already exists"
msgstr "Tietue tälle UUID-tunnukselle on jo olemassa"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__account_id
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__account_id
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__account_id
msgid "Account"
msgstr "Tili"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_needaction
msgid "Action Needed"
msgstr "Vaatii toimenpiteitä"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid ""
"Add a contextual action on the related model to open a sms composer with "
"this template"
msgstr ""
"Lisää kontekstisidonnainen toiminto asiaan liittyvään malliin sms-"
"sommittelijan avaamiseksi tämän mallin avulla"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_sms__uuid
msgid "Alternate way to identify a SMS record, used for delivery reports"
msgstr ""
"Vaihtoehtoinen tapa tunnistaa tekstiviestitietue, jota käytetään "
"toimitusraporteissa"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/core/failure_model_patch.js:0
msgid "An error occurred when sending an SMS"
msgstr "Tekstiviestin lähettämisessä tapahtui virhe"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/core/failure_model_patch.js:0
msgid "An error occurred when sending an SMS on “%(record_name)s”"
msgstr ""
"Tekstiviestin lähettämisessä tapahtui virhe osoitteessa \"%(record_name)s\""

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"An unknown error occurred. Please contact Odoo support if this error "
"persists."
msgstr ""
"Tuntematon virhe tapahtui. Ota yhteyttä Odoo-tukeen, jos tämä virhe jatkuu."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__model_id
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__model_id
msgid "Applies to"
msgstr "Koskee"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_reset_view_form
msgid ""
"Are you sure you want to reset these sms templates to their original "
"configuration? Changes and translations will be lost."
msgstr ""
"Oletko varma, että haluat palauttaa nämä tekstiviestimallit alkuperäiseen "
"määritykseensä? Muutokset ja käännökset menetetään."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_attachment_count
msgid "Attachment Count"
msgstr "Liitteiden määrä"

#. module: sms
#: model:ir.model,name:sms.model_base
msgid "Base"
msgstr "Pohja"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_blacklist
msgid "Blacklisted"
msgstr "Markkinointikiellossa"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "Markkinointikiellossa oleva puhelin on matkapuhelin"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "Markkinointikiellossa oleva puhelin on muu puhelin"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__body
#: model:ir.model.fields,field_description:sms.field_sms_template__body
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__body
msgid "Body"
msgstr "Viesti"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Buy credits"
msgstr "Osta krediittejä"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "Buy credits."
msgstr "Osta krediittejä."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__can_cancel
msgid "Can Cancel"
msgstr "Voi perua"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__can_resend
msgid "Can Resend"
msgstr "Voi lähettää uudelleen"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_code_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_tree
#: model_terms:ir.ui.view,arch_db:sms.sms_template_reset_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Cancel"
msgstr "Peruuta"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__canceled
msgid "Cancelled"
msgstr "Peruttu"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Choose a language:"
msgstr "Valitse kieli:"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.ir_actions_server_view_form
msgid "Choose a template..."
msgstr "Valitse malli..."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Choose an example"
msgstr "Valitse esimerkki"

#. module: sms
#. odoo-python
#: code:addons/sms/models/iap_account.py:0
#: code:addons/sms/wizard/sms_account_code.py:0
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid "Choose your sender name"
msgstr "Valitse lähettäjän nimi"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Close"
msgstr "Sulje"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__composition_mode
msgid "Composition Mode"
msgstr "Kirjoitustila"

#. module: sms
#: model:ir.model,name:sms.model_res_partner
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Contact"
msgstr "Kontakti"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Content"
msgstr "Sisältö"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_country_not_supported
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_country_not_supported
msgid "Country Not Supported"
msgstr "Maa ei ole tuettu"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_registration_needed
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_registration_needed
msgid "Country-specific Registration Required"
msgstr "Maakohtainen rekisteröinti vaaditaan"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "Country-specific registration required."
msgstr "Maakohtainen rekisteröinti vaaditaan."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_composer__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_sms__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_template__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_tracker__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__create_date
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__create_date
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__create_date
#: model:ir.model.fields,field_description:sms.field_sms_composer__create_date
#: model:ir.model.fields,field_description:sms.field_sms_resend__create_date
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__create_date
#: model:ir.model.fields,field_description:sms.field_sms_sms__create_date
#: model:ir.model.fields,field_description:sms.field_sms_template__create_date
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__create_date
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__create_date
#: model:ir.model.fields,field_description:sms.field_sms_tracker__create_date
msgid "Created on"
msgstr "Luotu"

#. module: sms
#: model:iap.service,unit_name:sms.iap_service_sms
msgid "Credits"
msgstr "Tekijät"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__partner_id
msgid "Customer"
msgstr "Asiakas"

#. module: sms
#: model:sms.template,name:sms.sms_template_demo_0
msgid "Customer: automated SMS"
msgstr "Asiakas: automaattinen tekstiviesti"

#. module: sms
#: model:sms.template,body:sms.sms_template_demo_0
msgid "Dear {{ object.display_name }} this is an automated SMS."
msgstr "Hyvä {{ object.display_name }} tämä on automaattinen tekstiviesti."

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__sent
msgid "Delivered"
msgstr "Toimitettu"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Discard"
msgstr "Hylkää"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__display_name
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__display_name
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__display_name
#: model:ir.model.fields,field_description:sms.field_sms_composer__display_name
#: model:ir.model.fields,field_description:sms.field_sms_resend__display_name
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__display_name
#: model:ir.model.fields,field_description:sms.field_sms_sms__display_name
#: model:ir.model.fields,field_description:sms.field_sms_template__display_name
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__display_name
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__display_name
#: model:ir.model.fields,field_description:sms.field_sms_tracker__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: sms
#: model:ir.model,name:sms.model_mail_followers
msgid "Document Followers"
msgstr "Dokumentin seuraajat"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_id
msgid "Document ID"
msgstr "Dokumentin ID"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_ids
msgid "Document IDs"
msgstr "Asiakirjan tunnukset"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_model_description
msgid "Document Model Description"
msgstr "Asiakirjamallin kuvaus"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_model
msgid "Document Model Name"
msgstr "Asiakirjamallin nimi"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_duplicate
msgid "Duplicate"
msgstr "Kopioi"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Edit Partners"
msgstr "Muokkaa kumppaneita"

#. module: sms
#: model:ir.model,name:sms.model_mail_thread
msgid "Email Thread"
msgstr "Sähköpostiviestiketju"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/phone_field/phone_field.js:0
msgid "Enable SMS"
msgstr "Ota tekstiviesti käyttöön"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
msgid "Enter a phone number to get an SMS with a verification code."
msgstr ""
"Kirjoita puhelinnumero saadaksesi tekstiviestin, jossa on vahvistuskoodi."

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__error
msgid "Error"
msgstr "Virhe"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__failure_type
msgid "Error Message"
msgstr "Virheviesti"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_expired
msgid "Expired"
msgstr "Vanhentunut"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__failure_type
msgid "Failure Type"
msgstr "Epäonnistumisen tyyppi"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__failure_type
msgid "Failure type"
msgstr "Epäonnistumisen tyyppi"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr ""
"Kenttä, jota käytetään puhdistetun puhelinnumeron tallentamiseen. Auttaa "
"nopeuttamaan hakuja ja vertailuja."

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__template_fs
msgid ""
"File from where the template originates. Used to reset broken template."
msgstr ""
"Tiedosto, josta malli on peräisin. Käytetään rikkinäisen mallin "
"nollaamiseen."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_follower_ids
msgid "Followers"
msgstr "Seuraajat"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seuraajat (kumppanit)"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_composer.py:0
msgid "Following numbers are not correctly encoded: %s"
msgstr "Seuraavia numeroita ei ole koodattu oikein: %s"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__has_insufficient_credit
msgid "Has Insufficient Credit"
msgstr "Ei tarpeeksi krediittejä"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__has_message
msgid "Has Message"
msgstr "Sisältää viestin"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_mail__has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_message__has_sms_error
msgid "Has SMS error"
msgstr "On tekstiviestivirhe"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__has_unregistered_account
msgid "Has Unregistered Account"
msgstr "On rekisteröimätön tili"

#. module: sms
#: model:ir.model,name:sms.model_iap_account
msgid "IAP Account"
msgstr "IAP Tili"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__id
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__id
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__id
#: model:ir.model.fields,field_description:sms.field_sms_composer__id
#: model:ir.model.fields,field_description:sms.field_sms_resend__id
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__id
#: model:ir.model.fields,field_description:sms.field_sms_sms__id
#: model:ir.model.fields,field_description:sms.field_sms_template__id
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__id
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__id
#: model:ir.model.fields,field_description:sms.field_sms_tracker__id
msgid "ID"
msgstr "ID"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Jos valittu, uudet viestit vaativat huomiotasi."

#. module: sms
#: model:ir.model.fields,help:sms.field_account_analytic_account__message_has_sms_error
#: model:ir.model.fields,help:sms.field_calendar_event__message_has_sms_error
#: model:ir.model.fields,help:sms.field_crm_team__message_has_sms_error
#: model:ir.model.fields,help:sms.field_crm_team_member__message_has_sms_error
#: model:ir.model.fields,help:sms.field_discuss_channel__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle_log_contract__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle_log_services__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle_model__message_has_sms_error
#: model:ir.model.fields,help:sms.field_gamification_badge__message_has_sms_error
#: model:ir.model.fields,help:sms.field_gamification_challenge__message_has_sms_error
#: model:ir.model.fields,help:sms.field_iap_account__message_has_sms_error
#: model:ir.model.fields,help:sms.field_lunch_supplier__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_blacklist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_blacklist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_cc__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_main_attachment__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_phone__message_has_sms_error
#: model:ir.model.fields,help:sms.field_maintenance_equipment__message_has_sms_error
#: model:ir.model.fields,help:sms.field_maintenance_equipment_category__message_has_sms_error
#: model:ir.model.fields,help:sms.field_maintenance_request__message_has_sms_error
#: model:ir.model.fields,help:sms.field_phone_blacklist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_category__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_pricelist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_product__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_template__message_has_sms_error
#: model:ir.model.fields,help:sms.field_rating_mixin__message_has_sms_error
#: model:ir.model.fields,help:sms.field_res_partner__message_has_error
#: model:ir.model.fields,help:sms.field_res_partner__message_has_sms_error
#: model:ir.model.fields,help:sms.field_res_users__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Jos valittu, joitakin viestejä ei ole toimitettu."

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr ""
"Jos puhdistettu puhelinnumero on mustalla listalla, yhteyshenkilö ei enää "
"saa massatekstiviestejä miltään listalta"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Ignore all"
msgstr "Ohita kaikki"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__outgoing
msgid "In Queue"
msgstr "Lähetysjonossa"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"Ilmaisee, onko mustalle listalle joutunut puhdistettu puhelinnumero "
"matkapuhelinnumero. Auttaa erottamaan, mikä numero on mustalla listalla, kun"
" mallissa on sekä matkapuhelin- että puhelinkenttä."

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"Ilmaisee, onko mustalle listalle merkitty puhdistettu puhelinnumero "
"puhelinnumero. Auttaa erottamaan, mikä numero on mustalla listalla, kun "
"mallissa on sekä matkapuhelin- että puhelinkenttä."

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__comment_single_recipient
msgid "Indicates if the SMS composer targets a single specific recipient"
msgstr ""
"Ilmaisee, onko tekstiviestisommitelu suunnattu yksittäiselle "
"vastaanottajalle"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_credit
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_credit
msgid "Insufficient Credit"
msgstr "Ei riittävästi krediittejä"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_invalid_destination
msgid "Invalid Destination"
msgstr "Virheellinen määränpää"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"Invalid phone number. Please make sure to follow the international format, "
"i.e. a plus sign (+), then country code, city code, and local phone number. "
"For example: ******-555-555"
msgstr ""
"Virheellinen puhelinnumero. Varmista, että noudatat kansainvälistä muotoa, "
"eli plusmerkki (+), sitten maatunnus, kaupunkitunnus ja paikallinen "
"puhelinnumero. Esim: ******-555-555"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_composer.py:0
msgid "Invalid recipient number. Please update it."
msgstr "Vastaanottajan numero on virheellinen. Päivitä se."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_is_follower
msgid "Is Follower"
msgstr "On seuraaja"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_valid
msgid "Is valid"
msgstr "On voimassa"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__mass_keep_log
msgid "Keep a note on document"
msgstr "Pidä dokumenttia silmällä"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__lang
msgid "Language"
msgstr "Kieli"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_composer__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_sms__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_template__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_tracker__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__write_date
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__write_date
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__write_date
#: model:ir.model.fields,field_description:sms.field_sms_composer__write_date
#: model:ir.model.fields,field_description:sms.field_sms_resend__write_date
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__write_date
#: model:ir.model.fields,field_description:sms.field_sms_sms__write_date
#: model:ir.model.fields,field_description:sms.field_sms_template__write_date
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__write_date
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__write_date
#: model:ir.model.fields,field_description:sms.field_sms_tracker__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: sms
#: model:ir.model,name:sms.model_sms_tracker
msgid "Link SMS to mailing/sms tracking models"
msgstr "Linkitä tekstiviestit postitus-/tekstiviestiseurantamalleihin"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__mail_message_id
msgid "Mail Message"
msgstr "KPI Sähköpostiviestien kokonaisarvo"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_tracker__mail_notification_id
msgid "Mail Notification"
msgstr "Posti-ilmoitus"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_ir_model__is_mail_thread_sms
msgid "Mail Thread SMS"
msgstr "SMS-viestiketju"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__to_delete
msgid "Marked for deletion"
msgstr "Merkitty poistettavaksi"

#. module: sms
#: model:ir.model,name:sms.model_mail_message
#: model:ir.model.fields,field_description:sms.field_sms_composer__body
#: model:ir.model.fields,field_description:sms.field_sms_resend__mail_message_id
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Message"
msgstr "Viesti"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_has_error
msgid "Message Delivery error"
msgstr "Ongelma viestin toimituksessa"

#. module: sms
#: model:ir.model,name:sms.model_mail_notification
msgid "Message Notifications"
msgstr "Viestien ilmoitukset"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_ids
msgid "Messages"
msgstr "Viestit"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_number_missing
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_number_missing
msgid "Missing Number"
msgstr "Puuttuva numero"

#. module: sms
#: model:ir.model,name:sms.model_ir_model
msgid "Models"
msgstr "Mallit"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__name
msgid "Name"
msgstr "Nimi"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__no_record
msgid "No Record"
msgstr "Ei tietuetta"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_not_allowed
msgid "Not Allowed"
msgstr "Ei Sallittu"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_not_delivered
msgid "Not Delivered"
msgstr "Ei toimitettu"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__ir_actions_server__sms_method__note
msgid "Note only"
msgstr "Huomautus vain"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid ""
"Note that this is not required, if you don't set a sender name, your SMS "
"will be sent from a short code."
msgstr ""
"Huomaa, että tätä ei tarvita, jos et aseta lähettäjän nimeä, tekstiviestisi "
"lähetetään lyhytkoodista."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__notification_id
msgid "Notification"
msgstr "Ilmoitus"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "Ilmoitustyyppi"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__number
msgid "Number"
msgstr "Numero"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__number_field_name
msgid "Number Field"
msgstr "Numerokenttä"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_needaction_counter
msgid "Number of Actions"
msgstr "Toimenpiteiden määrä"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_has_error_counter
msgid "Number of errors"
msgstr "Virheiden määrä"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Toimenpiteitä vaativien viestien määrä"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Toimitusvirheellisten viestien määrä"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__res_ids_count
msgid ""
"Number of recipients that will receive the SMS if sent in mass mode, without"
" applying the Active Domain value"
msgstr ""
"Niiden vastaanottajien määrä, jotka saavat tekstiviestin, jos se lähetetään "
"massatilassa ilman aktiivisen toimialueen arvoa"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_optout
msgid "Opted Out"
msgstr "Markkinointikiellon asettaneet"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Valinnainen käännöskieli (ISO-koodi), joka valitaan sähköpostia "
"lähetettäessä. Jos sitä ei ole asetettu, käytetään englanninkielistä "
"versiota. Tämän pitäisi yleensä olla sijoitusilmaus, joka antaa sopivan "
"kielen, esim. {{ object.partner_id.lang }}."

#. module: sms
#: model:ir.model,name:sms.model_sms_sms
msgid "Outgoing SMS"
msgstr "Lähtevät tekstiviestit"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__partner_id
msgid "Partner"
msgstr "Kumppani"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "Puhelin on markkinointikiellossa"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__phone_number
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__sms_number
msgid "Phone Number"
msgstr "Puhelinnumero"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__recipient_single_number_itf
msgid ""
"Phone number of the recipient. If changed, it will be recorded on "
"recipient's profile."
msgstr ""
"Vastaanottajan puhelinnumero. Jos sitä muutetaan, se tallennetaan "
"vastaanottajan profiiliin."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_mobile_search
msgid "Phone/Mobile"
msgstr "Puhelin / matkapuhelin"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_composer__composition_mode__comment
msgid "Post on a document"
msgstr "Lähetä asiakirjaan"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Preview of"
msgstr "Esikatselu"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_reset_view_form
msgid "Proceed"
msgstr "Käsittele"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__process
msgid "Processing"
msgstr "Käsittelyssä"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Put in queue"
msgstr "Laita jonoon"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__rating_ids
msgid "Ratings"
msgstr "Arviointi"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Reason"
msgstr "Syy"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Recipient"
msgstr "Vastaanottaja"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__partner_name
msgid "Recipient Name"
msgstr "Vastaanottajan nimi"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_number_itf
msgid "Recipient Number"
msgstr "Vastaanottajan numero"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__recipient_ids
msgid "Recipients"
msgstr "Vastaanottajat"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__numbers
msgid "Recipients (Numbers)"
msgstr "Vastaanottajat (lukumäärä)"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_description
msgid "Recipients (Partners)"
msgstr "Vastaanottajat (kumppanit)"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__resource_ref
msgid "Record reference"
msgstr "Viite tietueeseen"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_code_view_form
msgid "Register"
msgstr "Ilmoittaudu"

#. module: sms
#. odoo-python
#: code:addons/sms/models/iap_account.py:0
#: code:addons/sms/wizard/sms_account_phone.py:0
msgid "Register Account"
msgstr "Rekisteröidy"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "Register now."
msgstr "Rekisteröidy nyt."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_code_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
msgid "Register your SMS account"
msgstr "Rekisteröi SMS-tilisi"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_rejected
msgid "Rejected"
msgstr "Rechazado"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__model
msgid "Related Document Model"
msgstr "Liittyvä dokumenttimalli"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Remove the contextual action of the related model"
msgstr "Poista asiayhteyteen liittyvän mallin kontekstuaalinen toiminto"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__render_model
msgid "Rendering Model"
msgstr "Renderöintimalli"

#. module: sms
#: model:ir.actions.server,name:sms.ir_actions_server_sms_sms_resend
msgid "Resend"
msgstr "Lähetä uudelleen"

#. module: sms
#: model:ir.model,name:sms.model_sms_resend_recipient
msgid "Resend Notification"
msgstr "Lähetä ilmoitus uudelleen"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_template_reset_action
msgid "Reset SMS Template"
msgstr "Nollaa tekstiviestimalli"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Reset Template"
msgstr "Nollaa malli"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_tree
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Retry"
msgstr "Yritä uudelleen"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_button/sms_button.xml:0
#: code:addons/sms/static/src/core/notification_model_patch.js:0
#: model:ir.actions.act_window,name:sms.sms_sms_action
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_id
#: model:ir.model.fields.selection,name:sms.selection__mail_message__message_type__sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__notification_type__sms
#: model:ir.ui.menu,name:sms.sms_sms_menu
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "SMS"
msgstr "SMS"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.xml:0
msgid "SMS ("
msgstr "TEKSTIVIESTI ("

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__ir_actions_server__sms_method__comment
msgid "SMS (with note)"
msgstr "Tekstiviesti (huomautuksen kanssa)"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__ir_actions_server__sms_method__sms
msgid "SMS (without note)"
msgstr "Tekstiviesti (ilman huomautusta)"

#. module: sms
#: model:ir.model,name:sms.model_sms_account_phone
msgid "SMS Account Registration Phone Number Wizard"
msgstr "SMS-tilin puhelinnumeron rekisteröinnin ohjattu toiminto"

#. module: sms
#: model:ir.model,name:sms.model_sms_account_sender
msgid "SMS Account Sender Name Wizard"
msgstr "SMS-tilin lähettäjän nimen ohjattu toiminto"

#. module: sms
#: model:ir.model,name:sms.model_sms_account_code
msgid "SMS Account Verification Code Wizard"
msgstr "SMS-tilin vahvistuskoodin ohjattu toiminto"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_account_analytic_account__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_calendar_event__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_crm_team__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_crm_team_member__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_discuss_channel__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle_log_contract__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle_log_services__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle_model__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_gamification_badge__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_gamification_challenge__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_iap_account__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_lunch_supplier__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_blacklist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_blacklist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_cc__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_main_attachment__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_phone__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_maintenance_equipment__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_maintenance_equipment_category__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_maintenance_request__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_phone_blacklist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_category__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_pricelist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_product__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_template__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_rating_mixin__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_res_partner__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_res_users__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Tekstiviestin toimitusvirhe"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/messaging_menu/messaging_menu_patch.js:0
msgid "SMS Failure: %(modelName)s"
msgstr "Tekstiviestien epäonnistuminen: %(modelName)s"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/messaging_menu/messaging_menu_patch.js:0
msgid "SMS Failures"
msgstr "Tekstiviestivirheet"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_id_int
msgid "SMS ID"
msgstr "Tekstiviestin ID"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_number
msgid "SMS Number"
msgstr "SMS-numero"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "SMS Preview"
msgstr "SMS-esikatselu"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.xml:0
msgid "SMS Pricing"
msgstr "SMS-hinnoittelu"

#. module: sms
#: model:ir.model,name:sms.model_sms_resend
msgid "SMS Resend"
msgstr "Lähetä tekstiviesti uudelleen"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__state
msgid "SMS Status"
msgstr "SMS-tila"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_ir_actions_server__sms_template_id
#: model:ir.model.fields,field_description:sms.field_ir_cron__sms_template_id
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "SMS Template"
msgstr "SMS Malli"

#. module: sms
#: model:ir.model,name:sms.model_sms_template_preview
msgid "SMS Template Preview"
msgstr "SMS-mallin esikatselu"

#. module: sms
#: model:ir.model,name:sms.model_sms_template_reset
msgid "SMS Template Reset"
msgstr "SMS-mallin nollaus"

#. module: sms
#: model:ir.model,name:sms.model_sms_template
#: model:ir.ui.menu,name:sms.sms_template_menu
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_tree
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_tree
msgid "SMS Templates"
msgstr "SMS-mallit"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_template_reset.py:0
msgid "SMS Templates have been reset"
msgstr "SMS-mallit on nollattu"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_tracker_ids
msgid "SMS Trackers"
msgstr "Tekstiviestien seurannat"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "SMS content"
msgstr "SMS-sisältö"

#. module: sms
#. odoo-python
#: code:addons/sms/models/ir_actions_server.py:0
msgid "SMS template model of %(action_name)s does not match action model."
msgstr "SMS-mallin malli %(action_name)s ei vastaa toimintamallia."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__sms_tracker_id
msgid "SMS trackers"
msgstr "Tekstiviestien seurannat"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_tracker__sms_uuid
msgid "SMS uuid"
msgstr "SMS uuid"

#. module: sms
#: model:ir.actions.server,name:sms.ir_cron_sms_scheduler_action_ir_actions_server
msgid "SMS: SMS Queue Manager"
msgstr "TEKSTIVIESTI: SMS Jonon hallinta"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_sanitized
#: model:ir.model.fields,field_description:sms.field_sms_composer__sanitized_numbers
msgid "Sanitized Number"
msgstr "Puhdistettu numero"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_search
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_search
msgid "Search SMS Templates"
msgstr "Haku SMS-malleista"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Send & Close"
msgstr "Lähetä ja sulje"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_tree
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Send Now"
msgstr "Lähetä heti"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_button/sms_button.js:0
#: model:ir.actions.act_window,name:sms.res_partner_act_window_sms_composer_multi
#: model:ir.actions.act_window,name:sms.res_partner_act_window_sms_composer_single
#: model:ir.actions.act_window,name:sms.sms_composer_action_form
#: model:ir.model.fields.selection,name:sms.selection__ir_actions_server__state__sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Send SMS"
msgstr "Lähetä tekstiviesti"

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_template.py:0
msgid "Send SMS (%s)"
msgstr "Lähetä tekstiviesti (%s)"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_ir_actions_server__sms_method
#: model:ir.model.fields,field_description:sms.field_ir_cron__sms_method
msgid "Send SMS As"
msgstr "Lähetä tekstiviesti kuten"

#. module: sms
#: model:ir.model,name:sms.model_sms_composer
msgid "Send SMS Wizard"
msgstr "Lähetä tekstiviesti ohjatulla toiminnolla"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_composer__composition_mode__mass
msgid "Send SMS in batch"
msgstr "Lähetä tekstiviestejä erissä"

#. module: sms
#: model:iap.service,description:sms.iap_service_sms
msgid "Send SMS to your contacts directly from your database."
msgstr "Lähetä tekstiviestejä yhteystiedoillesi suoraan tietokannastasi."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Send an SMS"
msgstr "Lähetä tekstiviesti"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__mass_force_send
msgid "Send directly"
msgstr "Lähetä suoraan"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_composer__composition_mode__numbers
msgid "Send to numbers"
msgstr "Lähetä numeroihin"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
msgid "Send verification code"
msgstr "Lähetä vahvistuskoodi"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_iap_account__sender_name
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__sender_name
msgid "Sender Name"
msgstr "Lähettäjän nimi"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_resend_action
msgid "Sending Failures"
msgstr "Lähetysvirheet"

#. module: sms
#. odoo-python
#: code:addons/sms/models/ir_actions_server.py:0
msgid "Sending SMS can only be done on a not transient mail.thread model"
msgstr ""

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__pending
msgid "Sent"
msgstr "Lähetetty"

#. module: sms
#: model:ir.model,name:sms.model_ir_actions_server
msgid "Server Action"
msgstr "Palvelintoiminto"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_server
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_server
msgid "Server Error"
msgstr "Palvelinvirhe"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid "Set sender name"
msgstr "Aseta lähettäjän nimi"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Set up an account"
msgstr "Tilin perustaminen"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__sidebar_action_id
msgid "Sidebar action"
msgstr "Sivupalkin toiminto"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__sidebar_action_id
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr ""
"Sivupalkin toiminto, jolla tämä malli saadaan käyttöön liittyvän "
"asiakirjamallin tietueissa"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__comment_single_recipient
msgid "Single Mode"
msgstr "Single Mode"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid "Skip for now"
msgstr "Ohita toistaiseksi"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__sms_resend_id
msgid "Sms Resend"
msgstr "SMS uudelleenlähetys"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__sms_template_id
msgid "Sms Template"
msgstr "SMS-malli"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_number
msgid "Stored Recipient Number"
msgstr "Tallennettu vastaanottajan numero"

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid "Success"
msgstr "Onnistuminen"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__template_ids
msgid "Template"
msgstr "Malli"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__template_fs
msgid "Template Filename"
msgstr "Mallin tiedostonimi"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_template_preview_action
msgid "Template Preview"
msgstr "Mallin esikatselu"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__lang
msgid "Template Preview Language"
msgstr "Mallin kielen esikatselu"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_template_action
msgid "Templates"
msgstr "Mallit"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"The SMS Service is currently unavailable for new users and new accounts "
"registrations are suspended."
msgstr ""
"Tekstiviestipalvelu ei ole tällä hetkellä uusien käyttäjien käytettävissä. "
"Uusien tilien rekisteröinti on keskeytetty."

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid "The SMS Text Messages could not be resent."
msgstr "Tekstiviestejä ei voitu lähettää uudelleen."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "The content of the message violates rules applied by our providers."
msgstr "Viestin sisältö rikkoo palveluntarjoajiemme soveltamia sääntöjä."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "The destination country is not supported."
msgstr "Kohdemaa ei ole tuettu."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "The number you're trying to reach is not correctly formatted."
msgstr "Numero, johon yrität soittaa, ei ole oikein muotoiltu."

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__model_id
#: model:ir.model.fields,help:sms.field_sms_template_preview__model_id
msgid "The type of document this template can be used with"
msgstr "Asiakirjatyyppi, jonka kanssa tätä mallia voidaan käyttää"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "The verification code is incorrect."
msgstr "Vahvistuskoodi on virheellinen."

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid "There are no SMS Text Messages to resend."
msgstr "Uudelleenlähetettäviä tekstiviestejä ei ole."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "This SMS has been removed as the number was already used."
msgstr "Tämä tekstiviesti on poistettu, koska numero oli jo käytössä."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"This account already has an existing sender name and it cannot be changed."
msgstr ""
"Tällä tilillä on jo olemassa oleva lähettäjän nimi, eikä sitä voi muuttaa."

#. module: sms
#: model:ir.model.fields,help:sms.field_iap_account__sender_name
msgid "This is the name that will be displayed as the sender of the SMS."
msgstr "Tämä on nimi, joka näytetään tekstiviestin lähettäjänä."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.res_partner_view_form
msgid ""
"This phone number is blacklisted for SMS Marketing. Click to unblacklist."
msgstr ""
"Tämä puhelinnumero on markkinointikieltolistalla tekstiviestimarkkinointia "
"varten. Klikkaa poistaaksesi markkinointikiellon."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "This phone number/account has been banned from our service."
msgstr "Tämä puhelinnumero tai on poistettu palvelustamme."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__resend
msgid "Try Again"
msgstr "Yritä uudelleen"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_ir_actions_server__state
#: model:ir.model.fields,field_description:sms.field_ir_cron__state
#: model:ir.model.fields,field_description:sms.field_mail_mail__message_type
#: model:ir.model.fields,field_description:sms.field_mail_message__message_type
msgid "Type"
msgstr "Tyyppi"

#. module: sms
#: model:ir.model.fields,help:sms.field_ir_actions_server__state
#: model:ir.model.fields,help:sms.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Update a Record': update the values of a record\n"
"- 'Create Activity': create an activity (Discuss)\n"
"- 'Send Email': post a message, a note or send an email (Discuss)\n"
"- 'Send SMS': send SMS, log them on documents (SMS)- 'Add/Remove Followers': add or remove followers to a record (Discuss)\n"
"- 'Create Record': create a new record with new values\n"
"- 'Execute Code': a block of Python code that will be executed\n"
"- 'Send Webhook Notification': send a POST request to an external system, also known as a Webhook\n"
"- 'Execute Existing Actions': define an action that triggers several other server actions\n"
msgstr ""
"Palvelimen toimintatyyppi. Käytettävissä ovat seuraavat arvot:\n"
"- päivitä tietue: päivitä tietueen arvot\n"
"- 'Create Activity': luo toiminnon (Keskustele)\n"
"- 'Lähetä sähköposti': lähettää viestin, huomautuksen tai sähköpostin (Keskustele)\n"
"- 'Lähetä tekstiviesti': lähetä tekstiviestejä, kirjaa ne asiakirjoihin (tekstiviesti)- 'Lisää/poista seuraajia': lisää tai poista seuraajia tietueeseen (Keskustele)\n"
"- 'Luo tietue': luo uusi tietue uusilla arvoilla\n"
"- 'Execute Code': Python-koodin lohko, joka suoritetaan\n"
"- 'Send Webhook Notification': lähetä POST-pyyntö ulkoiseen järjestelmään, joka tunnetaan myös nimellä Webhook\n"
"- 'Execute Existing Actions': Määritä toiminto, joka käynnistää useita muita palvelintoimintoja\n"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__uuid
msgid "UUID"
msgstr "UUID"

#. module: sms
#: model:ir.model.constraint,message:sms.constraint_sms_sms_uuid_unique
msgid "UUID must be unique"
msgstr "UUID:n on oltava yksilöllinen"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__unknown
msgid "Unknown error"
msgstr "Tuntematon virhe"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_acc
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_acc
msgid "Unregistered Account"
msgstr "Rekisteröimätön tili"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__template_id
msgid "Use Template"
msgstr "Käytä mallipohjaa"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__mass_use_blacklist
msgid "Use blacklist"
msgstr "Käytä mustaa listaa"

#. module: sms
#: model:ir.model.fields,help:sms.field_mail_mail__message_type
#: model:ir.model.fields,help:sms.field_mail_message__message_type
msgid ""
"Used to categorize message generator\n"
"'email': generated by an incoming email e.g. mailgateway\n"
"'comment': generated by user input e.g. through discuss or composer\n"
"'email_outgoing': generated by a mailing\n"
"'notification': generated by system e.g. tracking messages\n"
"'auto_comment': generated by automated notification mechanism e.g. acknowledgment\n"
"'user_notification': generated for a specific recipient"
msgstr ""
"Käytetään viestigeneraattorin luokitteluun\n"
"'email': syntyy saapuvan sähköpostin, esim. mailgatewayn, tuloksena\n"
"'kommentti': syntyy käyttäjän syötteestä esim. discuss- tai composer-ohjelmassa\n"
"'email_outgoing': syntyy postituksesta\n"
"'notification': järjestelmän tuottama esim. seurantaviestit\n"
"'auto_comment': automaattisen ilmoitusmekanismin tuottama, esim. kuittaus\n"
"'user_notification': luodaan tietylle vastaanottajalle"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__verification_code
msgid "Verification Code"
msgstr "Vahvistuskoodi"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_ids_count
msgid "Visible records count"
msgstr "Näkyvien tietueiden määrä"

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid "Warning"
msgstr "Varoitus"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "We were not able to find your account in our database."
msgstr "Tiliäsi ei löydy tietokannastamme."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"We were not able to reach you via your phone number. If you have requested "
"multiple codes recently, please retry later."
msgstr ""
"Emme tavoittaneet sinua puhelinnumerosi kautta. Jos olet pyytänyt useita "
"koodeja äskettäin, yritä myöhemmin uudelleen."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__website_message_ids
msgid "Website Messages"
msgstr "Verkkosivun ilmoitukset"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__website_message_ids
msgid "Website communication history"
msgstr "Verkkosivun viestihistoria"

#. module: sms
#: model:ir.model.fields,help:sms.field_ir_model__is_mail_thread_sms
msgid "Whether this model supports messages and notifications through SMS"
msgstr "Tukeeko tämä malli viestejä ja ilmoituksia tekstiviestillä"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_sms__to_delete
msgid ""
"Will automatically be deleted, while notifications will not be deleted in "
"any case."
msgstr ""
"Poistetaan automaattisesti, kun taas ilmoituksia ei poisteta missään "
"tapauksessa."

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_number_format
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_number_format
msgid "Wrong Number Format"
msgstr "Väärä numeromuoto"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_resend.py:0
msgid "You do not have access to the message and/or related document."
msgstr "Sinulla ei ole pääsyä viestiin ja/tai siihen liittyvään asiakirjaan."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "You don't have an eligible IAP account."
msgstr "Sinulla ei ole hyväksyttävää IAP-tiliä."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "You don't have enough credits on your IAP account."
msgstr "Sinulla ei ole tarpeeksi krediittejä IAP-tililläsi."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "You tried too many times. Please retry later."
msgstr "Yritit liian monta kertaa. Yritä uudelleen myöhemmin."

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.js:0
msgid ""
"Your SMS Text Message must include at least one non-whitespace character"
msgstr "Tekstiviestissäsi on oltava vähintään yksi muu kuin välilyöntimerkki"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_account_code.py:0
msgid "Your SMS account has been successfully registered."
msgstr "SMS-tilisi on rekisteröity onnistuneesti."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"Your sender name must be between 3 and 11 characters long and only contain "
"alphanumeric characters."
msgstr ""
"Lähettäjän nimen on oltava 3-11 merkkiä pitkä ja sisällettävä vain "
"aakkosnumeerisia merkkejä."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid ""
"Your sender name must be between 3 and 11 characters long and only contain alphanumeric characters.\n"
"                        It must fit your company name, and you aren't allowed to modify it once you registered one, choose it carefully."
msgstr ""
"Lähettäjän nimen on oltava 3-11 merkkiä pitkä ja sisällettävä vain aakkosnumeerisia merkkejä.\n"
"                        Sen on vastattava yrityksesi nimeä, etkä saa muuttaa sitä, kun olet rekisteröinyt sen, joten valitse se huolellisesti."

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "Your sms account has not been activated yet."
msgstr "SMS-tiliäsi ei ole vielä aktivoitu."

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.xml:0
msgid "characters"
msgstr "merkkiä"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "e.g. +1 415 555 0100"
msgstr "esim. +1 415 555 0100"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "e.g. Calendar Reminder"
msgstr "esim. kalenterimuistutus"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "e.g. Contact"
msgstr "esim. yhteystiedot"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "e.g. en_US or {{ object.partner_id.lang }}"
msgstr "esim. en_US tai {{ object.partner_id.lang }}"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "out of"
msgstr "josta"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid ""
"recipients have an invalid phone number and will not receive this text "
"message."
msgstr ""
"vastaanottajilla on virheellinen puhelinnumero, eivätkä he saa tätä "
"tekstiviestiä."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "record:"
msgstr "tietue:"
