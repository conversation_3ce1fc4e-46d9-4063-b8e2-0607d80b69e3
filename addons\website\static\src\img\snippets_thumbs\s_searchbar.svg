<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <linearGradient id="linearGradient-1" x1="50%" x2="50%" y1="0%" y2="100%">
      <stop offset="0%" stop-color="#00A09D"/>
      <stop offset="100%" stop-color="#00E2FF"/>
    </linearGradient>
    <path id="path-2" d="M62 13.5c0-.55-.196-1.022-.587-1.413A1.926 1.926 0 0 0 60 11.5c-.55 0-1.022.196-1.413.587A1.926 1.926 0 0 0 58 13.5c0 .55.196 1.022.587 1.413.391.391.862.587 1.413.587.55 0 1.022-.196 1.413-.587.391-.391.587-.862.587-1.413zm2 3.462a.517.517 0 0 1-.16.378.517.517 0 0 1-.378.16.5.5 0 0 1-.38-.16l-1.442-1.439a2.88 2.88 0 0 1-1.678.522 2.91 2.91 0 0 1-1.151-.233 2.961 2.961 0 0 1-.947-.631 2.961 2.961 0 0 1-.63-.947 2.91 2.91 0 0 1-.234-1.15c0-.402.078-.785.233-1.151a2.98 2.98 0 0 1 .631-.947c.266-.265.581-.475.947-.63a2.91 2.91 0 0 1 1.15-.234c.402 0 .785.078 1.151.233.366.156.682.366.947.631.265.266.475.581.63.947.156.366.234.75.234 1.15a2.88 2.88 0 0 1-.522 1.679l1.443 1.443c.104.104.156.23.156.379z"/>
    <filter id="filter-3" width="114.3%" height="128.6%" x="-7.1%" y="-7.1%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.4 0"/>
    </filter>
    <path id="path-4" d="M55 10v8H34v-8h21zm-1 1H35v6h19v-6z"/>
    <filter id="filter-5" width="104.8%" height="125%" x="-2.4%" y="-6.2%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_products_searchbar">
      <rect width="82" height="60" class="bg"/>
      <g class="group" transform="translate(0 16)">
        <rect width="82" height="28" fill="url(#linearGradient-1)" class="rectangle" opacity=".4"/>
        <g class="box_solid" transform="translate(18 9.5)">
          <rect width="11" height="10" class="rectangle"/>
          <path fill="#FFF" fill-opacity=".95" d="M0 2.174L5.077 4.1V10L0 7.91V2.174zm11 0V7.91L5.923 10V4.1L11 2.174zM5.5 0L11 1.472 5.5 3.478 0 1.472 5.5 0z" class="combined_shape"/>
        </g>
        <g class="search">
          <use fill="#000" filter="url(#filter-3)" xlink:href="#path-2"/>
          <use fill="#FFF" fill-opacity=".95" xlink:href="#path-2"/>
        </g>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-5)" xlink:href="#path-4"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-4"/>
        </g>
      </g>
    </g>
  </g>
</svg>
