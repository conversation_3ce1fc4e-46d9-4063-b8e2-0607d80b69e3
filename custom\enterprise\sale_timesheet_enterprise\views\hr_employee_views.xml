<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_employee_form" model="ir.ui.view">
        <field name="name">view.employee.form.inherit.hr.employee.billable.time.target</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_form"/>
        <field name="arch" type="xml">
            <group name="application_group" position="inside">
                <field name="show_billable_time_target" invisible="1"/>
                <span class="o_wrap_label" invisible="not show_billable_time_target">
                    <label for="billable_time_target" groups="hr_timesheet.group_hr_timesheet_approver"/>
                </span>
                <span class="d-flex w-50" name="billable_time_target" invisible="not show_billable_time_target">
                    <field name="billable_time_target" widget="timesheet_uom_no_toggle" groups="hr_timesheet.group_hr_timesheet_approver"/>
                    <span class="m-14 w-100" groups="hr_timesheet.group_hr_timesheet_approver">per month</span>
                </span>
            </group>
        </field>
    </record>
</odoo>
