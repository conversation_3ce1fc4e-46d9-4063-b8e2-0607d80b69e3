# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_margin
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__purchase_num_invoiced
msgid "# Invoiced in Purchase"
msgstr "# Счет-фактура при покупке"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__sale_num_invoiced
msgid "# Invoiced in Sale"
msgstr "# Счет-фактура при продаже"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "# Purchased"
msgstr "# Куплено"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Analysis Criteria"
msgstr "Критерии анализа"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__sale_avg_price
msgid "Avg. Price in Customer Invoices."
msgstr "Avg. Цена в счетах-фактурах клиентов."

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__purchase_avg_price
msgid "Avg. Price in Vendor Bills"
msgstr "Avg. Цена в счетах поставщиков"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__purchase_avg_price
msgid "Avg. Purchase Unit Price"
msgstr "Avg. Цена единицы покупки"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__sale_avg_price
msgid "Avg. Sale Unit Price"
msgstr "Avg. Цена продажи единицы продукции"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Avg. Unit Price"
msgstr "Avg. Цена единицы продукции"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "Cancel"
msgstr "Отменить"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Catalog Price"
msgstr "Цена по каталогу"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__create_uid
msgid "Created by"
msgstr "Создано"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__create_date
msgid "Created on"
msgstr "Создано"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: product_margin
#: model:ir.model.fields.selection,name:product_margin.selection__product_margin__invoice_state__draft_open_paid
#: model:ir.model.fields.selection,name:product_margin.selection__product_product__invoice_state__draft_open_paid
msgid "Draft, Open and Paid"
msgstr "Черновик, открытый и оплаченный"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__expected_margin
msgid "Expected Margin"
msgstr "Ожидаемая маржа"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__expected_margin_rate
msgid "Expected Margin (%)"
msgstr "Ожидаемая маржа (%)"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__sale_expected
msgid "Expected Sale"
msgstr "Ожидаемая распродажа"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__expected_margin
msgid "Expected Sale - Normal Cost"
msgstr "Ожидаемая продажа - нормальная стоимость"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__sales_gap
msgid "Expected Sale - Turn Over"
msgstr "Ожидаемые продажи - Оборот"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__expected_margin_rate
msgid "Expected margin * 100 / Expected Sale"
msgstr "Ожидаемая маржа * 100 / Ожидаемая продажа"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__from_date
msgid "From"
msgstr "От"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "General Information"
msgstr "Основная информация"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__id
msgid "ID"
msgstr "ID"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__invoice_state
#: model:ir.model.fields,field_description:product_margin.field_product_product__invoice_state
msgid "Invoice State"
msgstr "Состояние счета-фактуры"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__date_from
msgid "Margin Date From"
msgstr "Дата наложения От"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__date_to
msgid "Margin Date To"
msgstr "Дата маржи До"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Margins"
msgstr "Маржа"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__normal_cost
msgid "Normal Cost"
msgstr "Нормальная стоимость"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__purchase_gap
msgid "Normal Cost - Total Cost"
msgstr "Нормальные затраты - Общие затраты"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "Open Margins"
msgstr "Открытая маржа"

#. module: product_margin
#: model:ir.model.fields.selection,name:product_margin.selection__product_margin__invoice_state__open_paid
#: model:ir.model.fields.selection,name:product_margin.selection__product_product__invoice_state__open_paid
msgid "Open and Paid"
msgstr "Открытые и платные"

#. module: product_margin
#: model:ir.model.fields.selection,name:product_margin.selection__product_margin__invoice_state__paid
#: model:ir.model.fields.selection,name:product_margin.selection__product_product__invoice_state__paid
msgid "Paid"
msgstr "Оплачено"

#. module: product_margin
#: model:ir.model,name:product_margin.model_product_margin
msgid "Product Margin"
msgstr "Маржа продукта"

#. module: product_margin
#. odoo-python
#: code:addons/product_margin/wizard/product_margin.py:0
#: model:ir.actions.act_window,name:product_margin.product_margin_act_window
#: model:ir.ui.menu,name:product_margin.menu_action_product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_graph
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Product Margins"
msgstr "Рентабельность продукции "

#. module: product_margin
#: model:ir.model,name:product_margin.model_product_product
msgid "Product Variant"
msgstr "Вариант продукта"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "Properties categories"
msgstr "Категории свойств"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__purchase_gap
msgid "Purchase Gap"
msgstr "Разрыв в покупке"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Purchases"
msgstr "Покупки"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Sales"
msgstr "Продажи"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__sales_gap
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Sales Gap"
msgstr "Разрыв в продажах"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Standard Price"
msgstr "Стандартная цена"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__normal_cost
msgid "Sum of Multiplication of Cost price and quantity of Vendor Bills"
msgstr "Сумма умножения себестоимости и количества счетов поставщика"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__turnover
msgid ""
"Sum of Multiplication of Invoice price and quantity of Customer Invoices"
msgstr "Сумма умножения цены счета-фактуры и количества счетов-фактур клиента"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__total_cost
msgid "Sum of Multiplication of Invoice price and quantity of Vendor Bills "
msgstr "Сумма умножения цены счета-фактуры и количества счетов поставщика"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__sale_expected
msgid ""
"Sum of Multiplication of Sale Catalog price and quantity of Customer "
"Invoices"
msgstr ""
"Сумма умножения цены каталога продаж и количества счетов-фактур клиентов"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__sale_num_invoiced
msgid "Sum of Quantity in Customer Invoices"
msgstr "Сумма количества в счетах-фактурах клиентов"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__purchase_num_invoiced
msgid "Sum of Quantity in Vendor Bills"
msgstr "Сумма количества в счетах поставщиков"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__to_date
msgid "To"
msgstr "Кому"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__total_cost
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Total Cost"
msgstr "Общая стоимость"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__total_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Total Margin"
msgstr "Общая маржа"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__total_margin_rate
msgid "Total Margin Rate(%)"
msgstr "Общая маржинальная ставка (%)"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__total_margin_rate
msgid "Total margin * 100 / Turnover"
msgstr "Общая маржа * 100 / Оборот"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__turnover
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Turnover"
msgstr "Rotazione"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__total_margin
msgid "Turnover - Total cost"
msgstr "Выручка — Себестоимость"
