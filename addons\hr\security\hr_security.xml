<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="base.module_category_human_resources_employees" model="ir.module.category">
        <field name="sequence">9</field>
    </record>

    <record id="group_hr_user" model="res.groups">
        <field name="name">Officer: Manage all employees</field>
        <field name="category_id" ref="base.module_category_human_resources_employees"/>
        <field name="implied_ids" eval="[(6, 0, [ref('base.group_user')])]"/>
        <field name="comment">The user will be able to approve document created by employees.</field>
    </record>

    <record id="group_hr_manager" model="res.groups">
        <field name="name">Administrator</field>
        <field name="comment">The user will have access to the human resources configuration as well as statistic reports.</field>
        <field name="category_id" ref="base.module_category_human_resources_employees"/>
        <field name="implied_ids" eval="[(4, ref('group_hr_user'))]"/>
        <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
    </record>

<data noupdate="1">
    <record id="base.default_user" model="res.users">
        <field name="groups_id" eval="[(4,ref('group_hr_manager'))]"/>
    </record>

    <record id="hr_employee_comp_rule" model="ir.rule">
        <field name="name">Employee multi company rule</field>
        <field name="model_id" ref="model_hr_employee"/>
        <field name="domain_force">['|', '|', '|',
            ('company_id', 'in', company_ids + [False]),
            ('parent_id.user_id', '=', user.id),
            ('id', '=', user.employee_id.parent_id.id),
            ('user_id', '=', user.id)
        ]</field>
    </record>

    <record id="hr_dept_comp_rule" model="ir.rule">
        <field name="name">Department multi company rule</field>
        <field name="model_id" ref="model_hr_department"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    <record id="hr_employee_public_comp_rule" model="ir.rule">
        <field name="name">Employee multi company rule</field>
        <field name="model_id" ref="model_hr_employee_public"/>
        <field name="domain_force">['|', '|', '|',
            ('company_id', 'in', company_ids + [False]),
            ('parent_id.user_id', '=', user.id),
            ('id', '=', user.employee_id.parent_id.id),
            ('user_id', '=', user.id)
        ]</field>
    </record>

    <record id="hr_job_comp_rule" model="ir.rule">
        <field name="name">Job multi company rule</field>
        <field name="model_id" ref="model_hr_job"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    <record id="ir_rule_res_partner_bank_internal_users" model="ir.rule">
        <field name="name">HR: Prevent non HR officers from accessing employee bank accounts</field>
        <field name="model_id" ref="base.model_res_partner_bank"/>
        <field name="domain_force">[('partner_id.employee_ids', '=', False)]</field>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <record id="ir_rule_res_partner_bank_employees" model="ir.rule">
        <field name="name">HR: Allow HR officers from accessing employee bank accounts</field>
        <field name="model_id" ref="base.model_res_partner_bank"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('hr.group_hr_user'))]"/>
    </record>

    <record id="ir_rule_hr_contract_type_multi_company" model="ir.rule">
        <field name="name">HR Contract Type: Multi Company</field>
        <field name="model_id" ref="model_hr_contract_type"/>
        <field name="domain_force">['|', ('country_id', '=', False), ('country_id', 'in', user.env.companies.country_id.ids)]</field>
    </record>

    <record id="mail_plan_rule_group_hr_manager" model="ir.rule">
        <field name="name">Manager can edit employee plan</field>
        <field name="groups" eval="[(4, ref('group_hr_manager'))]"/>
        <field name="model_id" ref="mail.model_mail_activity_plan"/>
        <field name="domain_force">[('res_model', '=', 'hr.employee')]</field>
        <field name="perm_read" eval="False"/>
    </record>

    <record id="mail_plan_templates_rule_group_hr_manager" model="ir.rule">
        <field name="name">Manager can edit employee plan template</field>
        <field name="groups" eval="[(4, ref('group_hr_manager'))]"/>
        <field name="model_id" ref="mail.model_mail_activity_plan_template"/>
        <field name="domain_force">[('plan_id.res_model', '=', 'hr.employee')]</field>
        <field name="perm_read" eval="False"/>
    </record>

</data>
</odoo>
