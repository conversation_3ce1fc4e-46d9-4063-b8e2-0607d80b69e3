# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_mondialrelay
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: delivery_mondialrelay
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_choose_delivery_carrier__mondialrelay_brand
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_delivery_carrier__mondialrelay_brand
msgid "Brand Code"
msgstr "Код бренда"

#. module: delivery_mondialrelay
#: model:ir.model,name:delivery_mondialrelay.model_res_partner
msgid "Contact"
msgstr "Контакты"

#. module: delivery_mondialrelay
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_choose_delivery_carrier__shipping_country_code
msgid "Country Code"
msgstr "Код страны"

#. module: delivery_mondialrelay
#: model:ir.model,name:delivery_mondialrelay.model_choose_delivery_carrier
msgid "Delivery Carrier Selection Wizard"
msgstr "Мастер выбора курьера доставки"

#. module: delivery_mondialrelay
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_choose_delivery_carrier__is_mondialrelay
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_delivery_carrier__is_mondialrelay
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_res_partner__is_mondialrelay
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_res_users__is_mondialrelay
msgid "Is Mondialrelay"
msgstr "Mondialrelay"

#. module: delivery_mondialrelay
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_choose_delivery_carrier__mondialrelay_last_selected
msgid "Last Relay Selected"
msgstr "Последнее выбранное реле"

#. module: delivery_mondialrelay
#: model:delivery.carrier,name:delivery_mondialrelay.delivery_carrier_mondialrelay_be_lu
#: model:delivery.carrier,name:delivery_mondialrelay.delivery_carrier_mondialrelay_es
#: model:delivery.carrier,name:delivery_mondialrelay.delivery_carrier_mondialrelay_fr_nl
#: model:product.template,name:delivery_mondialrelay.product_product_delivery_mondialrelay_product_template
msgid "Mondial Relay"
msgstr "Эстафета Мондиаль"

#. module: delivery_mondialrelay
#. odoo-python
#: code:addons/delivery_mondialrelay/models/sale_order.py:0
msgid ""
"Mondial Relay mismatching between delivery method and shipping address."
msgstr "Несоответствие способа доставки и адреса доставки Mondial Relay."

#. module: delivery_mondialrelay
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_choose_delivery_carrier__mondialrelay_allowed_countries
msgid "Mondialrelay Allowed Countries"
msgstr "Mondialrelay Разрешенные страны"

#. module: delivery_mondialrelay
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_choose_delivery_carrier__mondialrelay_last_selected_id
msgid "Mondialrelay Last Selected"
msgstr "Mondialrelay Последние выбранные"

#. module: delivery_mondialrelay
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_choose_delivery_carrier__mondialrelay_colLivMod
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_delivery_carrier__mondialrelay_packagetype
msgid "Mondialrelay Packagetype"
msgstr "Тип упаковки Mondialrelay"

#. module: delivery_mondialrelay
#. odoo-python
#: code:addons/delivery_mondialrelay/models/delivery_carrier.py:0
msgid "Operation not supported"
msgstr "Операция не поддерживается"

#. module: delivery_mondialrelay
#. odoo-python
#: code:addons/delivery_mondialrelay/wizard/choose_delivery_carrier.py:0
msgid "Please, choose a Parcel Point"
msgstr "Пожалуйста, выберите точку посылки"

#. module: delivery_mondialrelay
#: model:ir.model,name:delivery_mondialrelay.model_sale_order
msgid "Sales Order"
msgstr "Заказ на продажу"

#. module: delivery_mondialrelay
#: model:ir.model,name:delivery_mondialrelay.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Методы доставки"

#. module: delivery_mondialrelay
#: model:ir.model.fields,help:delivery_mondialrelay.field_choose_delivery_carrier__shipping_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Код страны ISO в двух символах.\n"
"Вы можете использовать это поле для быстрого поиска."

#. module: delivery_mondialrelay
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_choose_delivery_carrier__shipping_zip
msgid "Zip"
msgstr "Индекс"
