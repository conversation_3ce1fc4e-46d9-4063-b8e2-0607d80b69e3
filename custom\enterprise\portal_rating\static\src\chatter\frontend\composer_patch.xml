<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-inherit="mail.Composer" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('o-mail-Composer-coreMain')]" position="before">
            <div t-if="env.displayRating and !message" class="o-mail-Composer-starCard d-flex">
                <div class="o-mail-Composer-stars enabled" t-on-mouseleave="() => portalState.starValue = portalState.ratingValue">
                    <t t-set="index" t-value="0" />
                    <t t-foreach="Array(portalState.starValue)" t-as="num" t-key="num_index">
                        <i class="fa fa-star" role="img" aria-label="Full star" t-att-index="index" t-on-mousemove="onMoveStar" t-on-click="onClickStar"/>
                        <t t-set="index" t-value="index + 1" />
                    </t>
                    <t t-foreach="Array(5 - portalState.starValue)" t-as="num" t-key="num_index">
                        <i class="fa fa-star-o text-black-25" role="img" aria-label="Empty star" t-att-index="index" t-on-mousemove="onMoveStar" t-on-click="onClickStar"/>
                        <t t-set="index" t-value="index + 1" />
                    </t>
                </div>
            </div>
        </xpath>
    </t>
</templates>
