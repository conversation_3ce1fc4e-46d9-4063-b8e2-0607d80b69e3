# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_matrix
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid ""
"<small>Switch to the \"else\" condition of this block to view or edit the "
"table.</small>"
msgstr "<small>このブロックの \"他の\"条件に切り替えて、テーブルを表示または編集します</small>"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid "<strong>Product matrix block</strong>"
msgstr "<strong>プロダクトマトリクスブロック</strong>"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_color_1
msgid "Blue"
msgstr "青"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid "Cell name"
msgstr "セル名"

#. module: product_matrix
#. odoo-javascript
#: code:addons/product_matrix/static/src/xml/product_matrix_dialog.xml:0
msgid "Choose Product Variants"
msgstr "プロダクトバリアントを選択"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid "Column name"
msgstr "カラム名"

#. module: product_matrix
#. odoo-javascript
#: code:addons/product_matrix/static/src/xml/product_matrix_dialog.xml:0
msgid "Confirm"
msgstr "確定"

#. module: product_matrix
#. odoo-javascript
#: code:addons/product_matrix/static/src/xml/product_matrix_dialog.xml:0
msgid "Discard"
msgstr "破棄"

#. module: product_matrix
#: model:product.attribute,name:product_matrix.product_attribute_gender
msgid "Gender"
msgstr "性別"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_m
msgid "Men"
msgstr "メンズ"

#. module: product_matrix
#: model:product.template,name:product_matrix.matrix_product_template_shirt
msgid "My Company Tshirt (GRID)"
msgstr "自分の会社Tシャツ (GRID)"

#. module: product_matrix
#. odoo-javascript
#: code:addons/product_matrix/static/src/xml/product_matrix.xml:0
msgid "Not available"
msgstr "利用不可"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_color_2
msgid "Pink"
msgstr "ピンク"

#. module: product_matrix
#: model:ir.model,name:product_matrix.model_product_template
msgid "Product"
msgstr "プロダクト"

#. module: product_matrix
#: model:ir.model,name:product_matrix.model_product_template_attribute_value
msgid "Product Template Attribute Value"
msgstr "プロダクトテンプレート属性値"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_color_4
msgid "Rainbow"
msgstr "レインボー"

#. module: product_matrix
#: model:product.template,description_sale:product_matrix.matrix_product_template_shirt
msgid "Show your company love around you =)."
msgstr "愛社心を周りに見せましょう =)"

#. module: product_matrix
#: model:product.attribute,name:product_matrix.product_attribute_size
msgid "Size"
msgstr "サイズ"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid ""
"The matrix of product variants of this order will be displayed here, if "
"there are any."
msgstr "このオーダのプロダクトバリアントのマトリックスがあれば、ここに表示されます。"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.extra_price
msgid "Variant price"
msgstr "バリアント価格"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_w
msgid "Women"
msgstr "ウィメンズ"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_size_xl
msgid "XL"
msgstr "XL"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_size_xs
msgid "XS"
msgstr "XS"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_color_3
msgid "Yellow"
msgstr "黄"
