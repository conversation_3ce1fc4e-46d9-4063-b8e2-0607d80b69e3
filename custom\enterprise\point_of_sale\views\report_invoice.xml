<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="point_of_sale.report_invoice_document" inherit_id="account.report_invoice_document">
        <xpath expr="//i[hasclass('oe_payment_label')]" position="inside">
            <t t-if="payment_vals.get('pos_payment_name')">
                using <t t-esc="payment_vals['pos_payment_name']" />
            </t>
        </xpath>
        <xpath expr="//div[@name='origin']" position="after">
            <div class="col" t-if="o.pos_refunded_invoice_ids" name="source_invoice">
                <strong>Source Invoice</strong>
                <div t-field="o.pos_refunded_invoice_ids"/>
            </div>
        </xpath>
    </template>
</odoo>
