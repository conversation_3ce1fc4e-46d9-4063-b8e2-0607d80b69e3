.close-pos-popup {
    max-width: 800px !important;
    max-height: 800px;

    th:nth-child(2),
    td:nth-child(2) {
        text-align: right;
        padding-right: map-get($spacers, 3);
        white-space: nowrap;
    }

    th:nth-child(3),
    td:nth-child(3) {
        padding-left: map-get($spacers, 1);
    }

    th:nth-child(4),
    td:nth-child(4) {
        text-align: right;
        padding-left: map-get($spacers, 2);
    }
}

.cash-overview tr td:first-child {
    padding-left: map-get($spacers, 2);
}

@media screen and (max-width: 768px) {
    .pos .close-pos-popup {
        overflow-y: auto;
    }

    .pos .payment-method-header {
        visibility: hidden;
    }
}
