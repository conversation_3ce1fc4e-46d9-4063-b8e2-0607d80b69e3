<svg width="232" height="137" viewBox="0 0 232 137" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10 2.5H222C226.142 2.5 229.5 5.85786 229.5 10V127C229.5 131.142 226.142 134.5 222 134.5H10C5.85787 134.5 2.5 131.142 2.5 127V10C2.5 5.85786 5.85786 2.5 10 2.5Z" fill="#714B67" fill-opacity="0.75" stroke="#714B67" stroke-width="5"/>
<path d="M98.7909 104.627V110.134H94V95H99.8098C104.629 95 107.039 96.4907 107.039 99.4717C107.039 101.225 106.036 102.581 104.031 103.54L109.198 110.134H103.764L100.004 104.627H98.7909ZM98.7909 101.552H99.6885C101.362 101.552 102.199 100.921 102.199 99.6582C102.199 98.6159 101.378 98.0951 99.737 98.0951H98.7909V101.552Z" fill="white"/>
<path d="M115.529 110.134H110.811V95H121.29V98.2813H115.529V101.169H120.841V104.451H115.529V110.134Z" fill="white"/>
<path d="M124.201 110.134V95H129.016V110.134H124.201Z" fill="white"/>
<path d="M147.913 102.246C147.913 104.772 147.096 106.718 145.463 108.084C143.838 109.45 141.549 110.134 138.598 110.134H132.861V95H138.998C141.844 95 144.04 95.6211 145.585 96.8631C147.137 98.1054 147.913 99.8996 147.913 102.246ZM142.94 102.391C142.94 101.004 142.617 99.9755 141.97 99.306C141.331 98.6368 140.357 98.3022 139.047 98.3022H137.652V106.78H138.719C140.175 106.78 141.242 106.421 141.921 105.703C142.6 104.979 142.94 103.874 142.94 102.391Z" fill="white"/>
<g clip-path="url(#clip0_11_36)">
<path d="M85.0402 66.9514C84.9849 66.3632 84.9112 65.8914 84.8989 65.4134C84.8313 62.4723 84.8497 59.5312 84.7145 56.5962C84.5363 52.7544 86.3122 50.2912 89.7227 48.6675C93.6616 46.7864 97.5514 44.795 101.361 42.6688C104.034 41.1738 106.517 39.3478 109.116 37.7241C113.08 35.2548 117.338 33.5146 122.033 33.0857C129.044 32.4423 135.404 34.2744 140.972 38.5451C147.08 43.2264 150.736 49.4518 151.424 57.0987C152.414 68.1278 148.1 76.7919 138.821 82.8886C134.642 85.6275 129.941 86.902 124.946 87C121.677 87.0613 118.586 86.1728 115.544 85.0576C111.722 83.6545 108.219 81.6202 104.704 79.6043C100.095 76.9634 95.3638 74.5799 90.3433 72.803C90.0054 72.6804 89.6797 72.5028 89.3294 72.3373C90.3863 71.3692 91.388 70.4378 92.408 69.531C92.5125 69.433 92.7214 69.4268 92.8873 69.4268C93.9443 69.4207 95.0012 69.4452 96.0581 69.4207C97.6435 69.3839 98.639 68.3791 98.6144 66.8105C98.553 62.4968 98.4731 58.1832 98.3809 53.8757C98.3441 52.1049 97.8279 51.6208 96.052 51.5902C95.118 51.5779 94.1839 51.5841 93.2499 51.6331C91.517 51.725 90.6014 52.7054 90.626 54.4272C90.6506 56.1428 90.7305 57.8585 90.7182 59.5741C90.7182 60.0582 90.5891 60.5974 90.3556 61.0201C89.2004 63.1647 87.6272 64.9661 85.7223 66.4796C85.538 66.6266 85.3352 66.7492 85.0341 66.9575L85.0402 66.9514ZM125.437 43.4776C116.263 43.4592 108.895 50.763 108.852 59.9356C108.809 68.9857 116.244 76.4304 125.333 76.4426C134.446 76.4549 141.906 69.0837 141.93 60.0337C141.961 50.8978 134.581 43.4899 125.431 43.4715L125.437 43.4776Z" fill="white"/>
<path d="M170.486 83.1704C169.484 83.0724 168.987 82.7538 168.747 82.1165C168.489 81.4241 168.661 80.8236 169.177 80.3028C171.014 78.4401 172.557 76.363 173.866 74.102C180.539 62.5458 178.407 47.4604 168.802 38.1714C168.409 37.7915 168.089 37.2216 167.991 36.6947C167.874 36.0574 168.28 35.5121 168.901 35.2364C169.57 34.9423 170.246 35.0158 170.726 35.5734C172.323 37.4238 174.068 39.1885 175.433 41.1983C184.109 53.9798 182.487 71.3508 171.666 82.38C171.285 82.7721 170.738 83.005 170.486 83.1704Z" fill="white"/>
<path d="M173.073 60.2358C172.987 66.4857 170.523 72.5701 165.631 77.6803C165.207 78.1215 164.753 78.4585 164.095 78.3972C163.444 78.3359 162.989 78.0112 162.737 77.423C162.448 76.7367 162.608 76.124 163.124 75.6031C164.71 74.01 166.031 72.2209 167.1 70.2479C171.795 61.5838 170.302 50.714 163.438 43.6246C163.216 43.3979 162.983 43.1774 162.762 42.9445C162.043 42.1909 162.018 41.2534 162.688 40.5855C163.321 39.9605 164.359 39.9115 164.999 40.6284C166.344 42.148 167.751 43.643 168.87 45.328C171.703 49.5804 173.042 54.3168 173.067 60.2358L173.073 60.2358Z" fill="white"/>
<path d="M156.193 71.743C156.494 71.2957 156.746 70.7994 157.108 70.4011C160.967 66.2223 162.362 61.3694 161.115 55.8181C160.482 52.9811 159.062 50.5486 157.022 48.4653C156.721 48.1589 156.432 47.7852 156.303 47.3869C156.07 46.6639 156.432 45.9408 157.084 45.5793C157.686 45.2423 158.466 45.3587 159.044 45.9163C161.846 48.6124 163.696 51.866 164.421 55.671C165.631 62.0618 164.009 67.6928 159.579 72.4967C159.382 72.7111 159.173 72.9317 158.94 73.1033C158.405 73.5138 157.803 73.5689 157.207 73.2626C156.641 72.9685 156.34 72.4844 156.193 71.7369L156.193 71.743Z" fill="white"/>
<path d="M139.067 59.9601C139.091 67.4354 132.959 73.5689 125.431 73.5995C117.903 73.624 111.746 67.5457 111.709 60.0582C111.672 52.4174 117.75 46.3513 125.443 46.3329C132.934 46.3146 139.036 52.4235 139.067 59.9662L139.067 59.9601Z" fill="white"/>
<path d="M72.7606 76.4487C85.751 76.4487 96.1231 67.6182 96.756 54.8182C96.756 54.8182 96.2129 53.2091 95.1463 53.2091C94.0797 53.2091 93.5365 54.8182 93.5365 54.8182C92.9097 65.5778 83.7048 72.7478 72.7606 72.7478C61.8164 72.7478 52.1749 63.5385 52.1749 52.2213C52.1749 40.9041 61.4108 31.6948 72.7606 31.6948C81.6769 31.6948 89.0694 36.1683 91.9268 44.0909C91.9268 44.0909 92.9999 45.1637 94.0731 44.6273C95.1463 44.0909 94.6097 42.4818 94.6097 42.4818C91.1378 33.3337 83.1394 27.9939 72.7606 27.9939C59.3646 27.9939 48.4634 38.8637 48.4634 52.2213C48.4634 65.5789 59.3646 76.4487 72.7606 76.4487Z" fill="white"/>
</g>
<rect x="202.316" y="47" width="27.4737" height="42" rx="2" fill="#303030" stroke="#373737" stroke-width="4"/>
<circle cx="216.154" cy="64.3081" r="8.15891" fill="#232323"/>
<path d="M217.764 67.697C217.757 67.697 217.679 67.6487 217.532 67.552C217.385 67.4553 217.189 67.3586 216.943 67.2619C216.698 67.1652 216.449 67.1169 216.198 67.1169C215.947 67.1169 215.698 67.1652 215.453 67.2619C215.207 67.3586 215.012 67.4553 214.867 67.552C214.722 67.6487 214.643 67.697 214.632 67.697C214.562 67.697 214.381 67.552 214.089 67.2619C213.797 66.9718 213.651 66.792 213.651 66.7224C213.651 66.6721 213.671 66.6276 213.709 66.5889C214.011 66.2911 214.39 66.0572 214.846 65.887C215.303 65.7168 215.753 65.6317 216.198 65.6317C216.643 65.6317 217.093 65.7168 217.55 65.887C218.006 66.0572 218.385 66.2911 218.687 66.5889C218.725 66.6276 218.745 66.6721 218.745 66.7224C218.745 66.792 218.599 66.9718 218.307 67.2619C218.015 67.552 217.834 67.697 217.764 67.697ZM219.348 66.119C219.306 66.119 219.261 66.1036 219.215 66.0726C218.689 65.6665 218.201 65.3678 217.753 65.1763C217.304 64.9849 216.786 64.8892 216.198 64.8892C215.869 64.8892 215.54 64.9317 215.209 65.0168C214.878 65.1019 214.59 65.2044 214.344 65.3243C214.099 65.4442 213.879 65.5641 213.686 65.6839C213.493 65.8038 213.34 65.9063 213.228 65.9914C213.116 66.0765 213.056 66.119 213.048 66.119C212.982 66.119 212.804 65.974 212.514 65.6839C212.224 65.3939 212.079 65.214 212.079 65.1444C212.079 65.098 212.098 65.0555 212.137 65.0168C212.648 64.5063 213.266 64.1099 213.993 63.8275C214.721 63.5452 215.455 63.404 216.198 63.404C216.941 63.404 217.675 63.5452 218.402 63.8275C219.13 64.1099 219.748 64.5063 220.259 65.0168C220.298 65.0555 220.317 65.098 220.317 65.1444C220.317 65.214 220.172 65.3939 219.882 65.6839C219.592 65.974 219.414 66.119 219.348 66.119ZM220.92 64.5469C220.878 64.5469 220.835 64.5295 220.793 64.4947C220.1 63.8875 219.382 63.4301 218.637 63.1227C217.893 62.8152 217.08 62.6615 216.198 62.6615C215.316 62.6615 214.503 62.8152 213.759 63.1227C213.014 63.4301 212.296 63.8875 211.603 64.4947C211.561 64.5295 211.518 64.5469 211.476 64.5469C211.41 64.5469 211.231 64.4019 210.939 64.1118C210.647 63.8217 210.501 63.6419 210.501 63.5723C210.501 63.522 210.52 63.4775 210.559 63.4388C211.282 62.7195 212.143 62.1626 213.141 61.7681C214.139 61.3736 215.158 61.1763 216.198 61.1763C217.238 61.1763 218.257 61.3736 219.255 61.7681C220.253 62.1626 221.114 62.7195 221.837 63.4388C221.876 63.4775 221.895 63.522 221.895 63.5723C221.895 63.6419 221.749 63.8217 221.457 64.1118C221.165 64.4019 220.986 64.5469 220.92 64.5469Z" fill="white"/>
<rect x="213.228" y="78.2263" width="6.45614" height="1.61404" rx="0.807018" fill="#57E888"/>
<defs>
<clipPath id="clip0_11_36">
<rect width="132" height="59" fill="white" transform="translate(181 87) rotate(-180)"/>
</clipPath>
</defs>
</svg>
