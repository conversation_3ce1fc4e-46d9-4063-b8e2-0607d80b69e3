# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* data_recycle
# 
# Translators:
# Wil O<PERSON>o, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Jun<PERSON>, 2025\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.notification
msgid ""
"' recycling rule.<br/>\n"
"You can validate those changes"
msgstr ""
"' リサイクル規則<br/>\n"
"これらの変更を検証できます。"

#. module: data_recycle
#. odoo-python
#: code:addons/data_recycle/models/data_recycle_record.py:0
msgid "**Record Deleted**"
msgstr "**記録削除済**"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_merge_model_form
msgid "<span class=\"me-1\">Every</span>"
msgstr "<span class=\"me-1\">毎</span>"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__active
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__active
msgid "Active"
msgstr "アクティブ"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__recycle_action__archive
msgid "Archive"
msgstr "アーカイブ"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__recycle_mode__automatic
msgid "Automatic"
msgstr "自動"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__company_id
msgid "Company"
msgstr "会社"

#. module: data_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_cleaning_config
msgid "Configuration"
msgstr "設定"

#. module: data_recycle
#: model_terms:ir.actions.act_window,help:data_recycle.action_data_recycle_record
#: model_terms:ir.actions.act_window,help:data_recycle.action_data_recycle_record_notification
msgid "Configure rules to identify records to clean"
msgstr "クリーニングするレコードを特定するための規則を設定しましょう"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__create_uid
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__create_uid
msgid "Created by"
msgstr "作成者"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__create_date
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__create_date
msgid "Created on"
msgstr "作成日"

#. module: data_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_cleaning_root
msgid "Data Cleaning"
msgstr "データクレンジング"

#. module: data_recycle
#: model:ir.actions.server,name:data_recycle.ir_cron_clean_records_ir_actions_server
msgid "Data Recycle: Clean Records"
msgstr "データリサイクル: クリーンレコード"

#. module: data_recycle
#. odoo-python
#: code:addons/data_recycle/models/data_recycle_model.py:0
msgid "Data to Recycle"
msgstr "リサイクルするデータ"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__notify_frequency_period__days
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__time_field_delta_unit__days
msgid "Days"
msgstr "日"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__recycle_action__unlink
msgid "Delete"
msgstr "削除"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__time_field_delta
msgid "Delta"
msgstr "差分"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__time_field_delta_unit
msgid "Delta Unit"
msgstr "差分の単位"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_list
msgid "Discard"
msgstr "破棄"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_search
msgid "Discarded"
msgstr "破棄済"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__display_name
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__display_name
msgid "Display Name"
msgstr "表示名"

#. module: data_recycle
#: model:ir.actions.act_window,name:data_recycle.action_data_recycle_record
#: model:ir.actions.act_window,name:data_recycle.action_data_recycle_record_notification
msgid "Field Recycle Records"
msgstr "フィールドリサイクルレコード"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__domain
msgid "Filter"
msgstr "フィルター"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__id
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__id
msgid "ID"
msgstr "ID"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__include_archived
msgid "Include Archived"
msgstr "アーカイブ済を含む"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__last_notification
msgid "Last Notification"
msgstr "最終通知"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__write_uid
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__write_date
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: data_recycle
#: model:ir.model.fields,help:data_recycle.field_data_recycle_model__notify_user_ids
msgid "List of users to notify when there are new records to recycle"
msgstr "リサイクルする新規レコードがあるときに通知するユーザのリスト"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__recycle_mode__manual
msgid "Manual"
msgstr "手動"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__res_model_id
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__res_model_id
msgid "Model"
msgstr "モデル"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__res_model_name
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__res_model_name
msgid "Model Name"
msgstr "モデル名"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__notify_frequency_period__months
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__time_field_delta_unit__months
msgid "Months"
msgstr "月"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__name
msgid "Name"
msgstr "名称"

#. module: data_recycle
#: model_terms:ir.actions.act_window,help:data_recycle.action_data_recycle_record
#: model_terms:ir.actions.act_window,help:data_recycle.action_data_recycle_record_notification
msgid "No cleaning suggestions"
msgstr "クリーニング提案はありません"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__notify_frequency
msgid "Notify"
msgstr "通知"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__notify_frequency_period
msgid "Notify Frequency Period"
msgstr "通知頻度期間"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__notify_user_ids
msgid "Notify Users"
msgstr "ユーザ通知"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__res_id
msgid "Record ID"
msgstr "レコードID"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__name
msgid "Record Name"
msgstr "レコード名"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_merge_model_form
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_search
msgid "Records"
msgstr "レコード"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__records_to_recycle_count
msgid "Records To Recycle"
msgstr "リサイクルするレコード"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__recycle_action
msgid "Recycle Action"
msgstr "リサイクルアクション"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__recycle_mode
msgid "Recycle Mode"
msgstr "リサイクルモード"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__recycle_model_id
msgid "Recycle Model"
msgstr "リサイクルモデル"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__recycle_record_ids
msgid "Recycle Record"
msgstr "リサイクルレコード"

#. module: data_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_cleaning_config_rules_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_recycle_record
msgid "Recycle Records"
msgstr "リサイクルレコード"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_list
msgid "Recycle Rule"
msgstr "リサイクル規則"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_search
msgid "Recycle Rules"
msgstr "リサイクル規則"

#. module: data_recycle
#: model:ir.model,name:data_recycle.model_data_recycle_model
msgid "Recycling Model"
msgstr "リサイクルモデル"

#. module: data_recycle
#: model:ir.model,name:data_recycle.model_data_recycle_record
msgid "Recycling Record"
msgstr "リサイクルレコード"

#. module: data_recycle
#: model:ir.actions.act_window,name:data_recycle.action_data_recycle_config
msgid "Recyle Records Rules"
msgstr "リサイクルレコード規則"

#. module: data_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_cleaning_config_rules
msgid "Rules"
msgstr "規則"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_merge_model_form
msgid "Run Now"
msgstr "今すぐ実行"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_merge_model_form
msgid "Select a model to configure recycling actions"
msgstr "リサイクルアクションを設定するモデルを選択"

#. module: data_recycle
#: model:ir.model.constraint,message:data_recycle.constraint_data_recycle_model_check_notif_freq
msgid "The notification frequency should be greater than 0"
msgstr "通知頻度は0より大きくする必要があります。"

#. module: data_recycle
#. odoo-python
#: code:addons/data_recycle/models/data_recycle_model.py:0
msgid "This model doesn't manage archived records. Only deletion is possible."
msgstr "このモデルはアーカイブ済レコードを管理しません。削除のみ可能です。"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__time_field_id
msgid "Time Field"
msgstr "時間フィールド"

#. module: data_recycle
#. odoo-python
#: code:addons/data_recycle/models/data_recycle_record.py:0
msgid "Undefined Name"
msgstr "未定義の名前"

#. module: data_recycle
#. odoo-javascript
#: code:addons/data_recycle/static/src/views/data_recycle_list_view.xml:0
msgid "Unselect"
msgstr "選択解除"

#. module: data_recycle
#. odoo-javascript
#: code:addons/data_recycle/static/src/views/data_recycle_list_view.xml:0
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_list
msgid "Validate"
msgstr "検証"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.notification
msgid "We've identified"
msgstr "以下を確認しました:"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__notify_frequency_period__weeks
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__time_field_delta_unit__weeks
msgid "Weeks"
msgstr "週"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__time_field_delta_unit__years
msgid "Years"
msgstr "年"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.notification
msgid "here"
msgstr "クリック"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.notification
msgid "records to clean with the '"
msgstr "以下できれいにするレコード:"
