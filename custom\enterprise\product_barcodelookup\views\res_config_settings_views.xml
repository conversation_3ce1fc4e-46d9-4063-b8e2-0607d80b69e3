<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.product.barcodelookup</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="base_setup.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <setting id="module_auth_oauth" position="after">
                <setting id="barcode_lookup_setting" string="Barcode Database" documentation="https://www.barcodelookup.com/api-documentation">
                    <span class="text-muted">
                        Create products by scanning using <a href="https://www.barcodelookup.com" target="_blank">barcodelookup.com</a>
                    </span>
                    <div class="content-group">
                        <div class="row mt16">
                            <label for="barcodelookup_api_key" string="API Key:" class="col-lg-3 o_light_label"/>
                            <field name="barcodelookup_api_key" placeholder="e.g. d7vctmiv2rwgenebha8bxq7irooudn"/>
                        </div>
                    </div>
                </setting>
            </setting>
        </field>
    </record>
</odoo>
