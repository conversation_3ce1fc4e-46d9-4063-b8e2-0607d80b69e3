.orderline {
    border: $border-width solid transparent;
    border-radius: $border-radius-lg;
    transition: $transition-base;

    &:hover {
        background-color: $o-gray-200;
    }
    
    @media (hover: none) {
        &:hover {
            background-color: $white;
        }
    }

    &.selected {
        background-color: $o-component-active-bg;
        border: $border-width solid $o-component-active-border;
    }

    &::before {
        content: "";
        position: absolute;
        top: 50%;
        left: -10px;
        width: 6px;
        height: 85%;
        transform: translateY(-50%);
        border-top-right-radius: $border-radius-lg;
        border-bottom-right-radius: $border-radius-lg;
    }

    &.skip-change::before {
        background-color: $o-warning;
    }

    &.has-change::before {
        background-color: $o-success;
    }
}


.orderline-combo {
    &.skip-change::before, &.has-change::before {
        left: -3px;
        border-radius: $border-radius-lg;
    }
}
