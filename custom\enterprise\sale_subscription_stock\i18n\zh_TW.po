# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_subscription_stock
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-12 08:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_subscription_stock
#. odoo-python
#: code:addons/sale_subscription_stock/models/sale_order.py:0
msgid ""
"A system error prevented the automatic creation of delivery orders for this "
"subscription. To ensure your delivery is processed, please trigger it "
"manually by using the \"Subscription: Generate delivery\" action."
msgstr "系統發生錯誤，導致未能為此訂閱計劃自動建立送貨單。為確保送貨獲得處理，請執行「訂閱：產生送貨」操作，手動觸發送貨。"

#. module: sale_subscription_stock
#. odoo-python
#: code:addons/sale_subscription_stock/models/sale_order.py:0
msgid "Delivery creation failed"
msgstr "送貨建立失敗"

#. module: sale_subscription_stock
#: model:product.template,name:sale_subscription_stock.product_recurring_detergent_product_template
msgid "Detergent (SUB)"
msgstr "清潔劑（定期訂購）"

#. module: sale_subscription_stock
#: model:ir.model.fields,field_description:sale_subscription_stock.field_sale_order__display_recurring_stock_delivery_warning
msgid "Display Recurring Stock Delivery Warning"
msgstr "顯示定期庫存送貨警告"

#. module: sale_subscription_stock
#: model_terms:ir.ui.view,arch_db:sale_subscription_stock.product_template_form_view
msgid ""
"Recurring order with this product will be invoiced at the beginning of the "
"period."
msgstr "此產品的重複訂單，會在期初開立發票。"

#. module: sale_subscription_stock
#: model:ir.model,name:sale_subscription_stock.model_sale_order
msgid "Sales Order"
msgstr "銷售訂單"

#. module: sale_subscription_stock
#: model:ir.model,name:sale_subscription_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "銷售訂單資料行"

#. module: sale_subscription_stock
#: model:ir.model,name:sale_subscription_stock.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr "庫存補貨報告"

#. module: sale_subscription_stock
#: model:ir.actions.server,name:sale_subscription_stock.action_compute_price_bom_product
msgid "Subscription: Generate delivery"
msgstr "訂閱：產生送貨"

#. module: sale_subscription_stock
#. odoo-javascript
#: code:addons/sale_subscription_stock/static/src/report_stock_forecasted.xml:0
msgid "Subscriptions"
msgstr "訂閱"

#. module: sale_subscription_stock
#: model_terms:ir.ui.view,arch_db:sale_subscription_stock.sale_subscription_order_view_form
msgid ""
"The delivery order of the recurring product(s) will be created soon. If another delivery order exists,\n"
"                    recurring product will be added to it automatically."
msgstr ""
"系統將很快為循環產品建立送貨單。若已有另一送貨單，\n"
"                    循環產品會自動加入該送貨單內。"

#. module: sale_subscription_stock
#: model:ir.model,name:sale_subscription_stock.model_stock_picking
msgid "Transfer"
msgstr "轉移"
