<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.OrderChangeReceipt">
        <div class="pos-receipt m-0 p-0">
            <!-- Receipt Header -->
            <div class="receipt-header text-center">
                <div class="pos-receipt-title">
                    <t t-if="changes.diningModeUpdate">
                        <t t-if="changes.takeaway">Dine In -&gt; Take Out</t>
                        <t t-else="">Take Out -&gt; Dine In</t>
                    </t>
                    <t t-else="">
                        <t t-if="changes.takeaway">Take Out</t>
                        <t t-else="">Dine In</t>
                    </t>
                </div>
                <div class="o-employee-name" style="font-size: 78%;">
                    <span><t t-esc="changes.config_name"/> : <t t-esc="changes.time"/></span><br/>
                    <span>By: <t t-esc="changes.employee_name"/></span>
                </div>
                <span class="my-4" style="font-size: 120%;">
                    <t t-if="changes.table_name">Table <strong><t t-esc="changes.table_name"/></strong></t>
                    <t t-if="changes.tracking_number" class="fw-light my-3"> # <strong><t t-esc="changes.tracking_number"/></strong></t>
                </span>
            </div>
            <hr style="border: none; border-top: 4px dashed black;"/>

            <!-- Receipt Body -->
            <div class="pos-receipt-body pb-5" >
                <!-- Operational Title -->
                <t t-if="operational_title">
                    <div class="pos-receipt-title text-center" t-esc="operational_title" />
                </t>
                <!-- Order Lines -->
                <div t-foreach="changedlines" t-as="line" t-key="change_index" style="font-size: 120%;">
                    <div t-attf-class="orderline #{line.isCombo ? 'mx-5 px-2' : 'mx-1'}">
                        <div class="d-flex medium">
                            <span class="me-3" t-esc="line.quantity"/> <span class="product-name" t-esc="line.display_name"/>
                        </div>
                        <div t-if="line.attribute_value_ids?.length" class="mx-5" style="font-size: 91%;">
                            <t t-foreach="line.attribute_value_ids" t-as="attribute" t-key="attribute.id">
                                <p class="p-0 m-0">
                                    - <t t-esc="attribute.name" /><br/>
                                </p>
                            </t>
                        </div>
                        <div t-if="line.note" class="fst-italic" style="font-size: 91%;">
                            <t t-esc="line.note.split('\n').join(', ')"/><br/>
                        </div>
                    </div>
                </div>
                <!-- General Note -->
                <!-- if no orderline change that means general note change to handle with less arguments -->
                <t t-if="(!changedlines.length or fullReceipt) and changes.order_note.length">
                    <div class="mt-5 fst-italic" style="font-size: 109%;">
                        <t t-if="changes.order_note">
                            <t t-esc="changes.order_note.split('\n').join(', ')"/><br/>
                        </t>
                    </div>
                </t>
            </div>
        </div>
    </t>
</templates>
