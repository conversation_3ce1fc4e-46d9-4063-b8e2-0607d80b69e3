# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_stock_picking_batch
# 
# Translators:
# Wil <PERSON>do<PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: delivery_stock_picking_batch
#: model:ir.model.fields,help:delivery_stock_picking_batch.field_stock_picking_type__batch_max_weight
msgid ""
"A transfer will not be automatically added to batches that will exceed this weight if the transfer is added to it.\n"
"Leave this value as '0' if no weight limit."
msgstr ""
"No se añadirá el traslado automáticamente a un lote en el que exceda el peso cuando se añada.\n"
"Deje este valor como \"0\" si no hay límite de peso."

#. module: delivery_stock_picking_batch
#: model:ir.model.fields,help:delivery_stock_picking_batch.field_stock_picking_type__batch_group_by_carrier
msgid "Automatically group batches by carriers"
msgstr "Agrupar automáticamente lotes por método de envío"

#. module: delivery_stock_picking_batch
#: model:ir.model,name:delivery_stock_picking_batch.model_stock_picking_batch
msgid "Batch Transfer"
msgstr "Traslado por lote"

#. module: delivery_stock_picking_batch
#: model:ir.model.fields,field_description:delivery_stock_picking_batch.field_stock_picking_type__batch_group_by_carrier
msgid "Carrier"
msgstr "Transportista"

#. module: delivery_stock_picking_batch
#: model:ir.model.fields,field_description:delivery_stock_picking_batch.field_stock_picking_type__batch_max_weight
msgid "Maximum weight"
msgstr "Peso máximo"

#. module: delivery_stock_picking_batch
#: model:ir.model,name:delivery_stock_picking_batch.model_stock_picking_type
msgid "Picking Type"
msgstr "Tipo de albarán"

#. module: delivery_stock_picking_batch
#: model:ir.model,name:delivery_stock_picking_batch.model_stock_picking
msgid "Transfer"
msgstr "Traslado"

#. module: delivery_stock_picking_batch
#: model:ir.model.fields,field_description:delivery_stock_picking_batch.field_stock_picking_type__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Etiqueta de unidad de medida de peso"
