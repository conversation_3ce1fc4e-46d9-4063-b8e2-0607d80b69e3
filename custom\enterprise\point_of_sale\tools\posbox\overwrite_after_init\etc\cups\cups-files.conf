#
# File/directory/user/group configuration file for the CUPS scheduler.
# See "man cups-files.conf" for a complete description of this file.
#

# List of events that are considered fatal errors for the scheduler...
#FatalErrors config

# Do we call fsync() after writing configuration or status files?
#SyncOnClose Yes

# Default user and group for filters/backends/helper programs; this cannot be
# any user or group that resolves to ID 0 for security reasons...
#User lp
#Group lp

# Administrator user group, used to match @SYSTEM in cupsd.conf policy rules...
# This cannot contain the Group value for security reasons...
SystemGroup lpadmin pi


# User that is substituted for unauthenticated (remote) root accesses...
#RemoteRoot remroot

# Do we allow file: device URIs other than to /dev/null?
#FileDevice No

# Permissions for configuration and log files...
#ConfigFilePerm 0640
#LogFilePerm 00640

# Location of the file logging all access to the scheduler; may be the name
# "syslog". If not an absolute path, the value of ServerRoot is used as the
# root directory.  Also see the "AccessLogLevel" directive in cupsd.conf.
AccessLog /var/log/cups/access_log

# Location of cache files used by the scheduler...
#CacheDir /var/cache/cups

# Location of data files used by the scheduler...
#DataDir /usr/share/cups

# Location of the static web content served by the scheduler...
#DocumentRoot /usr/share/cups/doc-root

# Location of the file logging all messages produced by the scheduler and any
# helper programs; may be the name "syslog". If not an absolute path, the value
# of ServerRoot is used as the root directory.  Also see the "LogLevel"
# directive in cupsd.conf.
ErrorLog /var/log/cups/error_log

# Location of fonts used by older print filters...
#FontPath /usr/share/cups/fonts

# Location of LPD configuration
#LPDConfigFile 

# Location of the file logging all pages printed by the scheduler and any
# helper programs; may be the name "syslog". If not an absolute path, the value
# of ServerRoot is used as the root directory.  Also see the "PageLogFormat"
# directive in cupsd.conf.
PageLog /var/log/cups/page_log

# Location of the file listing all of the local printers...
#Printcap /run/cups/printcap

# Format of the Printcap file...
#PrintcapFormat bsd
#PrintcapFormat plist
#PrintcapFormat solaris

# Location of all spool files...
#RequestRoot /var/spool/cups

# Location of helper programs...
#ServerBin /usr/lib/cups

# SSL/TLS keychain for the scheduler...
#ServerKeychain ssl

# Location of other configuration files...
ServerRoot /root_bypass_ramdisks/etc/cups

# Location of Samba configuration file...
#SMBConfigFile 

# Location of scheduler state files...
#StateDir /run/cups

# Location of scheduler/helper temporary files. This directory is emptied on
# scheduler startup and cannot be one of the standard (public) temporary
# directory locations for security reasons...
#TempDir /var/spool/cups/tmp
