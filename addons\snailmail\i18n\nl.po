# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* snailmail
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__attachment_error
msgid "ATTACHMENT_ERROR"
msgstr "ATTACHMENT_ERROR"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_cover
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_cover
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__snailmail_cover
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Add a Cover Page"
msgstr "Een voorpagina toevoegen"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Address"
msgstr "Adres"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core/failure_model_patch.js:0
msgid ""
"An error occurred when sending a letter with Snailmail on “%(record_name)s”"
msgstr ""
"Er is een fout opgetreden bij het verzenden van een brief met Snailmail op "
"“%(record_name)s”"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core/failure_model_patch.js:0
msgid "An error occurred when sending a letter with Snailmail."
msgstr ""
"Er is een fout opgetreden bij het verzenden van een brief met Snailmail."

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "An error occurred when sending the document by post.<br>Error: %s"
msgstr ""
"Er is een fout opgetreden bij het verzenden van het document per post. "
"<br>Fout: %s"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "An unknown error happened. Please contact the support."
msgstr ""
"Er is een onbekende fout opgetreden. Neem contact op met de "
"ondersteuningsdienst."

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid "An unknown error occurred. Please contact our"
msgstr "Er is een onbekende fout opgetreden. Neem contact op met onze"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_id
msgid "Attachment"
msgstr "Bijlage"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_fname
msgid "Attachment Filename"
msgstr "Bestandsnaam bijlage"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core/notification_model_patch.js:0
msgid "Awaiting Dispatch"
msgstr "Wachten op verzending"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__duplex
msgid "Both side"
msgstr "Beide kanten"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_duplex
msgid "Both sides"
msgstr "Beide kanten"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid "Buy credits"
msgstr "Credits kopen"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__credit_error
msgid "CREDIT_ERROR"
msgstr "CREDIT_ERROR"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_form
msgid "Cancel"
msgstr "Annuleren"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Cancel Letter"
msgstr "Annuleer brief"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Cancel letter"
msgstr "Annuleer brief"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Cancel notification in failure"
msgstr "Annuleer notificatie die mislukt is"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core/notification_model_patch.js:0
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__canceled
msgid "Cancelled"
msgstr "Geannuleerd"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__city
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__city
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "City"
msgstr "Plaats"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Close"
msgstr "Afsluiten"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__color
msgid "Color"
msgstr "Kleur"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__company_id
msgid "Company"
msgstr "Bedrijf"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie-instellingen"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__country_id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__country_id
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Country"
msgstr "Land"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__cover
msgid "Cover Page"
msgstr "Voorpagina"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__create_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__create_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__create_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__create_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__display_name
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__display_name
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_datas
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_list
msgid "Document"
msgstr "Document"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__res_id
msgid "Document ID"
msgstr "Document ID"

#. module: snailmail
#: model:ir.model,name:snailmail.model_mail_thread
msgid "Email Thread"
msgstr "E-mail discussie"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core/notification_model_patch.js:0
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__error_code
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__error
msgid "Error"
msgstr "Fout"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__format_error
msgid "FORMAT_ERROR"
msgstr "FORMAT_ERROR"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
#: model:ir.actions.act_window,name:snailmail.snailmail_letter_missing_required_fields_action
msgid "Failed letter"
msgstr "Mislukte brief"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_notification__failure_type
msgid "Failure type"
msgstr "Soort mislukking"

#. module: snailmail
#: model:ir.actions.act_window,name:snailmail.snailmail_letter_format_error_action
msgid "Format Error"
msgstr "Formatteringsfout"

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter_format_error
msgid "Format Error Sending a Snailmail Letter"
msgstr "Formaat fout bij het verzenden van een Snailmail brief"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__id
msgid "ID"
msgstr "ID"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__pending
msgid "In Queue"
msgstr "In wachtrij"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__info_msg
msgid "Information"
msgstr "Informatie"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "Invalid recipient name."
msgstr "Ongeldige naam van de ontvanger."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__write_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__write_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__write_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__write_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_mail__letter_ids
#: model:ir.model.fields,field_description:snailmail.field_mail_message__letter_ids
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__letter_id
msgid "Letter"
msgstr "Brief"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "Letter sent by post with Snailmail"
msgstr "Brief verzonden met de post via Snailmail"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_list
msgid "Letters"
msgstr "Brieven"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__missing_required_fields
msgid "MISSING_REQUIRED_FIELDS"
msgstr "MISSING_REQUIRED_FIELDS"

#. module: snailmail
#: model:ir.model,name:snailmail.model_mail_message
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__message_id
msgid "Message"
msgstr "Bericht"

#. module: snailmail
#: model:ir.model,name:snailmail.model_mail_notification
msgid "Message Notifications"
msgstr "Bericht notificaties"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__model
msgid "Model"
msgstr "Model"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__no_price_available
msgid "NO_PRICE_AVAILABLE"
msgstr "NO_PRICE_AVAILABLE"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "Not enough credits for Snail Mail"
msgstr "Niet genoeg credits voor snailmail"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "Soort notificatie"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__notification_ids
msgid "Notifications"
msgstr "Meldingen"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "One or more required fields are empty."
msgstr "Een of meer verplichte velden zijn niet ingevuld."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__report_template
msgid "Optional report to print and attach"
msgstr "Optioneel rapport om af te drukken en te koppelen"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid ""
"Our service cannot read your letter due to its format.<br/>\n"
"                Please modify the format of the template or update your settings\n"
"                to automatically add a blank cover page to all letters."
msgstr ""
"Onze dienst kan je brief niet lezen vanwege het formaat.<br/>\n"
"Wijzig het formaat van de sjabloon of werk je instellingen bij\n"
"om automatisch een blanco voorblad toe te voegen aan alle brieven."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__partner_id
msgid "Partner"
msgstr "Partner"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "Please use an A4 Paper format."
msgstr "Gebruik een A4-papierformaat."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_duplex
msgid "Print Both sides"
msgstr "Beide kanten afdrukken"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_color
msgid "Print In Color"
msgstr "In kleur afdrukken"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid "Re-send letter"
msgstr "Brief opnieuw verzenden"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__partner_id
msgid "Recipient"
msgstr "Ontvanger"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__reference
msgid "Related Record"
msgstr "Gerelateerd record"

#. module: snailmail
#: model:ir.model,name:snailmail.model_ir_actions_report
msgid "Report Action"
msgstr "Rapport actie"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_form
msgid "Send Now"
msgstr "Nu verzenden"

#. module: snailmail
#: model:iap.service,description:snailmail.iap_service_snailmail
msgid "Send your customer invoices and follow-up reports by post, worldwide."
msgstr "Verstuur je klantfacturen en betaalherinneringen wereldwijd per post."

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core/notification_model_patch.js:0
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__sent
msgid "Sent"
msgstr "Verzonden"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__user_id
msgid "Sent by"
msgstr "Verzonden door"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "Snail Mails are successfully sent"
msgstr "Snailmails zijn succesvol verstuurd"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_message__message_type__snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__notification_type__snail
msgid "Snailmail"
msgstr "Snailmail"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_color
msgid "Snailmail Color"
msgstr "Snailmail kleur"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_cover_readonly
msgid "Snailmail Cover Readonly"
msgstr "Snailmail-omslag Alleen-lezen"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_credit
msgid "Snailmail Credit Error"
msgstr "Snailmail kredietfout"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/messaging_menu/messaging_menu_patch.js:0
msgid "Snailmail Failure: %(modelName)s"
msgstr "Snailmail fout: %(modelName)s"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/messaging_menu/messaging_menu_patch.js:0
msgid "Snailmail Failures"
msgstr "Snailmail-fouten"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_format
msgid "Snailmail Format Error"
msgstr "Snailmail formaatfout"

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter
#: model:ir.model.fields,field_description:snailmail.field_mail_notification__letter_id
msgid "Snailmail Letter"
msgstr "Snailmail brief"

#. module: snailmail
#: model:ir.actions.act_window,name:snailmail.action_mail_letters
#: model:ir.ui.menu,name:snailmail.menu_snailmail_letters
msgid "Snailmail Letters"
msgstr "Snailmail brieven"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_fields
msgid "Snailmail Missing Required Fields"
msgstr "Snailmail ontbrekende verplichte velden"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_price
msgid "Snailmail No Price Available"
msgstr "Snailmail geen prijs beschikbaar"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__message_id
msgid "Snailmail Status Message"
msgstr "Snailmail status bericht"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_trial
msgid "Snailmail Trial Error"
msgstr "Snailmail proeffout"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_error
msgid "Snailmail Unknown Error"
msgstr "Snailmail onbekende fout"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_mail__snailmail_error
#: model:ir.model.fields,field_description:snailmail.field_mail_message__snailmail_error
msgid "Snailmail message in error"
msgstr "Snailmail bericht in fout"

#. module: snailmail
#: model:ir.actions.server,name:snailmail.snailmail_print_ir_actions_server
msgid "Snailmail: process letters queue"
msgstr "Snailmail: brieven in de wachtrij verwerken"

#. module: snailmail
#: model:iap.service,unit_name:snailmail.iap_service_snailmail
msgid "Stamps"
msgstr "Postzegels"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__state_id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__state_id
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "State"
msgstr "Status"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__state
msgid "Status"
msgstr "Status"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__street
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__street
msgid "Street"
msgstr "Straat"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Street 2..."
msgstr "Straat 2..."

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Street..."
msgstr "Straat..."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__street2
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__street2
msgid "Street2"
msgstr "Straat2"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__trial_error
msgid "TRIAL_ERROR"
msgstr "TRIAL_ERROR"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "The address of the recipient is not complete"
msgstr "Het adres van de ontvanger is niet compleet"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid ""
"The attachment of the letter could not be sent. Please check its content and"
" contact the support if the problem persists."
msgstr ""
"De bijlage van de brief kon niet worden verzonden. Controleer de inhoud en "
"neem contact op met de ondersteuningsdienst als het probleem aanhoudt."

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "The country of the partner is not covered by Snailmail."
msgstr "Het land van de partner is niet ondersteund door Snailmail."

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid ""
"The country to which you want to send the letter is not supported by our "
"service."
msgstr ""
"Het land waarnaar je de brief wilt sturen, wordt niet ondersteund door onze "
"service."

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid ""
"The customer address is not complete. Update the address here and re-send "
"the letter."
msgstr ""
"Het adres van de klant is niet compleet. Update het adres hier en verzend de"
" brief opnieuw."

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "The document was correctly sent by post.<br>The tracking id is %s"
msgstr "Het document is correct per post verzonden. <br> De tracking-ID is %s"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid ""
"The letter could not be sent due to insufficient credits on your IAP "
"account."
msgstr ""
"De brief kon niet worden verzonden wegens onvoldoende credits op je IAP-"
"account."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:snailmail.field_mail_message__message_type
msgid "Type"
msgstr "Soort"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__unknown_error
msgid "UNKNOWN_ERROR"
msgstr "ONBEKENDE_FOUT"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Update Config and Re-send"
msgstr "Configuratie bijwerken en opnieuw verzenden"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Update address and re-send"
msgstr "Adres bijwerken en opnieuw verzenden"

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter_missing_required_fields
msgid "Update address of partner"
msgstr "Adres van de partner bijwerken"

#. module: snailmail
#: model:ir.model.fields,help:snailmail.field_mail_mail__message_type
#: model:ir.model.fields,help:snailmail.field_mail_message__message_type
msgid ""
"Used to categorize message generator\n"
"'email': generated by an incoming email e.g. mailgateway\n"
"'comment': generated by user input e.g. through discuss or composer\n"
"'email_outgoing': generated by a mailing\n"
"'notification': generated by system e.g. tracking messages\n"
"'auto_comment': generated by automated notification mechanism e.g. acknowledgment\n"
"'user_notification': generated for a specific recipient"
msgstr ""
"Wordt gebruikt om de berichtgenerator te categoriseren\n"
"'e-mail': gegenereerd door een inkomende e-mail, b.v. mailgateway\n"
"'commentaar': gegenereerd door gebruikersinvoer, b.v. via discussie of componist\n"
"'email_outgoing': gegenereerd door een mailing\n"
"'melding': gegenereerd door systeem, b.v. berichten volgen\n"
"'auto_comment': gegenereerd door een geautomatiseerd meldingsmechanisme, b.v. erkenning\n"
"'user_notification': gegenereerd voor een specifieke ontvanger"

#. module: snailmail
#: model:ir.model.fields,help:snailmail.field_snailmail_letter__state
msgid ""
"When a letter is created, the status is 'Pending'.\n"
"If the letter is correctly sent, the status goes in 'Sent',\n"
"If not, it will got in state 'Error' and the error message will be displayed in the field 'Error Message'."
msgstr ""
"Wanneer een brief wordt aangemaakt, is de status 'Concept'.\n"
"Als de brief correct is verzonden, gaat de status in 'Verzonden',\n"
"Als dat niet het geval is, krijgt het de status 'Fout' en wordt het foutbericht weergegeven in het veld 'Foutbericht'."

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid ""
"You don't have an IAP account registered for this service.<br>Please go to "
"<a href=%s target=\"new\">iap.odoo.com</a> to claim your free credits."
msgstr ""
"Je hebt geen IAP-account geregistreerd voor deze service. <br>Ga naar <a "
"href=%s target=\"new\"> iap.odoo.com</a> om je gratis credits te claimen."

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid ""
"You don't have enough credits to perform this operation.<br>Please go to "
"your <a href=%s target=\"new\">iap account</a>."
msgstr ""
"Je hebt niet genoeg credits om deze bewerking uit te voeren. <br>Ga naar je "
"<a href=%s target=\"new\"> IAP-account</a>."

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid "You need credits on your IAP account to send a letter."
msgstr "Je hebt credits op je IAP-account nodig om een brief te verzenden."

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "ZIP"
msgstr "Postcode"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__zip
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__zip
msgid "Zip"
msgstr "Postcode"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid "for further assistance."
msgstr "voor verdere assistentie."

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid "support"
msgstr "ondersteuning"
