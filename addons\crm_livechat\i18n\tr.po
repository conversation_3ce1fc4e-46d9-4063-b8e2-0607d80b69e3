# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_livechat
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>il, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON>özütok, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: crm_livechat
#. odoo-python
#: code:addons/crm_livechat/models/chatbot_script_step.py:0
msgid "%s's New Lead"
msgstr "%s'in Yeni Adayı"

#. module: crm_livechat
#: model:ir.model,name:crm_livechat.model_chatbot_script
msgid "Chatbot Script"
msgstr "Chatbot Komut Dosyası"

#. module: crm_livechat
#: model:ir.model,name:crm_livechat.model_chatbot_script_step
msgid "Chatbot Script Step"
msgstr "Chatbot Komut Dosyası Adımı"

#. module: crm_livechat
#: model:ir.model.fields.selection,name:crm_livechat.selection__chatbot_script_step__step_type__create_lead
msgid "Create Lead"
msgstr "Müşteri Adayı Oluştur"

#. module: crm_livechat
#. odoo-javascript
#: code:addons/crm_livechat/static/src/core/channel_commands.js:0
msgid "Create a new lead (/lead lead title)"
msgstr "Bir yeni aday oluştur (/aday başlığı)"

#. module: crm_livechat
#. odoo-python
#: code:addons/crm_livechat/models/discuss_channel.py:0
msgid ""
"Create a new lead: %(pre_start)s%(lead_command)s %(i_start)slead "
"title%(i_end)s%(pre_end)s"
msgstr ""
"Yeni müşteri adayı oluşturun: %(pre_start)s%(lead_command)s "
"%(i_start)smüşteri adayı unvanı%(i_end)s%(pre_end)s"

#. module: crm_livechat
#. odoo-python
#: code:addons/crm_livechat/models/discuss_channel.py:0
msgid "Created a new lead: %s"
msgstr "Yeni bir müşteri adayı oluşturuldu: %s"

#. module: crm_livechat
#: model:ir.model,name:crm_livechat.model_discuss_channel
msgid "Discussion Channel"
msgstr "Mesajlaşma Kanalı"

#. module: crm_livechat
#: model:ir.model.fields,field_description:crm_livechat.field_chatbot_script__lead_count
msgid "Generated Lead Count"
msgstr "Oluşturulan Müşteri Adayı Sayısı"

#. module: crm_livechat
#: model:chatbot.script.step,message:crm_livechat.chatbot_script_lead_generation_step_welcome
msgid "Hi there, what brings you to our website today? 👋"
msgstr "Merhaba, sizi bugün web sitemize getiren nedir? 👋"

#. module: crm_livechat
#: model:chatbot.script.step,message:crm_livechat.chatbot_script_lead_generation_step_noone_available
msgid "Hu-ho, it looks like none of our operators are available 🙁"
msgstr "Hu-ho, operatörlerimizin hiçbiri müsait değil gibi görünüyor 🙁"

#. module: crm_livechat
#: model:chatbot.script,title:crm_livechat.chatbot_script_lead_generation_bot
msgid "Lead Generation Bot"
msgstr "Müşteri Adayı Oluşturma Botu"

#. module: crm_livechat
#: model_terms:ir.ui.view,arch_db:crm_livechat.chatbot_script_view_form
msgid "Leads"
msgstr "Adaylar"

#. module: crm_livechat
#: model:ir.model.fields,field_description:crm_livechat.field_chatbot_script_step__crm_team_id
msgid "Sales Team"
msgstr "Satış Ekibi"

#. module: crm_livechat
#: model:ir.model.fields,field_description:crm_livechat.field_chatbot_script_step__step_type
msgid "Step Type"
msgstr "Adım Türü"

#. module: crm_livechat
#: model:chatbot.script.step,message:crm_livechat.chatbot_script_welcome_step_just_looking
msgid "Thank you, you should hear back from us very soon!"
msgstr "Teşekkür ederim, çok yakında bizden haber almalısınız!"

#. module: crm_livechat
#: model:ir.model.fields,help:crm_livechat.field_chatbot_script_step__crm_team_id
msgid ""
"Used in combination with 'create_lead' step type in order to automatically "
"assign the created lead/opportunity to the defined team"
msgstr ""
"Oluşturulan müşteri adayını/fırsatı tanımlanan ekibe otomatik olarak atamak "
"için 'create_lead' adım türüyle birlikte kullanılır"

#. module: crm_livechat
#: model:chatbot.script.step,message:crm_livechat.chatbot_script_welcome_step_pricing_email
msgid ""
"Would you mind leaving your email address so that we can reach you back?"
msgstr ""
"Size geri ulaşabilmemiz için e-posta adresinizi bırakmayı düşünür müsünüz?"
