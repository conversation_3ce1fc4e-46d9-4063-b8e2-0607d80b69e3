# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_attendance
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# Wil Odoo, 2025
# <AUTHOR> <EMAIL>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: lhmflexerp <<EMAIL>>, 2025\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
msgid "\"Check Out\" time cannot be earlier than \"Check In\" time."
msgstr "\"Tjek-ud\" tidspunkt kan ikke være tidligere end \"tjek-ind\" tidspunkt. "

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
msgid "%(worked_hours)s (%(check_in)s-%(check_out)s)"
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "'Documentation'"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_hr_attendance_kanban
msgid "<i class=\"fa fa-calendar me-1\" aria-label=\"Period\" role=\"img\" title=\"Period\"/>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Installation Manual"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
msgid ""
"<span class=\"fa fa-circle text-success me-1\" role=\"img\" aria-"
"label=\"Available\" title=\"Available\"/>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
msgid ""
"<span class=\"fa fa-circle text-warning me-1\" role=\"img\" aria-label=\"Not available\" title=\"Not available\">\n"
"                                    </span>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span class=\"me-2\">Time Period </span>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span class=\"me-2\">Time Period</span>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span class=\"me-2\">Tolerance</span>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span class=\"ms-2\">Hours</span>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"<span class=\"ms-2\">Minutes</span>\n"
"                                <br/>\n"
"                                <br/>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid ""
"<span class=\"o_stat_text\">\n"
"                            This Month\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            Denne måned\n"
"                        </span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "<span class=\"o_stat_text\">Extra Hours</span>"
msgstr "<span class=\"o_stat_text\">Ekstra timer</span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span> Minutes</span>"
msgstr "<span> Minutter</span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span> seconds</span>"
msgstr "<span> sekunder</span>"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__absence_management
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__absence_management
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Absence Management"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_needaction
msgid "Action Needed"
msgstr "Handling påkrævet"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_management_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Active Employees"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_employee_attendance_action_kanban
msgid ""
"Add a few employees to be able to select an employee here and perform his check in / check out.\n"
"                To create employees go to the Employees menu."
msgstr ""
"Tilføj et par medarbejdere for at kunne vælge en medarbejder her, og udfør hans tjek ind / tjek ud.\n"
"                For at oprette medarbejdere, går du til Medarbejderer menuen."

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__adjustment
msgid "Adjustment"
msgstr "Justering"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_manager
msgid "Administrator"
msgstr "Administrator"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
msgid "All"
msgstr "Alle"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.js:0
msgid "All departments"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Allow Users to Check in/out from Odoo."
msgstr "Tillad brugere at checke ind/ud fra Odoo."

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Allow a period of time (around working hours) where extra time will not be "
"counted, in benefit of the company"
msgstr ""
"Tillad en tidsperiode (uden for almindelig arbejdstid), hvor ekstra timer "
"ikke vil blive sammentalt, til virksomhedens fordel."

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Allow a period of time (around working hours) where extra time will not be "
"deducted, in benefit of the employee"
msgstr ""
"Tillad en tidsperiode (uden for almindelig arbejdstid), hvor ekstra timer "
"ikke vil blive fratrukket, til medarbejderens fordel."

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
msgid "Amount of extra hours"
msgstr "Antal ekstra timer"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree_management
msgid "Approve"
msgstr "Godkend"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Approve Extra Hours"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__overtime_status__approved
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_management_view_filter
msgid "Approved"
msgstr "Godkendt"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_overtime_validation__by_manager
msgid "Approved by Manager"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_management_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Archived Employees"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "At Work"
msgstr "På arbejde"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_attachment_count
msgid "Attachment Count"
msgstr "Antal vedhæftninger"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
#: code:addons/hr_attendance/static/src/views/attendance_helper_view.xml:0
#: model:ir.model,name:hr_attendance.model_hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__attendance_ids
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_tree_inherit_leave
msgid "Attendance"
msgstr "Tilstedeværelse"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_from_systray
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_from_systray
msgid "Attendance From Systray"
msgstr "Fremmøde fra Systray"

#. module: hr_attendance
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_install_kiosk_pwa
msgid "Attendance Kiosk"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_kiosk_delay
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_kiosk_delay
msgid "Attendance Kiosk Delay"
msgstr "Fremmøde kiosk forsinkelse"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_kiosk_key
msgid "Attendance Kiosk Key"
msgstr "Fremmøde kiosk key"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_kiosk_url
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_kiosk_url
msgid "Attendance Kiosk Url"
msgstr "Fremmøde kiosk url"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__attendance_manager_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__attendance_manager_id
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__attendance_manager_id
msgid "Attendance Manager"
msgstr "Fremmøde leder"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_kiosk_mode
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_kiosk_mode
msgid "Attendance Mode"
msgstr "Fremmøde tilstand"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_attendance_overtime
msgid "Attendance Overtime"
msgstr "Fremmøde overtid"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__attendance_state
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__attendance_state
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__attendance_state
msgid "Attendance Status"
msgstr "Tilstedeværelse status"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Attendance with barcode"
msgstr ""

#. module: hr_attendance
#: model:ir.actions.server,name:hr_attendance.hr_attendance_check_out_cron_ir_actions_server
msgid "Attendance: Automatically check-out employees"
msgstr ""

#. module: hr_attendance
#: model:ir.actions.server,name:hr_attendance.hr_attendance_absence_cron_ir_actions_server
msgid "Attendance: Detect Absences for employees"
msgstr ""

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_reporting
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_root
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Attendances"
msgstr "Fremmødte"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_employee.py:0
#: code:addons/hr_attendance/models/res_users.py:0
msgid "Attendances This Month"
msgstr "Fremmøde denne måned"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Attendances from Backend"
msgstr "Fremmøde fra backend"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__auto_check_out_tolerance
msgid "Auto Check Out Tolerance"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__auto_check_out
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__auto_check_out
msgid "Automatic Check Out"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__out_mode__auto_check_out
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Automatic Check-Out"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_overtime_validation__no_validation
msgid "Automatically Approved"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Automatically Check-Out Employees based on their working schedule with an "
"additional tolerance."
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Automatically Checked-Out"
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
msgid "Back"
msgstr "Tilbage"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_barcode_source__back
msgid "Back Camera"
msgstr "Bagkamera"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Badge with Barcode"
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Badge with Barcode on Tablet"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_pivot
msgid "Balance"
msgstr "Balance"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_kiosk_mode__barcode
msgid "Barcode / RFID"
msgstr "Stregkode / RFID"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_kiosk_mode__barcode_manual
msgid "Barcode / RFID and Manual Selection"
msgstr "Stregkode /RFID og manuel valg"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_barcode_source
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_barcode_source
msgid "Barcode Source"
msgstr "Stregkodekilde"

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__in_country_name
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__out_country_name
msgid "Based on IP Address"
msgstr "Baseret på IP adresse"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_employee_base
msgid "Basic Employee"
msgstr "Standard medarbejder"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
msgid "Before"
msgstr "Før"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_browser
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "Browser"
msgstr "Browser"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Can be converted as Time Off (cfr Time Off configuration)."
msgstr ""

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
msgid ""
"Cannot create new attendance record for %(empl_name)s, the employee hasn't "
"checked out since %(datetime)s"
msgstr ""
"Det er ikke muligt at oprette nyt fremmødeskema for %(empl_name)s, da den "
"ansatte har ikke tjekket ud siden %(datetime)s"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
msgid ""
"Cannot create new attendance record for %(empl_name)s, the employee was "
"already checked in on %(datetime)s"
msgstr ""
"Det er ikke muligt at oprette nyt fremmødeskema for %(empl_name)s, da den "
"ansatte allerede var tjekket ind kl. %(datetime)s"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_employee.py:0
msgid ""
"Cannot perform check out on %(empl_name)s, could not find corresponding "
"check in. Your attendances have probably been modified manually by human "
"resources."
msgstr ""
"Kan ikke udføre tjek-ud på %(empl_name)s, kunne ikke finde tilsvarende tjek-"
"ind. Dine fremmøder er højst sandsynligt blevet ændret manuelt af HR."

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/check_in_out/check_in_out.xml:0
msgid "Check IN"
msgstr "Tjek IND"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__check_in
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_check_in
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__last_check_in
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__last_check_in
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "Check In"
msgstr "Tjek-ind"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/check_in_out/check_in_out.xml:0
msgid "Check OUT"
msgstr "Tjek UD"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__check_out
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_check_out
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__last_check_out
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__last_check_out
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "Check Out"
msgstr "Tjek-ud"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
msgid "Check in"
msgstr "Tjek ind"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
msgid "Check out"
msgstr "Tjek ud"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_employee__attendance_state__checked_in
msgid "Checked in"
msgstr "Tjekket ind"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
msgid "Checked in at"
msgstr "Tjekket ind den"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_employee__attendance_state__checked_out
msgid "Checked out"
msgstr "Tjekket ud"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
msgid "Checked out at"
msgstr "Tjekket ud på"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Choose how long the greeting message will be displayed."
msgstr "Vælg hvor længe velkomsthilsnen skal vises."

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Choose how to record attendances"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_city
msgid "City"
msgstr "By"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "City (In)"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "City (Out)"
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Close"
msgstr "Luk"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__color
msgid "Color"
msgstr "Farve"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_company
msgid "Companies"
msgstr "Virksomheder"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__company_id
msgid "Company"
msgstr "Virksomhed"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Company Logo"
msgstr "Virksomhedslogo"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurer opsætning"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_settings
msgid "Configuration"
msgstr "Konfiguration"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Connect an RFID reader, and scan a token."
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_country_name
msgid "Country"
msgstr "Land"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Country (In)"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Country (Out)"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_employee_attendance_action_kanban
msgid "Create a new employee"
msgstr "Opret en ny medarbejder"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__create_uid
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__create_uid
msgid "Created by"
msgstr "Oprettet af"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__create_date
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__create_date
msgid "Created on"
msgstr "Oprettet den"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "Currently Working"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_overtime_search
msgid "Date"
msgstr "Dato"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__date
msgid "Day"
msgstr "Dag"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Define the camera used for the barcode scan."
msgstr "Definér kameraet, som anvendes til stregkodescan."

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Define the way the user will be identified by the application."
msgstr "Angiv hvordan brugeren vil blive identificeret af app'en."

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Departement"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__department_id
msgid "Department"
msgstr "Afdeling"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_pivot
msgid "Difference"
msgstr "Forskel"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__hr_attendance_display_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__hr_attendance_display_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__display_extra_hours
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Display Extra Hours"
msgstr "Vis ekstra timer"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Display Extra Hours in Kiosk mode and on User profile."
msgstr "Vis ekstra timer i kiosk tilstand og på brugeprofil."

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Display Extra Hours."
msgstr "Vis ekstra timer."

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__display_name
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__display_name
msgid "Display Name"
msgstr "Vis navn"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Display Time"
msgstr "Vis tid"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
msgid ""
"Do not have access, user cannot edit the attendances that are not his own."
msgstr ""
"Har ikke adgang, bruger kan ikke redigere fremmøde, som ikke er hans egen."

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_employee
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__employee_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__employee_id
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_management_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_overtime_search
msgid "Employee"
msgstr "Medarbejder"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
msgid "Employee Avatar"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_kiosk_use_pin
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_kiosk_use_pin
msgid "Employee PIN Identification"
msgstr "Medarbejder PIN identifikation"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree_management
msgid "Employee attendances"
msgstr "Medarbejder fremmøde"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_employee_attendance_action_kanban
msgid "Employees"
msgstr "Medarbejdere"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
msgid "Error: could not find corresponding employee."
msgstr "Fejl : kunne ikke finde den tilsvarende ansatte."

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Errors"
msgstr "Fejl"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__expected_hours
msgid "Expected Hours"
msgstr ""

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_overtime_action
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__validated_overtime_hours
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__duration
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_validated_hours_employee_simple_tree_view
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree_management
msgid "Extra Hours"
msgstr "Extra timer"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__duration_real
msgid "Extra Hours (Real)"
msgstr "Ekstra timer (reel)"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_overtime_validation
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_overtime_validation
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Extra Hours Validation"
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
msgid "Extra hours"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance_overtime__duration_real
msgid "Extra-hours including the threshold duration"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_follower_ids
msgid "Followers"
msgstr "Følgere"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_partner_ids
msgid "Followers (Partners)"
msgstr "Følgere (partnere)"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
msgid "From %s"
msgstr "From %s"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_barcode_source__front
msgid "Front Camera"
msgstr "Frontkamera"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "GPS Coordinates"
msgstr "GPS koordinater"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Generate new URL"
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/card_layout/card_layout.xml:0
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
msgid "Go back"
msgstr "Gå tilbage"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
msgid "Goodbye"
msgstr "Farvel"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_management_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_overtime_search
msgid "Group By"
msgstr "Sortér efter"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__has_message
msgid "Has Message"
msgstr "Har besked"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "Hours"
msgstr "Timer"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_last_month
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__hours_last_month
msgid "Hours Last Month"
msgstr "Timer forrige måned"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_last_month_display
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__hours_last_month_display
msgid "Hours Last Month Display"
msgstr "Timer forrige måned vis"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_previously_today
msgid "Hours Previously Today"
msgstr "Timer tidligere i dag"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
msgid "Hours Previously Today:"
msgstr "Timer tidligere i dag:"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_today
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__hours_today
msgid "Hours Today"
msgstr "Timer i dag"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
msgid "Hours Today:"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Hr Attendance Search"
msgstr "Hr fremmøde søgning"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__id
msgid "ID"
msgstr "ID"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_ip_address
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "IP Address"
msgstr "IP adresse"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Identify Manually"
msgstr "Identificer Manuelt"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"If checked, days not covered by an attendance will be visible in the Report."
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Hvis afkrydset, kræver nye beskeder din opmærksomhed "

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__message_has_error
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Hvis afkrydset har nogle beskeder en leveringsfejl"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Input Mode (In)"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Input Mode (Out)"
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Install"
msgstr "Installer"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
msgid "Invalid request"
msgstr "Ugyldig anmodning"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_is_follower
msgid "Is Follower"
msgstr "Er følger"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__in_mode__kiosk
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__out_mode__kiosk
msgid "Kiosk"
msgstr "Kiosk"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_action_open_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Kiosk Mode"
msgstr "Kiosk mode"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Kiosk Mode Adress"
msgstr "Kiosk tilstand adresse"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Kiosk Settings"
msgstr "Kiosk indstillinger"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_overtime_search
msgid "Last 3 Months"
msgstr "Seneste 3 måneder"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_attendance_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__last_attendance_id
msgid "Last Attendance"
msgstr "Sidste fremmøde"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_attendance_worked_hours
msgid "Last Attendance Worked Hours"
msgstr "Seneste fremmøde arbejdede timer"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__write_uid
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__write_uid
msgid "Last Updated by"
msgstr "Sidst opdateret af"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__write_date
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__write_date
msgid "Last Updated on"
msgstr "Sidst opdateret den"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_latitude
msgid "Latitude"
msgstr "Breddegrad"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Latitude (In)"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Latitude (Out)"
msgstr ""

#. module: hr_attendance
#: model:ir.actions.server,name:hr_attendance.action_load_demo_data
msgid "Load demo data"
msgstr "Indlæs demonstrations data"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/views/attendance_helper_view.xml:0
msgid "Load sample data"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "Localisation"
msgstr "Lokalisering"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_longitude
msgid "Longitude"
msgstr "Længdegrad"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Longitude (In)"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Longitude (Out)"
msgstr ""

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_management_action
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_view_attendances_management
msgid "Management"
msgstr "Administration"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Manager"
msgstr "Leder"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__in_mode__manual
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__out_mode__manual
msgid "Manual"
msgstr "Manuel"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Manual Attendance"
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Manual Attendance or with barcode"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_kiosk_mode__manual
msgid "Manual Selection"
msgstr "Manuel valg"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Manually (optional PIN)"
msgstr ""

#. module: hr_attendance
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_greeting_message
msgid "Message"
msgstr "Besked"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_has_error
msgid "Message Delivery error"
msgstr "Besked ved leveringsfejl"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_ids
msgid "Messages"
msgstr "Beskeder"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Method"
msgstr "Metode"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_mode
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "Mode"
msgstr "Tilstand"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Modes"
msgstr "Tilstande"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_management_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "My Attendances"
msgstr "Mine fremmøder"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_management_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "My Team"
msgstr "Mit team"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "No Badge defined on employees. Set one to test."
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__no_validated_overtime_hours
msgid "No Validated Overtime Hours"
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/views/attendance_helper_view.xml:0
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_management_action
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_reporting
msgid "No attendance records found"
msgstr "Ingen tilstedeværelses registreringer fundet"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.js:0
msgid "No employee corresponding to Badge ID '%(barcode)s.'"
msgstr "Ingen medarbejder der svarer til Badge ID '%(barcode)s.'"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_overtime_action
msgid "No overtime records found"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_needaction_counter
msgid "Number of Actions"
msgstr "Antal handlinger"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_has_error_counter
msgid "Number of errors"
msgstr "Antal fejl"

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Antal meddelelser der kræver handling"

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Antal beskeder med leveringsfejl"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
msgid "OK"
msgstr "Ok"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Odoo Logo"
msgstr "Odoo logo"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_officer
msgid "Officer: Manage attendances"
msgstr "Officer: administrér fremmøder"

#. module: hr_attendance
#: model:ir.actions.server,name:hr_attendance.open_kiosk_url
msgid "Open Kiosk Url"
msgstr "Åben kiosk url"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_browser
msgid "Out Browser"
msgstr "Ud browser"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_city
msgid "Out City"
msgstr "Ud by"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_country_name
msgid "Out Country Name"
msgstr "Ud landenavn"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_ip_address
msgid "Out Ip Address"
msgstr "Ud IP adresse"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_latitude
msgid "Out Latitude"
msgstr "Ud breddegrad"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_longitude
msgid "Out Longitude"
msgstr "Ud længdegrad"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_mode
msgid "Out Mode"
msgstr "Ud tilstand"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__overtime_hours
msgid "Over Time"
msgstr "Overtid"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/res_users.py:0
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__overtime_ids
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_overtime_graph
msgid "Overtime"
msgstr "Overtid"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__overtime_status
msgid "Overtime Status"
msgstr ""

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_view_attendances
msgid "Overview"
msgstr "Oversigt"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
msgid "Please enter your PIN to"
msgstr "Angiv venligst din PIN for at"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
msgid "Please return to the main menu."
msgstr "Gå venligst tilbage til hovedmenuen."

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Powered by"
msgstr "Drevet af"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_employee_public
msgid "Public Employee"
msgstr "Offentlig ansat"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "RFID Token with reader"
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "RFID Token with reader on tablet"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__rating_ids
msgid "Ratings"
msgstr "Bedømmelser"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/views/attendance_helper_view.xml:0
msgid "Ready to track attendances ?"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree_management
msgid "Refuse"
msgstr "Afslå"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Refuse Extra Hours"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__overtime_status__refused
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_management_view_filter
msgid "Refused"
msgstr "Afvist"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_reporting
msgid "Reporting"
msgstr "Rapportering"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS leveringsfejl"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Scan your badge"
msgstr "Skan dit badge"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_barcode_source__scanner
msgid "Scanner"
msgstr "Skanner"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
msgid "Search..."
msgstr "Søg..."

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Select on Tablet"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Set PIN codes in the employee detail form (in HR Settings tab)."
msgstr ""
"Angiv PIN koder medarbejder detaljer formularen (i HR Indstillinger fanen)."

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "Set my badge"
msgstr ""

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.action_hr_attendance_settings
msgid "Settings"
msgstr "Opsætning"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
msgid "Sign out"
msgstr "Log ud"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
msgid "Since"
msgstr "Siden"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__in_mode__systray
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__out_mode__systray
msgid "Systray"
msgstr "Systray"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__in_mode__technical
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__out_mode__technical
msgid "Technical"
msgstr "Teknisk"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/views/attendance_helper_view.xml:0
msgid "The attendance records of your employees will be displayed here."
msgstr "Fremmøde skemaerne for dine ansatte vil blive vist her."

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_management_action
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_reporting
msgid "The attendance reporting of your employees will be displayed here."
msgstr "Rapporteringen for dine medarbejderes fremmøde vil blive vist her."

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_overtime_action
msgid "The overtime records of your employees will be displayed here."
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_employee__attendance_manager_id
#: model:ir.model.fields,help:hr_attendance.field_hr_employee_public__attendance_manager_id
#: model:ir.model.fields,help:hr_attendance.field_res_users__attendance_manager_id
msgid ""
"The user set in Attendance will access the attendance of the employee "
"through the dedicated app and will be able to edit them."
msgstr ""
"Brugere, som er angivet i Fremmøde, kan tilgå medarbejderens fremmøde via "
"den dedikerede app og vil kunne redigere dem."

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance_own_reader
msgid ""
"The user will have access to his own attendances on his user / employee "
"profile"
msgstr ""
"Brugeren vil have adgang til sin egen fremmøde på hans bruger- / "
"medarbejderprofil"

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance_officer
msgid ""
"The user will have access to the attendance records and reporting of "
"employees where he's set as an attendance manager"
msgstr ""
"Brugeren vil have adgang til medarbejderes fremmøderegistreringer og - "
"rapportering, når han er angivet som fremmøde manager"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
msgid ""
"This attendance was automatically checked out because the employee exceeded "
"the allowed time for their scheduled work hours."
msgstr ""

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
msgid ""
"This attendance was automatically created to cover an unjustified absence on"
" that day."
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__overtime_status__to_approve
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_management_view_filter
msgid "To Approve"
msgstr "Til godkendelse"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
msgid "Today"
msgstr "I dag"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__overtime_company_threshold
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__overtime_company_threshold
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Tolerance Time In Favor Of Company"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__overtime_employee_threshold
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__overtime_employee_threshold
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Tolerance Time In Favor Of Employee"
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
msgid "Total"
msgstr "I alt"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__total_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__total_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__total_overtime
msgid "Total Overtime"
msgstr "Total overtid"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
msgid "Total today"
msgstr "Total i dag"

#. module: hr_attendance
#: model:ir.actions.server,name:hr_attendance.action_try_kiosk
msgid "Try kiosk"
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/views/attendance_helper_view.xml:0
msgid "Try the backend and reporting:"
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/views/attendance_helper_view.xml:0
msgid "Try the kiosk"
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/views/attendance_helper_view.xml:0
msgid "Try the top"
msgstr ""

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/controllers/main.py:0
msgid "Unknown"
msgstr "Ukendt"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Use PIN codes (defined on the Employee's profile) to check-in."
msgstr ""
"Anvend PIN koder (defineret på medarbejderens profil) til at tjekke ind."

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Use this url to access your kiosk mode from any device."
msgstr ""

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_users
msgid "User"
msgstr "Bruger"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_own_reader
msgid "User: Read his own attendances"
msgstr "Bruger: læse egne fremmøder"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "View on Maps"
msgstr "Se via Maps"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__website_message_ids
msgid "Website Messages"
msgstr "Beskeder fra hjemmesiden"

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__website_message_ids
msgid "Website communication history"
msgstr "Hjemmesidens kommunikations historik"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
msgid "Welcome"
msgstr "Velkommen"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_employee_simple_tree_view
msgid "Work Hours"
msgstr "Arbejdstimer"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree_management
msgid "Worked Extra Hours"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__worked_hours
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_overtime_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_validated_hours_employee_simple_tree_view
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_graph
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Worked Hours"
msgstr "Antal arbejdstimer"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree_management
msgid "Worked Time"
msgstr "Arbejdet tid"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "Worked hours this month"
msgstr "Arbejdstimer denne måned"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.js:0
msgid "Wrong Pin"
msgstr "Forkert PIN"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
msgid "You cannot duplicate an attendance."
msgstr "Du kan ikke kopiere et fremmøde."

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
msgid "You don't have the rights to execute that action."
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.js:0
msgid "Your badge Id is now set, you can scan your badge."
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.js:0
msgid "Your badge has already been set."
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
msgid "check in"
msgstr "tjek ind"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
msgid "check out"
msgstr "tjek ud"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "e.g. 1102021021"
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/views/attendance_helper_view.xml:0
msgid "icon (e.g for work from home)"
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/views/attendance_helper_view.xml:0
msgid "or"
msgstr "eller"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
msgid "with optional PIN code"
msgstr ""
