# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sms
# 
# Translators:
# <PERSON>, 2025
# Wil <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-07 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_invalid_count
msgid "# Invalid recipients"
msgstr "# 無效的收件人"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_valid_count
msgid "# Valid recipients"
msgstr "#有效的收件人"

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid ""
"%(count)s out of the %(total)s selected SMS Text Messages have successfully "
"been resent."
msgstr "已成功重新傳送 %(count)s 個 SMS 文字訊息。原本選取的總數為 %(total)s 個。"

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_template.py:0
msgid "%s (copy)"
msgstr "%s (副本)"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_composer.py:0
msgid "%s invalid recipients"
msgstr "%s無效的收件人"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
msgid "******-555-555"
msgstr "******-555-555"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.xml:0
msgid ", fits in"
msgstr "，需要"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.iap_account_view_form
msgid ""
"<i class=\"fa fa-warning\"/> An error occurred with your account. Please "
"contact the support for more information..."
msgstr "<i class=\"fa fa-warning\"/> 你的帳戶發生錯誤。請聯絡技術支援人員，以獲取更多資訊。"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.iap_account_view_form
msgid ""
"<i class=\"fa fa-warning\"/> You cannot send SMS while your account is not "
"registered."
msgstr "<i class=\"fa fa-warning\"/> 帳戶未註冊時，無法發送短信。"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.iap_account_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Register"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            註冊"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.iap_account_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            Set Sender Name"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                            設定發訊人名稱"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid ""
"<span class=\"o_stat_text\">Add</span>\n"
"                                <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">添加</span>\n"
"                                <span class=\"o_stat_text\">上下文動作</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "<span class=\"o_stat_text\">Preview</span>"
msgstr "<span class=\"o_stat_text\">預覽</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid ""
"<span class=\"o_stat_text\">Remove</span>\n"
"                                <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">移除</span>\n"
"                                <span class=\"o_stat_text\">上下文動作</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "<span class=\"text-warning\" invisible=\"not no_record\">No records</span>"
msgstr "<span class=\"text-warning\" invisible=\"not no_record\">沒有記錄</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.ir_actions_server_view_form
msgid ""
"<span invisible=\"sms_method != 'sms'\">\n"
"                                The message will be sent as an SMS to the recipients of the template\n"
"                                and will not appear in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"sms_method != 'note'\">\n"
"                                The SMS will not be sent, it will only be posted as an\n"
"                                internal note in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"sms_method != 'comment'\">\n"
"                                The SMS will be sent as an SMS to the recipients of the\n"
"                                template and it will also be posted as an internal note\n"
"                                in the messaging history.\n"
"                            </span>"
msgstr ""
"<span invisible=\"sms_method != 'sms'\">\n"
"                                訊息將以 SMS 簡訊形式發送給範本的收件人，\n"
"                                而且不會出現在訊息歷史記錄中。\n"
"                            </span>\n"
"                            <span invisible=\"sms_method != 'note'\">\n"
"                                簡訊不會被發送，只會在訊息歷史記錄中，\n"
"                                作為內部備註發佈。\n"
"                            </span>\n"
"                            <span invisible=\"sms_method != 'comment'\">\n"
"                                簡訊將以 SMS 簡訊形式發送給範本的收件人，\n"
"                                亦會在訊息歷史記錄中，\n"
"                                作為內部備註發佈。\n"
"                            </span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "<span> or to specify the country code.</span>"
msgstr "<span>或指定國家代碼。</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid ""
"<strong>Invalid number:</strong>\n"
"                        <span> make sure to set a country on the </span>"
msgstr ""
"<strong>號碼無效：</strong>\n"
"                        <span>請確保已設置國家，在</span>"

#. module: sms
#: model:ir.model.constraint,message:sms.constraint_sms_tracker_sms_uuid_unique
msgid "A record for this UUID already exists"
msgstr "此 UUID 的記錄已存在"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__account_id
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__account_id
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__account_id
msgid "Account"
msgstr "帳戶"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_needaction
msgid "Action Needed"
msgstr "需要採取行動"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid ""
"Add a contextual action on the related model to open a sms composer with "
"this template"
msgstr "在相關的模型上添加上下文操作以使用此模板打開簡訊作者"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_sms__uuid
msgid "Alternate way to identify a SMS record, used for delivery reports"
msgstr "簡訊記錄的另一種識別方法，用於發送報告"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/core/failure_model_patch.js:0
msgid "An error occurred when sending an SMS"
msgstr "發送短訊時發生錯誤"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/core/failure_model_patch.js:0
msgid "An error occurred when sending an SMS on “%(record_name)s”"
msgstr "為以下項目傳送 SMS 短訊時，發生錯誤：%(record_name)s"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"An unknown error occurred. Please contact Odoo support if this error "
"persists."
msgstr "發生未知錯誤。如果此錯誤持續發生，請聯絡 Odoo 技術支援人員。"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__model_id
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__model_id
msgid "Applies to"
msgstr "應用於"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_reset_view_form
msgid ""
"Are you sure you want to reset these sms templates to their original "
"configuration? Changes and translations will be lost."
msgstr "確定要將這些短訊範本重設為原始配置？所有變更及翻譯將遺失。"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_attachment_count
msgid "Attachment Count"
msgstr "附件數目"

#. module: sms
#: model:ir.model,name:sms.model_base
msgid "Base"
msgstr "計稅基數"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_blacklist
msgid "Blacklisted"
msgstr "列入黑名單"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "列入黑名單的電話是手機"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "列入黑名單的電話是市話"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__body
#: model:ir.model.fields,field_description:sms.field_sms_template__body
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__body
msgid "Body"
msgstr "內文"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Buy credits"
msgstr "購買點數"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "Buy credits."
msgstr "購買積分。"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__can_cancel
msgid "Can Cancel"
msgstr "可取消"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__can_resend
msgid "Can Resend"
msgstr "可重新發送"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_code_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_tree
#: model_terms:ir.ui.view,arch_db:sms.sms_template_reset_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Cancel"
msgstr "取消"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__canceled
msgid "Cancelled"
msgstr "已取消"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Choose a language:"
msgstr "選擇一種語言："

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.ir_actions_server_view_form
msgid "Choose a template..."
msgstr "選擇一個範本⋯"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Choose an example"
msgstr "選擇一個示例"

#. module: sms
#. odoo-python
#: code:addons/sms/models/iap_account.py:0
#: code:addons/sms/wizard/sms_account_code.py:0
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid "Choose your sender name"
msgstr "選擇發訊人名稱"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Close"
msgstr "關閉"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__composition_mode
msgid "Composition Mode"
msgstr "合成模式"

#. module: sms
#: model:ir.model,name:sms.model_res_partner
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Contact"
msgstr "聯絡人"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Content"
msgstr "內容"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_country_not_supported
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_country_not_supported
msgid "Country Not Supported"
msgstr "不支援國家/地區"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_registration_needed
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_registration_needed
msgid "Country-specific Registration Required"
msgstr "需要有該國特定註冊"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "Country-specific registration required."
msgstr "需要有在特定國家的註冊。"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_composer__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_sms__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_template__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_tracker__create_uid
msgid "Created by"
msgstr "建立人員"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__create_date
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__create_date
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__create_date
#: model:ir.model.fields,field_description:sms.field_sms_composer__create_date
#: model:ir.model.fields,field_description:sms.field_sms_resend__create_date
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__create_date
#: model:ir.model.fields,field_description:sms.field_sms_sms__create_date
#: model:ir.model.fields,field_description:sms.field_sms_template__create_date
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__create_date
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__create_date
#: model:ir.model.fields,field_description:sms.field_sms_tracker__create_date
msgid "Created on"
msgstr "建立於"

#. module: sms
#: model:iap.service,unit_name:sms.iap_service_sms
msgid "Credits"
msgstr "點數"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__partner_id
msgid "Customer"
msgstr "客戶"

#. module: sms
#: model:sms.template,name:sms.sms_template_demo_0
msgid "Customer: automated SMS"
msgstr "客戶：自動簡訊"

#. module: sms
#: model:sms.template,body:sms.sms_template_demo_0
msgid "Dear {{ object.display_name }} this is an automated SMS."
msgstr "親愛的{{ object.display_name }} 這是一條自動簡訊。"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__sent
msgid "Delivered"
msgstr "已送貨"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Discard"
msgstr "捨棄"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__display_name
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__display_name
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__display_name
#: model:ir.model.fields,field_description:sms.field_sms_composer__display_name
#: model:ir.model.fields,field_description:sms.field_sms_resend__display_name
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__display_name
#: model:ir.model.fields,field_description:sms.field_sms_sms__display_name
#: model:ir.model.fields,field_description:sms.field_sms_template__display_name
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__display_name
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__display_name
#: model:ir.model.fields,field_description:sms.field_sms_tracker__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: sms
#: model:ir.model,name:sms.model_mail_followers
msgid "Document Followers"
msgstr "單據關注者"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_id
msgid "Document ID"
msgstr "文件識別碼"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_ids
msgid "Document IDs"
msgstr "文件 IDs"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_model_description
msgid "Document Model Description"
msgstr "文件模型描述"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_model
msgid "Document Model Name"
msgstr "單據模型名稱"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_duplicate
msgid "Duplicate"
msgstr "複製"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Edit Partners"
msgstr "編輯夥伴"

#. module: sms
#: model:ir.model,name:sms.model_mail_thread
msgid "Email Thread"
msgstr "電郵對話串"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/phone_field/phone_field.js:0
msgid "Enable SMS"
msgstr "啟用短訊"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
msgid "Enter a phone number to get an SMS with a verification code."
msgstr "輸入電話號碼，以接收驗證碼短訊。"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__error
msgid "Error"
msgstr "錯誤"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__failure_type
msgid "Error Message"
msgstr "錯誤訊息"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_expired
msgid "Expired"
msgstr "到期"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__failure_type
msgid "Failure Type"
msgstr "失敗類型"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__failure_type
msgid "Failure type"
msgstr "失敗類型"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr "用於存儲已清理電話號碼的欄位。説明加快搜索和比較。"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__template_fs
msgid ""
"File from where the template originates. Used to reset broken template."
msgstr "範本來自的來源檔案。用於重設損壞的範本。"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_follower_ids
msgid "Followers"
msgstr "關注人"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_partner_ids
msgid "Followers (Partners)"
msgstr "關注人（業務夥伴）"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_composer.py:0
msgid "Following numbers are not correctly encoded: %s"
msgstr "以下數字未正確編碼：%s"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__has_insufficient_credit
msgid "Has Insufficient Credit"
msgstr "點數不足"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__has_message
msgid "Has Message"
msgstr "有訊息"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_mail__has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_message__has_sms_error
msgid "Has SMS error"
msgstr "有簡訊錯誤"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__has_unregistered_account
msgid "Has Unregistered Account"
msgstr "有未註冊的帳號"

#. module: sms
#: model:ir.model,name:sms.model_iap_account
msgid "IAP Account"
msgstr "IAP 帳戶"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__id
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__id
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__id
#: model:ir.model.fields,field_description:sms.field_sms_composer__id
#: model:ir.model.fields,field_description:sms.field_sms_resend__id
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__id
#: model:ir.model.fields,field_description:sms.field_sms_sms__id
#: model:ir.model.fields,field_description:sms.field_sms_template__id
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__id
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__id
#: model:ir.model.fields,field_description:sms.field_sms_tracker__id
msgid "ID"
msgstr "識別號"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_needaction
msgid "If checked, new messages require your attention."
msgstr "勾選代表有新訊息需要您留意。"

#. module: sms
#: model:ir.model.fields,help:sms.field_account_analytic_account__message_has_sms_error
#: model:ir.model.fields,help:sms.field_calendar_event__message_has_sms_error
#: model:ir.model.fields,help:sms.field_crm_team__message_has_sms_error
#: model:ir.model.fields,help:sms.field_crm_team_member__message_has_sms_error
#: model:ir.model.fields,help:sms.field_discuss_channel__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle_log_contract__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle_log_services__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle_model__message_has_sms_error
#: model:ir.model.fields,help:sms.field_gamification_badge__message_has_sms_error
#: model:ir.model.fields,help:sms.field_gamification_challenge__message_has_sms_error
#: model:ir.model.fields,help:sms.field_iap_account__message_has_sms_error
#: model:ir.model.fields,help:sms.field_lunch_supplier__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_blacklist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_blacklist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_cc__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_main_attachment__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_phone__message_has_sms_error
#: model:ir.model.fields,help:sms.field_maintenance_equipment__message_has_sms_error
#: model:ir.model.fields,help:sms.field_maintenance_equipment_category__message_has_sms_error
#: model:ir.model.fields,help:sms.field_maintenance_request__message_has_sms_error
#: model:ir.model.fields,help:sms.field_phone_blacklist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_category__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_pricelist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_product__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_template__message_has_sms_error
#: model:ir.model.fields,help:sms.field_rating_mixin__message_has_sms_error
#: model:ir.model.fields,help:sms.field_res_partner__message_has_error
#: model:ir.model.fields,help:sms.field_res_partner__message_has_sms_error
#: model:ir.model.fields,help:sms.field_res_users__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "勾選代表有訊息發生傳送錯誤。"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr "如果已清理的電話號碼在黑名單中，則該聯絡人將不會再收到來自任何列表的群發簡訊"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Ignore all"
msgstr "全部忽略"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__outgoing
msgid "In Queue"
msgstr "排隊"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr "指示列入黑名單的已清理電話號碼是否為手機號碼。當模型中同時存在手機和電話欄位時，有助於區分哪個號碼被列入黑名單。"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr "指示列入黑名單的已清理電話號碼是否為電話號碼。當模型中同時存在手機和電話欄位時，有助於區分哪個號碼被列入黑名單。"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__comment_single_recipient
msgid "Indicates if the SMS composer targets a single specific recipient"
msgstr "指示 SMS 編輯器是否針對單個特定收件人"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_credit
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_credit
msgid "Insufficient Credit"
msgstr "點數不足"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_invalid_destination
msgid "Invalid Destination"
msgstr "無效目的地"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"Invalid phone number. Please make sure to follow the international format, "
"i.e. a plus sign (+), then country code, city code, and local phone number. "
"For example: ******-555-555"
msgstr "電話號碼無效。請確保使用國際電話格式：首先是加號 (+)，然後是國家代碼、城市代碼，最後是當地電話號碼。例：******-555-555"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_composer.py:0
msgid "Invalid recipient number. Please update it."
msgstr "收件人號碼無效。請更新它。"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_is_follower
msgid "Is Follower"
msgstr "是關注人"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_valid
msgid "Is valid"
msgstr "有效"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__mass_keep_log
msgid "Keep a note on document"
msgstr "記下文件"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__lang
msgid "Language"
msgstr "語言"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_composer__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_sms__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_template__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_tracker__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__write_date
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__write_date
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__write_date
#: model:ir.model.fields,field_description:sms.field_sms_composer__write_date
#: model:ir.model.fields,field_description:sms.field_sms_resend__write_date
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__write_date
#: model:ir.model.fields,field_description:sms.field_sms_sms__write_date
#: model:ir.model.fields,field_description:sms.field_sms_template__write_date
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__write_date
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__write_date
#: model:ir.model.fields,field_description:sms.field_sms_tracker__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: sms
#: model:ir.model,name:sms.model_sms_tracker
msgid "Link SMS to mailing/sms tracking models"
msgstr "將短訊與郵件/短訊追蹤模型連結"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__mail_message_id
msgid "Mail Message"
msgstr "信件消息"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_tracker__mail_notification_id
msgid "Mail Notification"
msgstr "郵件通知"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_ir_model__is_mail_thread_sms
msgid "Mail Thread SMS"
msgstr "信件線程簡訊"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__to_delete
msgid "Marked for deletion"
msgstr "已標記作刪除"

#. module: sms
#: model:ir.model,name:sms.model_mail_message
#: model:ir.model.fields,field_description:sms.field_sms_composer__body
#: model:ir.model.fields,field_description:sms.field_sms_resend__mail_message_id
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Message"
msgstr "消息"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_has_error
msgid "Message Delivery error"
msgstr "訊息遞送錯誤"

#. module: sms
#: model:ir.model,name:sms.model_mail_notification
msgid "Message Notifications"
msgstr "訊息通知"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_ids
msgid "Messages"
msgstr "訊息"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_number_missing
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_number_missing
msgid "Missing Number"
msgstr "缺少電話號碼"

#. module: sms
#: model:ir.model,name:sms.model_ir_model
msgid "Models"
msgstr "型號"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__name
msgid "Name"
msgstr "名稱"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__no_record
msgid "No Record"
msgstr "無記錄"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_not_allowed
msgid "Not Allowed"
msgstr "未許可"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_not_delivered
msgid "Not Delivered"
msgstr "未送貨"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__ir_actions_server__sms_method__note
msgid "Note only"
msgstr "只留備註"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid ""
"Note that this is not required, if you don't set a sender name, your SMS "
"will be sent from a short code."
msgstr "請留意，此項並非必須填寫。若不設定發訊人名稱，短訊發送方將顯示為簡短代碼。"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__notification_id
msgid "Notification"
msgstr "通知"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "通知類型"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__number
msgid "Number"
msgstr "號碼"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__number_field_name
msgid "Number Field"
msgstr "數字欄位"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_needaction_counter
msgid "Number of Actions"
msgstr "動作數量"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_has_error_counter
msgid "Number of errors"
msgstr "錯誤數量"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "需要採取行動的訊息數目"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "有發送錯誤的郵件數量"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__res_ids_count
msgid ""
"Number of recipients that will receive the SMS if sent in mass mode, without"
" applying the Active Domain value"
msgstr "如果以群發模式發送，而不應用 Active Domain 值，將接收 SMS 的收件人數量"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_optout
msgid "Opted Out"
msgstr "選擇退出"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"發送電子郵件時要選擇的可選翻譯語言（ISO 代碼）。如果未設置，將使用英文版本。這通常應該是提供適當語言的佔位符表達式，例如{{ "
"object.partner_id.lang }}。"

#. module: sms
#: model:ir.model,name:sms.model_sms_sms
msgid "Outgoing SMS"
msgstr "出向簡訊"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__partner_id
msgid "Partner"
msgstr "業務夥伴"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "電話黑名單"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_phone__phone_number
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__sms_number
msgid "Phone Number"
msgstr "電話號碼"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__recipient_single_number_itf
msgid ""
"Phone number of the recipient. If changed, it will be recorded on "
"recipient's profile."
msgstr "收件人的電話號碼。如果更改，將記錄在收件人的個人資料中。"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_mobile_search
msgid "Phone/Mobile"
msgstr "電話/手機"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_composer__composition_mode__comment
msgid "Post on a document"
msgstr "提交在文件上"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Preview of"
msgstr "預覽"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_reset_view_form
msgid "Proceed"
msgstr "繼續"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__process
msgid "Processing"
msgstr "處理中"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Put in queue"
msgstr "加入佇列"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__rating_ids
msgid "Ratings"
msgstr "評分"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Reason"
msgstr "原因"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Recipient"
msgstr "收件人"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__partner_name
msgid "Recipient Name"
msgstr "收件人名稱"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_number_itf
msgid "Recipient Number"
msgstr "收件人編號"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__recipient_ids
msgid "Recipients"
msgstr "接收者"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__numbers
msgid "Recipients (Numbers)"
msgstr "收件人（數字）"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_description
msgid "Recipients (Partners)"
msgstr "收件人（合作夥伴）"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__resource_ref
msgid "Record reference"
msgstr "參照記錄"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_code_view_form
msgid "Register"
msgstr "註冊"

#. module: sms
#. odoo-python
#: code:addons/sms/models/iap_account.py:0
#: code:addons/sms/wizard/sms_account_phone.py:0
msgid "Register Account"
msgstr "註冊帳戶"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "Register now."
msgstr "立即註冊"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_code_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
msgid "Register your SMS account"
msgstr "註冊你的短訊帳戶"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_rejected
msgid "Rejected"
msgstr "已拒絕"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__model
msgid "Related Document Model"
msgstr "相關的單據模型"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Remove the contextual action of the related model"
msgstr "刪除相關模型的上下文操作"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__render_model
msgid "Rendering Model"
msgstr "呈現模型"

#. module: sms
#: model:ir.actions.server,name:sms.ir_actions_server_sms_sms_resend
msgid "Resend"
msgstr "重新發送"

#. module: sms
#: model:ir.model,name:sms.model_sms_resend_recipient
msgid "Resend Notification"
msgstr "重新發送通知"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_template_reset_action
msgid "Reset SMS Template"
msgstr "重設短訊範本"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Reset Template"
msgstr "重設範本"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_tree
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Retry"
msgstr "重試"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_button/sms_button.xml:0
#: code:addons/sms/static/src/core/notification_model_patch.js:0
#: model:ir.actions.act_window,name:sms.sms_sms_action
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_id
#: model:ir.model.fields.selection,name:sms.selection__mail_message__message_type__sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__notification_type__sms
#: model:ir.ui.menu,name:sms.sms_sms_menu
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "SMS"
msgstr "電話短訊（SMS）"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.xml:0
msgid "SMS ("
msgstr "短訊（"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__ir_actions_server__sms_method__comment
msgid "SMS (with note)"
msgstr "短訊（留下備註）"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__ir_actions_server__sms_method__sms
msgid "SMS (without note)"
msgstr "短訊（不留備註）"

#. module: sms
#: model:ir.model,name:sms.model_sms_account_phone
msgid "SMS Account Registration Phone Number Wizard"
msgstr "短訊帳戶註冊電話號碼精靈"

#. module: sms
#: model:ir.model,name:sms.model_sms_account_sender
msgid "SMS Account Sender Name Wizard"
msgstr "短訊帳戶發訊人名稱精靈"

#. module: sms
#: model:ir.model,name:sms.model_sms_account_code
msgid "SMS Account Verification Code Wizard"
msgstr "短訊帳戶驗證碼精靈"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_account_analytic_account__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_calendar_event__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_crm_team__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_crm_team_member__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_discuss_channel__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle_log_contract__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle_log_services__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle_model__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_gamification_badge__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_gamification_challenge__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_iap_account__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_lunch_supplier__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_blacklist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_blacklist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_cc__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_main_attachment__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_phone__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_maintenance_equipment__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_maintenance_equipment_category__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_maintenance_request__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_phone_blacklist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_category__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_pricelist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_product__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_template__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_rating_mixin__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_res_partner__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_res_users__message_has_sms_error
msgid "SMS Delivery error"
msgstr "簡訊發送錯誤"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/messaging_menu/messaging_menu_patch.js:0
msgid "SMS Failure: %(modelName)s"
msgstr "SMS 短訊操作失敗：%(modelName)s"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/messaging_menu/messaging_menu_patch.js:0
msgid "SMS Failures"
msgstr "簡訊失敗"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_id_int
msgid "SMS ID"
msgstr "短訊識別碼"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_number
msgid "SMS Number"
msgstr "簡訊號碼"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "SMS Preview"
msgstr "簡訊預覽"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.xml:0
msgid "SMS Pricing"
msgstr "簡訊價格"

#. module: sms
#: model:ir.model,name:sms.model_sms_resend
msgid "SMS Resend"
msgstr "簡訊重新發送"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__state
msgid "SMS Status"
msgstr "簡訊狀態"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_ir_actions_server__sms_template_id
#: model:ir.model.fields,field_description:sms.field_ir_cron__sms_template_id
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "SMS Template"
msgstr "簡訊範本"

#. module: sms
#: model:ir.model,name:sms.model_sms_template_preview
msgid "SMS Template Preview"
msgstr "簡訊範本預覽"

#. module: sms
#: model:ir.model,name:sms.model_sms_template_reset
msgid "SMS Template Reset"
msgstr "短訊範本重設"

#. module: sms
#: model:ir.model,name:sms.model_sms_template
#: model:ir.ui.menu,name:sms.sms_template_menu
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_tree
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_tree
msgid "SMS Templates"
msgstr "簡訊範本"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_template_reset.py:0
msgid "SMS Templates have been reset"
msgstr "短訊範本已重設"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_tracker_ids
msgid "SMS Trackers"
msgstr "短訊追蹤工具"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "SMS content"
msgstr "SMS 內容"

#. module: sms
#. odoo-python
#: code:addons/sms/models/ir_actions_server.py:0
msgid "SMS template model of %(action_name)s does not match action model."
msgstr "%(action_name)s 的短訊範本模型，與動作模型不匹配。"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__sms_tracker_id
msgid "SMS trackers"
msgstr "短訊追蹤器"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_tracker__sms_uuid
msgid "SMS uuid"
msgstr "短訊uuid"

#. module: sms
#: model:ir.actions.server,name:sms.ir_cron_sms_scheduler_action_ir_actions_server
msgid "SMS: SMS Queue Manager"
msgstr "簡訊：簡訊佇列管理器"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_sanitized
#: model:ir.model.fields,field_description:sms.field_sms_composer__sanitized_numbers
msgid "Sanitized Number"
msgstr "消毒數量"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_search
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_search
msgid "Search SMS Templates"
msgstr "搜尋簡訊模板"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Send & Close"
msgstr "發送並關閉"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_tree
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Send Now"
msgstr "立即發送"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_button/sms_button.js:0
#: model:ir.actions.act_window,name:sms.res_partner_act_window_sms_composer_multi
#: model:ir.actions.act_window,name:sms.res_partner_act_window_sms_composer_single
#: model:ir.actions.act_window,name:sms.sms_composer_action_form
#: model:ir.model.fields.selection,name:sms.selection__ir_actions_server__state__sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Send SMS"
msgstr "發送簡訊"

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_template.py:0
msgid "Send SMS (%s)"
msgstr "傳送簡訊 （%s）"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_ir_actions_server__sms_method
#: model:ir.model.fields,field_description:sms.field_ir_cron__sms_method
msgid "Send SMS As"
msgstr "傳送短訊形式"

#. module: sms
#: model:ir.model,name:sms.model_sms_composer
msgid "Send SMS Wizard"
msgstr "傳送簡訊嚮導"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_composer__composition_mode__mass
msgid "Send SMS in batch"
msgstr "批次傳送簡訊"

#. module: sms
#: model:iap.service,description:sms.iap_service_sms
msgid "Send SMS to your contacts directly from your database."
msgstr "直接在資料庫中，向聯絡人發送電話短信。"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Send an SMS"
msgstr "發送一條簡訊"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__mass_force_send
msgid "Send directly"
msgstr "直接發送"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_composer__composition_mode__numbers
msgid "Send to numbers"
msgstr "發送到號碼"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_phone_view_form
msgid "Send verification code"
msgstr "傳送驗證碼"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_iap_account__sender_name
#: model:ir.model.fields,field_description:sms.field_sms_account_sender__sender_name
msgid "Sender Name"
msgstr "發訊人名稱"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_resend_action
msgid "Sending Failures"
msgstr "發送失敗"

#. module: sms
#. odoo-python
#: code:addons/sms/models/ir_actions_server.py:0
msgid "Sending SMS can only be done on a not transient mail.thread model"
msgstr ""

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__pending
msgid "Sent"
msgstr "發送"

#. module: sms
#: model:ir.model,name:sms.model_ir_actions_server
msgid "Server Action"
msgstr "伺服器動作"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_server
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_server
msgid "Server Error"
msgstr "伺服器錯誤"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid "Set sender name"
msgstr "設定發訊人名稱"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Set up an account"
msgstr "設置帳戶"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__sidebar_action_id
msgid "Sidebar action"
msgstr "邊欄動作"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__sidebar_action_id
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr "在相關單據模型之記錄提供此模板的邊欄按鈕"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__comment_single_recipient
msgid "Single Mode"
msgstr "單模"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid "Skip for now"
msgstr "暫時跳過"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__sms_resend_id
msgid "Sms Resend"
msgstr "簡訊重新發送"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__sms_template_id
msgid "Sms Template"
msgstr "簡訊範本"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_number
msgid "Stored Recipient Number"
msgstr "存儲的收件人號碼"

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid "Success"
msgstr "成功"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_reset__template_ids
msgid "Template"
msgstr "範本文字"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__template_fs
msgid "Template Filename"
msgstr "範本檔案名稱"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_template_preview_action
msgid "Template Preview"
msgstr "模板預覽"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__lang
msgid "Template Preview Language"
msgstr "模板預覽語言"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_template_action
msgid "Templates"
msgstr "範本"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"The SMS Service is currently unavailable for new users and new accounts "
"registrations are suspended."
msgstr "短訊服務目前不對新的使用者開放，新帳戶註冊亦已暫停。"

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid "The SMS Text Messages could not be resent."
msgstr "無法重新發送 SMS 文本消息。"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "The content of the message violates rules applied by our providers."
msgstr "訊息內容違反我們服務商的規定。"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "The destination country is not supported."
msgstr "不支援目的地國家。"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "The number you're trying to reach is not correctly formatted."
msgstr "您嘗試撥打的號碼格式不正確。"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__model_id
#: model:ir.model.fields,help:sms.field_sms_template_preview__model_id
msgid "The type of document this template can be used with"
msgstr "該模板可使用的文件類型"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "The verification code is incorrect."
msgstr "驗證碼不正確。"

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid "There are no SMS Text Messages to resend."
msgstr "沒有要重新發送的 SMS 文本消息。"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "This SMS has been removed as the number was already used."
msgstr "因該號碼已被使用，此短信已被刪除。"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"This account already has an existing sender name and it cannot be changed."
msgstr "此帳戶已有目前使用的發訊人名稱，不可更改。"

#. module: sms
#: model:ir.model.fields,help:sms.field_iap_account__sender_name
msgid "This is the name that will be displayed as the sender of the SMS."
msgstr "這是作為短訊發訊人顯示的名稱。"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.res_partner_view_form
msgid ""
"This phone number is blacklisted for SMS Marketing. Click to unblacklist."
msgstr "此電話號碼已列入簡訊營銷黑名單。點擊取消黑名單。"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "This phone number/account has been banned from our service."
msgstr "此電話號碼/帳戶已被禁止使用我們的服務。"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__resend
msgid "Try Again"
msgstr "再試"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_ir_actions_server__state
#: model:ir.model.fields,field_description:sms.field_ir_cron__state
#: model:ir.model.fields,field_description:sms.field_mail_mail__message_type
#: model:ir.model.fields,field_description:sms.field_mail_message__message_type
msgid "Type"
msgstr "類型"

#. module: sms
#: model:ir.model.fields,help:sms.field_ir_actions_server__state
#: model:ir.model.fields,help:sms.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Update a Record': update the values of a record\n"
"- 'Create Activity': create an activity (Discuss)\n"
"- 'Send Email': post a message, a note or send an email (Discuss)\n"
"- 'Send SMS': send SMS, log them on documents (SMS)- 'Add/Remove Followers': add or remove followers to a record (Discuss)\n"
"- 'Create Record': create a new record with new values\n"
"- 'Execute Code': a block of Python code that will be executed\n"
"- 'Send Webhook Notification': send a POST request to an external system, also known as a Webhook\n"
"- 'Execute Existing Actions': define an action that triggers several other server actions\n"
msgstr ""
"伺服器操作類型。可使用以下的值：\n"
"- 更新記錄：更新某項記錄的值\n"
"- 建立活動：建立一項活動（聊天應用程式）\n"
"- 傳送電郵：發佈訊息、備註或傳送電子郵件（聊天應用程式）\n"
"- 傳送短訊：傳送 SMS 電話短訊，並在文件中建立相關記錄（短訊應用程式）\n"
"- 加入/移除關注者：將關注者加入記錄中，或從記錄中移除（聊天應用程式）\n"
"- 建立記錄：使用新的值去建立新記錄\n"
"- 執行程式碼：將會執行的 Python 程式碼段落\n"
"- 傳送網絡鈎子通知：向外部系統傳送一個 POST 請求（亦稱為 webhook 網絡鈎子）\n"
"- 執行現有操作：設定一項操作，用以觸發多項其他伺服器操作\n"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__uuid
msgid "UUID"
msgstr "UUID"

#. module: sms
#: model:ir.model.constraint,message:sms.constraint_sms_sms_uuid_unique
msgid "UUID must be unique"
msgstr "UUID必須獨一無二"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__unknown
msgid "Unknown error"
msgstr "未知的錯誤"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_acc
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_acc
msgid "Unregistered Account"
msgstr "未註冊賬戶"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__template_id
msgid "Use Template"
msgstr "使用模板"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__mass_use_blacklist
msgid "Use blacklist"
msgstr "使用黑名單"

#. module: sms
#: model:ir.model.fields,help:sms.field_mail_mail__message_type
#: model:ir.model.fields,help:sms.field_mail_message__message_type
msgid ""
"Used to categorize message generator\n"
"'email': generated by an incoming email e.g. mailgateway\n"
"'comment': generated by user input e.g. through discuss or composer\n"
"'email_outgoing': generated by a mailing\n"
"'notification': generated by system e.g. tracking messages\n"
"'auto_comment': generated by automated notification mechanism e.g. acknowledgment\n"
"'user_notification': generated for a specific recipient"
msgstr ""
"用於對訊息生成器進行分類\n"
"email(電子郵件)：由接收到的電子郵件生成，如 mailgateway\n"
"comment(評論)：由用戶輸入生成，例如通過討論或編輯器\n"
"email_outgoing(外寄郵件)：由郵寄產生\n"
"notification(通知)：由系統生成，例如追蹤訊息\n"
"auto_comment(自動評論)：由自動通知機制生成，例如確認\n"
"user_notification(用戶通知)：為特定收件人產生"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_account_code__verification_code
msgid "Verification Code"
msgstr "驗證碼"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_ids_count
msgid "Visible records count"
msgstr "可見記錄數"

#. module: sms
#. odoo-python
#: code:addons/sms/models/sms_sms.py:0
msgid "Warning"
msgstr "警告"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "We were not able to find your account in our database."
msgstr "未能在資料庫中找到你的帳戶。"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"We were not able to reach you via your phone number. If you have requested "
"multiple codes recently, please retry later."
msgstr "我們未能透過你的電話號碼聯絡你。如果你最近要求了多個代碼，請稍後再試。"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__website_message_ids
msgid "Website Messages"
msgstr "網站資訊"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__website_message_ids
msgid "Website communication history"
msgstr "網站溝通記錄"

#. module: sms
#: model:ir.model.fields,help:sms.field_ir_model__is_mail_thread_sms
msgid "Whether this model supports messages and notifications through SMS"
msgstr "該模型是否支援通過簡訊發送消息和通知。"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_sms__to_delete
msgid ""
"Will automatically be deleted, while notifications will not be deleted in "
"any case."
msgstr "將會自動刪除，而通知在任何情況下都不會刪除。"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_number_format
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_number_format
msgid "Wrong Number Format"
msgstr "錯誤的電話號碼格式"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_resend.py:0
msgid "You do not have access to the message and/or related document."
msgstr "您無權存取消息和/或相關的文件。"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "You don't have an eligible IAP account."
msgstr "您沒有符合條件的 IAP 帳戶。"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "You don't have enough credits on your IAP account."
msgstr "您的 IAP 帳戶上沒有足夠的積分。"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "You tried too many times. Please retry later."
msgstr "嘗試次數太多。請稍後再試。"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.js:0
msgid ""
"Your SMS Text Message must include at least one non-whitespace character"
msgstr "您的 SMS 文本消息必須包含至少一個非空白字符"

#. module: sms
#. odoo-python
#: code:addons/sms/wizard/sms_account_code.py:0
msgid "Your SMS account has been successfully registered."
msgstr "你的短訊帳戶已成功註冊。"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid ""
"Your sender name must be between 3 and 11 characters long and only contain "
"alphanumeric characters."
msgstr "你的發訊人名稱，長度必須為 3 至 11 個字元，並只可使用英數字元。"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_account_sender_view_form
msgid ""
"Your sender name must be between 3 and 11 characters long and only contain alphanumeric characters.\n"
"                        It must fit your company name, and you aren't allowed to modify it once you registered one, choose it carefully."
msgstr ""
"你的發訊人名稱長度必須介乎 3 至 11 個字元之間，並只可包含英數字元。\n"
"                        此名稱必須與你的公司名稱相符，一經註冊便不能修改，故此請謹慎選擇。"

#. module: sms
#. odoo-python
#: code:addons/sms/tools/sms_api.py:0
msgid "Your sms account has not been activated yet."
msgstr "你的短訊帳戶尚未啟動。"

#. module: sms
#. odoo-javascript
#: code:addons/sms/static/src/components/sms_widget/fields_sms_widget.xml:0
msgid "characters"
msgstr "字元"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "e.g. +1 415 555 0100"
msgstr "例如+1 415 555 0100"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "e.g. Calendar Reminder"
msgstr "例如:日曆：提醒"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "e.g. Contact"
msgstr "例如接觸"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "e.g. en_US or {{ object.partner_id.lang }}"
msgstr "例如en_US 或 {{ object.partner_id.lang }}"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "out of"
msgstr "除外"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid ""
"recipients have an invalid phone number and will not receive this text "
"message."
msgstr "收件人電話號碼無效，將不會收到此訊息。"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "record:"
msgstr "記錄："
