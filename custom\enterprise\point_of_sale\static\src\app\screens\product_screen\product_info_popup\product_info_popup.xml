<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="point_of_sale.ProductInfoPopup">
        <Dialog title.translate="Product information">
            <ProductInfoBanner t-if="!props.product.isConfigurable()" product="props.product" info="props.info"/>
            <div class="section-inventory mt-3 mb-4 pb-4 border-bottom text-start" t-if="!isVariant and props.info.productInfo.warehouses.length > 0">
                <h3 class="section-title">
                    Inventory
                    <t t-if="pos.session.update_stock_at_closing">(as of opening)</t>
                </h3>
                <div class="section-inventory-body">
                    <t t-foreach="props.info.productInfo.warehouses" t-as="warehouse" t-key="warehouse.name">
                        <div class="d-flex flex-column flex-md-row gap-2">
                            <div>
                                <t t-esc="warehouse.name" class="table-name"/>
                                :
                            </div>
                            <div>
                                <span class="me-1 fw-bolder"><t t-esc="warehouse.available_quantity" class="table-name"/></span>
                                <t t-esc="warehouse.uom"/> available,
                            </div>
                            <div>
                                <span class="me-1 fw-bolder"><t t-esc="warehouse.forecasted_quantity"/></span>
                                forecasted
                            </div>
                        </div>
                    </t>
                </div>
            </div>
            <div class="section-supplier mt-3 mb-4 pb-4 border-bottom text-start" t-if="props.info.productInfo.suppliers.length > 0">
                <h3 class="section-title">Replenishment</h3>
                <div class="section-supplier-body">
                    <t t-foreach="props.info.productInfo.suppliers" t-as="supplier" t-key="supplier.id">
                        <div class="d-flex flex-column flex-md-row gap-2">
                            <div>
                                <span t-esc="supplier.name" class="table-name"/>:
                            </div>
                            <span class="me-1 fw-bolder">
                                <t t-esc="supplier.delay"/> 
                                Days
                            </span>
                            <span t-if="_hasMarginsCostsAccessRights()">
                                <t t-esc="env.utils.formatCurrency(supplier.price)"/>
                            </span>
                        </div>
                    </t>
                </div>
            </div>
            <div class="financials-order d-grid gap-2 mt-3" style="grid-template-columns: repeat(2, 1fr);">
                <div class="section-financials text-start">
                    <h3 class="section-title">Financials</h3>
                    <div class="section-financials-body d-flex flex-column">
                        <table class="table table-borderless mb-0">
                            <tr>
                                <td>Price excl. Tax:</td>
                                <td><t t-esc="env.utils.formatCurrency(props.info.productInfo.all_prices.price_without_tax)"/></td>
                            </tr>
                            <tr t-if="_hasMarginsCostsAccessRights()">
                                <td>Cost:</td>
                                <td><t t-esc="props.info.costCurrency"/></td>
                            </tr>
                            <tr t-if="_hasMarginsCostsAccessRights()">
                                <td>Margin:</td>
                                <td><t t-esc="props.info.marginCurrency"/> (<t t-esc="props.info.marginPercent"/>%) </td>
                            </tr>
                        </table>
                        <table class="table table-borderless">
                            <t t-foreach="props.info.productInfo.pricelists" t-as="pricelist" t-key="pricelist.name">
                                <tr>
                                    <td t-esc="pricelist.name"/>
                                    <td t-esc="env.utils.formatCurrency(pricelist.price)"/>
                                </tr>
                            </t>
                        </table>
                    </div>
                </div>
                <div class="section-order text-start">
                    <h3 class="section-title">Order</h3>
                    <div class="section-order-body">
                        <table class="table table-borderless w-100 mb-0">
                            <tr>
                                <td>Total Price excl. Tax:</td>
                                <td t-esc="props.info.orderPriceWithoutTaxCurrency" class="table-value"/>
                            </tr>
                            <tr t-if="_hasMarginsCostsAccessRights()">
                                <td>Total Cost:</td>
                                <td t-esc="props.info.orderCostCurrency" class="table-value"/>
                            </tr>
                            <tr t-if="_hasMarginsCostsAccessRights()">
                                <td>Total Margin:</td>
                                <td class="table-value"><t t-esc="props.info.orderMarginCurrency"/> (<t t-esc="props.info.orderMarginPercent"/>%)</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <t t-set-slot="footer">
                <button class="btn btn-primary btn-lg lh-lg" t-on-click="props.close">Ok</button>
                <button t-if="allowProductEdition" class="btn btn-secondary btn-lg lh-lg" t-on-click="editProduct">Edit</button>
            </t>
        </Dialog>
    </t>
</templates>
