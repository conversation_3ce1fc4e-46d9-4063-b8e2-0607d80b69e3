# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_timesheet
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:04+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/sale_order.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    No activities found. Let's start a new one!\n"
"                </p><p>\n"
"                    Track your working hours by projects every day and invoice this time to your customers.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    لم يتم العثور على أي أنشطة. فلننشئ نشاطاً جديداً!\n"
"                </p><p>\n"
"                    تتبع ساعات عملك يومياً حسب المشاريع وقم بفوترة الوقت لعملائك.\n"
"                </p>\n"
"            "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/account_move.py:0
#: code:addons/sale_timesheet/models/project_project.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Record timesheets\n"
"                </p><p>\n"
"                    You can register and track your workings hours by project every\n"
"                    day. Every time spent on a project will become a cost and can be re-invoiced to\n"
"                    customers if required.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    تسجيل الجداول الزمنية\n"
"                </p><p>\n"
"                    يمكنك تسجيل وتتبع ساعات العمل حسب المشروع كل يوم.\n"
"                    سيتم تحويل الوقت المستغرق في إتمام مشروع إلى تكلفة ويمكن إعادة فوترته\n"
"                    للعملاء عند الحاجة.\n"
"                </p>\n"
"            "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
msgid ""
"'%(missing_plan_names)s' analytic plan(s) required on the analytic "
"distribution of the sale order item '%(so_line_name)s' linked to the "
"timesheet."
msgstr ""
"الخطة (الخطط) التحليلية '%(missing_plan_names)s' مطلوبة في التوزيع التحليلي "
"لبند أمر البيع '%(so_line_name)s' المرتبط بالجدول الزمني. "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_sale_page
msgid "- Timesheet product"
msgstr "- منتج الجداول الزمنية "

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "1 Onsite Interview"
msgstr "1 مقابلة شخصية "

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "1 Phone Call"
msgstr "1 مكالمة هاتفية "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "12 days / year, including <br>6 of your choice."
msgstr "12 يوماً / السنة، من ضمنها <br>6 من اختيارك. "

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "2 open days"
msgstr "يومان مفتوحان "

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "4 Days after Interview"
msgstr "4 أيام بعد المقابلة "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "<b>Daily Cost: </b>"
msgstr "<b>التكلفة اليومية: </b> "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "<b>Unit Price: </b>"
msgstr "<b>سعر الوحدة: </b> "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid ""
"<i class=\"fa fa-lightbulb-o\"/>\n"
"                        <span>\n"
"                            Define the rate at which an employee's time is billed based on their expertise, skills, or experience.\n"
"                            To bill the same service at a different rate, create separate sales order items.\n"
"                        </span>"
msgstr ""
"<i class=\"fa fa-lightbulb-o\"/>\n"
"                        <span>\n"
"                            حدد المعدل الذي تتم به فاتورة وقت الموظف بناءً على خبرته أو مهاراته أو تجربته.\n"
"                            لفوترة نفس الخدمة بسعر مختلف، قم بإنشاء عناصر أمر مبيعات منفصلة.\n"
"                        </span>"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "<small><b>READ</b></small>"
msgstr "<small><b>قراءة</b></small> "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_form_inherit
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning\" title=\"The sales "
"order associated with this timesheet entry has been cancelled.\" "
"invisible=\"sale_order_state != 'cancel'\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning\" title=\"لقد تم إلغاء"
" أمر البيع المرتبط بقيد الجداول الزمنية. \" invisible=\"sale_order_state != "
"'cancel'\"/>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_form_inherit
msgid "<span class=\"o_stat_text\">Invoice</span>"
msgstr "<span class=\"o_stat_text\">فاتورة</span>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.account_invoice_view_form_inherit_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
msgid "<span class=\"o_stat_text\">Recorded</span>"
msgstr "<span class=\"o_stat_text\">مسجل</span>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_form_inherit
msgid "<span class=\"o_stat_text\">Sales Order</span>"
msgstr "<span class=\"o_stat_text\">أمر البيع</span> "

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "<span class=\"text-muted small\">Days to get an Offer</span>"
msgstr "<span class=\"text-muted small\">أيام للحصول على عرض</span> "

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "<span class=\"text-muted small\">Process</span>"
msgstr "<span class=\"text-muted small\">معالجة</span> "

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "<span class=\"text-muted small\">Time to Answer</span>"
msgstr "<span class=\"text-muted small\">الوقت المحدد للإجابة</span> "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Amount Due:</strong>"
msgstr "<strong>المبلغ المستحق:</strong> "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Invoiced:</strong>"
msgstr "<strong>مفوتر:</strong> "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Invoices:</strong>"
msgstr "<strong>الفواتير:</strong> "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Sales Order:</strong>"
msgstr "<strong>أمر المبيعات:</strong> "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_timesheet_table_inherit
msgid "<strong>Time Remaining on SO: </strong>"
msgstr "<strong>الوقت المتبقي في أمر البيع: </strong> "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "<u>Profitability</u>"
msgstr "<u>الربحية</u> "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "A full-time position <br>Attractive salary package."
msgstr "منصب بدوام كامل <br>باقة راتب مغرية. "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"حسب تهيئة المنتج، يمكن حساب الكمية التي تم توصيلها تلقائياً بإحدى الطرق التالية:\n"
"  - يدوياً: تُحسب الكمية يدوياً في البند\n"
"  - تحليلياً من النفقات: تكون الكمية هي مجموع الكمية من النفقات المُرحلة\n"
"  - الجداول الزمنية: تكون الكمية هي مجموع الساعات المستغرقة لإجراء المهام المرتبطة ببند المبيعات\n"
"  - حركات المخزون: تأتي الكمية من عمليات الانتقاء المؤكدة\n"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Achieve monthly sales objectives"
msgstr "تحقيق أهداف المبيعات الشهرية "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Additional languages"
msgstr "لغات إضافية"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Administrative Work"
msgstr "العمل الإداري "

#. module: sale_timesheet
#: model:account.analytic.account,name:sale_timesheet.account_analytic_account_project_support
#: model:project.project,name:sale_timesheet.project_support
msgid "After-Sales Services"
msgstr "خدمات ما بعد المبيعات "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__allocated_hours
msgid "Allocated Time"
msgstr "الوقت المخصص "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__amount_to_invoice
msgid "Amount to invoice"
msgstr "المبلغ بانتظار الفوترة "

#. module: sale_timesheet
#: model:ir.model.constraint,message:sale_timesheet.constraint_project_sale_line_employee_map_uniqueness_employee
msgid ""
"An employee cannot be selected more than once in the mapping. Please remove "
"duplicate(s) and try again."
msgstr ""
"لا يمكن تحديد الموظف أكثر من مرة في التخطيط. يرجى إزالة النسخة (النسخ) ثم "
"حاول مجدداً. "

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "البند التحليلي"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__analytic_line_ids
msgid "Analytic lines"
msgstr "البنود التحليلية"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid ""
"As an employee of our company, you will <b>collaborate with each department\n"
"                        to create and deploy disruptive products.</b> Come work at a growing company\n"
"                        that offers great benefits with opportunities to moving forward and learn\n"
"                        alongside accomplished leaders. We're seeking an experienced and outstanding\n"
"                        member of staff.\n"
"                        <br><br>\n"
"                        This position is both <b>creative and rigorous</b> by nature you need to think\n"
"                        outside the box. We expect the candidate to be proactive and have a \"get it done\"\n"
"                        spirit. To be successful, you will have solid solving problem skills."
msgstr ""
"كموظف في شركتنا، سوف <b>تتعاون مع كل من الأقسام\n"
"                        لإنشاء المنتجات الثورية وجعلها متاحة للاستخدام.</b> انضم إلينا للعمل في شركة في نمو مستمر\n"
"                        والتي تمنحك الكثير من الفوائد والفرص للمضي قدماً والتعلم \n"
"                        بجانب القادة المتميزين. نحن نبحث عن موظف ذو خبرة\n"
"                        ومميز في عمله.\n"
"                        <br><br>\n"
"                        هذا المنصب <b>مبدع وحازم</b> في آن واحد. عليك التفكير خارج\n"
"                        الصندوق بطبيعتك. نتوقع من المُرشح أن يكون سبّاقاً وأن تكون لديه روح مفعمة بالعزم والحيوية\n"
"                        حتى يكون ناجحاً، من المهم أن يكون لديه مهارة حل المشاكل. "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Autonomy"
msgstr "الاستقلالية "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Bachelor Degree or Higher"
msgstr "شهادة البكالريوس أو ما يفوقها "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product_template.py:0
msgid "Based on Timesheets"
msgstr "بناءً على الجداول الزمنية "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__allow_billable
msgid "Billable"
msgstr "قابل للفوترة "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__billable_time
msgid "Billable Time"
msgstr "الوقت القابل للفوترة "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__timesheet_invoice_type
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__timesheet_invoice_type
msgid "Billable Type"
msgstr "النوع القابل للفوترة "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Billed"
msgstr "مفوتر "

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_manual
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_manual
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed Manually"
msgstr "تتم الفوترة يدوياً "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed at a Fixed Price"
msgstr "مفوترة بسعر ثابت "

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_fixed
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_fixed
msgid "Billed at a Fixed price"
msgstr "مفوترة بسعر ثابت "

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_milestones
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_milestones
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed on Milestones"
msgstr "الفوترة حسب مؤشرات التقدم "

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_time
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_time
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed on Timesheets"
msgstr "مفوتر في الجداول الزمنية "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Billing"
msgstr "الفوترة "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__billing_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billing Type"
msgstr "نوع الفوترة "

#. module: sale_timesheet
#: model:ir.ui.menu,name:sale_timesheet.menu_timesheet_billing_analysis
msgid "By Billing Type"
msgstr "حسب نوع الفوترة "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__sale_order_id
msgid "Choose the Sales Order to invoice"
msgstr "اختر أمر المبيعات الذي ترغب في فوترته "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__commercial_partner_id
msgid "Commercial Partner"
msgstr "الشريك التجاري "

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Configure your services"
msgstr "قم بتهيئة خدماتك "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__cost
msgid "Cost"
msgstr "التكلفة"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Costs"
msgstr "التكاليف"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.project_project_action_multi_create_invoice
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Create Invoice"
msgstr "إنشاء فاتورة"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_invoice
msgid "Create Invoice from project"
msgstr "إنشاء فاتورة من المشروع"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Create Sales Order from Project"
msgstr "إنشاء أمر مبيعات من المشروع"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Create content that will help our users on a daily basis"
msgstr "اصنع محتوى سيساعد مستخدمينا بشكل يومي "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__currency_id
msgid "Currency"
msgstr "العملة"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__partner_id
msgid "Customer"
msgstr "العميل"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_order_timesheet_product_template
msgid "Customer Care (Prepaid Hours)"
msgstr "خدمة العملاء (الساعات المدفوعة مسبقاً) "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_kanban_inherit_sale_timesheet
msgid "Customer Ratings"
msgstr "تقييمات العميل"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Customer Relationship"
msgstr "علاقة العميل "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Days Ordered,"
msgstr "الأيام المطلوبة، "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Days Remaining)"
msgstr "أيام متبقية) "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Discard"
msgstr "إهمال "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Discover our products."
msgstr "استكشف منتجاتنا. "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "Draft Invoice"
msgstr "مسودة فاتورة"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid ""
"Each employee has a chance to see the impact of his work.\n"
"                    You can make a real contribution to the success of the company.\n"
"                    <br>\n"
"                    Several activities are often organized all over the year, such as weekly\n"
"                    sports sessions, team building events, monthly drink, and much more"
msgstr ""
"لدى كل موظف الفرصة لرؤية البصمة التي يتركها عمله.\n"
"                    بمقدورك أن تساهم بشكل كبير في نجاح الشركة.\n"
"                    <br>\n"
"                    يتم تنظيم العديد من الأنشطة على مدار العام، كالتجمعات\n"
"                    الرياضية الأسبوعية وفعاليات بناء الفِرَق، والمشروبات الشهرية وغير ذلك الكثير "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Eat &amp; Drink"
msgstr "كل واشرب"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_product_elevator_installation_product_template
msgid "Elevator Installation"
msgstr "تركيب المصاعد "

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_hr_employee
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__employee_id
msgid "Employee"
msgstr "الموظف"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__employee_rate
msgid "Employee rate"
msgstr "معدل الموظف "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__date_end_invoice_timesheet
msgid "End Date"
msgstr "تاريخ الانتهاء"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Expand your knowledge of various business industries"
msgstr "وسع آفاق معرفتك بمختلف مجالات الأعمال "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Expected"
msgstr "المتوقع "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Experience in writing online content"
msgstr "خبرة في كتابة المحتوى عبر الإنترنت "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Fixed price services"
msgstr "الخدمات ثابتة السعر "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Fruit, coffee and <br>snacks provided."
msgstr "يتم توفير <br>الفواكه والقهوة والوجبات الخفيفة. "

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_manual_product_template
msgid "Furniture Delivery (Manual)"
msgstr "توصيل الأثاث (يدوي) "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Google Adwords experience"
msgstr "خبرة في Google Adwords"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Great team of smart people, in a friendly and open culture"
msgstr "فريق رائع من الأفراد اللامعين، في بيئة ودودة ولطيفة "

#. module: sale_timesheet
#: model:hr.job,name:sale_timesheet.job_labour
msgid "Handyman"
msgstr "حِرَفي "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__has_multi_sol
msgid "Has Multi Sol"
msgstr "يحتوي على عدة بنود أمر بيع "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Highly creative and autonomous"
msgstr "الإبداع والاستقلالية "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__display_cost
msgid "Hourly Cost"
msgstr "التكلفة بالساعة"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Hours Ordered,"
msgstr "ساعات تم طلبها، "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Hours Remaining)"
msgstr "ساعات متبقية) "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__id
msgid "ID"
msgstr "المُعرف"

#. module: sale_timesheet
#: model:hr.job,name:sale_timesheet.job_interior_designer
msgid "Interior Designer"
msgstr "مصمم داخلي "

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_product_interior_designing_product_template
msgid "Interior Designing"
msgstr "التصميم الداخلي "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__timesheet_invoice_id
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__timesheet_invoice_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.report_timesheet_account_move
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Invoice"
msgstr "الفاتورة"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_res_config_settings__invoice_policy
msgid "Invoice Policy"
msgstr "سياسة الفاتورة "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product_template.py:0
msgid "Invoice based on timesheets (delivered quantity)."
msgstr "قم بالفوترة بناءً على الجداول الزمنية (الكميات التي قد تم توصيلها). "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_analytic_line__timesheet_invoice_id
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__timesheet_invoice_id
msgid "Invoice created from the timesheet"
msgstr "الفاتورة المنشأة من الجداول الزمنية "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Invoiced"
msgstr "مفوتر"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Invoices"
msgstr "فواتير العملاء "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Invoicing"
msgstr "الفوترة"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__is_project_map_empty
msgid "Is Project map empty"
msgstr "خريطة المشروع فارغة "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__is_so_line_edited
msgid "Is Sales Order Item Manually Edited"
msgstr "تم تحرير عنصر أمر البيع يدوياً "

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_move
msgid "Journal Entry"
msgstr "قيد اليومية"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_move_line
msgid "Journal Item"
msgstr "عنصر اليومية"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_timesheet_2_product_template
msgid "Junior Architect (Invoice on Timesheets)"
msgstr "مهندس معماري مبتدئ (الفوترة على الجداول الزمنية) "

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_milestones_product_template
msgid "Kitchen Assembly (Milestones)"
msgstr "تركيب المطبخ (مؤشرات التقدم) "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Lead the entire sales cycle"
msgstr "ترأس دورة المبيعات بأكملها "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__service_type
#: model:ir.model.fields,help:sale_timesheet.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"تعيين الكمية يدوياً في الطلب: يتم احتساب الفاتورة حسب الكمية المُدخلة يدوياً، دون إنشاء حساب تحليلي.\n"
"الجداول الزمنية على العقود: تُحتسب الفاتورة حسب الساعات المتتبعة في الجداول الزمنية ذات الصلة.\n"
"إنشاء مهمة وتتبع الساعات: إنشاء مهمة عند تصديق أمر البيع وتتبع ساعات العمل. "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__margin
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Margin"
msgstr "الهامش"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Master demos of our software"
msgstr "النسخ التجريبية الرئيسية لبرنامجنا "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Materials"
msgstr "المواد"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "طريقة تحديث الكمية التي قد تم توصيلها "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Milestone services"
msgstr "خدمات مؤشرات الأداء "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Must Have"
msgstr "يلزم توافره "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Negotiate and contract"
msgstr "فاوض وتعاقد "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Nice to have"
msgstr "من الجيد توافره "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "No Invoice"
msgstr "لا توجد فاتورة "

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.action_timesheet_from_invoice
msgid "No activities found"
msgstr "لم يتم العثور على أي أنشطة "

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_from_sales_order_item
msgid "No activities found. Let's start a new one!"
msgstr "لم يتم العثور على أي أنشطة. فلنبدأ نشاطاً جديداً! "

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_billing_report
msgid "No data yet!"
msgstr "لا توجد أي بيانات بعد! "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "No dumb managers, no stupid tools to use, no rigid working hours"
msgstr "لا مدراء غير متفهمين، أو أدوات سخيفة، أو ساعات عمل متشددة "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid ""
"No waste of time in enterprise processes, real responsibilities and autonomy"
msgstr ""
"لن يضيع أي وقت عند القيام بالعمليات المؤسسية والمسؤوليات الحقيقية "
"والاستقلالية "

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__non_billable
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__non_billable
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Non-Billable"
msgstr "غير قابلة للفوترة"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_form_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_tree_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_task_view_form_inherit_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheete_analysis_report_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_list_inherited
msgid "Non-billable"
msgstr "غير قابل للفوترة "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__non_billable_time
msgid "Non-billable Time"
msgstr "الوقت غير القابل للفوترة "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Not Billed"
msgstr "غير مفوتر "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__timesheet_revenues
msgid "Number of hours spent multiplied by the unit price per hour/day."
msgstr "عدد الساعات المقضية ضرب سعر الوحدة في الساعة/اليوم. "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__billable_time
msgid "Number of hours/days linked to a SOL."
msgstr "عدد الساعات/الأيام المرتبطة ببند أمر البيع. "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__non_billable_time
msgid "Number of hours/days not linked to a SOL."
msgstr "عدد الساعات/الأيام غير المرتبطة ببند أمر البيع. "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_advance_payment_inv__date_end_invoice_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_advance_payment_inv__date_start_invoice_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_advance_payment_inv_timesheet_view_form
msgid ""
"Only timesheets not yet invoiced (and validated, if applicable) from this "
"period will be invoiced. If the period is not indicated, all timesheets not "
"yet invoiced (and validated, if applicable) will be invoiced without "
"distinction."
msgstr ""
"وحدها الجداول الزمنية التي لم تتم فوترتها بعد (وتصديقها، إذا انطبق الأمر) من"
" هذه الفترة ستتم فوترتها. إذا لم تتم الإشارة إلى الفترة، ستتم فوترة كافة "
"الجداول الزمنية التي لم تتم فوترتها بعد (وتصديقها، إذا انطبق الأمر) دون "
"تمييز. "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Operation not supported"
msgstr "العملية غير مدعومة "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__order_id
msgid "Order Reference"
msgstr "مرجع الطلب "

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__other_costs
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__other_costs
msgid "Other costs"
msgstr "التكاليف الأخرى "

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__other_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__other_revenues
msgid "Other revenues"
msgstr "الإيرادات الأخرى "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Our Product"
msgstr "منتجنا "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Passion for software products"
msgstr "الشغف حول منتجات البرامج "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__service_upsell_threshold
#: model:ir.model.fields,help:sale_timesheet.field_product_template__service_upsell_threshold
msgid ""
"Percentage of time delivered compared to the prepaid amount that must be "
"reached for the upselling opportunity activity to be triggered."
msgstr ""
"نسبة الوقت المقضي مقارنة بالكمية المدفوعة مسبقاً التي يجب الوصول إليها حتى "
"يتم تشغيل نشاط فرصة الارتقاء بالصفقة. "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Perfect written English"
msgstr "مهارات كتابية ممتازة باللغة الإنجليزية "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Perks"
msgstr "المزايا "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Personal Evolution"
msgstr "التطور الشخصي "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Play any sport with colleagues, <br>the bill is covered."
msgstr "مارس أي رياضة مع زملائك، <br>وسوف نقوم بتغطية التكاليف. "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__pricing_type
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__pricing_type
msgid "Pricing"
msgstr "الأسعار "

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_template
msgid "Product"
msgstr "المنتج"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_product
msgid "Product Variant"
msgstr "متغير المنتج "

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_project
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__project_id
msgid "Project"
msgstr "المشروع"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_sale_line_employee_map
msgid "Project Sales line, employee mapping"
msgstr "بند مبيعات المشروع، تخطيط الموظف "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__project_template_id
msgid "Project Template"
msgstr "قالب المشروع"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_update
msgid "Project Update"
msgstr "تحديث المشروع "

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__fixed_rate
msgid "Project rate"
msgstr "معدل المشروع "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_invoice__project_id
msgid "Project to make billable"
msgstr "المشروع لجعله قابلاً للفوترة "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Qualify the customer needs"
msgstr "قم بتأهيل احتياجات العميل "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Quotation"
msgstr "عرض سعر"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Real responsibilities and challenges in a fast evolving company"
msgstr "مسؤوليات وتحديات حقيقية في شركة سريعة التطور "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__remaining_hours_available
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__remaining_hours_available
msgid "Remaining Hours Available"
msgstr "الساعات المتبيقية المتاحة "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Responsibilities"
msgstr "المسؤوليات "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Revenues"
msgstr "الإيرادات "

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_billing_report
msgid ""
"Review your timesheets by billing type and make sure your time is billable."
msgstr ""
"قم بمراجعة جداولك الزمنية حسب نوع الفوترة وتأكد من أن وقتك قابل للفوترة. "

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_product_roofing_product_template
msgid "Roofing"
msgstr "تركيب الأسقف "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_sale_page
msgid "S0001"
msgstr "S0001"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "فاتورة الدفعة المقدمة للمبيعات"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
#: model:ir.model,name:sale_timesheet.model_sale_order
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__sale_order_id
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__order_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_report_search_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Sales Order"
msgstr "أمر البيع"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__so_line
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__sale_line_id
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__so_line
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_timesheet_table_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.report_timesheet_sale_order
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Sales Order Item"
msgstr "عنصر أمر المبيعات"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_order_line
msgid "Sales Order Line"
msgstr "بند أمر المبيعات"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_kanban_inherit_sale_timesheet_so_button
msgid "Sales Orders"
msgstr "أوامر البيع"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__sale_line_employee_ids
msgid ""
"Sales order item that will be selected by default on the timesheets of the corresponding employee. It bypasses the sales order item defined on the project and the task, and can be modified on each timesheet entry if necessary. In other words, it defines the rate at which an employee's time is billed based on their expertise, skills or experience, for instance.\n"
"If you would like to bill the same service at a different rate, you need to create two separate sales order items as each sales order item can only have a single unit price at a time.\n"
"You can also define the hourly company cost of your employees for their timesheets on this project specifically. It will bypass the timesheet cost set on the employee."
msgstr ""
"عنصر أمر البيع الذي سيتم تحديده افتراضياً في جداول بيانات الموظف المعني. يتخطى عنصر أمر البيع المحدد في المشروع والمهمة، ويمكن تعديله في كل قيد للجداول الزمنية، إذا لزم الأمر. مما يعني أنه يحدد السعر الذي تتم فوترة وقت الموظف به، بناءً على المهارات أو الخبرات، على سبيل المثال. \n"
"إذا كنت ترغب في فوترة نفس الخدمة بسعر مختلف، عليك إنشاء عنصرَي أمر بيع منفصلين، حيث أن كل عنصر أمر بيع يمكن أن يكون له سعر وحدة واحد في نفس الوقت. \n"
"يمكنك أيضاً تحديد تكلفة الساعة في الشركة للموظفين مقابل جداولهم الزمنية لهذا المشروع بالتحديد. سيتخطى تكلفة الجداول الزمنية المحددة للموظف. "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_analytic_line__so_line
msgid ""
"Sales order item to which the time spent will be added in order to be "
"invoiced to your customer. Remove the sales order item for the timesheet "
"entry to be non-billable."
msgstr ""
"عنصر أمر البيع الذي ستتم إضافة الوقت المقضي إليه حتى تتم فوترته إلى عميلك. "
"قم بإزالة عنصر أمر البيع لقيد الجداول الزمنية حتى يصبح غير قابل للفوترة. "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr "أمر البيع الذي ترتبط به هذه المهمة. "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Search in Invoice"
msgstr "البحث في الفاتورة"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Search in Sales Order"
msgstr "البحث في أمر البيع "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Sell services and invoice time spent"
msgstr "قم ببيع الخدمات وفوترة الوقت المقضي "

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_timesheet_1_product_template
msgid "Senior Architect (Invoice on Timesheets)"
msgstr "مهندس معماري كبير (الفوترة على الجداول الزمنية) "

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__service_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__service_revenues
msgid "Service Revenues"
msgstr "إيرادات الخدمة "

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.time_product_product_template
msgid "Service on Timesheets"
msgstr "الخدمة في الجداول الزمنية "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__timesheet_product_id
#: model:ir.model.fields,help:sale_timesheet.field_project_task__timesheet_product_id
msgid ""
"Service that will be used by default when invoicing the time spent on a "
"task. It can be modified on each task individually by selecting a specific "
"sales order item."
msgstr ""
"الخدمة التي سيتم اسخدامها افتراضياً عند فوترة الوقت المقضي في مهمة ما. يمكن "
"تعديله في كل مهمة على حدة عن طريق تحديد عنصر أمر بيع محدد. "

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.product_template_action_default_services
#: model:project.project,label_tasks:sale_timesheet.project_support
msgid "Services"
msgstr "الخدمات"

#. module: sale_timesheet
#: model:hr.job,name:sale_timesheet.job_engineer
msgid "Site Manager"
msgstr "مدير ميداني "

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_product_solar_installation_product_template
msgid "Solar Panel Installation"
msgstr "تركيب ألواح الطاقة الشمسية "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Sport Activity"
msgstr "النشاط الرياضي"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__date_start_invoice_timesheet
msgid "Start Date"
msgstr "تاريخ البدء "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__sale_order_state
msgid "Status"
msgstr "الحالة "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Strong analytical skills"
msgstr "مهارات تحليلية قوية "

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_task
msgid "Task"
msgstr "المهمة"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__task_rate
msgid "Task rate"
msgstr "سعر المهمة "

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "تحليل المهام"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Technical Expertise"
msgstr "خبرات تقنية "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product_product.py:0
#: code:addons/sale_timesheet/models/product_template.py:0
msgid ""
"The %s product is required by the Timesheets app and cannot be archived nor "
"deleted."
msgstr "يتطلب تطبيق الجداول الزمنية وجود المنتج %s ولا يمكن فوترته أو حذفه. "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product_template.py:0
msgid ""
"The %s product is required by the Timesheets app and cannot be linked to a "
"company."
msgstr ""
"يتطلب تطبيق الجداول الزمنية وجود المنتج %s ولا يمكن أن يكون مرتبطاً بشركة. "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_invoice.py:0
msgid "The selected Sales Order should contain something to invoice."
msgstr "ينبغي أن يحتوي أمر المبيعات المحدد على بنود لفوترتها."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__pricing_type
#: model:ir.model.fields,help:sale_timesheet.field_project_task__pricing_type
msgid ""
"The task rate is perfect if you would like to bill different services to "
"different customers at different rates. The fixed rate is perfect if you "
"bill a service at a fixed rate per hour or day worked regardless of the "
"employee who performed it. The employee rate is preferable if your employees"
" deliver the same service at a different rate. For instance, junior and "
"senior consultants would deliver the same service (= consultancy), but at a "
"different rate because of their level of seniority."
msgstr ""
"خاصية سعر المهمة مثالية عندما ترغب في فوترة عدة خدمات لعدة عملاء بأسعار "
"مختلفة. خاصية السعر الثابت مثالية إذا كنت تقوم بفوترة الخدمة بسعر ثابت حسب "
"الساعات أو الأيام المقضية بغض النظر عن الموظف الذي قام بالخدمة. يفضل استخدام"
" سعر الموظف إذا كان موظفوك يؤدون نفس الخدمة بأسعار مختلفة. على سبيل المثال، "
"الاستشاريين المبتدئين وذوي الخبرة يقدمون ذات الخدمة (=استشارة) بأسعار مختلفة"
" بسبب مدى أقدميتهم. "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_sale_line_employee_map__cost
msgid ""
"This cost overrides the employee's default employee hourly wage in "
"employee's HR Settings"
msgstr ""
"تتخطى هذه التكلفة تكلفة الأجر بالساعة الافتراضية للموظف في إعدادات الموارد "
"البشرية للموظف "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_upsell_threshold
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_upsell_threshold
msgid "Threshold"
msgstr "الحد الأدنى "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Time Billing"
msgstr "فوترة الوقت "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__remaining_hours_so
#: model:ir.model.fields,field_description:sale_timesheet.field_report_project_task_user__remaining_hours_so
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__remaining_hours
msgid "Time Remaining on SO"
msgstr "الوقت المتبقي في أمر البيع "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_graph_employee_per_date
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
msgid "Time Spent"
msgstr "الوقت المستغرق"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Time-based services"
msgstr "الخدمات المبنية على الوقت "

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_plan
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_plan_pivot
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_graph_employee_per_date
msgid "Timesheet"
msgstr "الجدول الزمني "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Timesheet Activities"
msgstr "أنشطة الجداول الزمنية "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_graph_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_pivot_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_graph_employee_per_date
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
msgid "Timesheet Costs"
msgstr "تكاليف الجداول الزمنية "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__timesheet_product_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__timesheet_product_id
msgid "Timesheet Product"
msgstr "منتج الجداول الزمنية "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_report_search_sale_timesheet
msgid "Timesheet Report"
msgstr "تقرير الجداول الزمنية "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__timesheet_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__timesheet_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__timesheet_revenues
msgid "Timesheet Revenues"
msgstr "إيرادات الجداول الزمنية "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_total_duration
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_total_duration
msgid "Timesheet Total Duration"
msgstr "إجمالي مدة الجداول الزمنية "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/account_move.py:0
#: model:ir.actions.act_window,name:sale_timesheet.action_timesheet_from_invoice
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_sales_order
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_sales_order_item
#: model:ir.actions.report,name:sale_timesheet.timesheet_report_account_move
#: model:ir.actions.report,name:sale_timesheet.timesheet_report_sale_order
#: model:ir.model.fields.selection,name:sale_timesheet.selection__sale_order_line__qty_delivered_method__timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_graph_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
msgid "Timesheets"
msgstr "الجداول الزمنية "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Billed Manually)"
msgstr "الجداول الزمنية (تتم فوترتها يدوياً) "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Billed on Milestones)"
msgstr "الجداول الزمنية (تتم فوترتها حسب مؤشرات التقدم) "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Billed on Timesheets)"
msgstr "الجداول الزمنية (تتم فوترتها حسب الجداول الزمنية) "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Fixed Price)"
msgstr "الجداول الزمنية (سعر ثابت) "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Non-Billable)"
msgstr "الجداول الزمنية (غير قابلة للفوترة) "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_pivot_invoice_type
msgid "Timesheets Analysis"
msgstr "تحليل الجداول الزمنية "

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_timesheets_analysis_report
msgid "Timesheets Analysis Report"
msgstr "تقرير تحليل الجداول الزمنية "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_advance_payment_inv_timesheet_view_form
msgid "Timesheets Period"
msgstr "فترة الجداول الزمنية "

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_billing_report
msgid "Timesheets by Billing Type"
msgstr "الجداول الزمنية حسب نوع الفوترة "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_sale_page
msgid "Timesheets for the"
msgstr "الجداول الزمنية لـ"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets of %s"
msgstr "الجداول الزمنية لـ %s "

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_type__timesheet
msgid "Timesheets on project (one fare per SO/Project)"
msgstr "الجداول الزمنية للمشروع (أجرة واحدة لكل أمر بيع/مشروع) "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets revenues"
msgstr "إيرادات الجداول الزمنية "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__margin
msgid "Timesheets revenues minus the costs"
msgstr "إيرادات الجداول الزمنية ناقص التكاليف "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Timesheets taken into account when invoicing your time"
msgstr "الجداول الزمنية التي يتم اعتبارها عند فوترة وقتك "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_res_config_settings__invoice_policy
msgid "Timesheets taken when invoicing time spent"
msgstr "الجداول الزمنية التي يتم اعتبارها عند فوترة الوقت المقضي "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Timesheets without a sales order item are reported as"
msgstr ""
"يتم الإبلاغ عن الجداول الزمنية التي لا تحتوي على عنصر أمر مبيعات على أنها "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "To Bill"
msgstr "بانتظار الفوترة "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "To Invoice"
msgstr "بانتظار الفوترة"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_list_inherited
msgid "Total"
msgstr "الإجمالي"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_invoice__amount_to_invoice
msgid ""
"Total amount to invoice on the sales order, including all items (services, "
"storables, expenses, ...)"
msgstr ""
"إجمالي المبلغ المراد فوترته في أمر المبيعات، شامل كافة العناصر (الخدمات، "
"والمنتجات المخزنة، والنفقات،...) "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_bank_statement_line__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_account_move__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_sale_order__timesheet_total_duration
msgid ""
"Total recorded duration, expressed in the encoding UoM, and rounded to the "
"unit"
msgstr ""
"إجمالي المدة المسجلة، معبر عنها بوحدة قياس الترميز، ومقربة إلى الوحدة "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_type
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_type
msgid "Track Service"
msgstr "تتبع الخدمة"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.action_timesheet_from_invoice
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_from_sales_order_item
msgid ""
"Track your working hours by projects every day and invoice this time to your"
" customers."
msgstr "تتبع ساعات عملك في كل مشروع كل يوم وقم بفوترة هذا الوقت لعملائك. "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Trainings"
msgstr "التدريبات "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__price_unit
msgid "Unit Price"
msgstr "سعر الوحدة"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Valid work permit for Belgium"
msgstr "تصريح عمل صالح لبلجيكا "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Value does not exist in the pricing type"
msgstr "القيمة غير موجودة في نوع التسعير "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_invoice_page_inherit
msgid "View Timesheet"
msgstr "عرض الجداول الزمنية "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_invoice_page_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_order_portal_content_inherit
msgid "View Timesheets"
msgstr "عرض الجداول الزمنية "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_product_timesheet_form
msgid "Warn the salesperson for an upsell when work done exceeds"
msgstr "قم بتحذير مندوب المبيعات للارتقاء بالصفقة عندما يتخطى العمل المنجز "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "What We Offer"
msgstr "ما نقدمه لك "

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "What's great in the job?"
msgstr "ما الرائع بشأن الوظيفة؟ "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_invoice.py:0
msgid "You can only apply this action from a project."
msgstr "لا يمكن تطبيق هذا الإجراء إلا من مشروع."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid ""
"You cannot link a billable project to a sales order item that comes from an "
"expense or a vendor bill."
msgstr ""
"لا يمكنك ربط مشروع قابل للفوترة بعنصر أمر بيع  يأتي من نفقة أو فاتورة مورّد."
" "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid ""
"You cannot link a billable project to a sales order item that is not a "
"service."
msgstr "لا يمكنك ربط مشروع قابل للفوترة بعنصر أمر بيع  ليس خدمة. "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
msgid "You cannot modify timesheets that are already invoiced."
msgstr "لا يمكنك تعديل جداول زمنية قد تمت فوترتها بالفعل. "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
msgid "You cannot remove a timesheet that has already been invoiced."
msgstr "لا يمكنك إزالة جداول زمنية قد تمت فوترتها بالفعل. "

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__billing_type__manually
msgid "billed manually"
msgstr "تتم الفوترة يدوياً "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/sale_order_line.py:0
msgid "days remaining"
msgstr "أيام متبقية "

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__billing_type__not_billable
msgid "not billable"
msgstr "غير قابل للفوترة "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_product_timesheet_form
msgid "of hours sold."
msgstr "من الساعات المباعة. "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/sale_order_line.py:0
msgid "remaining"
msgstr "متبقي "
