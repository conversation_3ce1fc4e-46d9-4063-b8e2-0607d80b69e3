<?xml version="1.0"?>
<odoo>

    <!-- Lab Test type -->
    <record id="hms_test_view" model="ir.ui.view">
        <field name="name">Laboratory Test</field>
        <field name="model">acs.lab.test</field>
        <field name="arch" type="xml">
            <form string="Lab Tests">
                <sheet>
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" invisible="active"/>
                    <group>
                        <group>
                            <field name="name" required="1"/>
                            <field name="product_id" domain="[('type','=','service'), ('hospital_product_type', 'in', ['pathology', 'radiology'])]" context="{'default_detailed_type': 'service', 'default_hospital_product_type': 'pathology'}"/>
                            <field name="list_price"/>
                            <field name="sample_type_id" invisible="test_type == 'radiology'"/>

                        </group>
                        <group>
                            <field name="code"/>
                            <field name="acs_tat"/>
                            <field name="result_value_type" invisible="test_type == 'radiology'"/>
                        </group>
                    </group>

                    <notebook invisible="test_type == 'radiology'">
                        <page name="diagnosis" string="Diagnosis">
                            <field name="critearea_ids" colspan="4" nolabel="1" widget="section_and_note_one2many" context="{'default_result_value_type': result_value_type}">
                                <tree editable="bottom" string="Test Cases">
                                    <control>
                                        <create string="Add a line"/>
                                        <create string="Add a section" context="{'default_display_type': 'line_section'}"/>
                                    </control>
                                    <field name="sequence" widget="handle"/>
                                    <field name="display_type" invisible="1"/>
                                    <field name="name" required="1" widget="section_and_note"/>
                                    <field name="normal_range_male" required="not display_type"/>
                                    <field name="normal_range_female" required="not display_type"/>
                                    <field name="lab_uom_id"/>
                                    <field name="result_value_type"/>
                                    <field name="remark"/>
                                </tree>
                            </field>
                        </page>
                        <page name="consumable_lines" string="Consumed Material">
                            <field name="consumable_line_ids" nolabel="1" colspan="4">
                                <tree string="Line" editable="top">
                                    <field name="product_id" expand="1" required="1"/>
                                    <field name="product_uom" required="1" groups="uom.group_uom"/>
                                    <field name="product_uom_category_id" invisible="1"/>
                                    <field name="qty" required="1"/>
                                    <field name="tracking" invisible="1"/>
                                    <field name="move_id" invisible="1"/>
                                </tree>
                            </field>
                        </page>
                        <page name="other_info" string="Other Information">
                            <group>
                                <group>
                                    <field name="test_type"/>
                                    <field name="subsequent_test_ids" widget="many2many_tags" domain="[('test_type','=',test_type)]"/>
                                </group>
                                <group>
                                    <field name="acs_use_other_test_sample" invisible="test_type == 'radiology'"/>
                                    <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                                    <field name="active" invisible="1"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                    <field name="description" placeholder="Description"/>
                </sheet>
            </form>
        </field>
    </record>

    <record id="hms_test_tree" model="ir.ui.view">
        <field name="name">Lab Tests</field>
        <field name="model">acs.lab.test</field>
        <field name="arch" type="xml">
            <tree string="Lab Test">
                <field name="name"/>
                <field name="code"/>
                <field name="list_price"/>
                <field name="active" invisible="1"/>
            </tree>
        </field>
    </record>

    <record id="hms_action_form_test" model="ir.actions.act_window">
        <field name="name">Lab Tests</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">acs.lab.test</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('test_type','=','pathology')]</field>
        <field name="context">{'default_test_type': 'pathology'}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No Record Found
            </p>
            <p>
                Click to add new Lab Test.
            </p>
        </field>
    </record>

</odoo>