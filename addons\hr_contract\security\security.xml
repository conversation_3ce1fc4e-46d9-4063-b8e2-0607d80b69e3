<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record model="ir.module.category" id="base.module_category_human_resources_contracts">
            <field name="description">Enable the user to see and manage the contracts from Employee application.</field>
            <field name="sequence">10</field>
        </record>

        <record id="hr_contract.group_hr_contract_employee_manager" model="res.groups">
            <field name="name">Employee Manager</field>
            <field name="category_id" ref="base.module_category_human_resources_contracts"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        </record>

        <record id="hr_contract.group_hr_contract_manager" model="res.groups">
            <field name="name">Administrator</field>
            <field name="category_id" ref="base.module_category_human_resources_contracts"/>
            <field name="implied_ids" eval="[(4, ref('hr_contract.group_hr_contract_employee_manager')), (4, ref('hr.group_hr_user'))]"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>

        <record id="base.default_user" model="res.users">
            <field name="groups_id" eval="[(4,ref('hr_contract.group_hr_contract_manager'))]"/>
        </record>

        <record id="ir_rule_hr_contract_history_multi_company" model="ir.rule">
            <field name="name">HR Contract History: Multi Company</field>
            <field name="model_id" ref="model_hr_contract_history"/>
            <field name="domain_force">['|', ('employee_id.company_id', '=', False), ('employee_id.company_id', 'in', company_ids)]</field>
        </record>

        <record id="ir_rule_hr_contract_employee_manager" model="ir.rule">
            <field name="name">HR Contract: Employee Manager</field>
            <field name="model_id" ref="model_hr_contract"/>
            <field name="groups" eval="[(4, ref('hr_contract.group_hr_contract_employee_manager'))]"/>
            <field name="domain_force">['|', ('employee_id.parent_id.user_id', '=', user.id), ('employee_id.user_id', '=', user.id)]</field>
        </record>

        <record id="ir_rule_hr_contract_manager" model="ir.rule">
            <field name="name">HR Contract: Contract Manager</field>
            <field name="model_id" ref="model_hr_contract"/>
            <field name="groups" eval="[(4, ref('hr_contract.group_hr_contract_manager'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
        </record>

        <record id="ir_rule_hr_contract_multi_company" model="ir.rule">
            <field name="name">HR Contract: Multi Company</field>
            <field name="model_id" ref="model_hr_contract"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
        </record>

        <record id="ir_rule_hr_payroll_structure_type_multi_company" model="ir.rule">
            <field name="name">HR Payroll Structure Type: Multi Company</field>
            <field name="model_id" ref="model_hr_payroll_structure_type"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|', ('country_id', '=', False), ('country_id', 'in', user.env.companies.mapped('country_id').ids)]</field>
        </record>

    </data>
</odoo>
