<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="point_of_sale.OrderWidget">
        <t t-if="props.lines?.length">
            <div class="d-flex flex-column flex-grow-1 rounded-3 bg-white overflow-hidden">
                <div t-ref="scrollable" t-att-style="props.style" t-att-class="props.class" class="order-container d-flex flex-column flex-grow-1 overflow-y-auto text-start">
                    <t t-foreach="props.lines" t-as="line" t-key="line_index">
                        <t t-if="props.slots?.default" t-slot="default" line="line"/>
                        <Orderline t-else="" line="line" />
                    </t>
                    <div class="mt-1 bg-opacity-75" t-attf-class="{{ props.screenName == 'ReceiptScreen' ? 'p-0' : 'p-2 border-bottom border-top border-opacity-75'}}" t-if="props.generalNote">
                        <b class="fw-bolder">General Note</b>
                        <t t-foreach="props.generalNote.split('\n')" t-as="subNote" t-key="subNote_index">
                            <br/>• <t t-esc="subNote"/>
                        </t>
                    </div>
                </div>
                <t t-set="taxTotals" t-value="props.taxTotals"/>
                <div t-if="taxTotals" class="order-summary d-flex flex-column gap-1 px-3 py-2 border-top fw-bolder lh-sm">
                    <div t-if="taxTotals.has_tax_groups"
                         class="tax-info subentry d-flex justify-content-between w-100 fs-6 text-muted"
                    >
                        Taxes
                        <div id="order-widget-taxes">
                            <span t-esc="formatMonetary(taxTotals.order_sign * taxTotals.tax_amount_currency, {currencyId: taxTotals.currency_id})"
                                  class="tax"/>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between w-100 fs-2">
                        Total
                        <span t-esc="formatMonetary(taxTotals.order_sign * taxTotals.order_total, {currencyId: taxTotals.currency_id})"
                              class="total"/>
                    </div>
                </div>
            </div>
            <t t-slot="details"/>
        </t>
        <t t-else="">
            <div t-if="!props.slots?.emptyCart" class="d-flex flex-column align-items-center justify-content-center flex-grow-1 bg-100 rounded-3 gap-3">
                <CenteredIcon icon="'fa-shopping-cart fa-4x'" text="emptyCartText()" class="'h-unset'"/>
                <t t-slot="details"/>
            </div>
            <t t-else="" t-slot="emptyCart" />
        </t>
    </t>
</templates>
