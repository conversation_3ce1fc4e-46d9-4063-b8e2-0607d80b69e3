.o_hr_department_kanban .o_kanban_renderer {
    --KanbanRecord-width: 450px;
    --KanbanRecord-width-small: 350px;
}
.o_hr_employee_profile_form_view .o_form_renderer{
    .o_form_sheet_bg {
        max-width: unset;
    }
}

.o_icon_employee_absent {
    color: $warning;
}

.o_hr_employee_form_view .o_form_renderer {
    .o_form_sheet_bg {
        max-width: unset;
    }
    .o_employee_chat_btn {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 5px;
        @include media-breakpoint-down(md) {
            margin-left: 0!important;
        }
    }
    .o_employee_avatar {
        position: relative;
        width: fit-content;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        .o_employee_availability {
            position: absolute;
            top: -5px;
            right: -5px;
            padding-bottom: 1px;
            border-radius: 50%;
            background-color: $o-view-background-color;
            height: 1rem;
            width: 1rem;
            * {
                margin-bottom: -1px;
                height: 1rem;
                width: 1rem;
            }
        }
    }
    .oe_title {
        flex: 1;
    }
}

.o_hr_employee_kanban .o_kanban_renderer {
    .o_employee_availability {
        margin: unset !important;
    }
    .o_kanban_record_bottom {
        margin: 0 var(--KanbanRecord-padding-h);
        .fa.fa-comments {
            margin-bottom: 5px;
        }
    }
}

.hr_tags {
    margin-right: 20%;
}

.o_hr_narrow_field {
    width: 8rem!important;
    max-width: 8rem!important;
    * {
        max-width: 100%;
    }
}

@for $size from 1 through 15 {
    .o_hr_narrow_field-#{$size} {
        width: #{$size}rem!important;
        max-width: #{$size}rem!important;
    }
}
