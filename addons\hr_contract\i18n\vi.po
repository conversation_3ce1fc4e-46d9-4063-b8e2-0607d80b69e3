# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_contract
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__contract_count
msgid "# Contracts"
msgstr "# Hợp đồng"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_resource_calendar__contracts_count
msgid "# Contracts using it"
msgstr "# Hợp đồng đang sử dụng"

#. module: hr_contract
#. odoo-python
#: code:addons/hr_contract/report/hr_contract_history.py:0
msgid "%s's Contracts History"
msgstr "Lịch sử hợp đồng của %s"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "/ month"
msgstr "/tháng"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form3
msgid ""
"<span class=\"o_stat_text text-danger\" invisible=\"not contract_warning\" title=\"In Contract Since\">\n"
"                                    In Contract Since\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text text-danger\" invisible=\"not contract_warning\" title=\"Ký hợp đồng từ\">\n"
"                                    Ký hợp đồng từ\n"
"                                </span>"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form3
msgid ""
"<span class=\"o_stat_text text-success\" invisible=\"contract_warning\" "
"title=\"In Contract Since\"> In Contract Since</span>"
msgstr ""
"<span class=\"o_stat_text text-success\" invisible=\"contract_warning\" "
"title=\"Ký hợp đồng từ\"> Ký hợp đồng từ</span>"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form3
msgid ""
"<span invisible=\"contracts_count != 1\" class=\"o_stat_text text-danger\">\n"
"                                    Contract\n"
"                                </span>\n"
"                                <span invisible=\"contracts_count == 1\" class=\"o_stat_text text-danger\">\n"
"                                    Contracts\n"
"                                </span>"
msgstr ""
"<span invisible=\"contracts_count != 1\" class=\"o_stat_text text-danger\">\n"
"                                    Hợp đồng\n"
"                                </span>\n"
"                                <span invisible=\"contracts_count == 1\" class=\"o_stat_text text-danger\">\n"
"                                    Hợp đồng\n"
"                                </span>"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid ""
"<span invisible=\"contracts_count == 1\" class=\"o_stat_text\">\n"
"                                        Contracts\n"
"                                    </span>\n"
"                                    <span invisible=\"contracts_count &gt; 1\" class=\"o_stat_text\">\n"
"                                        Contract\n"
"                                    </span>"
msgstr ""
"<span invisible=\"contracts_count == 1\" class=\"o_stat_text\">\n"
"                                        Hợp đồng\n"
"                                    </span>\n"
"                                    <span invisible=\"contracts_count &gt; 1\" class=\"o_stat_text\">\n"
"                                        Hợp đồng\n"
"                                    </span>"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.res_config_settings_view_form
msgid "<span>Days</span>"
msgstr "<span>Ngày</span>"

#. module: hr_contract
#. odoo-python
#: code:addons/hr_contract/models/hr_contract.py:0
msgid ""
"According to Employee's Working Permit Expiration Date, this contract has "
"been put in red on the %s. Please advise and correct."
msgstr ""
"Theo Ngày hết hạn Giấy phép lao động của nhân viên, hợp đồng này đã được "
"đánh dấu màu đỏ trên %s. Xin vui lòng tham khảo và chỉnh sửa."

#. module: hr_contract
#. odoo-python
#: code:addons/hr_contract/models/hr_contract.py:0
msgid ""
"According to the contract's end date, this contract has been put in red on "
"the %s. Please advise and correct."
msgstr ""
"Theo ngày kết thúc hợp đồng, hợp đồng này đã được đánh dấu màu đỏ trên %s. "
"Xin vui lòng tham khảo và chỉnh sửa."

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_needaction
msgid "Action Needed"
msgstr "Tác vụ cần thiết"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__active
msgid "Active"
msgstr "Đang hoạt động"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__active_employee
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__active_employee
msgid "Active Employee"
msgstr "Nhân viên đang hoạt động"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Active Employees"
msgstr "Nhân viên đang hoạt động"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_ids
msgid "Activities"
msgstr "Hoạt động"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Hoạt động ngoại lệ"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_state
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__activity_state
msgid "Activity State"
msgstr "Trạng thái hoạt động"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_type_icon
msgid "Activity Type Icon"
msgstr "Biểu tượng loại hoạt động"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_mail_activity_schedule
msgid "Activity schedule plan Wizard"
msgstr "Tính năng kế hoạch hoạt động"

#. module: hr_contract
#: model:res.groups,name:hr_contract.group_hr_contract_manager
msgid "Administrator"
msgstr "Quản trị viên"

#. module: hr_contract
#. odoo-python
#: code:addons/hr_contract/models/hr_contract.py:0
msgid ""
"An employee can only have one contract at the same time. (Excluding Draft and Cancelled contracts).\n"
"\n"
"Employee: %(employee_name)s"
msgstr ""
"Một nhân viên chỉ có thể có một hợp đồng cùng lúc. (Không tính hợp đồng nháp và hợp đồng đã hủy).\n"
"\n"
"Nhân viên: %(employee_name)s"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Archived"
msgstr "Đã lưu trữ"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_attachment_count
msgid "Attachment Count"
msgstr "Số lượng tệp đính kèm"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_res_users__bank_account_id
msgid "Bank Account"
msgstr "Tài khoản ngân hàng"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_employee_base
msgid "Basic Employee"
msgstr "Người dùng cơ bản"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__calendar_mismatch
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__calendar_mismatch
msgid "Calendar Mismatch"
msgstr "Lịch không khớp"

#. module: hr_contract
#. odoo-javascript
#: code:addons/hr_contract/static/src/widgets/tooltip_warning_widget.js:0
msgid ""
"Calendar Mismatch: The employee's calendar does not match this contract's "
"calendar. This could lead to unexpected behaviors."
msgstr ""
"Lịch không khớp: Lịch của nhân viên không khớp với lịch của hợp đồng này. "
"Điều này có thể dẫn đến những hành vi không mong muốn."

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__state__cancel
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__state__cancel
msgid "Cancelled"
msgstr "Đã hủy"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_res_company
msgid "Companies"
msgstr "Công ty"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__company_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__company_id
msgid "Company"
msgstr "Công ty"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__vehicle
#: model:ir.model.fields,field_description:hr_contract.field_res_users__vehicle
msgid "Company Vehicle"
msgstr "Xe công ty"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__company_country_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__company_country_id
msgid "Company country"
msgstr "Quốc gia công ty"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_res_config_settings
msgid "Config Settings"
msgstr "Cài đặt cấu hình"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__contract_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_departure_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract.res_config_settings_view_form
msgid "Contract"
msgstr "Hợp đồng"

#. module: hr_contract
#. odoo-python
#: code:addons/hr_contract/models/hr_contract.py:0
msgid ""
"Contract %(contract)s: start date (%(start)s) must be earlier than contract "
"end date (%(end)s)."
msgstr ""
"Hợp đồng %(contract)s: ngày bắt đầu (%(start)s) phải sớm hơn ngày hợp đồng "
"kết thúc (%(end)s)."

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__contracts_count
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__contracts_count
msgid "Contract Count"
msgstr "Số lượng hợp đồng"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_departure_wizard_view_form
msgid "Contract End Date"
msgstr "Ngày kết thúc hợp đồng"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.res_config_settings_view_form
msgid "Contract Expiration Notice Period"
msgstr "Giai đoạn thông báo hết hạn hợp đồng"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_res_company__contract_expiration_notice_period
#: model:ir.model.fields,field_description:hr_contract.field_res_config_settings__contract_expiration_notice_period
msgid "Contract Expiry Notice Period"
msgstr "Giai đoạn thông báo hết hạn hợp đồng"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Contract History"
msgstr "Lịch sử hợp đồng"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__name
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Contract Name"
msgstr "Tên hợp đồng"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__name
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Contract Reference"
msgstr "Mã hợp đồng"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Contract Start Date"
msgstr "Ngày bắt đầu hợp đồng"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__contract_type_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__contract_type_id
msgid "Contract Type"
msgstr "Kiểu Hợp đồng"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__contract_wage
msgid "Contract Wage"
msgstr "Mức lương hợp đồng"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__contract_warning
msgid "Contract Warning"
msgstr "Lịch không khớp"

#. module: hr_contract
#: model:mail.message.subtype,description:hr_contract.mt_contract_pending
#: model:mail.message.subtype,description:hr_contract.mt_department_contract_pending
msgid "Contract about to expire"
msgstr "Hợp đồng sắp hết hạn"

#. module: hr_contract
#: model:mail.message.subtype,description:hr_contract.mt_contract_close
msgid "Contract expired"
msgstr "Hợp động Hết hạn"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_contract_history
msgid "Contract history"
msgstr "Lịch sử hợp đồng"

#. module: hr_contract
#: model:mail.message.subtype,name:hr_contract.mt_department_contract_pending
msgid "Contract to Renew"
msgstr "Hợp đồng chờ gia hạn"

#. module: hr_contract
#: model:ir.actions.act_window,name:hr_contract.action_hr_contract
#: model:ir.actions.act_window,name:hr_contract.hr_contract_history_view_form_action
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__contract_ids
#: model:ir.ui.menu,name:hr_contract.hr_menu_contract
#: model:ir.ui.menu,name:hr_contract.menu_hr_employee_contracts
#: model:ir.ui.menu,name:hr_contract.menu_human_resources_configuration_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_list
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_activity
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_tree
#: model_terms:ir.ui.view,arch_db:hr_contract.resource_calendar_view_form
msgid "Contracts"
msgstr "Hợp đồng"

#. module: hr_contract
#: model:ir.actions.act_window,name:hr_contract.hr_contract_history_to_review_view_list_action
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Contracts to Review"
msgstr "Hợp đồng cần xem lại"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Contracts to review"
msgstr "Hợp đồng cần xem lại"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__under_contract_state
msgid "Contractual Status"
msgstr "Trạng thái hợp đồng"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__country_id
msgid "Country"
msgstr "Quốc gia"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__country_code
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__country_code
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__country_code
msgid "Country Code"
msgstr "Mã Quốc gia"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Create"
msgstr "Tạo"

#. module: hr_contract
#: model_terms:ir.actions.act_window,help:hr_contract.action_hr_contract
msgid "Create a new contract"
msgstr "Tạo hợp đồng mới"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__create_uid
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__create_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__currency_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__currency_id
msgid "Currency"
msgstr "Tiền tệ"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__contract_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Current Contract"
msgstr "Hợp đồng Hiện hành"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Current Contracts"
msgstr "Hợp đồng hiện hành"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_employee__contract_id
msgid "Current contract of the employee"
msgstr "Hợp đồng hiện tại của nhân viên"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Currently Under Contract"
msgstr "Hiện đang có hợp đồng"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__default_resource_calendar_id
msgid "Default Working Hours"
msgstr "Giờ làm việc mặc định"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_kanban
msgid "Delete"
msgstr "Xoá"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__department_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__department_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Department"
msgstr "Phòng ban"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Công cụ hỗ trợ quá trình nghỉ việc"

#. module: hr_contract
#. odoo-python
#: code:addons/hr_contract/wizard/hr_departure_wizard.py:0
msgid ""
"Departure date can't be earlier than the start date of current contract."
msgstr "Ngày nghỉ việc không thể sớm hơn ngày bắt đầu hợp đồng hiện tại."

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Details"
msgstr "Chi tiết"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__display_name
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__display_name
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_kanban
msgid "Edit Contract"
msgstr "Sửa Hợp đồng"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_employee
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__employee_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__employee_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Employee"
msgstr "Nhân viên"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_contract
msgid "Employee Contract"
msgstr "Hợp đồng lao động"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__contract_ids
msgid "Employee Contracts"
msgstr "Hợp đồng Lao động"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Employee Information"
msgstr "Thông tin nhân viên"

#. module: hr_contract
#: model:res.groups,name:hr_contract.group_hr_contract_employee_manager
msgid "Employee Manager"
msgstr "Quản lý của nhân viên"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_res_users__bank_account_id
msgid "Employee bank account to pay salaries"
msgstr "Tài khoản ngân hàng của nhân viên để trả lương"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__wage
#: model:ir.model.fields,help:hr_contract.field_hr_contract_history__wage
msgid "Employee's monthly gross wage."
msgstr "Lương gộp hàng tháng của nhân viên."

#. module: hr_contract
#: model:ir.actions.act_window,name:hr_contract.hr_contract_history_view_list_action
msgid "Employees"
msgstr "Nhân viên"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__date_end
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__date_end
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "End Date"
msgstr "Ngày kết thúc"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__date_end
msgid "End date of the contract (if it's a fixed-term contract)."
msgstr "Ngày kết thúc hợp đồng (nếu là một hợp đồng có thời hạn cố định)."

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__trial_date_end
msgid "End date of the trial period (if there is one)."
msgstr "Ngày kết thúc thời hạn thử việc(nếu có)."

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__trial_date_end
msgid "End of Trial Period"
msgstr "Kết thúc Thử việc"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__state__close
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__state__close
#: model:mail.message.subtype,name:hr_contract.mt_contract_close
msgid "Expired"
msgstr "Đã hết hạn"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__first_contract_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__first_contract_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee_public__first_contract_date
msgid "First Contract Date"
msgstr "Ngày hợp đồng đầu tiên"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_follower_ids
msgid "Followers"
msgstr "Người theo dõi"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_partner_ids
msgid "Followers (Partners)"
msgstr "Người theo dõi (Đối tác)"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font biểu tượng, ví dụ: fa-tasks"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_kanban
msgid "From"
msgstr "Từ"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid "Fully Flexible"
msgstr "Hoàn toàn linh hoạt"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Future Activities"
msgstr "Hoạt động trong tương lai"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Group By"
msgstr "Nhóm theo"

#. module: hr_contract
#: model:ir.actions.server,name:hr_contract.ir_cron_data_contract_update_state_ir_actions_server
msgid "HR Contract: update state"
msgstr "Hợp đồng nhân sự: cập nhật trạng thái"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__hr_responsible_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__hr_responsible_id
msgid "HR Responsible"
msgstr "Chịu trách nhiệm"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__has_message
msgid "Has Message"
msgstr "Có tin nhắn"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__date_hired
msgid "Hire Date"
msgstr "Ngày thuê"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__id
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__id
msgid "ID"
msgstr "ID"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_exception_icon
msgid "Icon"
msgstr "Biểu tượng"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Biểu tượng cho thấy một hoạt động ngoại lệ."

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Nếu chọn, bạn cần chú ý tới các tin nhắn mới."

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_has_error
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Nếu chọn, một số tin nhắn sẽ có lỗi gửi."

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__active_employee
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Nếu trường hoạt động được đặt là Sai, bạn sẽ được phép ẩn bản ghi tài nguyên"
" mà không cần xóa bỏ. "

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__is_under_contract
msgid "Is Currently Under Contract"
msgstr "Là đang có hợp đồng"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_is_follower
msgid "Is Follower"
msgstr "Là người theo dõi"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__job_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__job_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Job Position"
msgstr "Vị trí công việc"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__kanban_state
msgid "Kanban State"
msgstr "Trạng thái kanban"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__write_uid
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__write_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Late Activities"
msgstr "Hoạt động chậm trễ"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__legal_name
msgid "Legal Name"
msgstr "Tên pháp lý"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_ir_ui_menu
msgid "Menu"
msgstr "Menu"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_has_error
msgid "Message Delivery error"
msgstr "Lỗi gửi tin nhắn"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_ids
msgid "Messages"
msgstr "Tin nhắn"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Monthly Wage"
msgstr "Lương tháng"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Hạn chót hoạt động của tôi"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__state__draft
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__state__draft
msgid "New"
msgstr "Mới"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Sự kiện trên lịch cho hoạt động tiếp theo"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Hạn chót cho hoạt động tiếp theo"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_summary
msgid "Next Activity Summary"
msgstr "Tóm tắt hoạt động tiếp theo"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_type_id
msgid "Next Activity Type"
msgstr "Loại hoạt động tiếp theo"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "No Contracts"
msgstr "Không hợp đồng"

#. module: hr_contract
#: model_terms:ir.actions.act_window,help:hr_contract.hr_contract_history_view_list_action
msgid "No data to display"
msgstr "Không có dữ liệu để hiển thị"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__under_contract_state__blocked
msgid "Not Under Contract"
msgstr "Không có hợp đồng"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__notes
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Notes"
msgstr "Ghi chú"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_needaction_counter
msgid "Number of Actions"
msgstr "Số lượng tác vụ"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.res_config_settings_view_form
msgid ""
"Number of days prior to the contract end date that a contract expiration "
"warning is triggered."
msgstr ""
"Số ngày trước ngày kết thúc hợp đồng mà cảnh báo hết hạn hợp đồng được kích "
"hoạt."

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.res_config_settings_view_form
msgid ""
"Number of days prior to the work permit expiration date that a warning is "
"triggered."
msgstr ""
"Số ngày trước ngày hết hạn giấy phép lao động mà cảnh báo được kích hoạt."

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_has_error_counter
msgid "Number of errors"
msgstr "Số lượng lỗi"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Số tin nhắn cần xử lý"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Số tin nhắn bị gửi lỗi"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__kanban_state__normal
msgid "Ongoing"
msgstr "Đang diễn ra"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Open Contract"
msgstr "Mở hợp đồng"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__hr_responsible_id
msgid "Person responsible for validating the employee's contracts."
msgstr "Người chịu trách nhiệm xác thực hợp đồng của nhân viên."

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_employee_public
msgid "Public Employee"
msgstr "Nhân viên chung"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__rating_ids
msgid "Ratings"
msgstr "Đánh giá"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__kanban_state__done
msgid "Ready"
msgstr "Sẵn sàng"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_list
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Reference Working Time"
msgstr "Giờ làm việc tham khảo"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "Chi tiết ngày nghỉ của tài nguyên"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_resource_calendar
msgid "Resource Working Time"
msgstr "Tài nguyên thời gian làm việc"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_resource_resource
msgid "Resources"
msgstr "Tài nguyên"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_user_id
msgid "Responsible User"
msgstr "Người phụ trách"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__state__open
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__state__open
msgid "Running"
msgstr "Đang chạy"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Running Contracts"
msgstr "Hợp đồng còn hiệu lực"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Lỗi gửi SMS"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Salary Information"
msgstr "Thông tin Lương"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_payroll_structure_type
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__structure_type_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__structure_type_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__name
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Salary Structure Type"
msgstr "Loại cấu trúc lương"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Search Contract"
msgstr "Tìm kiếm hợp đồng"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Search Reference Contracts"
msgstr "Tìm kiếm mã hợp đồng"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_departure_wizard__set_date_end
msgid "Set Contract End Date"
msgstr "Đặt ngày kết thúc hợp đồng"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_departure_wizard__set_date_end
msgid "Set the end date on the current contract."
msgstr "Đặt ngày kết thúc cho hợp đồng hiện tại."

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Show all records which have a next action date before today"
msgstr ""
"Hiển thị tất cả các bản ghi có ngày tác vụ tiếp theo trước ngày hôm nay"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__date_start
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__date_start
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Start Date"
msgstr "Ngày bắt đầu"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__state
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__state
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Status"
msgstr "Trạng thái"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__activity_state
#: model:ir.model.fields,help:hr_contract.field_hr_contract_history__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Trạng thái dựa trên hoạt động\n"
"Quá hạn: Hạn chót hạn đã qua\n"
"Hôm nay: Hôm nay là ngày phải thực hiện\n"
"Kế hoạch: Cần thực hiện trong tương lai."

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__state
msgid "Status of the contract"
msgstr "Tình trạng của hợp đồng"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__country_code
#: model:ir.model.fields,help:hr_contract.field_hr_contract_history__country_code
#: model:ir.model.fields,help:hr_contract.field_hr_payroll_structure_type__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Mã quốc gia ISO với hai ký tự.\n"
"Bạn có thể sử dụng trường này để tìm kiếm nhanh."

#. module: hr_contract
#. odoo-python
#: code:addons/hr_contract/models/hr_contract.py:0
msgid "The contract of %s is about to expire."
msgstr "The contract of %s is about to expire."

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid "The default working hours are set in configuration."
msgstr "Giờ làm việc mặc định được thiết lập trong cấu hình."

#. module: hr_contract
#. odoo-python
#: code:addons/hr_contract/models/hr_contract.py:0
msgid "The work permit of %s is about to expire."
msgstr "Giấy phép lao động của %s sắp hết hạn."

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_kanban
msgid "To"
msgstr "Đến"

#. module: hr_contract
#: model:mail.message.subtype,name:hr_contract.mt_contract_pending
msgid "To Renew"
msgstr "Cần gia hạn"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Today Activities"
msgstr "Hoạt động hôm nay"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Type in notes about this contract..."
msgstr "Nhập ghi chú về hợp đồng này..."

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Loại hoạt động ngoại lệ trong bản ghi."

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__under_contract_state__done
msgid "Under Contract"
msgstr "Có hợp đồng"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_res_users
msgid "User"
msgstr "Người dùng"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__visa_no
msgid "Visa No"
msgstr "Số Visa"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__wage
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__wage
msgid "Wage"
msgstr "Tiền công/tiền lương"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__kanban_state__blocked
msgid "Warning"
msgstr "Cảnh báo"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__website_message_ids
msgid "Website Messages"
msgstr "Thông báo trên trang web"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__website_message_ids
msgid "Website communication history"
msgstr "Lịch sử trao đổi qua trang web"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.res_config_settings_view_form
msgid "Work Permit Expiration Notice Period"
msgstr "Giai đoạn thông báo hết hạn giấy phép làm việc"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_res_company__work_permit_expiration_notice_period
#: model:ir.model.fields,field_description:hr_contract.field_res_config_settings__work_permit_expiration_notice_period
msgid "Work Permit Expiry Notice Period"
msgstr "Giai đoạn thông báo hết hạn giấy phép làm việc"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__permit_no
msgid "Work Permit No"
msgstr "Số giấy phép LĐ"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__resource_calendar_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__resource_calendar_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Working Schedule"
msgstr "Thời gian làm việc"

#. module: hr_contract
#. odoo-python
#: code:addons/hr_contract/models/hr_employee.py:0
msgid "You cannot delete an employee with a running contract."
msgstr "Bạn không thể xoá một nhân viên có hợp đồng còn hiệu lực."
