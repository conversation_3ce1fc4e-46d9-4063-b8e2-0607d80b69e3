<templates id="template" xml:space="preserve">

    <t t-name="portal.chatter_message_count">
        <t t-set="count" t-value="widget._messageCount"/>
        <div class="o_message_counter"/>
    </t>

    <!--
        Widget PortalComposer (standalone)

        required many options: token, res_model, res_id, ...
    -->
    <t t-name="portal.Composer">
        <div class="o_portal_chatter_composer" t-if="widget.options['allow_composer']">
            <t t-set="discussion_url" t-value="window.encodeURI(window.location.href.split('#')[0] + '#discussion')"/>
            <t t-if="!widget.options['display_composer']">
                <h4>Leave a comment</h4>
                <p>You must be <a t-attf-href="/web/login?redirect=#{discussion_url}">logged in</a> to post a comment.</p>
            </t>
            <t t-if="widget.options['display_composer']">
                <div class="alert alert-danger mb8 d-none o_portal_chatter_composer_error" role="alert">
                    Oops! Something went wrong. Try to reload the page and log in.
                </div>
                <div class="d-flex">
                    <img alt="Avatar" class="o_avatar o_portal_chatter_avatar align-self-start me-3 rounded" t-attf-src="/web/image/res.partner/#{widget.options['partner_id']}/avatar_128"
                         t-if="!widget.options['is_user_public'] or !widget.options['token']"/>
                    <div class="flex-grow-1">
                        <div class="o_portal_chatter_composer_input">
                            <div class="o_portal_chatter_composer_body d-flex flex-nowrap align-items-start flex-grow-1 mb-4 border rounded-3">
                                <div class="d-flex flex-column flex-grow-1 rounded-3">
                                    <div class="position-relative flex-grow-1">
                                        <textarea rows="4" name="message" class="form-control border-0" placeholder="Write a message..." style="resize:none;"/>
                                    </div>
                                    <div class="d-flex flex-row align-self-end p-2">
                                        <div class="d-flex px-1">
                                            <button class="o_portal_chatter_attachment_btn btn fa fa-paperclip border-0" type="button" title="Add attachment"/>
                                        </div>
                                        <button t-out="widget.options['send_button_label']" t-attf-data-action="/mail/message/post" class="o_portal_chatter_composer_btn btn btn-primary o-last rounded-3" type="submit"/>
                                    </div>
                                </div>
                            </div>
                            <div class="o_portal_chatter_attachments mt-3"/>
                        </div>
                        <div class="d-none">
                            <input type="file" class="o_portal_chatter_file_input" multiple="multiple"/>
                        </div>
                    </div>
                </div>
            </t>
        </div>
    </t>

    <t t-name="portal.Chatter.Attachments">
        <div t-if="attachments.length" class="d-flex flex-grow-1 flex-wrap gap-1">
            <div t-foreach="attachments" t-as="attachment" t-key="attachment_index" class="bg-light p-2 rounded position-relative">
                <div class="o_portal_chatter_attachment text-center" t-att-data-id="attachment.id">
                    <button t-if="showDelete and attachment.state == 'pending'" class="o_portal_chatter_attachment_delete btn btn-sm btn-outline-danger" title="Delete">
                        <i class="fa fa-times"/>
                    </button>
                    <a t-attf-href="/web/content/#{attachment.id}?download=true&amp;#{attachment.access_token and 'access_token=' + attachment.access_token}" target="_blank" class="d-flex flex-row">
                        <div class='oe_attachment_embedded o_image' t-att-title="attachment.name" t-att-data-mimetype="attachment.mimetype"/>
                        <div class='o_portal_chatter_attachment_name align-self-center text-truncate' t-att-data-tooltip="attachment.name" data-tooltip-position="top">
                            <t t-esc='attachment.name'/>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </t>

    <!--
        Widget PortalChatter, and subtemplates
    -->

    <t t-name="portal.pager">
        <div class="o_portal_chatter_pager">
            <t t-if="Object.keys(widget._pagerData).length > 0">
                <ul class="pagination" t-if="widget._pagerData['pages'].length &gt; 1">
                    <li t-if="widget._pagerData['page'] != widget._pagerData['page_previous']" t-att-data-page="widget._pagerData['page_previous']" class="page-item o_portal_chatter_pager_btn">
                        <a href="#" class="page-link"><i class="oi oi-chevron-left" role="img" aria-label="Previous" title="Previous"/></a>
                    </li>
                    <t t-foreach="widget._pagerData['pages']" t-as="page" t-key="page_index">
                        <li t-att-data-page="page" t-attf-class="page-item #{page == widget._pagerData['page'] ? 'o_portal_chatter_pager_btn active' : 'o_portal_chatter_pager_btn'}">
                            <a href="#" class="page-link"><t t-esc="page"/></a>
                        </li>
                    </t>
                    <li t-if="widget._pagerData['page'] != widget._pagerData['page_next']" t-att-data-page="widget._pagerData['page_next']" class="page-item o_portal_chatter_pager_btn">
                        <a href="#" class="page-link"><i class="oi oi-chevron-right" role="img" aria-label="Next" title="Next"/></a>
                    </li>
                </ul>
            </t>
        </div>
    </t>

</templates>
