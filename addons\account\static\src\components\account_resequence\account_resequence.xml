<?xml version="1.0" encoding="utf-8"?>
<templates>

    <t t-name="account.ResequenceRenderer" >
            <t t-set="value" t-value="this.getValue()"/>
            <table t-if="value.changeLines.length" class="table table-sm">
                <thead><tr>
                    <th>Date</th>
                    <th>Before</th>
                    <th>After</th>
                </tr></thead>
                <tbody>
                    <t t-foreach="value.changeLines" t-as="changeLine" t-key="changeLine.id">
                        <ChangeLine changeLine="changeLine" ordering="value.ordering"/>
                    </t>
                </tbody>
            </table>
    </t>

    <t t-name="account.ResequenceChangeLine">
        <tr>
            <td t-out="props.changeLine.date"/>
            <td t-out="props.changeLine.current_name"/>
            <td t-if="props.ordering == 'keep'" t-out="props.changeLine.new_by_name" t-attf-class="{{ props.changeLine.new_by_name != props.changeLine.new_by_date ? 'animate' : ''}}"/>
            <td t-else="" t-out="props.changeLine.new_by_date" t-attf-class="{{ props.changeLine.new_by_name != props.changeLine.new_by_date ? 'animate' : ''}}"/>
        </tr>
    </t>
</templates>
