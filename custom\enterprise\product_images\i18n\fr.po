# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_images
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: product_images
#. odoo-python
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
msgid ""
"%(matching_images_count)s matching images have been found for "
"%(product_count)s products."
msgstr ""
"%(matching_images_count)s images correspondantes ont été trouvées pour "
"%(product_count)s produits."

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid ""
"<span invisible=\"nb_products_selected &lt;= 10000\">\n"
"                            As only 10,000 products can be processed per day, the remaining will be\n"
"                            done tomorrow.\n"
"                        </span>"
msgstr ""
"<span invisible=\"nb_products_selected &lt;= 10000\">\n"
"                            Comme il n'est possible de traiter que 10 000 produits par jour, le reste sera\n"
"                            traité demain.\n"
"                        </span>"

#. module: product_images
#. odoo-python
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
msgid ""
"A task to process products in the background is already running. Please try "
"againlater."
msgstr ""
"Une tâche pour traiter les produits est déjà en cours en arrière-plan. "
"Veuillez réessayer plus tard."

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.res_config_settings_view_form
msgid "API Key"
msgstr "Clé API"

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid "Cancel"
msgstr "Annuler"

#. module: product_images
#: model:ir.model,name:product_images.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de configuration"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__create_date
msgid "Created on"
msgstr "Créé le"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: product_images
#: model:ir.model,name:product_images.model_product_fetch_image_wizard
msgid ""
"Fetch product images from Google Images based on the product's barcode "
"number."
msgstr ""
"Collecter des images de produit depuis Google Images en fonction du code-"
"barres du produit."

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid "Get Pictures"
msgstr "Obtenir des images"

#. module: product_images
#: model:ir.actions.act_window,name:product_images.product_product_action_get_pic_with_barcode
#: model:ir.actions.act_window,name:product_images.product_template_action_get_pic_with_barcode
msgid "Get Pictures from Google Images"
msgstr "Obtenir des images à partir de Google Images"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_res_config_settings__google_custom_search_key
msgid "Google Custom Search API Key"
msgstr "Clé API recherche personnalisée Google"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__id
msgid "ID"
msgstr "ID"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_product__image_fetch_pending
msgid "Image Fetch Pending"
msgstr "Collecte d'images en attente"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__write_uid
msgid "Last Updated by"
msgstr "Mis à jour par"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__write_date
msgid "Last Updated on"
msgstr "Mis à jour le"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__nb_products_unable_to_process
msgid "Number of product unprocessable"
msgstr "Quantité de produits impossibles à traiter"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__nb_products_to_process
msgid "Number of products to process"
msgstr "Quantité de produits à traiter"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__nb_products_selected
msgid "Number of selected products"
msgstr "Quantité de produits sélectionnés"

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid ""
"Please note that some images might not be royalty-free. You should not\n"
"                        publish these on your website."
msgstr ""
"Notez que certaines images pourraient ne pas être libres de droits. Vous ne devriez pas\n"
"les publier sur votre site web."

#. module: product_images
#: model:ir.actions.server,name:product_images.ir_cron_fetch_image_ir_actions_server
msgid "Product Images: Get product images from Google"
msgstr "Images de produits : Obtenir des images de Google"

#. module: product_images
#: model:ir.model,name:product_images.model_product_product
msgid "Product Variant"
msgstr "Variante de produit"

#. module: product_images
#. odoo-python
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
msgid "Product images"
msgstr "Images de produits"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__products_to_process
msgid "Products To Process"
msgstr "Produits à traiter"

#. module: product_images
#. odoo-python
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
msgid ""
"Products are processed in the background. Images will be updated "
"progressively."
msgstr ""
"Les produits sont traités en arrière-plan. Les images seront mises à jour "
"progressivement."

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.res_config_settings_view_form
msgid "Search Engine ID"
msgstr "ID moteur de recherche"

#. module: product_images
#. odoo-python
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
msgid "The API Key and Search Engine ID must be set in the General Settings."
msgstr ""
"La clé API et l'ID du moteur de recherche doivent être configurées dans les "
"paramètres généraux."

#. module: product_images
#. odoo-python
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
msgid ""
"The Custom Search API is not enabled in your Google project. Please visit "
"your Google Cloud Platform project page and enable it, then retry. If you "
"enabled this API recently, please wait a few minutes and retry."
msgstr ""
"La recherche personnalisée API n'est pas activée dans votre projet Google. "
"Allez à voter page de projet Google Cloud Platform et activez-la, puis "
"réessayez. Si vous avez activé cet API récemment, veuillez attendre quelques"
" minutes et réessayer plus tard."

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_res_config_settings__google_pse_id
msgid "The identifier of the Google Programmable Search Engine"
msgstr "L'identifiant du moteur de recherche programmable de Google"

#. module: product_images
#: model:ir.model.fields,help:product_images.field_product_fetch_image_wizard__products_to_process
msgid ""
"The list of selected products that meet the criteria (have a barcode and no "
"image)"
msgstr ""
"La liste des produits sélectionnés qui correspondent aux critères (ont un "
"code-barres et pas d'image)"

#. module: product_images
#. odoo-python
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
msgid ""
"The scheduled action \"Product Images: Get product images from Google\" has "
"been deleted. Please contact your administrator to have the action restored "
"or to reinstall the module \"product_images\"."
msgstr ""
"L'action planifiée \"Images de produits : Obtenir des images de Google\" a "
"été supprimée. Veuillez contacter votre administrateur pour restaurer "
"l'action ou réinstaller le module \"product_images\"."

#. module: product_images
#. odoo-python
#: code:addons/product_images/models/ir_cron_trigger.py:0
msgid "This action is already scheduled. Please try again later."
msgstr "Cette action est déjà planifiée. Veuillez réessayer plus tard."

#. module: product_images
#: model:ir.model,name:product_images.model_ir_cron_trigger
msgid "Triggered actions"
msgstr "Actions déclenchées"

#. module: product_images
#: model:ir.model.fields,help:product_images.field_product_product__image_fetch_pending
msgid "Whether an image must be fetched for this product. Handled by a cron."
msgstr "Si une image doit être récupérée pour ce produit. Géré par un cron."

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid "You selected"
msgstr "Vous avez sélectionné"

#. module: product_images
#. odoo-python
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
msgid "Your API Key or your Search Engine ID is incorrect."
msgstr "Votre clé API ou votre ID de moteur de recherche est incorrect."

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid "of which will be processed."
msgstr "parmi ceux-ci seront traités."

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid ""
"products will not be\n"
"                            processed because they either already have an image or their barcode\n"
"                            number is not set."
msgstr ""
"les produits ne seront pas traités \n"
"car ils ont déjà une image ou leur numéro de code-barres \n"
"n'est pas défini."

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid "products,"
msgstr "produits,"
