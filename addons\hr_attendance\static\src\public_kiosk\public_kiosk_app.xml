<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

<t t-name="hr_attendance.companyHeader">
    <img t-att-src="companyImageUrl" alt="Company Logo" class="o_hr_attendance_kiosk_company_image align-self-center"/>
</t>

<t t-name="hr_attendance.BarcodeScanner" t-inherit="barcodes.BarcodeScanner" t-inherit-mode="primary">
    <xpath expr="//div[hasclass('o_barcode_mobile_container')]" position="replace">
        <div class="o_hr_attendance_kiosk d-flex justify-content-around" t-att-class="{'position-relative' : !isDisplayStandalone}">
            <button t-if="isBarcodeScannerSupported" t-on-click="openMobileScanner" class="o_mobile_barcode btn btn-light btn-lg p-5 rounded-3" t-att-class="{'position-absolute' : !isDisplayStandalone}">
                <i class="fa fa-3x fa-barcode mb-3"/>
                <span class="d-block">Scan your badge</span>
            </button>
            <a t-if="!isDisplayStandalone" class="o_hr_attendance_install_btn btn btn-secondary d-flex align-items-center justify-content-center fw-bolder position-relative" t-att-href="installURL" target="_blank">Install</a>
        </div>
    </xpath>
</t>

<t t-name="hr_attendance.PublicKiosksSettingScreen">
    <div class="align-self-center" t-att-class="{'h2 mt-5': !env.isSmall, 'my-3 text-center h5': env.isSmall}">
        Choose how to record attendances
    </div>
    <div class="o_hr_kiosk_mode_main d-flex m-auto" t-att-class="{'gap-5': !env.isSmall, 'gap-3 flex-column': env.isSmall}">
        <div class="d-flex flex-column align-items-center text-center gap-2" t-att-style="!env.isSmall ? 'max-width: min-content;' : ''">
            <button
                t-on-click="() => this.setSetting('manual')"
                class="btn btn-light"
                t-att-class="{
                    'd-flex flex-row rounded-2 ps-2 align-items-center gap-2' : env.isSmall,
                    'btn-lg rounded-3': !env.isSmall
                }">
                <t t-if="env.isSmall">
                    <img src="/hr_attendance/static/img/tablet-pin.svg" alt="Manual Attendance" width="40%"/>
                    <h6 class="m-0">Manually (optional PIN)</h6>
                </t>
                <t t-else="">
                    <img src="/hr_attendance/static/img/tablet-pin.svg" height="170px" alt="Manual Attendance"/>
                </t>
            </button>
            <t t-if="!env.isSmall">
                <h5 class="m-0">Select on Tablet</h5>
                <em>with optional PIN code</em>
            </t>
        </div>
        <div class="d-flex flex-column align-items-center text-center gap-2" t-att-style="!env.isSmall ? 'max-width: min-content;' : ''">
            <button
                t-on-click="() => this.setSetting('barcode')"
                class="btn btn-light"
                t-att-class="{
                    'flex-row d-flex rounded-2 ps-2 align-items-center gap-2' : env.isSmall,
                    'btn-lg rounded-3': !env.isSmall
                }">
                <t t-if="env.isSmall">
                    <img src="/hr_attendance/static/img/tablet-cam.svg" alt="Attendance with barcode" width="40%"/>
                    <h6 class="m-0">Badge with Barcode</h6>
                </t>
                <t t-else="">
                    <img src="/hr_attendance/static/img/tablet-cam.svg" height="170px" alt="Attendance with barcode"/>
                </t>
            </button>
            <t t-if="!env.isSmall">
                <h5 class="m-0 w-75">Badge with Barcode on Tablet</h5>
            </t>
        </div>
        <div class="d-flex flex-column align-items-center text-center gap-2" t-att-style="!env.isSmall ? 'max-width: min-content;' : ''">
            <button
                t-on-click="() => this.setSetting('barcode_manual')"
                class="btn btn-light"
                t-att-class="{
                    'flex-row d-flex rounded-2 ps-2 align-items-center gap-2' : env.isSmall,
                    'btn-lg rounded-3': !env.isSmall
                }">
                <t t-if="env.isSmall">
                    <img src="/hr_attendance/static/img/tablet-rfid.svg" alt="Manual Attendance or with barcode" width="40%"/>
                    <h6 class="m-0">RFID Token with reader</h6>
                </t>
                <t t-else="">
                    <img src="/hr_attendance/static/img/tablet-rfid.svg" height="170px"
                    alt="Manual Attendance or with barcode"/>
                </t>
            </button>
            <t t-if="!env.isSmall">
                <h5 class="m-0 w-75">RFID Token with reader on tablet</h5>
            </t>
        </div>
    </div>
    <div class="o_hr_kiosk_mode_bottom my-5 mx-auto">
        Powered by <img t-att-height="env.isSmall ? 21 : 32" src="/web/static/img/logo.png" alt="Odoo Logo"/>
    </div>
</t>

<t t-name="hr_attendance.NoBadgeEmployeeScreen">
    <div class="alert alert-info align-items-center align-self-center d-flex gap-2" t-att-class="{'flex-wrap': env.isSmall}" style="width: fit-content;" role="alert">
        <span class="m-0" t-att-class="{'text-align-center w-100': env.isSmall}" t-att-style="!env.isSmall? 'min-width: fit-content;' : ''">
            No Badge defined on employees. Set one to test.
        </span>
        <div class="input-group">
            <input id="manual_barcode"
                t-model="state.barcode"
                type="text"
                name="barcode"
                t-ref="demobadgeID"
                class="input-group-text text-start"
                placeholder="e.g. 1102021021"
                t-att-class="{'flex-grow-1': env.isSmall}"
                t-att-style="env.isSmall? 'width: 15ch;' : ''"/>
            <button type="button" class="btn btn-primary" t-on-click="setBadgeID" t-att-class="{'flex-shrink-0': env.isSmall}">
                Set my badge
            </button>
        </div>
    </div>
</t>

<t t-name="hr_attendance.public_kiosk_app">
    <MainComponentsContainer/>
    <CardLayout fromTrialMode="this.props.fromTrialMode" companyImageUrl="this.companyImageUrl" kioskReturn.bind="kioskReturn">
        <t t-if="this.state.active_display === 'settings'">
            <t t-call="hr_attendance.PublicKiosksSettingScreen"/>
        </t>
        <t t-if="this.state.active_display === 'main'">
            <t t-if="isFreshDb and !state.barcodeIsSet">
                <t t-call="hr_attendance.NoBadgeEmployeeScreen"/>
            </t>
            <t t-elif="state.displayDemoMessage">
                <div
                    class="alert alert-info flex-row justify-content-between d-flex"
                    t-att-class="{'align-self-center col-6': !env.isSmall}"
                    t-att-style="!env.isSmall? 'min-width: fit-content;' : ''">
                        <div>
                            Connect an RFID reader, and scan a token.
                            <DocumentationLink path="'/applications/hr/attendances/hardware.html'" label.translate="'Documentation'" alertLink="true"/>
                        </div>
                        <button t-on-click="removeDemoMessage" type="button" class="btn-close float-end ms-2" title="Close"/>
                </div>
            </t>
            <div class="o_hr_kiosk_mode_main d-flex flex-column flex-sm-row gap-3 m-auto">
                <t t-if="this.props.kioskMode !== 'manual'">
                <KioskBarcodeScanner
                    token="this.props.token"
                    barcodeSource="this.props.barcodeSource"
                    onBarcodeScanned="(ev) => this.onBarcodeScanned(ev)"/>
                </t>
            </div>
            <div class="o_hr_kiosk_mode_bottom d-flex flex-column flex-md-row gap-2 align-items-center justify-content-between my-5">
                <t t-if="this.props.kioskMode !== 'barcode'">
                    <button t-on-click="() => this.switchDisplay('manual')"  class="btn btn-light btn-lg rounded-3">
                        <i class="fa fa-user-o me-2"/>
                        <span >Identify Manually</span>
                    </button>
                    <span>
                        Powered by <img height="32" src="/web/static/img/logo.png" alt="Odoo Logo"/>
                    </span>
                </t>
                <t t-else="">
                    <div class="o_hr_kiosk_mode_bottom mx-auto">
                        Powered by <img height="32" src="/web/static/img/logo.png" alt="Odoo Logo"/>
                    </div>
                </t>
            </div>
        </t>
        <t t-if="this.state.active_display === 'manual'">
            <KioskManualSelection
                displayBackButton="!this.manualKioskMode || this.props.fromTrialMode"
                departments="this.props.departments"
                onSelectEmployee="(e) => this.kioskConfirm(e)"
                onClickBack="() => this.kioskReturn()"
                token="this.props.token"/>
        </t>
        <t t-if="this.state.active_display === 'greet'">
            <KioskGreetings employeeData="this.employeeData" kioskReturn="() => this.kioskReturn(true)"/>
        </t>
        <t t-if="this.state.active_display === 'pin'">
            <KioskPinCode
                employeeData="this.employeeData"
                onPinConfirm="(id, pin) => this.onManualSelection(id, pin)"
                onClickBack="() => this.kioskReturn()"/>
        </t>
    </CardLayout>
</t>
</templates>
