# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_email_template
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# Anna, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.product_template_form_view
msgid "Automatic Email at Invoice"
msgstr "Automaatne meiliaadress müügiarvel"

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.email_template_form_simplified
msgid "Body"
msgstr "Sisu"

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.email_template_form_simplified
#: model_terms:ir.ui.view,arch_db:product_email_template.product_template_form_view
msgid "Email Template"
msgstr "E-kirja näidis"

#. module: product_email_template
#: model:ir.model,name:product_email_template.model_account_move
msgid "Journal Entry"
msgstr "Andmiku kanne"

#. module: product_email_template
#: model:ir.model,name:product_email_template.model_product_template
msgid "Product"
msgstr "Toode"

#. module: product_email_template
#: model:ir.model.fields,field_description:product_email_template.field_product_product__email_template_id
#: model:ir.model.fields,field_description:product_email_template.field_product_template__email_template_id
msgid "Product Email Template"
msgstr "Toote e-maili mall"

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.product_template_form_view
msgid "Send a product-specific email once the invoice is validated"
msgstr "Saada arve kinnitamisel tootepõhine e-kiri"

#. module: product_email_template
#: model:ir.model.fields,help:product_email_template.field_product_product__email_template_id
#: model:ir.model.fields,help:product_email_template.field_product_template__email_template_id
msgid ""
"When validating an invoice, an email will be sent to the customer based on "
"this template. The customer will receive an email for each product linked to"
" an email template."
msgstr ""
"Arve kinnitamisel saadetakse selle malli alusel kliendile e-kiri. Klient "
"saab kirja iga meilimalli külge lingitud toote kohta."
