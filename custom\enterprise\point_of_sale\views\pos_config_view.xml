<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="pos_config_view_form" model="ir.ui.view">
        <field name="name">pos.config.form.view</field>
        <field name="model">pos.config</field>
        <field name="arch" type="xml">
            <form string="Point of Sale Configuration">
                <sheet>
                    <widget name="web_ribbon" title="Archived" bg_color="text-bg-danger" invisible="active"/>
                    <field name="active" invisible="1"/>
                    <field name="company_has_template" invisible="1"/>
                    <field name="has_active_session" invisible="1"/>
                    <field name="other_devices" invisible="1"/>
                    <field name="is_posbox" invisible="1"/>
                    <field name="module_pos_hr" invisible="1"/>

                    <div class="oe_title" id="title">
                        <label for="name"/>
                        <h1><field name="name" placeholder="e.g. NYC Shop"/></h1>
                    </div>
                    <!-- HIDE this div in create_mode (when '+ New Shop' is clicked in the general settings.) -->
                    <div invisible="context.get('pos_config_create_mode', False)">
                        <div class="o_notification_alert alert alert-warning" invisible="not has_active_session" role="alert">
                            A session is currently opened for this PoS. Some settings can only be changed after the session is closed.
                            <button class="btn" style="padding:0" name="open_ui" type="object">Click here to close the session</button>
                        </div>
                        <div class="o_notification_alert alert alert-warning" invisible="company_has_template" role="alert">
                            There is no Chart of Accounts configured on the company. Please go to the invoicing settings to install a Chart of Accounts.
                        </div>
                    </div>

                    <!-- SHOW this div in create_mode (when '+ New Shop' is clicked in the general settings.) -->
                    <div id="restaurant_on_create" class="row mt16 o_settings_container" invisible="not context.get('pos_config_create_mode', False)">
                        <setting>
                                <field name="module_pos_restaurant" />
                        </setting>
                    </div>

                    <!-- HIDE this div in create_mode (when '+ New Shop' is clicked in the general settings.) -->
                    <div class="row mt16 o_settings_container" invisible="context.get('pos_config_create_mode', False)">
                        <setting
                                title="Employees can scan their badge or enter a PIN to log in to a PoS session. These credentials are configurable in the *HR Settings* tab of the employee form."
                                string="Log in with Employees"
                                help="Allow to log and switch between selected Employees">
                            <field name="module_pos_hr" readonly="has_active_session" />
                            <div class="content-group mt16" invisible="not module_pos_hr">
                                <div class="text-warning" id="warning_text_employees">
                                    Save this page and come back here to set up the feature.
                                </div>
                            </div>
                        </setting>
                        <setting id="other_devices" string="ePos Printer" help="Connect device to your PoS without an IoT Box">
                            <field name="other_devices" />
                        </setting>
                        <setting string="IoT Box" help="Connect devices using an IoT Box">
                                <field name="is_posbox" />
                                <div class="content-group pos_iot_config ms-3" invisible="not is_posbox">
                                    <div class="row">
                                        <div class="col-lg-4 o_light_label">
                                            <label string="IoT Box IP Address" for="proxy_ip"/>
                                        </div>
                                        <div class="col-lg-8">
                                            <field name="proxy_ip"/>
                                        </div>
                                    </div>
                                    <div class="row iot_barcode_scanner">
                                        <div class="col-lg-5 o_light_label">
                                            <label string="Scanner" for="iface_scan_via_proxy"/>
                                        </div>
                                        <div class="col-lg-7">
                                            <field name="iface_scan_via_proxy"/>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-lg-5 o_light_label">
                                            <label string="Electronic Scale" for="iface_electronic_scale"/>
                                        </div>
                                        <div class="col-lg-7">
                                            <field name="iface_electronic_scale"/>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-lg-5 o_light_label">
                                            <label string="Receipt Printer" for="iface_print_via_proxy"/>
                                        </div>
                                        <div class="col-lg-7">
                                            <field name="iface_print_via_proxy"/>
                                       </div>
                                    </div>
                                    <div class="row" invisible="not iface_print_via_proxy">
                                        <div class="col-lg-5 o_light_label ps-4">
                                            <label string="Cashdrawer" for="iface_cashdrawer"/>
                                        </div>
                                        <div class="col-lg-7">
                                            <field name="iface_cashdrawer"/>
                                        </div>
                                    </div>
                                </div>
                        </setting>
                        <div groups="base.group_system">
                            <p>
                                More settings: <a href="#" name="%(action_pos_configuration)d" type="action" class="btn-link o_form_uri" role="button">Configurations > Settings</a>
                            </p>
                        </div>
                    </div>
                </sheet>

                <!-- Replace the default save/discard buttons so that when any of the buttons is clicked, the modal immediately closes. -->
                <footer invisible="not context.get('pos_config_open_modal', False)">
                    <button string="Save" special="save" class="btn-primary"/>
                    <button string="Discard" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="view_pos_config_tree" model="ir.ui.view">
        <field name="name">pos.config.list.view</field>
        <field name="model">pos.config</field>
        <field name="arch" type="xml">
            <list string="Point of Sale Configuration">
                <field name="name" />
                <field name="company_id"  options="{'no_create': True}" groups="base.group_multi_company"/>
                <field name="last_session_closing_date" string="Closing"/>
                <field name="currency_id" column_invisible="True"/>
                <field name="last_session_closing_cash" widget="monetary" string="Balance"/>
            </list>
        </field>
    </record>

    <record id="view_pos_config_search" model="ir.ui.view">
        <field name="name">pos.config.search.view</field>
        <field name="model">pos.config</field>
        <field name="arch" type="xml">
            <search string="Point of Sale Config">
                <field name="name"/>
                <field name="picking_type_id" />
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
            </search>
        </field>
    </record>

    <record id="action_pos_config_kanban" model="ir.actions.act_window">
        <field name="name">Point of Sale</field>
        <field name="path">point-of-sale</field>
        <field name="res_model">pos.config</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="domain"></field>
        <field name="search_view_id" ref="view_pos_config_search" />
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new PoS
            </p><p>
                Configure at least one Point of Sale.
            </p>
        </field>
    </record>

    <record id="action_pos_config_tree" model="ir.actions.act_window">
        <field name="name">Point of Sale List</field>
        <field name="res_model">pos.config</field>
        <field name="view_mode">list,form</field>
        <field name="domain"></field>
        <field name="search_view_id" ref="view_pos_config_search" />
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new PoS
            </p><p>
                Configure at least one Point of Sale.
            </p>
        </field>
    </record>

    <!-- Products sub Category -->
    <menuitem id="menu_products_pos_category"
        action="point_of_sale.product_pos_category_action"
        parent="point_of_sale.pos_menu_products_configuration"
        sequence="1"/>
    <menuitem id="pos_menu_products_attribute_action"
        action="product.attribute_action"
        parent="point_of_sale.pos_menu_products_configuration"  groups="product.group_product_variant" sequence="2"/>

    <menuitem id="menu_pos_dashboard" action="action_pos_config_kanban" parent="menu_point_root" name="Dashboard" sequence="1"/>
    <menuitem id="menu_point_of_sale_list" name="Point of Sales" parent="menu_point_config_product"
              action="action_pos_config_tree" sequence="10"/>

</odoo>
