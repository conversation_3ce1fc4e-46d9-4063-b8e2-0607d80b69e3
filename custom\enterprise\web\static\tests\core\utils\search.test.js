import { describe, expect, test } from "@odoo/hoot";

import { fuzzyLookup, fuzzyTest } from "@web/core/utils/search";

describe.current.tags("headless");

test("fuzzyLookup", () => {
    const data = [
        { name: "<PERSON>" },
        { name: "<PERSON>" },
        { name: "<PERSON>" },
        { name: "<PERSON>" },
        { name: "<PERSON><PERSON><PERSON><PERSON>" },
    ];
    expect(fuzzyLookup("ba", data, (d) => d.name)).toEqual([
        { name: "<PERSON>" },
        { name: "<PERSON>" },
    ]);
    expect(fuzzyLookup("g", data, (d) => d.name)).toEqual([{ name: "<PERSON>" }]);
    expect(fuzzyLookup("z", data, (d) => d.name)).toEqual([]);
    expect(fuzzyLookup("brand", data, (d) => d.name)).toEqual([{ name: "<PERSON>" }]);
    expect(fuzzyLookup("jâ", data, (d) => d.name)).toEqual([{ name: "<PERSON>" }]);
    expect(fuzzyLookup("je", data, (d) => d.name)).toEqual([
        { name: "<PERSON><PERSON><PERSON><PERSON>" },
        { name: "<PERSON>" },
    ]);
    expect(fuzzyLookup("", data, (d) => d.name)).toEqual([]);
});

test("fuzzyTest", () => {
    expect(fuzzyTest("a", "Abby White")).toBe(true);
    expect(fuzzyTest("ba", "Brandon Green")).toBe(true);
    expect(fuzzyTest("je", "Jérémy red")).toBe(true);
    expect(fuzzyTest("jé", "Jeremy red")).toBe(true);
    expect(fuzzyTest("z", "Abby White")).toBe(false);
    expect(fuzzyTest("ba", "Abby White")).toBe(false);
});
