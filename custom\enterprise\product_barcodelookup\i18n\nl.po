# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_barcodelookup
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "API Key:"
msgstr "API Key:"

#. module: product_barcodelookup
#: model:ir.model.fields,field_description:product_barcodelookup.field_res_config_settings__barcodelookup_api_key
msgid "API key"
msgstr "API key"

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "Barcode Database"
msgstr "Barcode Database"

#. module: product_barcodelookup
#: model:ir.model.fields,help:product_barcodelookup.field_res_config_settings__barcodelookup_api_key
msgid "Barcode Lookup API Key for create product from barcode."
msgstr ""
"Barcode Lookup API Key om een product aan te maken op basis van een barcode."

#. module: product_barcodelookup
#: model:ir.model,name:product_barcodelookup.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie-instellingen"

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "Create products by scanning using"
msgstr "Maak producten aan door te scannen aan de hand van"

#. module: product_barcodelookup
#: model:ir.model,name:product_barcodelookup.model_product_template
msgid "Product"
msgstr "Product"

#. module: product_barcodelookup
#: model:ir.model,name:product_barcodelookup.model_product_product
msgid "Product Variant"
msgstr "Productvariant"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_8
msgid "age group"
msgstr "leeftijdsgroep"

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "barcodelookup.com"
msgstr "barcodelookup.com"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_6
msgid "brand"
msgstr "merk"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_1
msgid "color"
msgstr "kleur"

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "e.g. d7vctmiv2rwgenebha8bxq7irooudn"
msgstr "bijv. d7vctmiv2rwgenebha8bxq7irooudn"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_2
msgid "gender"
msgstr "geslacht"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_5
msgid "manufacturer"
msgstr "fabrikant"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_3
msgid "material"
msgstr "materiaal"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_4
msgid "pattern"
msgstr "patroon"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_7
msgid "size"
msgstr "grootte"
