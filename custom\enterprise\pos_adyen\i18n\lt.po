# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_adyen
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>ada <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> Versada <<EMAIL>>, 2024\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: pos_adyen
#: model_terms:ir.ui.view,arch_db:pos_adyen.res_config_settings_view_form
msgid "Add tip through payment terminal (Adyen)"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_api_key
msgid "Adyen API key"
msgstr ""

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "Adyen Error"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_latest_response
msgid "Adyen Latest Response"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_terminal_identifier
msgid "Adyen Terminal Identifier"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_test_mode
msgid "Adyen Test Mode"
msgstr ""

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "An unexpected error occurred. Message from Adyen: %s"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_config__adyen_ask_customer_for_tip
msgid "Ask Customers For Tip"
msgstr ""

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "Authentication failed. Please check your Adyen credentials."
msgstr ""

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid ""
"Cancelling the payment failed. Please cancel it manually on the payment "
"terminal."
msgstr ""

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "Cannot process transactions with negative amount."
msgstr ""

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigūracijos nustatymai"

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid ""
"Could not connect to the Odoo server, please check your internet connection "
"and try again."
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_event_url
msgid "Event URL"
msgstr ""

#. module: pos_adyen
#. odoo-python
#: code:addons/pos_adyen/models/pos_payment_method.py:0
msgid "Invalid Adyen request"
msgstr ""

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "Message from Adyen: %s"
msgstr ""

#. module: pos_adyen
#. odoo-python
#: code:addons/pos_adyen/models/pos_config.py:0
msgid ""
"Please configure a tip product for POS %s to support tipping with Adyen."
msgstr ""

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Pardavimo taško konfigūracija"

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Pardavimo taško mokėjimo būdai"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_res_config_settings__pos_adyen_ask_customer_for_tip
msgid "Pos Adyen Ask Customer For Tip"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_test_mode
msgid "Run transactions in the test environment."
msgstr ""

#. module: pos_adyen
#. odoo-python
#: code:addons/pos_adyen/models/pos_payment_method.py:0
msgid ""
"Terminal %(terminal)s is already used in company %(company)s on payment "
"method %(payment_method)s."
msgstr ""

#. module: pos_adyen
#. odoo-python
#: code:addons/pos_adyen/models/pos_payment_method.py:0
msgid ""
"Terminal %(terminal)s is already used on payment method %(payment_method)s."
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_event_url
msgid "This URL needs to be pasted on Adyen's portal terminal settings."
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_api_key
msgid ""
"Used when connecting to Adyen: https://docs.adyen.com/user-management/how-"
"to-get-the-api-key/#description"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_terminal_identifier
msgid "[Terminal model]-[Serial number], for example: P400Plus-*********"
msgstr ""
