<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <linearGradient id="linearGradient-1" x1="0%" x2="100%" y1="0%" y2="100%">
      <stop offset="0%" stop-color="#00A09D"/>
      <stop offset="100%" stop-color="#00E2FF"/>
    </linearGradient>
    <rect id="path-2" width="5" height="2" x="0" y="0"/>
    <path id="path-3" d="M5.6 3zm1.4-3v1H0v-1h7z"/>
    <filter id="filter-4" width="107.1%" height="150%" x="-3.6%" y="-12.5%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_mega_menu_big_icons_subtitles">
      <rect width="82" height="60" class="bg"/>
      <g class="group" transform="translate(15 14)">
        <g class="group">
          <circle cx="3" cy="3" r="3" fill="url(#linearGradient-1)" class="oval"/>
          <g class="rectangle" transform="translate(7 1)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-2"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
          </g>
          <g class="combined_shape" transform="translate(7 4)">
            <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
          </g>
        </g>
        <g class="group" transform="translate(19)">
          <circle cx="3" cy="3" r="3" fill="url(#linearGradient-1)" class="oval"/>
          <g class="rectangle" transform="translate(7 1)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-2"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
          </g>
          <g class="combined_shape" transform="translate(7 4)">
            <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
          </g>
        </g>
        <g class="group" transform="translate(38)">
          <circle cx="3" cy="3" r="3" fill="url(#linearGradient-1)" class="oval"/>
          <g class="rectangle" transform="translate(7 1)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-2"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
          </g>
          <g class="combined_shape" transform="translate(7 4)">
            <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
          </g>
        </g>
        <g class="group" transform="translate(0 12)">
          <circle cx="3" cy="3" r="3" fill="url(#linearGradient-1)" class="oval"/>
          <g class="rectangle" transform="translate(7 1)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-2"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
          </g>
          <g class="combined_shape" transform="translate(7 4)">
            <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
          </g>
        </g>
        <g class="group" transform="translate(19 12)">
          <circle cx="3" cy="3" r="3" fill="url(#linearGradient-1)" class="oval"/>
          <g class="rectangle" transform="translate(7 1)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-2"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
          </g>
          <g class="combined_shape" transform="translate(7 4)">
            <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
          </g>
        </g>
        <g class="group" transform="translate(38 12)">
          <circle cx="3" cy="3" r="3" fill="url(#linearGradient-1)" class="oval"/>
          <g class="rectangle" transform="translate(7 1)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-2"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
          </g>
          <g class="combined_shape" transform="translate(7 4)">
            <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
          </g>
        </g>
        <g class="group" transform="translate(0 24)">
          <circle cx="3" cy="3" r="3" fill="url(#linearGradient-1)" class="oval"/>
          <g class="rectangle" transform="translate(7 1)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-2"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
          </g>
          <g class="combined_shape" transform="translate(7 4)">
            <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
          </g>
        </g>
        <g class="group" transform="translate(19 24)">
          <circle cx="3" cy="3" r="3" fill="url(#linearGradient-1)" class="oval"/>
          <g class="rectangle" transform="translate(7 1)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-2"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
          </g>
          <g class="combined_shape" transform="translate(7 4)">
            <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
          </g>
        </g>
        <g class="group" transform="translate(38 24)">
          <circle cx="3" cy="3" r="3" fill="url(#linearGradient-1)" class="oval"/>
          <g class="rectangle" transform="translate(7 1)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-2"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
          </g>
          <g class="combined_shape" transform="translate(7 4)">
            <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>
