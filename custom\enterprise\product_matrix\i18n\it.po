# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_matrix
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid ""
"<small>Switch to the \"else\" condition of this block to view or edit the "
"table.</small>"
msgstr ""
"<small>Seleziona la condizione \"altro\" in questo blocco per vedere o "
"modificare la tabella.</small>"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid "<strong>Product matrix block</strong>"
msgstr "<strong>Blocco matrice prodotti</strong>"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_color_1
msgid "Blue"
msgstr "Blu"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid "Cell name"
msgstr "Nome cella"

#. module: product_matrix
#. odoo-javascript
#: code:addons/product_matrix/static/src/xml/product_matrix_dialog.xml:0
msgid "Choose Product Variants"
msgstr "Scelta varianti prodotto"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid "Column name"
msgstr "Nome colonna"

#. module: product_matrix
#. odoo-javascript
#: code:addons/product_matrix/static/src/xml/product_matrix_dialog.xml:0
msgid "Confirm"
msgstr "Conferma"

#. module: product_matrix
#. odoo-javascript
#: code:addons/product_matrix/static/src/xml/product_matrix_dialog.xml:0
msgid "Discard"
msgstr "Abbandona"

#. module: product_matrix
#: model:product.attribute,name:product_matrix.product_attribute_gender
msgid "Gender"
msgstr "Sesso"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_m
msgid "Men"
msgstr "Uomini"

#. module: product_matrix
#: model:product.template,name:product_matrix.matrix_product_template_shirt
msgid "My Company Tshirt (GRID)"
msgstr "Maglietta My Company (GRIGLIA)"

#. module: product_matrix
#. odoo-javascript
#: code:addons/product_matrix/static/src/xml/product_matrix.xml:0
msgid "Not available"
msgstr "Non disponibile"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_color_2
msgid "Pink"
msgstr "Rosa"

#. module: product_matrix
#: model:ir.model,name:product_matrix.model_product_template
msgid "Product"
msgstr "Prodotto"

#. module: product_matrix
#: model:ir.model,name:product_matrix.model_product_template_attribute_value
msgid "Product Template Attribute Value"
msgstr "Valore attributo del modello prodotto"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_color_4
msgid "Rainbow"
msgstr "Arcobaleno"

#. module: product_matrix
#: model:product.template,description_sale:product_matrix.matrix_product_template_shirt
msgid "Show your company love around you =)."
msgstr "Mostra a tutti l'amore per la tua azienda =)."

#. module: product_matrix
#: model:product.attribute,name:product_matrix.product_attribute_size
msgid "Size"
msgstr "Taglia"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid ""
"The matrix of product variants of this order will be displayed here, if "
"there are any."
msgstr ""
"La matrice delle varianti prodotto dell'ordine verrà visualizzata qui, se ce"
" ne sono."

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.extra_price
msgid "Variant price"
msgstr "Prezzo variante"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_w
msgid "Women"
msgstr "Donne"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_size_xl
msgid "XL"
msgstr "XL"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_size_xs
msgid "XS"
msgstr "XS"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_color_3
msgid "Yellow"
msgstr "Giallo"
