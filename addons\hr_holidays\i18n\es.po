# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_holidays
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-07 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid " to %(date_to_utc)s"
msgstr " al %(date_to_utc)s"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important &gt;&lt;/td&gt;"
msgstr "!important &gt;&lt;/td&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important /&gt;"
msgstr "!important /&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important/&gt;"
msgstr "!important/&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important; font-size: 10px\" &gt;"
msgstr "!important; font-size: 10px\" &gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important; font-size: 8px; min-width: 18px\"&gt;"
msgstr "!important; font-size: 8px; min-width: 18px\"&gt;"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "%(allocation_name)s (from %(date_from)s to %(date_to)s)"
msgstr "%(allocation_name)s (del %(date_from)s al %(date_to)s)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "%(allocation_name)s (from %(date_from)s to No Limit)"
msgstr "%(allocation_name)s (del %(date_from)s y no tiene límite)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "%(employee)s on Time Off : %(duration)s"
msgstr "%(employee)s está ausente: %(duration)s"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "%(employee_name)s - from %(date_from)s to %(date_to)s - %(state)s"
msgstr "%(employee_name)s - del %(date_from)s al %(date_to)s - %(state)s"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "%(holiday_name)s has been refused."
msgstr "Se ha rechazado %(holiday_name)s."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"%(leave_name)s has been cancelled with the justification: <br/> %(reason)s."
msgstr ""
" %(leave_name)s ha sido cancelado con la siguiente justificación: <br/> "
"%(reason)s."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "%(leave_type)s: %(duration)s (%(start)s)"
msgstr "%(leave_type)s: %(duration)s (%(start)s)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/wizard/hr_leave_allocation_generate_multi_wizard.py:0
msgid "%(name)s (%(duration)s %(request_unit)s(s))"
msgstr "%(name)s (%(duration)s %(request_unit)s)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "%(name)s (%(duration)s day(s))"
msgstr "%(name)s (%(duration)s día(s))"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "%(name)s (%(duration)s hour(s))"
msgstr "%(name)s (%(duration)s hora(s))"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_type.py:0
msgid "%(name)s (%(time)g remaining out of %(maximum)g days)"
msgstr "%(name)s (%(time)g restantes de %(maximum)g días)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_type.py:0
msgid "%(name)s (%(time)g remaining out of %(maximum)g hours)"
msgstr "%(name)s (%(time)g restantes de %(maximum)g horas)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "%(name)s: %(duration)s"
msgstr "%(name)s: %(duration)s"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "%(person)s on %(leave_type)s: %(duration)s (%(start)s)"
msgstr "%(person)s en %(leave_type)s: %(duration)s (%(start)s)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "%(person)s: %(duration)s (%(start)s)"
msgstr "%(person)s: %(duration)s (%(start)s)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan.py:0
#: code:addons/hr_holidays/models/hr_leave_type.py:0
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "%s: Time Off"
msgstr "%s: Ausencia"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&gt;"
msgstr "&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;/td&gt;"
msgstr "&lt;/td&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;/th&gt;"
msgstr "&lt;/th&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid ""
"&lt;td class=\"text-center oe_leftfit oe_rightfit\" style=\"background-"
"color:"
msgstr ""
"&lt;td class=\"text-center oe_leftfit oe_rightfit\" style=\"background-"
"color:"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;td style=background-color:"
msgstr "&lt;td style=background-color:"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;th class=\"text-center\" colspan="
msgstr "&lt;th class=\"text-center\" colspan="

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "(valid until"
msgstr "(válido hasta"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "- Valid for"
msgstr "- Válido para"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "00:00"
msgstr "00:00"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<i class=\"fa fa-check\"/> Validate"
msgstr "<i class=\"fa fa-check\"/> Validar"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\" invisible=\"allocation_type == 'accrual' or state != "
"'confirm'\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\" invisible=\"allocation_type == 'accrual' or state != "
"'confirm'\"/>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\" invisible=\"allocation_type == 'accrual'\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\" invisible=\"allocation_type == 'accrual'\"/>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_activity
msgid "<i class=\"fa fa-long-arrow-right\" title=\"to\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\" title=\"to\"/>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<i class=\"fa fa-thumbs-up\"/> Approve"
msgstr "<i class=\"fa fa-thumbs-up\"/> Aprobar"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<i class=\"fa fa-times\"/> Refuse"
msgstr "<i class=\"fa fa-times\"/> Rechazar"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
msgid ""
"<span class=\"ml8\" invisible=\"request_unit == 'hour'\">Days</span>\n"
"                            <span class=\"ml8\" invisible=\"request_unit != 'hour'\">Hours</span>"
msgstr ""
"<span class=\"ml8\" invisible=\"request_unit == 'hour'\">Días</span>\n"
"                            <span class=\"ml8\" invisible=\"request_unit != 'hour'\">Horas</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid ""
"<span class=\"ml8\" invisible=\"type_request_unit == 'hour'\">Days</span>\n"
"                                <span class=\"ml8\" invisible=\"type_request_unit != 'hour'\">Hours</span>"
msgstr ""
"<span class=\"ml8\" invisible=\"type_request_unit == 'hour'\">Días</span>\n"
"                                <span class=\"ml8\" invisible=\"type_request_unit != 'hour'\">Horas</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_public_form_view_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Back On\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            Vuelve el\n"
"                        </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.resource_calendar_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Time Off\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            Ausencias\n"
"                        </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid ""
"<span class=\"o_stat_text\">\n"
"                           Time Off\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                           Ausencias\n"
"                        </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "<span class=\"o_stat_text\">Accruals</span>"
msgstr "<span class=\"o_stat_text\">Acumulados</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "<span class=\"o_stat_text\">Allocations</span>"
msgstr "<span class=\"o_stat_text\">Asignaciones</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "<span class=\"o_stat_text\">Time Off</span>"
msgstr "<span class=\"o_stat_text\">Ausencia</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<span class=\"text-muted\"> to </span>"
msgstr "<span class=\"text-muted\"> al </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<span class=\"text-muted\">from </span>"
msgstr "<span class=\"text-muted\">del </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid ""
"<span>The employee has a different timezone than yours! Here dates and times are displayed in the employee's timezone</span>\n"
"                    ("
msgstr ""
"<span>¡El empleado está en una zona horaria diferente a la suya! Aquí se muestran la fecha y hora en la zona horaria del empleado</span>\n"
" ("

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid ""
"<span>You can only take this time off in whole days, so if your schedule has"
" half days, it won't be used efficiently.</span>"
msgstr ""
"<span>Solo puede tomar esta ausencia en días completos, si su horario tiene "
"medios días, no se podrán usar de manera eficiente.</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "<strong>Departments and Employees</strong>"
msgstr "<strong>Departamentos y empleados</strong>"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "A cancelled leave cannot be modified."
msgstr "No puede modificar un permiso que no se puede modificar."

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_action_approve_department
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_holiday_allocation_id
msgid ""
"A great way to keep track on employee’s PTOs, sick days, and approval "
"status."
msgstr ""
"Una excelente manera de realizar un seguimiento del tiempo personal pagado "
"de los empleados, los permisos por enfermedad y el estado de aprobación."

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_my
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_new_request
msgid ""
"A great way to keep track on your time off requests, sick days, and approval"
" status."
msgstr ""
"Una excelente manera de llevar el seguimiento de sus solicitudes de "
"ausencias, permisos por enfermedad y estados de aprobación."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "A time off cannot be duplicated."
msgstr "Un ausencia no puede ser duplicada."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__show_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__show_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__show_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__show_leaves
msgid "Able to see Remaining Time Off"
msgstr "Puede ver el tiempo de ausencia restante"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__time_type__leave
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
msgid "Absence"
msgstr "Ausencia"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__absence_of_today
msgid "Absence by Today"
msgstr "Ausencias por día"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
msgid ""
"Absent Employee(s), Whose time off requests are either confirmed or "
"validated on today"
msgstr ""
"Empleados ausentes, cuyas solicitudes de ausencias son confirmadas o "
"validadas hoy"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_employee_action_from_department
msgid "Absent Employees"
msgstr "Empleados ausentes"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__is_absent
msgid "Absent Today"
msgstr "Ausente hoy"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "Accrual (Future):"
msgstr "Acumulación (futuro):"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__allocation_type__accrual
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation_generate_multi_wizard__allocation_type__accrual
msgid "Accrual Allocation"
msgstr "Asignación acumulada"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Accrual Level"
msgstr "Nivel de acumulación"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_accrual_plan
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__accrual_plan_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__accrual_plan_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__accrual_plan_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Accrual Plan"
msgstr "Plan de acumulación"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_accrual_level
msgid "Accrual Plan Level"
msgstr "Nivel de plan de acumulación"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan.py:0
msgid "Accrual Plan's Employees"
msgstr "Plan de acumulación de empleados"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.open_view_accrual_plans
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_accrual_menu_configuration
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_tree
msgid "Accrual Plans"
msgstr "Planes de acumulación"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.hr_leave_allocation_cron_accrual_ir_actions_server
msgid "Accrual Time Off: Updates the number of time off"
msgstr "Ausencias acumuladas: actualiza la cantidad de ausencias"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__accrual_validity
msgid "Accrual Validity"
msgstr "Validez de la acumulación"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__accrual_validity_count
msgid "Accrual Validity Count"
msgstr "Número de acumulaciones válidas"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__accrual_validity_type
msgid "Accrual Validity Type"
msgstr "Tipo de validez de la acumulación"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__accruals_ids
msgid "Accruals"
msgstr "Acumulaciones"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__accrual_count
msgid "Accruals count"
msgstr "Número de acumulaciones"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__accrued_gain_time
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__accrued_gain_time
msgid "Accrued Gain Time"
msgstr "Tiempo de ganancia acumulado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_needaction
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__active
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__active
msgid "Active"
msgstr "Activo"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Active Allocations"
msgstr "Asignaciones activas"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__active_employee
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__active_employee
msgid "Active Employee"
msgstr "Empleado activo"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_resource_popover.xml:0
msgid "Active but on leave"
msgstr "Activo pero de vacaciones"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de actividad de excepción"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_type_icon
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icono de tipo de actvidad"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.mail_activity_type_action_config_hr_holidays
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_menu_config_activity_type
msgid "Activity Types"
msgstr "Tipos de actividad"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Add a description..."
msgstr "Añadir una descripción.."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Add a reason..."
msgstr "Añadir una razón..."

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Add some description for the people that will validate it"
msgstr "Añada una descripción para que las personas la validen"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__added_value_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__added_value_type
msgid "Added Value Type"
msgstr "Tipo de valor añadido"

#. module: hr_holidays
#: model:res.groups,name:hr_holidays.group_hr_holidays_manager
msgid "Administrator"
msgstr "Administrador"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__transition_mode__end_of_accrual
msgid "After this accrual's period"
msgstr "Después de este periodo de acumulación"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_date_from_period__pm
msgid "Afternoon"
msgstr "Tarde"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_allocation_action_all
msgid "All Allocations"
msgstr "Todas las asignaciones"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.action_hr_holidays_dashboard
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_action_approve_department
msgid "All Time Off"
msgstr "Todas las ausencias"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__action_with_unused_accruals__all
msgid "All accrued time carried over"
msgstr "Todo el tiempo acumulado traspasado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__last_several_days
msgid "All day"
msgstr "Todo el día"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "Allocated ("
msgstr "Asignado ("

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__duration_display
msgid "Allocated (Days/Hours)"
msgstr "Asignado (Días/Horas)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__allocation_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__duration
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__leave_type__allocation
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Allocation"
msgstr "Asignación"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_allocation_approval
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_status_normal_tree
msgid "Allocation Approval"
msgstr "Aprobación de asignación"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocation_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocation_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocation_display
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__allocation_display
msgid "Allocation Display"
msgstr "Visualización de asignaciones"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__allocation_mode
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__allocation_mode
msgid "Allocation Mode"
msgstr "Modo de asignación"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allocation_notif_subtype_id
msgid "Allocation Notification Subtype"
msgstr "Subtipo de notificación de asignación"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocation_remaining_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocation_remaining_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocation_remaining_display
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__allocation_remaining_display
msgid "Allocation Remaining Display"
msgstr "Mostrar asignación restante"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/wizard/hr_leave_allocation_generate_multi_wizard.py:0
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__allocation_id
#: model:mail.message.subtype,description:hr_holidays.mt_leave_allocation
#: model:mail.message.subtype,name:hr_holidays.mt_leave_allocation
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Allocation Request"
msgstr "Solicitud de asignación"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_activity
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Allocation Requests"
msgstr "Solicitudes de asignación"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_allocation_second_approval
msgid "Allocation Second Approval"
msgstr "Segunda aprobación de asignación"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__allocation_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__allocation_type
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Allocation Type"
msgstr "Tipo de asignación"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"Allocation must be confirmed \"To Approve\" or validated once \"Second "
"Approval\" in order to approve it."
msgstr ""
"La asignación debe ser confirmada (“A aprobar”) o validada una vez (“Segunda"
" aprobación”) para poder aprobarla."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "Allocation of %(leave_type)s: %(amount).2f %(unit)s to %(target)s"
msgstr "Asignación de %(leave_type)s: %(amount).2f %(unit)s a %(target)s"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__week_day
msgid "Allocation on"
msgstr "Asignado el"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"Allocation request must be confirmed, second approval or validated in order "
"to refuse it."
msgstr ""
"La solicitud de asignación debe estar confirmada, en segunda aprobación o "
"validada para poder rechazarla."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "Allocation state must be \"Refused\" in order to be reset to \"To Approve\"."
msgstr ""
"El estado de la asignación debe ser \"Rechazado\" para poder regresarla a "
"\"A aprobar\","

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__allocation_to_approve_count
msgid "Allocation to Approve"
msgstr "Asignación a aprobar"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_allocation_action_approve_department
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allocation_count
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_menu_manager_approve_allocations
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Allocations"
msgstr "Asignaciones"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allows_negative
msgid "Allow Negative Cap"
msgstr "Permitir límite negativo"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Allow To Attach Supporting Document"
msgstr "Permitir adjuntar justificantes"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__allocation_mode
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_generate_multi_wizard__allocation_mode
msgid ""
"Allow to create requests in batchs:\n"
"- By Employee: for a specific employee\n"
"- By Company: all employees of the specified company\n"
"- By Department: all employees of the specified department\n"
"- By Employee Tag: all employees of the specific employee group category"
msgstr ""
"Permite crear solicitudes en lote:\n"
"- Por empleado: para un empleado en especifico\n"
"- Por compañía: todos los empleados de la compañía especificada\n"
"- Por departamento: todos los empleados del departamento especificado\n"
"- Por etiqueta de empleado: todos los empleados de las etiquetas/categorías de empleado especificadas"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__already_accrued
msgid "Already Accrued"
msgstr "Ya acumulado"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Amount"
msgstr "Importe"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "An employee already booked time off which overlaps with this period:%s"
msgstr ""
"Un empleado ya ha reservado una ausencia que coincide con este periodo:%s"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Analyze from"
msgstr "Analizar de"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_evaluation_report_graph
msgid "Appraisal Analysis"
msgstr "Análisis de evaluaciones"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allocation_validation_type
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Approval"
msgstr "Aprobación"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/static/src/views/calendar/common/calendar_common_popover.xml:0
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Approve"
msgstr "Aprobar"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.ir_actions_server_approve_allocations
msgid "Approve Allocations"
msgstr "Aprobar asignaciones"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/report/holidays_summary_report.py:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_holidays_summary_employee__holiday_type__approved
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__validate
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form_dashboard_new_time_off
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Approved"
msgstr "Aprobado"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Approved Requests"
msgstr "Solicitudes aprobadas"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "Approved:"
msgstr "Aprobado:"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__apr
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__apr
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__apr
msgid "April"
msgstr "Abril"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
msgid "Archived"
msgstr "Archivado"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.js:0
msgid "Are you sure you want to delete this record?"
msgstr "¿Está seguro que desea eliminar ese registro?"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
msgid "Arrow"
msgstr "Flecha"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
msgid "Arrow icon"
msgstr "Icono de flecha"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_date__allocation
msgid "At the allocation date"
msgstr "En la fecha de asignación"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__accrued_gain_time__end
msgid "At the end of the accrual period"
msgstr "Al final del periodo de acumulación"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__accrued_gain_time__start
msgid "At the start of the accrual period"
msgstr "Al inicio del periodo de acumulación"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_date__year_start
msgid "At the start of the year"
msgstr "Al inicio del año"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_view_search
msgid "At work"
msgstr "En el trabajo"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__supported_attachment_ids
msgid "Attach File"
msgstr "Adjuntar archivo"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_attachment_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__attachment_ids
msgid "Attachments"
msgstr "Archivos adjuntos"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__aug
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__aug
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__aug
msgid "August"
msgstr "Agosto"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/wizard/hr_leave_generate_multi_wizard.py:0
msgid ""
"Automatic time off spliting during batch generation is not managed for ovelapping time off declared in hours. Conflicting time off:\n"
"%s"
msgstr ""
"La división automática de la ausencia durante la generación de lotes no se gestiona en caso de solapamiento del tiempo de ausencia registrado en horas. Ausencia con conflicto:\n"
"%s"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "Available"
msgstr "Disponible"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__remaining_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__remaining_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__remaining_leaves
msgid "Available Time Off Days"
msgstr "Días de ausencia disponibles"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "Available:"
msgstr "Disponible:"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/thread_icon.patch.xml:0
msgid "Away"
msgstr "Ausente"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_public_form_view_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid "Back On"
msgstr "Regresa el"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/persona_model_patch.js:0
msgid "Back on %s"
msgstr "Regresa el %s"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_balance
msgid "Balance"
msgstr "Saldo"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_dashboard.xml:0
msgid "Balance at the"
msgstr "Saldo en el"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__is_based_on_worked_time
msgid "Based on worked time"
msgstr "Según el tiempo trabajado"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_employee_base
msgid "Basic Employee"
msgstr "Empleado básico"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_holidays_summary_employee__holiday_type__both
msgid "Both Approved and Confirmed"
msgstr "Aprobados y confirmados"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation_generate_multi_wizard__allocation_mode__company
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_generate_multi_wizard__allocation_mode__company
msgid "By Company"
msgstr "Por compañía"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation_generate_multi_wizard__allocation_mode__department
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_generate_multi_wizard__allocation_mode__department
msgid "By Department"
msgstr "Por departamento"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation_generate_multi_wizard__allocation_mode__employee
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_generate_multi_wizard__allocation_mode__employee
msgid "By Employee"
msgstr "Por empleado"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation_generate_multi_wizard__allocation_mode__category
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_generate_multi_wizard__allocation_mode__category
msgid "By Employee Tag"
msgstr "Por etiqueta de empleado"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__allocation_validation_type__manager
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__manager
msgid "By Employee's Approver"
msgstr "Por aprobador de empleado"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__allocation_validation_type__both
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__both
msgid "By Employee's Approver and Time Off Officer"
msgstr "Por aprobador del empleado y por el encargado de ausencias"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__allocation_validation_type__hr
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__hr
msgid "By Time Off Officer"
msgstr "Por el encargado de ausencias"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_calendar_event
msgid "Calendar Event"
msgstr "Evento del calendario"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__can_approve
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__can_approve
msgid "Can Approve"
msgstr "Puede aprobar"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__can_cancel
msgid "Can Cancel"
msgstr "Puede cancelar"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__can_modify_value_type
msgid "Can Modify Value Type"
msgstr "Puede modificar el tipo de valor"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__can_reset
msgid "Can reset"
msgstr "Puede restablecer"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Cancel"
msgstr "Cancelar"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/static/src/views/hooks.js:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holidays_cancel_leave_form
msgid "Cancel Time Off"
msgstr "Cancelar ausencia"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_holidays_cancel_leave
msgid "Cancel Time Off Wizard"
msgstr "Asistente de cancelación de ausencias"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__cancel
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Cancelled"
msgstr "Cancelada"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Cancelled Time Off"
msgstr "Ausencia cancelada"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__cap_accrued_time
msgid "Cap accrued time"
msgstr "Limitar el tiempo acumulado"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Cap:"
msgstr "Límite:"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__carried_over_days_expiration_date
msgid "Carried over days expiration date"
msgstr "Fecha de vencimiento de los días traspasados"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Carry Over Validity"
msgstr "Validez del traspaso"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__action_with_unused_accruals
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Carry over"
msgstr "Traspasar"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__action_with_unused_accruals__maximum
msgid "Carry over with a maximum"
msgstr "Traspasar con un máximo"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Carry over:"
msgstr "Traspasar:"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Carry-Over Date"
msgstr "Fecha de traspaso"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__carryover_date
msgid "Carry-Over Time"
msgstr "Tiempo de traspaso"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__carryover_day
msgid "Carryover Day"
msgstr "Día de traspaso"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__carryover_day_display
msgid "Carryover Day Display"
msgstr "Mostrar día de traspaso"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__carryover_month
msgid "Carryover Month"
msgstr "Mes de traspaso"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_employee_base.py:0
msgid ""
"Changing this working schedule results in the affected employee(s) not "
"having enough leaves allocated to accomodate for their leaves already taken "
"in the future. Please review this employee's leaves and adjust their "
"allocation accordingly."
msgstr ""
"Cambiar este horario de trabajo puede ocasionar que los empleados afectados "
"no tengan suficientes días de ausencia asignados para el futuro. Revise los "
"días de ausencia del empleado y ajuste sus asignaciones adecuadamente. "

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__maximum_leave
msgid "Choose a cap for this accrual."
msgstr "Elija un límite para esta acumulación."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__responsible_ids
msgid ""
"Choose the Time Off Officers who will be notified to approve allocation or "
"Time Off Request. If empty, nobody will be notified"
msgstr ""
"Elija a los encargados de ausencias que recibirán notificación para aprobar "
"asignaciones o solicitudes de ausencias. Si no hay personas asignadas, no se"
" enviará una notificación."

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Click on any date or on this button to request a time-off"
msgstr ""
"Haga clic en cualquier fecha o en este botón para solicitar una ausencia"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__color
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__color
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__color
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Color"
msgstr "Color"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__employee_company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__company_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Company"
msgstr "Compañía"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_comp
msgid "Compensatory Days"
msgstr "Días compensatorios"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_configuration
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Configuration"
msgstr "Configuración"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.js:0
msgid "Confirmation"
msgstr "Confirmación"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/report/holidays_summary_report.py:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_holidays_summary_employee__holiday_type__confirmed
msgid "Confirmed"
msgstr "Confirmado"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/report/holidays_summary_report.py:0
msgid "Confirmed and Approved"
msgstr "Confirmado y aprobado"

#. module: hr_holidays
#: model_terms:web_tour.tour,rainbow_man_message:hr_holidays.hr_holidays_tour
msgid "Congrats, we can see that your request has been validated."
msgstr "Enhorabuena, vemos que su solicitud ha sido validada."

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid ""
"Count of allocations for this time off type (approved or waiting for "
"approbation) with a validity period starting this year."
msgstr ""
"Número de asignaciones para este tipo de ausencia (aprobado o en espera de "
"aprobación) con un periodo de validez que empieza este año."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Count of plans linked to this time off type."
msgstr "Número de planes vinculados a este tipo de ausencia."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid ""
"Count of time off requests for this time off type (approved or waiting for "
"approbation) with a start date in the current year."
msgstr ""
"Número de solicitudes de ausencia para este tipo de ausencia (aprobado o en "
"espera de aprobación) con una fecha de inicio en el año actual."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__country_id
msgid "Country"
msgstr "País"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__country_code
msgid "Country Code"
msgstr "Código de país"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__icon_id
msgid "Cover Image"
msgstr "Imagen de portada"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
msgid "Create Allocations"
msgstr "Crear asignaciones"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_approve_department
msgid "Create a new time off allocation"
msgstr "Crear una nueva asignación de ausencias"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_all
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_my
msgid "Create a new time off allocation request"
msgstr "Crear una nueva solicitud de asignación de ausencia"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__create_date
msgid "Created on"
msgstr "Creado el"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__current_leave_state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__current_leave_state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__current_leave_state
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__current_leave_state
msgid "Current Time Off Status"
msgstr "Estado de ausencias actual"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__current_leave_id
msgid "Current Time Off Type"
msgstr "Tipo de ausencia actual"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Current Year"
msgstr "Año actual"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Currently Valid"
msgstr "Válido actualmente"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_unit_hours
msgid "Custom Hours"
msgstr "Horas personalizadas"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__daily
msgid "Daily"
msgstr "Diariamente"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_new_request
#: model:ir.ui.menu,name:hr_holidays.hr_leave_menu_new_request
msgid "Dashboard"
msgstr "Tablero"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Date"
msgstr "Fecha"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_date_from_period
msgid "Date Period Start"
msgstr "Fecha de inicio del periodo "

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__lastcall
msgid "Date of the last accrual allocation"
msgstr "Fecha de la última asignación de acumulación"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__nextcall
msgid "Date of the next accrual allocation"
msgstr "Fecha de la siguiente asignación de acumulación"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Dates"
msgstr "Fechas"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__type_request_unit__day
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__request_unit__day
msgid "Day"
msgstr "Día"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__accrual_validity_type__day
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__added_value_type__day
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__start_type__day
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__added_value_type__day
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid "Days"
msgstr "Días"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__dec
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__dec
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__dec
msgid "December"
msgstr "Diciembre"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__max_allowed_negative
msgid ""
"Define the maximum level of negative days this kind of time off can reach. "
"Value must be at least 1."
msgstr ""
"Defina el nivel máximo de días negativos que este tipo de ausencia puede "
"alcanzar. El valor debe ser de al menos 1. "

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "Delete"
msgstr "Eliminar"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_department
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__department_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Department"
msgstr "Departamento"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Department search"
msgstr "Búsqueda de departamento"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__department_ids
msgid "Departments"
msgstr "Departamentos"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Asistente de salida"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__description
msgid "Description"
msgstr "Descripción"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__name_validity
msgid "Description with validity"
msgstr "Descripción con validez"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holidays_cancel_leave_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_generate_multi_wizard_view_form
msgid "Discard"
msgstr "Descartar"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Display Option"
msgstr "Mostrar opción"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__create_calendar_meeting
msgid "Display Time Off in Calendar"
msgstr "Mostrar ausencias en el calendario"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/resource.py:0
msgid ""
"Due to a change in global time offs, %s extra day(s) have been taken from "
"your allocation. Please review this leave if you need it to be changed."
msgstr ""
"Debido a un cambio de la ausencia global, se han tomado %s día(s) extra(s) "
"de su asignación. Por favor, revise este permiso si necesita que se "
"modifique."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/resource.py:0
msgid ""
"Due to a change in global time offs, this leave no longer has the required "
"amount of available allocation and has been set to refused. Please review "
"this leave."
msgstr ""
"Debido a un cambio de las ausencias globales, este día de ausencia ya no "
"tiene la cantidad requerida de asignaciones y se ha rechazado. Revise este "
"día de ausencia."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/resource.py:0
msgid ""
"Due to a change in global time offs, you have been granted %s day(s) back."
msgstr ""
"Debido a un cambio en las ausencias globales, le devolvemos %s día(s)."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__duration
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Duration"
msgstr "Duración"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_days
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_graph
msgid "Duration (Days)"
msgstr "Duración (días)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_hours
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_graph
msgid "Duration (Hours)"
msgstr "Duración (horas)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_of_days_display
msgid "Duration (days)"
msgstr "Duración (días)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_of_hours_display
msgid "Duration (hours)"
msgstr "Duración (horas)"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__number_of_days
msgid "Duration in days. Reference field to use when necessary."
msgstr ""
"Duración en días. Campo de referencia a utilizar cuando sea necesario."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
msgid "Edit Allocation"
msgstr "Editar asignación"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "Edit Time Off"
msgstr "Editar ausencia"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_employee
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__employee_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Employee"
msgstr "Empleado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__active_employee
msgid "Employee Active"
msgstr "Empleado activo"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__employee_company_id
msgid "Employee Company"
msgstr "Compañía del empleado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__employee_requests
msgid "Employee Requests"
msgstr "Solicitudes de empleados"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__category_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__category_id
msgid "Employee Tag"
msgstr "Etiqueta del empleado"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Employee accrue"
msgstr "Acumulación del empleado"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban_approve_department
msgid "Employee's image"
msgstr "Foto del empleado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__emp
msgid "Employee(s)"
msgstr "Empleado(s)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__employees_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__employee_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__employee_ids
msgid "Employees"
msgstr "Empleados"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Employees Off Today"
msgstr "Empleados no disponibles hoy"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__end_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__date_to
msgid "End Date"
msgstr "Fecha de finalización"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__employee_requests__yes
msgid "Extra Days Requests Allowed"
msgstr "Solicitudes de días adicionales permitidas"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__employee_requests
msgid ""
"Extra Days Requests Allowed: User can request an allocation for himself.\n"
"\n"
"        Not Allowed: User cannot request an allocation."
msgstr ""
"Se permiten solicitudes de días adicionales: el usuario puede solicitar una asignación para sí mismo.\n"
"\n"
"        No permitido: el usuario no puede solicitar asignaciones."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__feb
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__feb
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__feb
msgid "February"
msgstr "Febrero"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__duration_display
msgid ""
"Field allowing to see the allocation duration in days or hours depending on "
"the type_request_unit"
msgstr ""
"Campo que permite ver la duración de la asignación en días u horas "
"dependiendo de type_request_unit"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__duration_display
msgid ""
"Field allowing to see the leave request duration in days or hours depending "
"on the leave_type_request_unit"
msgstr ""
"El campo que permite ver la duración de la solicitud de ausencia en días u "
"horas dependiendo de leave_type_request_unit"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__first_approver_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__approver_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "First Approval"
msgstr "Primera aprobación"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_day
msgid "First Day"
msgstr "Primer día"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_day_display
msgid "First Day Display"
msgstr "Pantalla del primer día"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_month
msgid "First Month"
msgstr "Primer mes"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_month_day
msgid "First Month Day"
msgstr "Primer mes y día"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_month_day_display
msgid "First Month Day Display"
msgstr "Pantalla del primer mes y día"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_follower_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_partner_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Contactos)"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_type_icon
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome p. ej. fa-tasks"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__number_of_days_display
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__number_of_hours_display
msgid ""
"For an Accrual Allocation, this field contains the theorical amount of time "
"given to the employee, due to a previous start date, on the first run of the"
" plan. This can be manually edited."
msgstr ""
"Para una asignación acumulada, este campo contiene la cantidad teórica del "
"tiempo que se le da al empleado debido a una fecha de inicio anterior la "
"primera vez que se ejecutó el plan. Esto se puede editar manualmente."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__frequency
msgid "Frequency"
msgstr "Frecuencia"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__fri
msgid "Friday"
msgstr "Viernes"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__start_datetime
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "From"
msgstr "Desde"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leave_date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leave_date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leave_date_from
msgid "From Date"
msgstr "Desde la fecha"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Future Activities"
msgstr "Actividades futuras"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_generate_multi_wizard_view_form
msgid "Generate Time Off"
msgstr "Generar ausencia"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_allocation_generate_multi_wizard
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
msgid "Generate time off allocations for multiple employees"
msgstr "Generar asignaciones de ausencia para varios empleados"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_generate_multi_wizard
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_generate_multi_wizard_view_form
msgid "Generate time off for multiple employees"
msgstr "Generar ausencia para varios empleados"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/wizard/hr_leave_allocation_generate_multi_wizard.py:0
msgid "Generated Allocations"
msgstr "Asignaciones generadas"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/wizard/hr_leave_generate_multi_wizard.py:0
msgid "Generated Time Off"
msgstr "Ausencia generada"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_dashboard.xml:0
msgid "Grant Time"
msgstr "Otorgar tiempo"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Group By"
msgstr "Agrupar por"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__group_days_leave
msgid "Group Time Off"
msgstr "Ausencia grupal"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.action_hr_approval
msgid "HR Approval"
msgstr "Aprobación por RR. HH."

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_holidays_summary_employee
msgid "HR Time Off Summary Report By Employee"
msgstr "Informe resumido de tiempo personal de RR. HH. por empleado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_unit_half
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__type_request_unit__half_day
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__request_unit__half_day
msgid "Half Day"
msgstr "Medio día"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__has_department_manager_access
msgid "Has Department Manager Access"
msgstr "Tiene acceso de gerente al departamento"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__has_mandatory_day
msgid "Has Mandatory Day"
msgstr "Tiene día obligatorio"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__has_message
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__has_valid_allocation
msgid "Has Valid Allocation"
msgstr "Tiene una asignación válida"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__is_hatched
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__is_hatched
msgid "Hatched"
msgstr "Rayado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__holiday_status
msgid "Holiday Status"
msgstr "Estado de vacaciones"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_report_hr_holidays_report_holidayssummary
msgid "Holidays Summary Report"
msgstr "Informe de resumen de días festivos"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_hour_from
msgid "Hour from"
msgstr "Hora desde"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_hour_to
msgid "Hour to"
msgstr "Hora hasta"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__hourly
msgid "Hourly"
msgstr "Por horas"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__added_value_type__hour
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__added_value_type__hour
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__type_request_unit__hour
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__request_unit__hour
msgid "Hours"
msgstr "Horas"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__hr_icon_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__hr_icon_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__hr_icon_display
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__hr_icon_display
msgid "Hr Icon Display"
msgstr "Visualización del icono RR. HH."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__id
msgid "ID"
msgstr "ID"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_exception_icon
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_exception_icon
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar una actividad de excepción."

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/avatar_card_resource_popover.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
msgid "Idle"
msgstr "Inactivo"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_needaction
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si está marcada, hay nuevos mensajes que requieren su atención."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_has_error
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_has_sms_error
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_has_error
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si está marcada, algunos mensajes tienen error de envío."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_plan__is_based_on_worked_time
msgid ""
"If checked, the accrual period will be calculated according to the work "
"days, not calendar days."
msgstr ""
"Si está seleccionado, el periodo de acumulación se calculará de acuerdo a "
"los días de trabajo, no a los días de calendario."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__allows_negative
msgid ""
"If checked, users request can exceed the allocated days and balance can go "
"in negative."
msgstr ""
"Si está seleccionado, las solicitudes de los usuarios pueden exceder los "
"días acumulados y el saldo puede cambiar a negativo."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__active_employee
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__active_employee
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Si el campo activo es False, le permitirá ocultar el registro del recurso "
"sin eliminarlo."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__active
msgid ""
"If the active field is set to false, it will allow you to hide the time off "
"type without removing it."
msgstr ""
"Si el campo activo se establece en False, le permitirá ocultar el tipo de "
"ausencias sin eliminarlo."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_duration_check
msgid ""
"If you want to change the number of days you should use the 'period' mode"
msgstr "Si desea cambiar la cantidad de días, debe usar el modo 'periodo'"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__transition_mode__immediately
msgid "Immediately"
msgstr "Inmediatamente"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "Incorrect state for new allocation"
msgstr "Estado incorrecto para la nueva asignación"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_is_follower
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__is_name_custom
msgid "Is Name Custom"
msgstr "¿Es un nombre personalizado?"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__is_officer
msgid "Is Officer"
msgstr "Es encargado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__unpaid
msgid "Is Unpaid"
msgstr "No está pagada"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__jan
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__jan
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__jan
msgid "January"
msgstr "Enero"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__job_id
msgid "Job"
msgstr "Trabajo"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Job Position"
msgstr "Puesto de trabajo"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__jul
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__jul
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__jul
msgid "July"
msgstr "Julio"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__jun
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__jun
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__jun
msgid "June"
msgstr "Junio"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_my
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_new_request
msgid "Keep track of your PTOs."
msgstr "Lleva el registro de sus ausencias pagadas."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__time_type
msgid "Kind of Time Off"
msgstr "Tipo de ausencia"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Late Activities"
msgstr "Actividades atrasadas"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__leave_id
msgid "Leave"
msgstr "Abandonar"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__leave_type_increases_duration
msgid "Leave Type Increases Duration"
msgstr "El tipo de permiso incrementa la duración"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__holiday_status__left
msgid "Left"
msgstr "Restante"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
msgid "Legend"
msgstr "Leyenda"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Let's approve it"
msgstr "Vamos a aprobarlo"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Let's discover the Time Off application"
msgstr "Descubramos la aplicación de Ausencias"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Let's go validate it"
msgstr "Vamos a validarlo"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Let's try to create a Sick Time Off, select it in the list"
msgstr "Probemos crear una ausencia por enfermedad, selecciónelo en la lista"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__level_count
msgid "Levels"
msgstr "Niveles"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__maximum_leave
msgid "Limit to"
msgstr "Limitar a"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_main_attachment_id
msgid "Main Attachment"
msgstr "Archivo adjunto principal"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_management
msgid "Management"
msgstr "Administración"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__is_manager
msgid "Manager"
msgstr "Gerente"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.action_manager_approval
msgid "Manager Approval"
msgstr "Aprobación del gerente"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_mandatory_day
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
msgid "Mandatory Day"
msgstr "Día obligatorio"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_mandatory_day_action
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_mandatory_day_menu_configuration
msgid "Mandatory Days"
msgstr "Días obligatorios"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__mar
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__mar
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__mar
msgid "March"
msgstr "Marzo"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "Mark as ready to approve"
msgstr "Marcar como listo para aprobar"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__max_leaves
msgid "Max Leaves"
msgstr "Máximo de permisos"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holiday_status_view_kanban
msgid "Max Time Off:"
msgstr "Máximo de ausencias:"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__max_leaves
msgid "Maximum Allowed"
msgstr "Máximo permitido"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__max_allowed_negative
msgid "Maximum Excess Amount"
msgstr "Cantidad máxima de exceso"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__virtual_remaining_leaves
msgid ""
"Maximum Time Off Allowed - Time Off Already Taken - Time Off Waiting "
"Approval"
msgstr ""
"Tiempo personal máximo permitido - Tiempo de ausencia ya tomado - Tiempo de "
"ausencia en espera de aprobación"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__postpone_max_days
msgid "Maximum amount of accruals to transfer"
msgstr "Importe máximo de acumulaciones a transferir"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__may
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__may
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__may
msgid "May"
msgstr "Mayo"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_action_approve_department
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_holiday_allocation_id
msgid "Meet the time off dashboard."
msgstr "Conozca el tablero de ausencias."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__meeting_id
msgid "Meeting"
msgstr "Reunión"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_has_error
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_has_error
msgid "Message Delivery error"
msgstr "Error de envío de mensaje"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_mail_message_subtype
msgid "Message subtypes"
msgstr "Subtipos de mensaje"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__level_ids
msgid "Milestone"
msgstr "Hito"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__transition_mode
msgid "Milestone Transition"
msgstr "Transición del hito"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__cap_accrued_time_yearly
msgid "Milestone cap"
msgstr "Límite de hitos"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_generate_multi_wizard_view_form
msgid "Mode"
msgstr "Modo"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__mon
msgid "Monday"
msgstr "Lunes"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Month"
msgstr "Mes"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__monthly
msgid "Monthly"
msgstr "Mensual"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__accrual_validity_type__month
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__start_type__month
msgid "Months"
msgstr "Meses"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_date_from_period__am
msgid "Morning"
msgstr "Mañana"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/components/multi_time_off_generation_menu/multiple_time_off_generation_menu.xml:0
#: model:ir.actions.act_window,name:hr_holidays.action_hr_leave_allocation_generate_multi_wizard
#: model:ir.actions.act_window,name:hr_holidays.action_hr_leave_generate_multi_wizard
msgid "Multiple Requests"
msgstr "Múltiples solicitudes"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Fecha límite de mi actividad"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_allocation_action_my
#: model:ir.ui.menu,name:hr_holidays.menu_open_allocation
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "My Allocations"
msgstr "Mis Asignaciones"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "My Department"
msgstr "Mi departamento"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "My Requests"
msgstr "Mis solicitudes"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "My Team"
msgstr "Mi equipo"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_my_leaves
msgid "My Time"
msgstr "Mi tiempo"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_my
#: model:ir.ui.menu,name:hr_holidays.hr_leave_menu_my
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "My Time Off"
msgstr "Mis ausencias"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__name
#: model_terms:ir.ui.view,arch_db:hr_holidays.resource_calendar_leaves_tree_inherit
msgid "Name"
msgstr "Nombre"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Negative Cap"
msgstr "Límite negativo"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.xml:0
msgid "New"
msgstr "Nuevo"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "New %(leave_type)s Request created by %(user)s"
msgstr "%(user)s creó una nueva solicitud de %(leave_type)s"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/hooks.js:0
msgid "New Allocation"
msgstr "Nueva asignación"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_dashboard.xml:0
msgid "New Allocation Request"
msgstr "Nueva solicitud de asignación"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"New Allocation Request created by %(user)s: %(count)s Days of "
"%(allocation_type)s"
msgstr ""
"Nueva solicitud de asignación creada por %(user)s: %(count)s días de "
"%(allocation_type)s"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "New Milestone"
msgstr "Nuevo hito"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.js:0
msgid "New Time Off"
msgstr "Nueva ausencia"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Siguiente evento en el calendario de actividades."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_date_deadline
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Fecha límite de la siguiente actividad"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_summary
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_type_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo de la siguiente actividad"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__requires_allocation__no
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "No Limit"
msgstr "Sin límite"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__allocation_validation_type__no_validation
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__no_validation
msgid "No Validation"
msgstr "Sin validación"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.action_hr_available_holidays_report
#: model_terms:ir.actions.act_window,help:hr_holidays.action_hr_leave_report
#: model_terms:ir.actions.act_window,help:hr_holidays.mail_activity_type_action_config_hr_holidays
msgid "No data to display"
msgstr "No hay datos para mostrar"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_report_action
msgid "No data yet!"
msgstr "¡Todavía no hay información!!"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "No limit"
msgstr "Sin límite"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "No rule has been set up for this accrual plan."
msgstr "No se ha establecido ninguna regla para este plan de acumulación."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Nobody will be notified"
msgstr "Nadie recibirá una notificación"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__show_on_dashboard
msgid ""
"Non-visible allocations can still be selected when taking a leave, but will "
"simply not be displayed on the leave dashboard."
msgstr ""
"Es posible seleccionar las asignaciones no visibles al pedir un permiso, "
"pero no se mostrarán en el tablero del permiso"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
msgid "None"
msgstr "Ninguno"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__action_with_unused_accruals__lost
msgid "None. Accrued time reset to 0"
msgstr "Ninguno. El tiempo acumulado reestablecido a 0"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__employee_requests__no
msgid "Not Allowed"
msgstr "No permitido"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__responsible_ids
msgid "Notified Time Off Officer"
msgstr "Encargado de ausencias notificado"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__nov
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__nov
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__nov
msgid "November"
msgstr "Noviembre"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_needaction_counter
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_of_days
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__number_of_days
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__number_of_days
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_list
msgid "Number of Days"
msgstr "Número de días"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__number_of_hours
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__number_of_hours
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_tree
msgid "Number of Hours"
msgstr "Número de horas"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leaves_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leaves_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leaves_count
msgid "Number of Time Off"
msgstr "Número de ausencias"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__number_of_days
msgid "Number of days of the time off request. Used in the calculation."
msgstr "Número de días de la solicitud de ausencia. Se usa en el cálculo. "

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_has_error_counter
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__number_of_hours
msgid "Number of hours of the time off request. Used in the calculation."
msgstr "Número de horas de la solicitud de ausencia. Se usa en el cálculo. "

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_needaction_counter
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren una acción"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_has_error_counter
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__oct
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__oct
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__oct
msgid "October"
msgstr "Octubre"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Off Today"
msgstr "Ausente hoy"

#. module: hr_holidays
#: model:res.groups,name:hr_holidays.group_hr_holidays_user
msgid "Officer: Manage all requests"
msgstr "Encargado: gestionar todas las solicitudes"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_resource_popover.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_view_search
msgid "On Time Off"
msgstr "Ausente"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__hr_icon_display__presence_holiday_absent
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__hr_icon_display__presence_holiday_absent
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__hr_icon_display__presence_holiday_absent
msgid "On leave"
msgstr "Con permiso"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/avatar_card_resource_popover.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
#: code:addons/hr_holidays/static/src/thread_icon.patch.xml:0
msgid "Online"
msgstr "En línea"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "Only"
msgstr "Solo"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"Only %s's Time Off Approver, a time off Officer/Responsible or Administrator"
" can approve or refuse allocation requests."
msgstr ""
"Solo un aprobador de ausencias de %s, un encargado/responsable o "
"administrador de ausencias puede aprobar o rechazar solicitudes de "
"asignación."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Only a Time Off Manager can reset a refused leave."
msgstr "Solo un gerente de ausencias puede restablecer un permiso rechazado."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Only a Time Off Manager can reset a started leave."
msgstr ""
"Solo un gerente de ausencias puede restablecer un permiso ya iniciado."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Only a Time Off Manager can reset other people leaves."
msgstr ""
"Solo un gerente de ausencias puede restablecer los permisos de otras "
"personas."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"Only a Time Off Officer or Manager can approve/refuse its own requests."
msgstr ""
"Solo un gerente de ausencias puede aprobar o rechazar sus propias "
"solicitudes."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Only a manager can modify a canceled leave."
msgstr "Solo un gerente puede modificar un permiso cancelado"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "Only a time off Administrator can approve their own requests."
msgstr ""
"Solo un administrador de ausencias puede aprobar sus propias solicitudes."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"Only a time off Officer/Responsible or Administrator can approve or refuse "
"allocation requests."
msgstr ""
"Solo un encargado/responsable o administrador de ausencias puede aprobar o "
"rechazar solicitudes de asignación."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_employee_base.py:0
msgid "Operation not supported"
msgstr "Operación no admitida"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_date__other
msgid "Other"
msgstr "Otro"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/avatar_card_resource_popover.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
#: code:addons/hr_holidays/static/src/thread_icon.patch.xml:0
msgid "Out of office"
msgstr "Fuera de la oficina"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_dashboard
msgid "Overview"
msgstr "Vista general"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_cl
msgid "Paid Time Off"
msgstr "Ausencia pagada"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.hr_holiday_status_dv
msgid "Parental Leaves"
msgstr "Permisos paternales"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_dashboard.xml:0
msgid "Pending Requests"
msgstr "Solicitudes pendientes"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_mandatory_day_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Period"
msgstr "Periodo"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__holiday_status__planned
msgid "Planned"
msgstr "Planificado"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "Planned:"
msgstr "Planificado:"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__hr_icon_display__presence_holiday_present
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__hr_icon_display__presence_holiday_present
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__hr_icon_display__presence_holiday_present
msgid "Present but on leave"
msgstr "Presente pero de vacaciones"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Print"
msgstr "Imprimir"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holidays_cancel_leave_form
msgid "Provide a reason to cancel an approved time off"
msgstr ""
"Proporcione una razón para cancelar una solicitud de ausencia aprobada"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.resource_calendar_form_inherit
msgid "Public"
msgstr "Público"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__include_public_holidays_in_duration
msgid "Public Holiday Included"
msgstr "Día festivo incluido"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: model:ir.actions.act_window,name:hr_holidays.open_view_public_holiday
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_public_time_off_menu_configuration
msgid "Public Holidays"
msgstr "Días festivos"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__include_public_holidays_in_duration
msgid ""
"Public holidays should be counted in the leave duration when applying for "
"leaves"
msgstr ""
"Los días festivos se deben tomar en cuenta en la duración de los permisos al"
" solicitar permisos."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__added_value
msgid "Rate"
msgstr "Tasa"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__rating_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__rating_ids
msgid "Ratings"
msgstr "Calificaciones"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__reason
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Reason"
msgstr "Razón"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__notes
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__notes
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__notes
msgid "Reasons"
msgstr "Razones"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/static/src/views/calendar/common/calendar_common_popover.xml:0
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Refuse"
msgstr "Rechazar"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__refuse
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form_dashboard_new_time_off
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Refused"
msgstr "Rechazado"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Refused Time Off"
msgstr "Ausencia rechazada"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__allocation_type__regular
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation_generate_multi_wizard__allocation_type__regular
msgid "Regular Allocation"
msgstr "Asignación regular"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_tree
msgid "Remaining Days"
msgstr "Días restantes"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_tree
msgid "Remaining Hours"
msgstr "Horas restantes"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid "Remaining leaves"
msgstr "Permisos restantes"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_report
msgid "Reporting"
msgstr "Informes"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
msgid "Request Allocation"
msgstr "Solicitar asignación"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_date_to
msgid "Request End Date"
msgstr "Fecha de finalización de la solicitud"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_date_from
msgid "Request Start Date"
msgstr "Fecha de inicio de la solicitud"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
msgid "Request Time off"
msgstr "Solicitar ausencia"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__leave_type
msgid "Request Type"
msgstr "Tipo de solicitud"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__duration_display
msgid "Requested (Days/Hours)"
msgstr "Solicitado (Días/Horas)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__requires_allocation
msgid "Requires allocation"
msgstr "Necesita asignación"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Reset"
msgstr "Restablecer"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__resource_calendar_id
msgid "Resource Calendar"
msgstr "Calendario de recursos"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.resource_calendar_global_leaves_action_from_calendar
msgid "Resource Time Off"
msgstr "Ausencia de recurso"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "Detalle de las ausencias del recurso"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_resource_calendar
msgid "Resource Working Time"
msgstr "Tiempo de trabajo de recursos"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_user_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Rules"
msgstr "Reglas"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Run until"
msgstr "Ejecutar hasta"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_has_sms_error
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de envío del SMS"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__sat
msgid "Saturday"
msgstr "Sábado"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
msgid "Save"
msgstr "Guardar"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Search Time Off"
msgstr "Buscar ausencias"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
msgid "Search Time Off Type"
msgstr "Buscar tipo de ausencia"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Search allocations"
msgstr "Buscar asignaciones"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__second_approver_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__second_approver_id
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__validate1
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Second Approval"
msgstr "Segunda aprobación"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_day
msgid "Second Day"
msgstr "Segundo día"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_day_display
msgid "Second Day Display"
msgstr "Pantalla del segundo día"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_month
msgid "Second Month"
msgstr "Segundo mes"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_month_day
msgid "Second Month Day"
msgstr "Segundo mes y día"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_month_day_display
msgid "Second Month Day Display"
msgstr "Pantalla del segundo mes y día"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "Second approval request for %(allocation_type)s"
msgstr "Solicitud de segunda aprobación de %(allocation_type)s"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Second approval request for %(leave_type)s"
msgstr "Segunda solicitud de aprobación de %(leave_type)s"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Select Time Off"
msgstr "Seleccionar ausencia"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__holiday_type
msgid "Select Time Off Type"
msgstr "Seleccionar el tipo de ausencia"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__validation_type
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__allocation_validation_type
msgid ""
"Select the level of approval needed in case of request by employee\n"
"            #     - No validation needed: The employee's request is automatically approved.\n"
"            #     - Approved by Time Off Officer: The employee's request need to be manually approved\n"
"            #       by the Time Off Officer, Employee's Approver or both."
msgstr ""
" Seleccione el nivel de aprobación necesario en caso de solicitudes de empleado\n"
"            #     - No se requiere validación: la solicitud del empleado se aprueba automáticamente.\n"
"            #     - Aprobado por el encargado de ausencias: el encargado de ausencias\n"
"            #       debe aprobar la solicitud del empleado manualmente."

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Select the request you just created"
msgstr "Seleccione la solicitud que acaba de crear"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_employee__leave_manager_id
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_base__leave_manager_id
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_public__leave_manager_id
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_report_calendar__leave_manager_id
#: model:ir.model.fields,help:hr_holidays.field_res_users__leave_manager_id
msgid ""
"Select the user responsible for approving \"Time Off\" of this employee.\n"
"If empty, the approval is done by an Administrator or Approver (determined in settings/users)."
msgstr ""
"Selecciona el usuario responsable de aprobar \"ausencias\" de este empleado.\n"
"Si se queda vacío, la aprobación la realiza un administrador o un aprobador (determinado en ajustes/usuarios)."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__sep
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__sep
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__carryover_month__sep
msgid "September"
msgstr "Septiembre"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__sequence
msgid "Sequence is generated automatically by start time delta."
msgstr "La secuencia se genera automáticamente al empezar delta de tiempo."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__postpone_max_days
msgid "Set a maximum of accruals an allocation keeps at the end of the year."
msgstr ""
"Establezca un máximo de acumulaciones que una asignación puede tener al "
"final del año."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__show_on_dashboard
msgid "Show On Dashboard"
msgstr "Mostrar en el tablero"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__show_transition_mode
msgid "Show Transition Mode"
msgstr "Mostrar modo de transición"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Mostrar todos los registros que tienen la próxima fecha de acción antes de "
"hoy"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_sl
#: model:mail.message.subtype,description:hr_holidays.mt_leave_sick
#: model:mail.message.subtype,name:hr_holidays.mt_leave_sick
msgid "Sick Time Off"
msgstr "Ausencias por enfermedad"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "Some leaves cannot be linked to any allocation. To see those leaves,"
msgstr ""
"No es posible vincular algunos a alguna asignación. Para consultarlos "

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan.py:0
msgid ""
"Some of the accrual plans you're trying to delete are linked to an existing "
"allocation. Delete or cancel them first."
msgstr ""
"Algunos de los planes de acumulación que intenta eliminar están vinculados a"
" una asignación existente. Primero elimínelos o cancélelos."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_plan__time_off_type_id
msgid ""
"Specify if this accrual plan can only be used with this Time Off Type.\n"
"                Leave empty if this accrual plan can be used with any Time Off Type."
msgstr ""
"Especifique si este plan de acumulación solo se puede utilizar con este tipo de ausencia.\n"
"                Deje vacío si este plan de acumulación solo se puede usar con cualquier tipo."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_plan__transition_mode
msgid ""
"Specify what occurs if a level transition takes place in the middle of a pay period.\n"
"\n"
"                'Immediately' will switch the employee to the new accrual level on the exact date during the ongoing pay period.\n"
"\n"
"                'After this accrual's period' will keep the employee on the same accrual level until the ongoing pay period is complete.\n"
"                After it is complete, the new level will take effect when the next pay period begins."
msgstr ""
"Especifique qué ocurre si se produce una transición de nivel en mitad de un periodo de pago.\n"
"\n"
"                \"De inmediato\" cambiará al empleado al nuevo nivel de acumulación en la fecha exacta durante el periodo de pago en curso.\n"
"\n"
"                \"Después del periodo de acumulación\" mantendrá al empleado en el mismo nivel de asignación hasta que el periodo de pago previsto se efectúe.\n"
"                Después de llevarse a cabo, el nuevo nivel será efectivo cuando comience el siguiente periodo de pago."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Start Accruing"
msgstr "Empezar a acumular"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__start_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__date_from
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__start_count
msgid "Start after"
msgstr "Empezar después"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__state
msgid "State"
msgstr "Estado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__state
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Status"
msgstr "Estado"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_state
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado basado en actividades\n"
"Vencida: la fecha límite ya ha pasado\n"
"Hoy: la fecha límite es hoy\n"
"Planificada: actividades futuras."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__is_striked
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__is_striked
msgid "Striked"
msgstr "Tachado"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid "Submit your request"
msgstr "Envíe su solicitud"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Sum"
msgstr "Suma"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__sun
msgid "Sunday"
msgstr "Domingo"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__supported_attachment_ids_count
msgid "Supported Attachment Ids Count"
msgstr "Supported Attachment Ids Count"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__leave_type_support_document
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__support_document
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Supporting Document"
msgstr "Documentos de apoyo"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Supporting Documents"
msgstr "Documentos de apoyo"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__leave_type_request_unit
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__request_unit
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__request_unit
msgid "Take Time Off in"
msgstr "Coger ausencia en"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__holiday_status__taken
msgid "Taken"
msgstr "Tomado"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"EL código ISO del país en dos caracteres.\n"
"Puede utilizar este campo para una búsqueda rápida."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"The Start Date of the Validity Period must be anterior to the End Date."
msgstr ""
"La fecha de inicio del periodo de validez debe ser anterior a la fecha de "
"finalización."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__start_count
msgid ""
"The accrual starts after a defined period from the allocation start date. "
"This field defines the number of days, months or years after which accrual "
"is used."
msgstr ""
"La acumulación comienza después de un periodo definido desde la fecha de "
"inicio de la asignación. Este campo define el número de días, meses o años "
"después de los cuales se utiliza la acumulación."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_type.py:0
msgid ""
"The allocation requirement of a time off type cannot be changed once leaves "
"of that type have been taken. You should create a new time off type instead."
msgstr ""
"La asignación de un tipo de ausencia no puede modificarse una vez que se han"
" tomado permisos de este tipo. En su lugar, deberá crear un nuevo tipo de "
"ausencia."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__color
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__color
msgid ""
"The color selected here will be used in every screen with the time off type."
msgstr ""
"El color seleccionado aquí se utilizará en cada pantalla con el tipo de "
"ausencia."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_accrual_level_check_dates
msgid "The dates you've set up aren't correct. Please check them."
msgstr "Las fechas que estableció no son correctas. Compruébelas."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__time_type
msgid ""
"The distinction between working time (ex. Attendance) and absence (ex. "
"Training) will be used in the computation of Accrual's plan rate."
msgstr ""
"La distinción entre tiempo trabajado (p. ej. asistencia) y ausencia (p. ej. "
"formación) se utilizará en el cálculo de la tasa del plan de acumulación."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_allocation_duration_check
msgid "The duration must be greater than 0."
msgstr "La duración debe ser mayor que 0."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/wizard/hr_departure_wizard.py:0
msgid "The employee no longer works in the company"
msgstr "El empleado ya no trabaja en la compañía"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"The following employees are not supposed to work during that period:\n"
" %s"
msgstr ""
"Los siguientes empleados no deben trabajar durante ese periodo:\n"
" %s"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid ""
"The leaves planned in the future are exceeding the maximum value of the allocation.\n"
"                It will not be possible to take all of them."
msgstr ""
"Los días de ausencia planificados en el futuro exceden el valor máximo de la asignación.\n"
"                No será posible tomarlos todos."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_type_check_negative
msgid ""
"The maximum excess amount should be greater than 0. If you want to set 0, "
"disable the negative cap instead."
msgstr ""
"La cantidad máxima de exceso debe ser superior a 0. Desactive el límite "
"negativo si quiere que sea 0."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__expiring_carryover_days
msgid ""
"The number of carried over days that will expire on "
"carried_over_days_expiration_date"
msgstr ""
"El número de días a traspasar que vencerán en "
"carried_over_days_expiration_date"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__added_value
msgid ""
"The number of hours/days that will be incremented in the specified Time Off "
"Type for every period"
msgstr ""
"El número de horas/días que se incrementarán en el tipo de ausencia "
"especificado para cada periodo"

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_date_check3
msgid ""
"The request start date must be before or equal to the request end date."
msgstr ""
"La fecha de inicio de la solicitud debe ser antes de la fecha de "
"finalización de la solicitud o la misma fecha."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_mandatory_day_date_from_after_day_to
msgid "The start date must be anterior than the end date."
msgstr "La fecha de inicio debe ser anterior a la fecha de finalización."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_date_check2
msgid "The start date must be before or equal to the end date."
msgstr ""
"La fecha de inicio debe ser anterior o igual a la fecha de finalización."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__state
msgid ""
"The status is 'To Approve', when an allocation request is created.\n"
"The status is 'Refused', when an allocation request is refused by manager.\n"
"The status is 'Approved', when an allocation request is approved by manager."
msgstr ""
"El estado es 'A aprobar', cuando el usuario confirma una solicitud de asignación.\n"
"El estado es 'Rechazado', cuando el gerente rechaza una solicitud de asignación.\n"
"El estado es 'Aprobado', cuando el gerente aprueba una solicitud de asignación."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__state
msgid ""
"The status is set to 'To Submit', when a time off request is created.\n"
"The status is 'To Approve', when time off request is confirmed by user.\n"
"The status is 'Refused', when time off request is refused by manager.\n"
"The status is 'Approved', when time off request is approved by manager."
msgstr ""
"El estado se establece en 'A enviar', cuando se crea una solicitud de ausencia.\n"
"El estado es 'A aprobar', cuando el usuario confirma la solicitud de ausencia.\n"
"El estado es 'Rechazado', cuando el gerente rechaza la solicitud de ausencia.\n"
"El estado es 'Aprobado', cuando el gerente aprueba la solicitud de ausencia."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "The time off has been automatically approved"
msgstr "La ausencia se ha autorizado automáticamente"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "The time off has been cancelled: %s"
msgstr "Se ha cancelado la ausencia: %s"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__sequence
msgid ""
"The type with the smallest sequence is the default value in time off request"
msgstr ""
"El tipo con la secuencia más pequeña es el valor por defecto en la solicitud"
" de ausencia"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"There is no employee set on the time off. Please make sure you're logged in "
"the correct company."
msgstr ""
"No hay ningún empleado establecido en la ausencia. Asegúrese de haber "
"iniciado sesión en la compañía correcta."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "There is no valid allocation to cover that request."
msgstr "No hay ninguna asignación válida para cubrir esa solicitud."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"This allocation have already ran once, any modification won't be effective "
"to the days allocated to the employee. If you need to change the "
"configuration of the allocation, delete and create a new one."
msgstr ""
"Ya se ejecutó esta asignación una vez, cualquier modificación no se aplicará"
" a los días asignados al empleado. Si necesita cambiar la configuración de "
"la asignación, cancélela y cree una nueva."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__first_approver_id
msgid ""
"This area is automatically filled by the user who validate the time off"
msgstr ""
"Esta área se completa automáticamente con el usuario que valida la ausencia"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__second_approver_id
msgid ""
"This area is automatically filled by the user who validate the time off with"
" second level (If time off type need second validation)"
msgstr ""
"Este área se rellena automáticamente con el usuario que valida las ausencias"
" en segundo nivel (si el tipo de ausencia necesita una segunda validación)"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__approver_id
msgid ""
"This area is automatically filled by the user who validates the allocation"
msgstr ""
"Esta área se completa automáticamente con el usuario que valida la "
"asignación"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__second_approver_id
msgid ""
"This area is automatically filled by the user who validates the allocation "
"with second level (If time off type need second validation)"
msgstr ""
"Este área se rellena automáticamente con el usuario que valida la asignación"
" en segundo nivel (si el tipo de ausencia necesita una segunda validación)"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__accrual_validity_type
msgid "This field defines the unit of time after which the accrual ends."
msgstr ""
"Este campo define la unidad de tiempo después de la cual termina la "
"acumulación."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__start_type
msgid "This field defines the unit of time after which the accrual starts."
msgstr ""
"Este campo define la unidad de tiempo después de la cual comienza la "
"acumulación."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__has_valid_allocation
msgid "This indicates if it is still possible to use this type of leave"
msgstr "Esto indica si aún es posible usar este tipo de permiso"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "This modification is not allowed in the current state."
msgstr "No se permite esta modificación en el estado actual."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "This time off cannot be cancelled."
msgstr "Esta ausencia no se puede cancelar."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__leaves_taken
msgid ""
"This value is given by the sum of all time off requests with a negative "
"value."
msgstr ""
"Este valor viene dado por la suma de todas las solicitudes de ausencia con "
"un valor negativo."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__max_leaves
msgid ""
"This value is given by the sum of all time off requests with a positive "
"value."
msgstr ""
"Este valor viene dado por la suma de todas las solicitudes de ausencia con "
"un valor positivo."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__thu
msgid "Thursday"
msgstr "Jueves"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_holiday_allocation_id
#: model:ir.model,name:hr_holidays.model_hr_leave
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leave_manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leave_manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leave_manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__leave_manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__leave_manager_id
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__leave_type__request
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_root
#: model:ir.ui.menu,name:hr_holidays.menu_open_department_leave_approve
#: model:mail.message.subtype,name:hr_holidays.mt_leave
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
msgid "Time Off"
msgstr "Ausencias"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_allocation
msgid "Time Off Allocation"
msgstr "Asignación de ausencias"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/report/hr_leave_employee_type_report.py:0
#: model:ir.actions.act_window,name:hr_holidays.action_hr_available_holidays_report
#: model:ir.actions.act_window,name:hr_holidays.action_hr_leave_report
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_report_action
msgid "Time Off Analysis"
msgstr "Análisis de ausencias"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_approval
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_status_normal_tree
msgid "Time Off Approval"
msgstr "Aprobación de ausencias"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_tree_inherit_leave
msgid "Time Off Approver"
msgstr "Aprobador de ausencias"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_report_calendar
msgid "Time Off Calendar"
msgstr "Calendario de ausencias"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_resource_calendar__associated_leaves_count
msgid "Time Off Count"
msgstr "Número de ausencias"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_employee.py:0
msgid "Time Off Dashboard"
msgstr "Tablero de ausencias"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__private_name
msgid "Time Off Description"
msgstr "Descripción de la ausencia"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__leave_notif_subtype_id
msgid "Time Off Notification Subtype"
msgstr "Subtipo de notificación de ausencias"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_all
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_approve_department
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_my
msgid ""
"Time Off Officers allocate time off days to employees (e.g. paid time off).<br>\n"
"                Employees request allocations to Time Off Officers (e.g. recuperation days)."
msgstr ""
"Los encargados de ausencias asignan días de ausencia a los empleados (p. ej. ausencia pagada).<br>\n"
"                Los empleados solicitan asignaciones a los encargados de ausencias (p. ej. días de recuperación)."

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/calendar_controller.js:0
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_my_request
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_cancel_leave__leave_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__leave_id
#: model:ir.model.fields,field_description:hr_holidays.field_resource_calendar_leaves__holiday_id
#: model:mail.message.subtype,description:hr_holidays.mt_leave
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_employee_view_dashboard
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_activity
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_calendar
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_dashboard
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Time Off Request"
msgstr "Solicitud de ausencia"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Time Off Requests"
msgstr "Solicitudes de ausencia"

#. module: hr_holidays
#: model:res.groups,name:hr_holidays.group_hr_holidays_responsible
msgid "Time Off Responsible"
msgstr "Responsable de ausencias"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_second_approval
msgid "Time Off Second Approve"
msgstr "Segunda aprobación de ausencia"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.action_hr_holidays_summary_employee
#: model:ir.actions.report,name:hr_holidays.action_report_holidayssummary
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_graph
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_list
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_pivot
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Time Off Summary"
msgstr "Resumen de ausencias"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_employee_type_report
#: model:ir.model,name:hr_holidays.model_hr_leave_report
msgid "Time Off Summary / Report"
msgstr "Resumen/informe de ausencias"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holiday_status_view_kanban
msgid "Time Off Taken:"
msgstr "Ausencias tomadas:"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__time_off_type_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation_generate_multi_wizard__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__leave_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_generate_multi_wizard__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__name
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_status_normal_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Time Off Type"
msgstr "Tipo de ausencia"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.open_view_holiday_status
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_status_menu_configuration
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
msgid "Time Off Types"
msgstr "Tipos de ausencia"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__leave_validation_type
msgid "Time Off Validation"
msgstr "Validación de ausencias"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Time Off of Your Team Member"
msgstr "Ausencias de los miembros de su equipo"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__leave_to_approve_count
msgid "Time Off to Approve"
msgstr "Ausencias a aprobar"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Time Off."
msgstr "Ausencias."

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.hr_leave_cron_cancel_invalid_ir_actions_server
msgid "Time Off: Cancel invalid leaves"
msgstr "Ausencia: cancelar días de ausencia no válidos"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Time off"
msgstr "Ausencias"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__leaves_taken
msgid "Time off Already Taken"
msgstr "Ausencias ya tomadas"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.action_hr_holidays_by_employee_and_type_report
msgid "Time off Analysis by Employee and Time Off Type"
msgstr "Análisis de ausencias por empleado y tipo de ausencia"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_graph
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_pivot
msgid "Time off Summary"
msgstr "Resumen de ausencias"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__leaves_taken
msgid "Time off Taken"
msgstr "Ausencia tomada"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Time off of people you are manager of"
msgstr "Ausencias de las personas que gestiona"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"Time off request must be confirmed (\"To Approve\") in order to approve it."
msgstr ""
"La solicitud de ausencia debe ser confirmada (\"A aprobar\") para poder "
"aprobarla."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Time off request must be confirmed in order to approve it."
msgstr "La solicitud de ausencia debe ser confirmada para poder aprobarla."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Time off request must be confirmed or validated in order to refuse it."
msgstr ""
"La solicitud de ausencia debe ser confirmada o validada para poder "
"rechazarla."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"Time off request state must be \"Refused\" or \"Cancelled\" in order to be "
"reset to \"Confirmed\"."
msgstr ""
"El estado de la solicitud de ausencia debe ser “Rechazado” o “Cancelado” "
"para restablecerlo a “Confirmado”."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__tz
msgid "Timezone"
msgstr "Zona horaria"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Title"
msgstr "Título"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__stop_datetime
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "To"
msgstr "Hasta"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__confirm
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "To Approve"
msgstr "A aprobar"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "To Approve or Approved Allocations"
msgstr "Asignaciones a aprobar o aprobadas"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leave_date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leave_date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leave_date_to
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__leave_date_to
msgid "To Date"
msgstr "Hasta la fecha"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_dashboard.xml:0
msgid "Today"
msgstr "Hoy"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Today Activities"
msgstr "Actividades de hoy"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocations_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocations_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocations_count
msgid "Total number of allocations"
msgstr "Número total de asignaciones"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocation_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocation_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocation_count
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__allocation_count
msgid "Total number of days allocated."
msgstr "Número total de días asignados."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_employee__remaining_leaves
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_base__remaining_leaves
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_public__remaining_leaves
msgid ""
"Total number of paid time off allocated to this employee, change this value "
"to create allocation/time off request. Total based on all the time off types"
" without overriding limit."
msgstr ""
"Número total de ausencias pagadas asignadas a este empleado. Cambie este "
"valor para crear una solicitud de asignación / ausencia. Total basado en "
"todos los tipos de ausencia sin límite superior."

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_training
msgid "Training Time Off"
msgstr "Ausencia por formación"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.mail_activity_type_action_config_hr_holidays
msgid ""
"Try to add some records, or make sure that there is no active filter in the "
"search bar."
msgstr ""
"Intente añadir algunos registros o asegúrese de que no haya ningún filtro "
"activo en la barra de búsqueda."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__tue
msgid "Tuesday"
msgstr "Martes"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__bimonthly
msgid "Twice a month"
msgstr "Dos veces al mes"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__biyearly
msgid "Twice a year"
msgstr "Dos veces al año"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/resource.py:0
msgid ""
"Two public holidays cannot overlap each other for the same working hours."
msgstr ""
"Dos días festivos no se pueden traslapar en las mismas horas laborables."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Type"
msgstr "Tipo"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__type_request_unit
msgid "Type Request Unit"
msgstr "Unidad del tipo de solicitud"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_exception_decoration
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción en el registro."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__tz
msgid "Tz"
msgstr "Zona horaria"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__tz_mismatch
msgid "Tz Mismatch"
msgstr "Discordancia de zona horaria"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Unlimited"
msgstr "Sin límite"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_unpaid
msgid "Unpaid"
msgstr "No pagado"

#. module: hr_holidays
#: model:mail.message.subtype,description:hr_holidays.mt_leave_unpaid
#: model:mail.message.subtype,name:hr_holidays.mt_leave_unpaid
msgid "Unpaid Time Off"
msgstr "Ausencia no pagada"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Unread Messages"
msgstr "Mensajes sin leer"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Up to"
msgstr "Hasta"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_res_users
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__user_id
msgid "User"
msgstr "Usuario"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/avatar_card_resource_popover.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
msgid "User is idle"
msgstr "Usuario está inactivo"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/avatar_card_resource_popover.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
msgid "User is online"
msgstr "El usuario está en línea"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_holidays/static/src/avatar_card_resource_popover.xml:0
#: code:addons/hr_holidays/static/src/im_status_patch.xml:0
msgid "User is out of office"
msgstr "El usuario está fuera de la oficina"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/static/src/views/calendar/common/calendar_common_popover.xml:0
#: code:addons/hr_holidays/static/src/views/view_dialog/form_view_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Validate"
msgstr "Validar"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
msgid "Validated"
msgstr "Validado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__validation_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__validation_type
msgid "Validation Type"
msgstr "Tipo de validación"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/wizard/hr_departure_wizard.py:0
msgid ""
"Validity End date has been updated because Employee no longer works in the "
"company"
msgstr ""
"La fecha de validez se ha actualizado porque el empleado ya no trabaja en la"
" compañía."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "Validity Period"
msgstr "Periodo de validez "

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Validity Start"
msgstr "Inicio de la validez"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Validity Stop"
msgstr "Fin de la validez"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__virtual_remaining_leaves
msgid "Virtual Remaining Time Off"
msgstr "Ausencias virtuales restantes"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__confirm
msgid "Waiting Approval"
msgstr "En espera de aprobación"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Waiting For Me"
msgstr "Esperándome"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__validate1
msgid "Waiting Second Approval"
msgstr "En espera de segunda aprobación"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Waiting for Approval"
msgstr "En espera de aprobación"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__website_message_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__website_message_ids
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicación del sitio web"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__wed
msgid "Wednesday"
msgstr "Miércoles"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__weekly
msgid "Weekly"
msgstr "Semanalmente"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__action_with_unused_accruals
msgid ""
"When the Carry-Over Time is reached, according to Plan's setting, select "
"what you want to happen with the unused time off: None (time will be reset "
"to zero), All accrued time carried over to the next period; or Carryover "
"with a maximum)."
msgstr ""
"Cuando se alcanza el tiempo de traspaso, seleccione qué es lo que quiere que"
" pase con las ausencias no tomadas, según los ajustes del plan: Ninguno (el "
"tiempo regresará a ser cero), todo el tiempo acumulado se pasará al "
"siguiente período, o se traspasará un máximo de tiempo."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__cap_accrued_time
msgid ""
"When the field is checked the balance of an allocation using this accrual "
"plan will never exceed the specified amount."
msgstr ""
"Si el campo está seleccionado, el saldo de la asignación que utiliza este "
"plan de asignación nunca sobrepasará la cantidad especificada."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__cap_accrued_time_yearly
msgid ""
"When the field is checked the total amount accrued each year will be capped "
"at the specified amount"
msgstr ""
"Cuando el campo está seleccionado, la cantidad total acumulada cada año "
"estará limitada a la cantidad especificada."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__time_type__other
msgid "Worked Time"
msgstr "Tiempo trabajado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_mandatory_day__resource_calendar_id
msgid "Working Hours"
msgstr "Horas laborables"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__yearly
msgid "Yearly"
msgstr "Anualmente"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__yearly_day
msgid "Yearly Day"
msgstr "Día anual"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__yearly_day_display
msgid "Yearly Day Display"
msgstr "Pantalla de día anual"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__yearly_month
msgid "Yearly Month"
msgstr "Mes anual"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__maximum_leave_yearly
msgid "Yearly limit to"
msgstr "Límite anual a"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__start_type__year
msgid "Years"
msgstr "Años"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__requires_allocation__yes
msgid "Yes"
msgstr "Sí"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__requires_allocation
msgid ""
"Yes: Time off requests need to have a valid allocation.\n"
"\n"
"              No Limit: Time Off requests can be taken without any prior allocation."
msgstr ""
"Sí: las solicitudes de ausencias deben tener una asignación válida.\n"
"\n"
"              Sin límite: las solicitudes de ausencias se pueden realizar sin asignaciones previas."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "You are not allowed to request time off on a Mandatory Day"
msgstr "No tiene permitido solicitar una ausencia en un día obligatorio."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__accrual_validity_count
msgid ""
"You can define a period of time where the days carried over will be "
"available"
msgstr ""
"Puede definir el periodo en el que estarán disponibles los días traspasados "

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_accrual_level_start_count_check
msgid "You can not start an accrual in the past."
msgstr "No puede iniciar una acumulación en el pasado."

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/tours/hr_holidays_tour.js:0
msgid ""
"You can select the period you need to take off, from start date to end date"
msgstr ""
"Puede seleccionar el periodo que necesita tomar de ausencia, desde la fecha "
"de inicio hasta la fecha de finalización."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "You cannot delete a time off which is in %s state"
msgstr "No puede eliminar una ausencia que está en estado %s "

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "You cannot delete a time off which is in the past"
msgstr "No puede eliminar una ausencia pasada"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"You cannot delete an allocation request which has some validated leaves."
msgstr ""
"No puede eliminar una solicitud de asignación que tiene permisos validados."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid "You cannot delete an allocation request which is in %s state."
msgstr "No puede eliminar una solicitud de asignación que está en estado %s."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"You cannot first approve a time off for %s, because you are not his time off"
" manager"
msgstr ""
"No puede ser el primero en aprobar una ausencia para %s porque no es su "
"gerente de ausencias"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan_level.py:0
msgid ""
"You cannot have a cap on accrued time without setting a maximum amount."
msgstr ""
"No puede tener un límite en el tiempo acumulado sin configurar una cantidad "
"máxima."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_accrual_level_valid_yearly_cap_value
msgid ""
"You cannot have a cap on yearly accrued time without setting a maximum "
"amount."
msgstr ""
"No puede tener un límite en el tiempo anual acumulado sin establecer una "
"cantidad máxima."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_type.py:0
msgid ""
"You cannot modify the 'Public Holiday Included' setting since one or more "
"leaves for that                         time off type are overlapping with "
"public holidays, meaning that the balance of those employees would be "
"affected by this change."
msgstr ""
"No puede modificar la función \"Día festivo incluido\" ya que uno o más "
"permisos para ese tipo de ausencia se traslapan con días festivos, por lo "
"que este cambio afectará el saldo de esos empleados."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"You cannot reduce the duration below the duration of leaves already taken by"
" the employee."
msgstr ""
"No puede reducir la duración por debajo de los permisos que ya tomó el "
"empleado."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"You cannot refuse this allocation request since the employee has already "
"taken leaves for it. Please refuse or delete those leaves first."
msgstr ""
"No puede rechazar esta solicitud de asignación puesto que el empleado ya ha "
"tomado permisos para ella. Por favor, rechace o elimine primero esos "
"permisos."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"You don't have the rights to apply second approval on a time off request"
msgstr ""
"No tiene acceso para aplicar la segunda aprobación en una solicitud de "
"ausencia"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "You must be %s's Manager to approve this leave"
msgstr "Debe ser el gerente de %s para aprobar este permiso"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
msgid ""
"You must be either %s's Time Off Approver or Time off Administrator to "
"validate this allocation request."
msgstr ""
"Usted debe ser el aprobador de ausencias de %s o el administrador de "
"ausencias para validar esta solicitud de asignación."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"You must be either %s's manager or Time off Manager to approve this leave"
msgstr ""
"Para aprobar este permiso debe ser ya sea el gerente de %s o el gerente de "
"ausencias"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"You must either be a Time off Officer or Time off Manager to approve this "
"leave"
msgstr ""
"Debe ser un encargado o responsable de ausencias para aprobar este permiso"

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_accrual_level_added_value_greater_than_zero
msgid "You must give a rate greater than 0 in accrual plan levels."
msgstr "Debe dar una tasa mayor que 0 en niveles de plan de acumulación."

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"You must have manager rights to modify/validate a time off that already "
"begun"
msgstr ""
"Debe tener derechos de gerente para modificar/validar una ausencia ya "
"comenzada"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid ""
"You've already booked time off which overlaps with this period:\n"
"%s\n"
"Attempting to double-book your time off won't magically make your vacation 2x better!\n"
msgstr ""
"Ya ha reservado una ausencia que coincide con este periodo:\n"
"%s\n"
"Intentar reservar dos veces su ausencia no hará que sus vacaciones sean 2 veces mejores por arte de magia.\n"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Your %(leave_type)s planned on %(date)s has been accepted"
msgstr ""
"Su ausencia de %(leave_type)s planificada para el %(date)s ha sido aceptada"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Your %(leave_type)s planned on %(date)s has been refused"
msgstr ""
"Su ausencia de %(leave_type)s planificada para el %(date)s ha sido rechazada"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "Your Time Off"
msgstr "Su ausencia"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/wizard/hr_holidays_cancel_leave.py:0
msgid "Your time off has been cancelled."
msgstr "Se ha cancelado su ausencia."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "after"
msgstr "después de"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "after allocation start date"
msgstr "después de la fecha de inicio de la asignación"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "all"
msgstr "todas"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "and"
msgstr "y"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "and on the"
msgstr "y el "

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "available"
msgstr "disponibles"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_available_holidays_report_tree
msgid "by Employee"
msgstr "por empleado"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_summary_all
msgid "by Type"
msgstr "por tipo"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "can be used before the allocation expires."
msgstr "se puede usar antes de que la asignación ya no sea válida."

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "click here"
msgstr "haga clic aquí"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "day of the month"
msgstr "día del mes"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
msgid "day(s)"
msgstr "dia(s)"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "days"
msgstr "días"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "days of the months"
msgstr "días del mes"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_activity
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_activity
msgid "days)"
msgstr "días)"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_generate_multi_wizard_view_form
msgid "e.g. Extra recuperation, Company unavailability, ..."
msgstr "p. ej, recuperación extra, indisponibilidad de la compañía..."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_generate_multi_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "e.g. Time Off type (From validity start to validity end / no limit)"
msgstr ""
"p. ej. Tipo de ausencia (desde el inicio de la validez hasta el final de la "
"validez / sin límite)"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "from %(date_from)s to %(date_to)s - %(state)s"
msgstr "del %(date_from)s al %(date_to)s - %(state)s"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
msgid "hour(s)"
msgstr "hora(s)"

#. module: hr_holidays
#. odoo-javascript
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "hours"
msgstr "horas"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/leave_stats/leave_stats.xml:0
msgid "in"
msgstr "en"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "initially"
msgstr "inicialmente"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave_accrual_plan_level.py:0
msgid "last day"
msgstr "último día"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "new request"
msgstr "nueva solicitud"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "no"
msgstr "no"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "of"
msgstr "de"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "of the"
msgstr "de"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "of the month"
msgstr "del mes"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "on"
msgstr "el"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "on the"
msgstr "en el"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "refused"
msgstr "rechazado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__sequence
msgid "sequence"
msgstr "secuencia"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "taken"
msgstr "tomados"

#. module: hr_holidays
#. odoo-python
#: code:addons/hr_holidays/models/hr_leave.py:0
msgid "the accruated amount is insufficient for that duration."
msgstr "la cantidad acumulada no es suficiente para esa duración."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "to"
msgstr "a"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "to refuse"
msgstr "a rechazar"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "up to"
msgstr "hasta"

#. module: hr_holidays
#. odoo-javascript
#: code:addons/hr_holidays/static/src/dashboard/time_off_card.xml:0
msgid "valid until"
msgstr "válido hasta"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "validate"
msgstr "validar"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "validated"
msgstr "validado"
