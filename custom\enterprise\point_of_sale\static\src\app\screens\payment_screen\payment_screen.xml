<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.PaymentScreen">
        <t t-if="ui.isSmall">
            <div class="payment-screen screen d-flex flex-column h-100 p-2 bg-200">
                <t t-call="point_of_sale.PaymentScreenDue" />
                <t t-call="point_of_sale.PaymentScreenMethods" />
                <div class="d-flex flex-grow-1 flex-column overflow-y-auto gap-1">
                    <PaymentScreenStatus order="currentOrder" />
                    <PaymentScreenPaymentLines
                        paymentLines="paymentLines"
                        deleteLine.bind="deletePaymentLine"
                        sendForceDone.bind="sendForceDone"
                        sendPaymentReverse.bind="sendPaymentReverse"
                        sendPaymentCancel.bind="sendPaymentCancel"
                        sendPaymentRequest.bind="sendPaymentRequest"
                        selectLine.bind="selectPaymentLine"
                        updateSelectedPaymentline.bind="updateSelectedPaymentline" />
                </div>
                <t t-call="point_of_sale.PaymentScreenButtons" />
                <div t-attf-class="d-flex switchpane gap-2 mt-2">
                    <t t-call="point_of_sale.PaymentScreenBack" />
                    <t t-call="point_of_sale.PaymentScreenValidate" />
                </div>
            </div>
        </t>
        <t t-else="">
            <div class="payment-screen screen d-flex flex-column h-100 ">
                <div class="main-content d-flex gap-2 h-100 bg-200 overflow-auto">
                    <div class="left-content d-flex flex-column col-md-4 p-2">
                        <t t-call="point_of_sale.PaymentScreenMethods" />
                        <t t-call="point_of_sale.PaymentScreenButtons" />
                        <Numpad class="'py-2'"  buttons="getNumpadButtons()"/>
                        <div t-attf-class="d-flex flex-row gap-2">
                            <t t-call="point_of_sale.PaymentScreenBack" />
                            <t t-call="point_of_sale.PaymentScreenValidate" />
                        </div>
                    </div>
                    <div class="center-content d-flex flex-column flex-grow-1 gap-2 py-2">
                        <t t-call="point_of_sale.PaymentScreenDue" />
                        <div class="payment-summary d-flex flex-grow-1 flex-column gap-2 overflow-y-auto justify-content-center rounded-3 bg-view">
                            <PaymentScreenStatus order="currentOrder" />
                            <PaymentScreenPaymentLines
                                paymentLines="paymentLines"
                                deleteLine.bind="deletePaymentLine"
                                sendForceDone.bind="sendForceDone"
                                sendPaymentReverse.bind="sendPaymentReverse"
                                sendPaymentCancel.bind="sendPaymentCancel"
                                sendPaymentRequest.bind="sendPaymentRequest"
                                selectLine.bind="selectPaymentLine"
                                updateSelectedPaymentline.bind="updateSelectedPaymentline" />
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </t>

    <t t-name="point_of_sale.PaymentScreenDue">
        <section class="paymentlines-container rounded-3 bg-view"
            t-att-class="{'paymentlines-empty': paymentLines.length === 0 }">
            <div class="total text-center py-2 py-lg-4 text-success">
                <t t-esc="this.env.utils.formatCurrency(currentOrder.getTotalDue())" />
            </div>
        </section>
    </t>

    <t t-name="point_of_sale.PaymentScreenValidate">
        <t t-if="ui.isSmall">
            <button class="btn-switchpane validation-button btn btn-primary btn-lg flex-fill py-3 lh-lg"
                t-att-class="{ secondary: !(currentOrder.is_paid() and currentOrder._isValidEmptyOrder()) }"
                t-on-click="() => this.validateOrder()">
                <span>Validate</span>
            </button>
        </t>
        <t t-else="">
            <button class="button next validation btn btn-primary btn-lg w-50 py-3 lh-lg"
                t-attf-class="{{currentOrder.is_paid() and currentOrder._isValidEmptyOrder() ? 'highlight' : 'disabled'}}"
                t-on-click="() => this.validateOrder()">
                <span class="next_text">Validate</span>
            </button>
        </t>
    </t>

    <t t-name="point_of_sale.PaymentScreenBack">
        <t t-if="ui.isSmall">
            <button class="btn-switchpane back-button validation-button btn btn-light btn-lg lh-lg "
                t-on-click="() => pos.onClickBackButton()">
                <i class="oi oi-chevron-left fa-fw" role="img" aria-label="Go Back" title="Go Back" />
            </button>
        </t>
        <t t-else="">
            <button class="button back back-button validation btn btn-light btn-lg w-50 py-3 lh-lg "
                t-on-click="() => pos.onClickBackButton()">
                <span class="back_text">Back</span>
            </button>
        </t>
    </t>

    <t t-name="point_of_sale.PaymentScreenButtons">
        <div class="payment-buttons d-flex flex-column gap-2">
            <div class="d-flex flex-column flex-sm-row gap-2 w-100">
                <t t-set="hasOptionalButtons" t-value="pos.config.iface_tipproduct and pos.config.tip_product_id or pos.config.iface_cashdrawer or pos.config.ship_later" />
                <button class="button partner-button btn btn-light btn-lg w-100 w-md-50 lh-lg text-truncate"
                    t-att-class="{ 'highlight text-action': currentOrder.get_partner() }"
                    t-on-click="() => this.pos.selectPartner()">
                    <i class="fa fa-user me-2" role="img" title="Customer" />
                    <span class="partner-name">
                        <t t-if="currentOrder.get_partner()" t-esc="currentOrder.get_partner().name"/>
                        <t t-else="">Customer</t>
                    </span>
                </button>
                <button class="button js_invoice btn btn-light btn-lg d-flex justify-content-between align-items-baseline w-100 w-md-50 lh-lg" t-att-class="{ 'highlight active': currentOrder.is_to_invoice() }"
                    t-on-click="toggleIsToInvoice">
                    <span><i class="fa fa-file-text-o me-2" />Invoice</span>
                    <i class="fa me-2" t-attf-class="{{ currentOrder.is_to_invoice() ? 'fa-check-square text-action' : 'fa-square-o' }}" /> 
                </button>
            </div>
            <div t-if="(pos.config.iface_tipproduct and pos.config.tip_product_id) or pos.config.iface_cashdrawer or pos.config.ship_later" class="d-flex flex-column gap-2 w-100">
                <button t-if="pos.config.iface_tipproduct and pos.config.tip_product_id" class="button btn btn-light btn-lg d-flex justify-content-between align-items-baseline w-100 lh-lg" t-att-class="{ 'highlight active text-action': currentOrder.get_tip() }"
                    t-on-click="addTip">
                    <span t-attf-class="{{currentOrder.get_tip() ? 'me-auto' : 'mx-auto' }}"><i class="fa fa-heart me-2" />Tip</span>
                    <t t-if="currentOrder.get_tip() != 0">
                        <t t-esc="env.utils.formatCurrency(currentOrder.get_tip())" />
                    </t>
                </button>
                <button t-if="pos.config.iface_cashdrawer" class="button col js_cashdrawer btn btn-light py-3 text-start rounded-0 border-top border-start border-end"
                    t-on-click="openCashbox">
                    <i class="fa fa-archive me-2" />Open Cashbox 
                </button>
                <button t-if="pos.config.ship_later" class="button btn btn-light btn-lg d-flex justify-content-between align-items-baseline w-100 lh-lg" t-att-class="{ 'highlight active text-action': currentOrder.getShippingDate() }"
                    t-on-click="toggleShippingDatePicker">
                    <span t-attf-class="{{currentOrder.getShippingDate() ? 'me-auto' : 'mx-auto' }}"><i class="fa fa-clock-o me-2" />Ship Later</span>
                    <span t-if="currentOrder.getShippingDate()">
                        <t t-esc="currentOrder.getShippingDate()" />
                    </span>
                </button>
            </div>
        </div>
    </t>

    <t t-name="point_of_sale.PaymentScreenMethods">
        <div class="paymentmethods-container mb-3" t-att-class="ui.isSmall ? 'my-2 rounded-3' : 'flex-grow-1'">
            <div class="paymentmethods  gap-1 gap-lg-2" t-att-class="{ 'd-grid p-1 gap-1 overflow-y-auto rounded-3 bg-300': ui.isSmall, 'd-flex flex-column gap-lg-2': !ui.isSmall }">
                <t t-foreach="payment_methods_from_config" t-as="paymentMethod" t-key="paymentMethod.id">
                    <div class="button paymentmethod btn btn-light btn-lg lh-lg flex-fill"
                        t-att-class="{'opacity-50': this.pos.paymentTerminalInProgress and paymentMethod.use_payment_terminal}"
                        t-on-click="() => this.addNewPaymentLine(paymentMethod)">
                        <div class="payment-method-display d-flex align-items-center gap-2 text-wrap text-start lh-sm">
                            <img class="payment-method-icon" t-att-src="paymentMethodImage(paymentMethod.id)" />
                            <span class="payment-name" t-esc="pos.getPaymentMethodDisplayText(paymentMethod, currentOrder)" />
                        </div>
                    </div>
                </t>
            </div>
        </div>
    </t>

</templates>
