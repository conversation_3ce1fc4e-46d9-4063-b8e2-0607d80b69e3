# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_adyen
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_adyen
#: model_terms:ir.ui.view,arch_db:pos_adyen.res_config_settings_view_form
msgid "Add tip through payment terminal (Adyen)"
msgstr "Thêm tiền tip qua thiết bị đầu cuối thanh toán (Adyen)"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_api_key
msgid "Adyen API key"
msgstr "Khoá API Adyen"

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "Adyen Error"
msgstr "Lỗi Adyen"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_latest_response
msgid "Adyen Latest Response"
msgstr "Phản hồi Adyen gần nhất"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_terminal_identifier
msgid "Adyen Terminal Identifier"
msgstr "Định danh thiết bị đầu cuối Adyen"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_test_mode
msgid "Adyen Test Mode"
msgstr "Chế độ kiểm thử Adyen"

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "An unexpected error occurred. Message from Adyen: %s"
msgstr "Đã xảy ra lỗi ngoài dự kiến. Thông báo từ Adyen: %s"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_config__adyen_ask_customer_for_tip
msgid "Ask Customers For Tip"
msgstr "Yêu cầu tiền tip từ khách hàng"

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "Authentication failed. Please check your Adyen credentials."
msgstr ""
"Xác minh không thành công. Vui lòng kiểm tra thông tin đăng nhập Adyen. "

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid ""
"Cancelling the payment failed. Please cancel it manually on the payment "
"terminal."
msgstr ""
"Hủy thanh toán không thành công. Vui lòng hủy thủ công trên thiết bị đầu "
"cuối thanh toán. "

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "Cannot process transactions with negative amount."
msgstr "Không thể xử lý giao dịch có số tiền âm. "

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_res_config_settings
msgid "Config Settings"
msgstr "Cài đặt cấu hình"

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid ""
"Could not connect to the Odoo server, please check your internet connection "
"and try again."
msgstr ""
"Không thể kết nối với máy chủ Odoo, vui lòng kiểm tra kết nối mạng và thử "
"lại. "

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_event_url
msgid "Event URL"
msgstr "URL sự kiện"

#. module: pos_adyen
#. odoo-python
#: code:addons/pos_adyen/models/pos_payment_method.py:0
msgid "Invalid Adyen request"
msgstr "Yêu cầu Adyen không hợp lệ"

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "Message from Adyen: %s"
msgstr "Thông báo từ Adyen: %s"

#. module: pos_adyen
#. odoo-python
#: code:addons/pos_adyen/models/pos_config.py:0
msgid ""
"Please configure a tip product for POS %s to support tipping with Adyen."
msgstr ""
"Vui lòng cấu hình sản phẩm tiền tip cho POS %s để hỗ trợ nhận tiền tip qua "
"Adyen. "

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Cấu hình điểm bán hàng"

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Phương thức thanh toán điểm bán hàng"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_res_config_settings__pos_adyen_ask_customer_for_tip
msgid "Pos Adyen Ask Customer For Tip"
msgstr "Pos Adyen yêu cầu tiền tip từ khách hàng"

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_test_mode
msgid "Run transactions in the test environment."
msgstr "Chạy giao dịch trong môi trường kiểm thử. "

#. module: pos_adyen
#. odoo-python
#: code:addons/pos_adyen/models/pos_payment_method.py:0
msgid ""
"Terminal %(terminal)s is already used in company %(company)s on payment "
"method %(payment_method)s."
msgstr ""
"Thiết bị đầu cuối %(terminal)s đã được sử dụng tại công ty %(company)s trong"
" phương thức thanh toán %(payment_method)s."

#. module: pos_adyen
#. odoo-python
#: code:addons/pos_adyen/models/pos_payment_method.py:0
msgid ""
"Terminal %(terminal)s is already used on payment method %(payment_method)s."
msgstr ""
"Thiết bị đầu cuối %(terminal)s đã được sử dụng trong phương thức thanh toán "
"%(payment_method)s."

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_event_url
msgid "This URL needs to be pasted on Adyen's portal terminal settings."
msgstr "URL này cần được dán vào cài đặt cổng thông tin của Adyen."

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_api_key
msgid ""
"Used when connecting to Adyen: https://docs.adyen.com/user-management/how-"
"to-get-the-api-key/#description"
msgstr ""
"Được sử dụng khi kết nối với Adyen: https://docs.adyen.com/user-"
"management/how-to-get-the-api-key/#description"

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_terminal_identifier
msgid "[Terminal model]-[Serial number], for example: P400Plus-123456789"
msgstr "[Mẫu thiết bị đầu cuối]-[Số sê-ri], ví dụ: P400Plus-123456789"
