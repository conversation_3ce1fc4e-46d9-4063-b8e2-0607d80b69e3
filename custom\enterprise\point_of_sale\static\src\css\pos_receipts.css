.pos-receipt-print {
    width: 512px;
    font-size: 27px;
    color: #000000;
}

.pos-receipt .pos-receipt-right-align {
    float: right;
    display: flex;
}

.pos-receipt .pos-receipt-center-align {
    text-align: center;
}

.pos-receipt .pos-receipt-left-padding {
    padding-left: 2em;
}

.pos-receipt .pos-receipt-logo {
    width: 50%;
    display: block;
    margin: 0 auto;
}

.pos-receipt .pos-receipt-qrcode {
    display: block;
    margin: 0 auto;
}

.pos-receipt .pos-receipt-contact {
    text-align: center;
    font-size: 75%;
}

.pos-receipt .pos-receipt-order-data {
    text-align: center;
    font-size: 75%;
}

.pos-receipt .pos-receipt-amount {
    font-size: 125%;
    text-align: start;
}

.pos-receipt .pos-receipt-title {
    font-weight: bold;
    font-size: 125%;
    text-align: center;
}

.pos-receipt .pos-receipt-header {
    font-size: 125%;
    text-align: center;
}

.pos-payment-terminal-receipt {
    text-align: center;
    font-size: 75%;
}

.pos-payment-terminal-receipt pre {
    font-family: inherit;
}

.responsive-price {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
}

.responsive-price > .pos-receipt-right-align {
    margin-left: auto;
}

.pos-receipt .pos-receipt-taxes {
    display: flex;
    flex-direction: column;
}

.pos-receipt .orderlines {
    /*rtl:ignore*/
    direction: ltr;
}
