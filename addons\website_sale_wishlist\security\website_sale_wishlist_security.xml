<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

        <record id="product_wishlist_rule" model="ir.rule">
            <field name="name">See own Wishlist</field>
            <field name="model_id" ref="model_product_wishlist"/>
            <field name="domain_force">[('partner_id','=', user.partner_id.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_portal')), (4, ref('base.group_user'))]"/>
        </record>

        <record id="all_product_wishlist_rule" model="ir.rule">
            <field name="name">See all wishlist</field>
            <field name="model_id" ref="model_product_wishlist"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('sales_team.group_sale_manager'))]"/>
        </record>

</odoo>
