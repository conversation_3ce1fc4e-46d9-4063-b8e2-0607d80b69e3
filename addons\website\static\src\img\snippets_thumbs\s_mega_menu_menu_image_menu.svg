<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <rect id="path-1" width="13" height="2" x="0" y="0"/>
    <filter id="filter-2" width="107.7%" height="200%" x="-3.8%" y="-25%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <path id="path-3" d="M12 12v1H0v-1h12zm-2-3v1H0V9h10zm2-3v1H0V6h12z"/>
    <filter id="filter-4" width="108.3%" height="128.6%" x="-4.2%" y="-7.1%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <rect id="path-5" width="15.059" height="19.07" x="0" y="0"/>
    <linearGradient id="linearGradient-7" x1="72.875%" x2="40.332%" y1="46.279%" y2="33.212%">
      <stop offset="0%" stop-color="#008374"/>
      <stop offset="100%" stop-color="#006A59"/>
    </linearGradient>
    <linearGradient id="linearGradient-8" x1="88.517%" x2="50%" y1="38.775%" y2="50%">
      <stop offset="0%" stop-color="#00AA89"/>
      <stop offset="100%" stop-color="#009989"/>
    </linearGradient>
    <rect id="path-9" width="14" height="2" x="39" y="0"/>
    <filter id="filter-10" width="107.1%" height="200%" x="-3.6%" y="-25%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <path id="path-11" d="M52 12v1H39v-1h13zm-2-3v1H39V9h11zm2-3v1H39V6h13z"/>
    <filter id="filter-12" width="107.7%" height="128.6%" x="-3.8%" y="-7.1%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_mega_menu_menu_image_menu">
      <rect width="82" height="60" class="bg"/>
      <g class="group" transform="translate(15 20)">
        <g class="rectangle">
          <use fill="#000" filter="url(#filter-2)" xlink:href="#path-1"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-1"/>
        </g>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
        </g>
        <g class="image_1_border" transform="translate(19)">
          <rect width="16" height="20" fill="#FFF" class="rectangle"/>
          <g class="oval___oval_mask" transform="translate(.47 .465)">
            <mask id="mask-6" fill="#fff">
              <use xlink:href="#path-5"/>
            </mask>
            <use fill="#79D1F2" class="mask" xlink:href="#path-5"/>
            <ellipse cx="9.647" cy="4.884" fill="#F3EC60" class="oval" mask="url(#mask-6)" rx="3.529" ry="3.488"/>
            <ellipse cx="15.294" cy="20.93" fill="url(#linearGradient-7)" class="oval" mask="url(#mask-6)" rx="11.059" ry="6.977"/>
            <ellipse cx="-8.235" cy="21.163" fill="url(#linearGradient-8)" class="oval" mask="url(#mask-6)" rx="17.647" ry="10.93"/>
          </g>
          <path fill="#FFF" d="M16 0v20H0V0h16zm-1 1H1v18h14V1z" class="rectangle_2"/>
        </g>
        <g class="rectangle">
          <use fill="#000" filter="url(#filter-10)" xlink:href="#path-9"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-9"/>
        </g>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-12)" xlink:href="#path-11"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-11"/>
        </g>
      </g>
    </g>
  </g>
</svg>
