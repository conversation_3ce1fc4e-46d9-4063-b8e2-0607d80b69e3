# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* point_of_sale
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# Rune Restad, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# Wil <PERSON>doo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-07 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: <PERSON>m<PERSON>l (https://app.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_picking.py:0
msgid ""
"\n"
"This issue occurs because the quantity becomes zero after rounding during the conversion. To fix this, adjust the conversion factors or rounding method to ensure that even the smallest quantity in the original unit does not round down to zero in the target unit."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_picking.py:0
msgid " - From \"%(uom_from)s\" to \"%(uom_to)s\""
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_printer__printer_type__iot
msgid " Use a printer connected to the IoT Box"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
msgid "% Disc"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_warehouse.py:0
msgid "%(name)s Picking POS"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "%(name)s REFUND"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid ""
"%(old_pm)s changed to %(new_pm)s and from %(old_amount)s to %(new_amount)s"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "%(old_pm)s changed to %(new_pm)s for %(old_amount)s"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid "%(payment_method)s POS payment of %(partner)s in %(session)s"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "%(product_name)s: Deleted line (quantity: %(qty)s)"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "%(product_name)s: Ordered quantity: %(old_qty)s"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "%(refunded_order)s REFUND"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid "%(title)s %(product_name)s with %(taxes)s"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/receipt_header/receipt_header.js:0
msgid "%(vatLabel)s: %(vatId)s"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.js:0
msgid "%s customer(s) found for \"%s\"."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid ""
"%s has a total amount of %s, are you sure you want to delete this order?"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.js:0
msgid "%s orders imported"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid "%s untaxed"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
msgid "'Select an order'"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
msgid "(as of opening)"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "+ New Shop"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "0.00"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "00014-001-0001"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "10"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "100.00"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "1000"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "10000"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "10000.00"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "123.45"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_userlabel
msgid "1234567890"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "2-03-2000 9:00 AM"
msgstr ""

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.product_attribute_value_39
msgid "39"
msgstr ""

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.product_attribute_value_40
msgid "40"
msgstr ""

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.product_attribute_value_41
msgid "41"
msgstr ""

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.product_attribute_value_42
msgid "42"
msgstr ""

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.product_attribute_value_43
msgid "43"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "45"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__ticket_code
msgid ""
"5 digits alphanumeric code to be used by portal user to request an invoice"
msgstr ""

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.blue_denim_jeans_template
msgid "5-pocket jeans in cotton denim with a slight stretch for good comfort."
msgstr ""

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.blue_denim_jeans_slim_template
msgid "5-pocket jeans in cotton denim.."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "5.00"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "50.00"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "567789"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "7897"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "95.00"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "987657"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "99.99"
msgstr ""

#. module: point_of_sale
#: model:mail.template,body_html:point_of_sale.pos_email_marketing_template
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <h4>\n"
"        Dear <t t-out=\"object.partner_id.name or 'Valuable Customer'\">Azure Interior</t>,\n"
"    </h4>\n"
"    <p style=\"font-size: 13px;\">\n"
"We have a limited-time offer only for you, enjoy a flat 20% off on your next purchase with us.\n"
"We can't wait to continue serving you.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_pos_kanban
msgid ""
"<i class=\"fa fa-fw fa-shopping-bag me-1\" aria-label=\"Shopping cart\" "
"role=\"img\" title=\"Shopping cart\"/>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-info-circle me-1\" title=\"This setting is common to all "
"PoS.\" pos-data-toggle=\"tooltip\"/>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "<i class=\"fa fa-pencil\"/> Edit"
msgstr "<i class=\"fa fa-pencil\"/> Rediger"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid ""
"<p>Dear %(client_name)s,<br/>Here is your Receipt %(is_invoiced)sfor"
"             %(pos_name)s amounting in %(amount)s from %(company_name)s. "
"</p>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid ""
"<span class=\"float-end\" invisible=\"is_total_cost_computed\">To be "
"determined</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Barcodes</span>\n"
"                                <i class=\"fa fa-info-circle me-1\" title=\"This setting is common to all PoS.\" pos-data-toggle=\"tooltip\"/>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "<span class=\"o_stat_text\">Cash Register</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "<span class=\"o_stat_text\">Journal Items</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "<span class=\"oe_inline\"><b>Skip Preview Screen</b></span>"
msgstr "<span class=\"oe_inline\"><b>Hopp over forhåndsvisning</b></span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Balance</span>"
msgstr "<span>Balanse</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Closing</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"<span>Please note that the kiosk only works with Adyen &amp; Stripe "
"terminals</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Rapportering</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>View</span>"
msgstr "<span>Vis</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid ""
"<strong> &gt; Payment Terminals</strong>\n"
"                                    in order to install a Payment Terminal and make a fully integrated payment method."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "<strong>Amount of discounts</strong>:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "<strong>Amounting to:</strong>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "<strong>Config names</strong>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "<strong>End of session note:</strong>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "<strong>Number of discounts</strong>:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "<strong>Opening of session note:</strong>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_invoice_document
msgid "<strong>Source Invoice</strong>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "<strong>Total</strong>"
msgstr "<strong>Total</strong>"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid "? Clicking \"Confirm\" will validate the payment."
msgstr "? Ved å klikke \"Valider\" vil betalingen bli validert."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__uuid
msgid ""
"A globally unique identifier for this pos configuration, used to prevent "
"conflicts in client-generated data."
msgstr ""
"En global identifikator for dette kassapunktet. Brukes til å unngå "
"konflikter i data generert fra klienten."

#. module: point_of_sale
#: model:ir.model.constraint,message:point_of_sale.constraint_pos_note_name_unique
msgid "A note with this name already exists"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__login_number
msgid ""
"A sequence number that is incremented each time a user resumes the pos "
"session"
msgstr ""
"Et sekvensnummer som inkrementeres hver gang en bruker gjennopptar "
"kassaøkten"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__sequence_number
msgid "A sequence number that is incremented with each order"
msgstr "Et sekvensnummer som inkrementeres for hver ordre"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
msgid ""
"A session is a period of time, usually one day, during which you sell "
"through the Point of Sale."
msgstr ""
"En økt er en tidsperiode. Vanligvis én dag, der du selger igjennom "
"kassasystemet."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"A session is currently opened for this PoS. Some settings can only be "
"changed after the session is closed."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__sequence_number
msgid "A session-unique sequence number for the order"
msgstr "Et unikt sekvensummer for ordren"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__receipt_footer
msgid "A short text that will be inserted as a footer in the printed receipt."
msgstr "En kort tekst som blir brukt som bunntekst på kvitteringer."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__receipt_header
msgid "A short text that will be inserted as a header in the printed receipt."
msgstr "En kort tekst som blir brukt som topptekst på kvitteringer."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_receipt/cash_move_receipt.xml:0
msgid "AMOUNT"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept customer tips or convert their change to a tip"
msgstr "Ta imot tips fra kunder eller konverter vekslepengene deres til tips"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with Mercado Pago on a terminal"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with Viva Wallet on a terminal or tap on phone"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a PayTM payment terminal"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Razorpay payment terminal"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Six payment terminal"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Stripe payment terminal"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with an Adyen payment terminal"
msgstr "Ta imot betalinger med betalingsterminal fra Adyen"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/pos_kanban_view/pos_kanban_view.js:0
msgid "Access Denied"
msgstr "Tilgang avvist"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__access_token
msgid "Access Token"
msgstr "Tilgangstoken"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__access_warning
msgid "Access warning"
msgstr "Adgangsadvarsel"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
msgid "Account"
msgstr "Konto"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_cash_rounding
msgid "Account Cash Rounding"
msgstr "Kontantavrunding"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__account_move_id
msgid "Account Move"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__bank_payment_ids
msgid "Account payments representing aggregated and bank split payments."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__outstanding_account_id
msgid ""
"Account used as outstanding account when creating accounting payment records"
" for bank payments."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Accounting"
msgstr "Regnskap"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__invoice_journal_id
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_invoice_journal_id
msgid "Accounting journal used to create invoices."
msgstr "Regnskapsjournal som blir brukt ved fakturering."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__journal_id
#: model:ir.model.fields,help:point_of_sale.field_pos_order__sale_journal
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_journal_id
msgid ""
"Accounting journal used to post POS session journal entries and POS invoice "
"payments."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__message_needaction
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_needaction
msgid "Action Needed"
msgstr "Handling påkrevet"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/control_buttons.xml:0
msgid "Actions"
msgstr "Handlinger"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__module_pos_sms
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_module_pos_sms
msgid "Activate SMS feature for point_of_sale"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__active
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__active
msgid "Active"
msgstr "Aktiv"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_ids
msgid "Activities"
msgstr "Aktiviteter"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekorering for Aktivitetsunntak"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_state
msgid "Activity State"
msgstr "Aktivitetsstatus"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikon type Aktivitet"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/product_configurator_popup/product_configurator_popup.xml:0
msgid "Add"
msgstr "Legg til"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_note_button/customer_note_button.js:0
msgid "Add %s"
msgstr "Legg til %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid "Add Tip"
msgstr "Legg til tips"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_company__point_of_sale_ticket_unique_code
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__point_of_sale_ticket_unique_code
msgid ""
"Add a 5-digit code on the receipt to allow the user to request the invoice "
"for an order on the portal."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
msgid "Add a closing note..."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Add a custom message to header and footer"
msgstr "Legg til en tilpasset beskjed i topptekst og bunntekst"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
msgid "Add a customer"
msgstr "Legg til kunde"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_method_form
msgid "Add a new payment method"
msgstr "Legg til en ny betalingsmetode"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_printer_form
msgid "Add a new restaurant order printer"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/opening_control_popup/opening_control_popup.xml:0
msgid "Add an opening note..."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Add internal notes on order lines for the kitchen"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/combo_configurator_popup/combo_configurator_popup.xml:0
msgid "Add to order"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "Added %(payment_method)s with %(amount)s"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Additional required information:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Additional required invoicing information:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Additional required user information:"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
msgid "Address"
msgstr "Adresse"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Adds a button to set a global discount"
msgstr ""

#. module: point_of_sale
#: model:res.groups,name:point_of_sale.group_pos_manager
msgid "Administrator"
msgstr "Administrator"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__cash_control
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_cash_control
msgid "Advanced Cash Control"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Adyen"
msgstr "Adyen"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_adyen
msgid "Adyen Payment Terminal"
msgstr "Adyen Betalingsterminal"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_line/partner_line.xml:0
msgid "All Orders"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
msgid "All active orders"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid ""
"All available pricelists must be in the same currency as the company or as "
"the Sales Journal set on this point of sale if you use the Accounting "
"application."
msgstr ""
"Alle tilgjengelige prislister må ha samme valuta som firmaet eller "
"salgsjournalen, som brukes for dette kassapunktet, hvis du bruker "
"regnskapsmodulen."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "All notes"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid ""
"All payment methods must be in the same currency as the Sales Journal or the"
" company currency if that is not set."
msgstr ""
"Alle betalingsmetoder må ha samme valuta som salgsjournalen, eventuelt samme"
" valuta som firmaet hvis valuta på salgsjournalen ikke er satt."

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_all_sales_lines
msgid "All sales lines"
msgstr "Alle salgslinjer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allow Ship Later"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allow cashiers to set a discount per line"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allow to access each other's active orders"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allow to log and switch between selected Employees"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allowed"
msgstr "Tillatt"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__is_total_cost_computed
msgid ""
"Allows to know if all the total cost of the order lines have already been "
"computed"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__is_total_cost_computed
msgid "Allows to know if the total cost has already been computed or not"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__amount
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__amount
msgid "Amount"
msgstr "Beløp"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__amount_authorized_diff
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_amount_authorized_diff
msgid "Amount Authorized Difference"
msgstr "Maks tillatt kassadifferanse"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "Amount for %(old_pm)s changed from %(old_amount)s to %(new_amount)s"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__amount_to_balance
msgid "Amount to balance"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "Amount total"
msgstr "Totalbeløp"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/qr_code_popup/qr_code_popup.xml:0
msgid "Amount:"
msgstr "Beløp:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
msgid ""
"An error has occurred when trying to close the session.\n"
"You will be redirected to the back-end to manually close the session."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/models/product_product.js:0
msgid ""
"An error occurred when loading product prices. Make sure all pricelists are "
"available in the POS."
msgstr ""
"En feil oppstod ved innlasting av produktpriser. Sjekk at alle prislister er"
" tilgjengelig for kassesystemet."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid ""
"An error occurred while closing the session. Unsynced orders will be "
"available in the next session. The page will be reloaded."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/models/data_service.js:0
msgid "An error occurred while loading the Point of Sale: \n"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__name
msgid "An internal identification of the point of sale."
msgstr "En intern identifikator for kassapunktet."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_printer__name
msgid "An internal identification of the printer"
msgstr "Intern identifikator for skriveren"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid "Another session is already opened for this point of sale."
msgstr "En annen økt er allerede igangsatt for dette kassapunktet."

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_apple_pie_product_template
msgid "Apple Pie"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/input_popups/text_input_popup.xml:0
msgid "Apply"
msgstr "Bruk"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Archived"
msgstr "Arkivert"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid "Are you sure that the customer wants to  pay"
msgstr "Er du sikker på at kunden vil betale"

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.product_sourdough_loaf_product_template
msgid "Artisan sourdough bread with a crisp crust and soft interior."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "As of"
msgstr "Pr"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__picking_policy__direct
msgid "As soon as possible"
msgstr "Så snart som mulig"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment_method.py:0
msgid ""
"At least one bank account must be defined on the journal to allow "
"registering QR code payments with Bank apps."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_update_stock_quantities__closing
msgid "At the session closing"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_company__point_of_sale_update_stock_quantities
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__update_stock_quantities
msgid ""
"At the session closing: A picking is created for the entire session when it's closed\n"
" In real time: Each order sent to the server create its own picking"
msgstr ""

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.sport_shoes_product_template
msgid "Athletic sport shoes, designed for comfort and performance."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__message_attachment_count
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_attachment_count
msgid "Attachment Count"
msgstr "Antall vedlegg"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/product_configurator_popup/product_configurator_popup.xml:0
msgid "Attribute selection"
msgstr ""

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_attribute_action
msgid "Attributes"
msgstr "Attributter"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Authorized Difference"
msgstr "Tillatt differanse"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__auto_validate_terminal_payment
msgid "Auto Validate Terminal Payment"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__rescue
msgid "Auto-generated session for orphan orders, ignored in constraints"
msgstr "Autogenerert økt for ordrer uten tilknyttet økt"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order_line__price_type__automatic
msgid "Automatic"
msgstr "Automatisk"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_auto
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_print_auto
msgid "Automatic Receipt Printing"
msgstr "Automatisk kvitteringsutskrift"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_cashdrawer
msgid "Automatically open the cashdrawer."
msgstr "Åpne kassaskuffen automatisk."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Automatically validate order"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_auto_validate_terminal_payment
#: model:ir.model.fields,help:point_of_sale.field_pos_config__auto_validate_terminal_payment
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_auto_validate_terminal_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Automatically validates orders paid with a payment terminal."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_avatax
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_module_pos_avatax
msgid "AvaTax PoS Integration"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Available"
msgstr "Tilgjengelig"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__available_payment_method_ids
msgid "Available Payment Methods"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_available_categ_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_available_categ_ids
msgid "Available PoS Product Categories"
msgstr "Tilgjengelige produktkategorier for kasse"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__available_pricelist_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_available_pricelist_ids
msgid "Available Pricelists"
msgstr "Tilgjengelige prislister"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__available_in_pos
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__available_in_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_search_view_pos
msgid "Available in POS"
msgstr "Tilgjengelig i kassaløsningen"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__average_price
msgid "Average Price"
msgstr "Gjennomsnittspris"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
msgid "Back"
msgstr "Tilbake"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#: code:addons/point_of_sale/static/src/app/screens/login_screen/login_screen.js:0
msgid "Backend"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__customer_display_bg_img
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_customer_display_bg_img
msgid "Background Image"
msgstr "Bakgrunnsbilde"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__customer_display_bg_img_name
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_customer_display_bg_img_name
msgid "Background Image Name"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_userlabel
msgid "Badge ID"
msgstr "Navnskilt-ID"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_bagel_product_template
msgid "Bagel"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/pos_kanban_view/pos_kanban_view.js:0
msgid "Bakery"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid "Bakery Shop"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_tree
msgid "Balance"
msgstr "Balanse"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__bank
msgid "Bank"
msgstr "Bank"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__bank_payment_ids
msgid "Bank Payments"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Kontoutskrift-linje"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/pos_kanban_view/pos_kanban_view.js:0
msgid "Bar"
msgstr "Søyle"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "Strekkode-nomenklatur"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_barcode_rule
msgid "Barcode Rule"
msgstr "Strekkoderegel"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
msgid "Barcode Scanner"
msgstr "Strekkodeleser"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Barcode Scanner/Card Reader"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__basic_receipt
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_basic_receipt
msgid "Basic Receipt"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_difference
msgid "Before Closing Difference"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Billing address:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_tree
msgid "Bills"
msgstr "Fakturaer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Bills & Receipts"
msgstr ""

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.product_attribute_value_black
msgid "Black"
msgstr "Svart"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.t_shirt_black_embroidered_product_template
msgid "Black embroidered t-shirt"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.blue_denim_jeans_template
msgid "Blue Denim Jeans"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_blueberry_muffin_product_template
msgid "Blueberry Muffin"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Boost your sales with multiple kinds of programs: Coupons, Promotions, Gift "
"Card, Loyalty. Specific conditions can be set (products, customers, minimum "
"purchase amount, period). Rewards can be discounts (% or amount) or free "
"products."
msgstr ""
"Øk salget med flere typer programmer: Kuponger, kampanjer, gavekort, "
"lojalitet. Spesifikke betingelser kan settes (produkter, kunder, "
"minimumskjøp, periode). Belønninger kan være rabatter (prosent eller beløp) "
"eller gratis produkter."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__card_brand
msgid "Brand of card"
msgstr ""

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_breads
msgid "Breads"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
msgid "Buffer:"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_bus_mixin
msgid "Bus Mixin"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_butter_croissant_product_template
msgid "Butter Croissant"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/order_change_receipt_template.xml:0
msgid "By:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_via_proxy
msgid "Bypass browser printing and prints via the hardware proxy."
msgstr "Hopp over nettleserutskrift og print via proxy."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_receipt/cash_move_receipt.xml:0
msgid "CASH"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/models/pos_order.js:0
msgid "CHANGE"
msgstr "VEKSEL"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid "Can't change customer"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_daily_sales_reports_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Cancel"
msgstr "Avbryt"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/control_buttons.xml:0
#: model:ir.actions.server,name:point_of_sale.pos_order_set_cancel
msgid "Cancel Order"
msgstr "Kanseller ordre"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
msgid "Cancel Orders"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/qr_code_popup/qr_code_popup.js:0
msgid "Cancel Payment"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
msgid "Cancel Payment Request"
msgstr "Avbryt betalingsforespørsel"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__cancel
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__cancel
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Cancelled"
msgstr "Kansellert"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/order_summary/order_summary.js:0
msgid "Cannot modify a tip"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid "Cannot return change without a cash payment method"
msgstr "Kan ikke gi veksel uten en betalingsmetode av type kontant "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/scale_screen/scale_service.js:0
msgid "Cannot weigh product - IoT Box is disconnected"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/scale_screen/scale_service.js:0
msgid "Cannot weigh product - Scale is not connected to IoT Box"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid "Card"
msgstr "Kort"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__card_no
msgid "Card Number(Last 4 Digit)"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__cardholder_name
msgid "Card Owner name"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Card's Brand"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
msgid "Cardholder Name"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
msgid "Cart"
msgstr "Kort"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__is_cash_count
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__cash
#: model:pos.payment.method,name:point_of_sale.cash_payment_method_furniture
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Cash"
msgstr "Kontanter"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/report_sale_details.py:0
msgid "Cash %(session_name)s"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid "Cash Bakery"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid "Cash Clothes Shop"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
msgid "Cash Count"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid "Cash Furn. Shop"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
msgid "Cash In"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
msgid "Cash In / Out"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
msgid "Cash In/Out"
msgstr "Innskudd/uttak"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_journal_id
msgid "Cash Journal"
msgstr "Kontantjournal"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__statement_line_ids
msgid "Cash Lines"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Cash Move 1"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/report_sale_details.py:0
msgid "Cash Opening"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
msgid "Cash Out"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__cash_rounding
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Cash Rounding"
msgstr "Avrunding"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_cash_rounding
msgid "Cash Rounding (PoS)"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Cash Roundings"
msgstr "Avrundinger"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
msgid "Cash control - closing"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/opening_control_popup/opening_control_popup.js:0
msgid "Cash control - opening"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid "Cash difference observed during the counting (Loss) - closing"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid "Cash difference observed during the counting (Profit) - closing"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid "Cash in / out"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.js:0
msgid "Cash in/out of %s is ignored."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid "Cash register"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__rounding_method
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_rounding_method
msgid "Cash rounding"
msgstr "Avrunding"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_cashdrawer
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_cashdrawer
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Cashdrawer"
msgstr "Kassaskuff"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__cashier
msgid "Cashier"
msgstr "Ekspeditør"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.casual_denim_short_product_template
msgid "Casual Denim Short"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.casual_t_shirt_product_template
msgid "Casual T-shirt"
msgstr ""

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.casual_denim_short_product_template
msgid "Casual denim shorts, comfortable and stylish for everyday wear."
msgstr ""

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.t_shirt_black_embroidered_product_template
msgid ""
"Casual slim t-shirt 100% cotton with white embroidery. Various sizes "
"available"
msgstr ""

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.t_shirt_slim_product_template
msgid "Casual slim t-shirt 100% cotton. Various sizes available"
msgstr ""

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.casual_t_shirt_product_template
msgid "Casual t-shirt 100% cotton. Various colors and sizes available."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid ""
"Categories are used to browse your products through the\n"
"                touchscreen interface."
msgstr ""
"Kategorier brukes for å bla igjennom produkter\n"
"i brukergrensesnittet."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/category_selector/category_selector.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_category_kanban
msgid "Category"
msgstr "Kategori"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__name
msgid "Category Name"
msgstr "Kategorinavn"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__pos_categ_ids
#: model:ir.model.fields,help:point_of_sale.field_product_template__pos_categ_ids
msgid "Category used in the Point of Sale."
msgstr "Kategorier brukt i kassasystemet."

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_chairs
msgid "Chairs"
msgstr "Stoler"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_status/payment_status.xml:0
#: code:addons/point_of_sale/static/src/customer_display/customer_display.xml:0
msgid "Change"
msgstr "Veksel"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid "Change Tip"
msgstr "Endre tips"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__to_weight
#: model:ir.model.fields,help:point_of_sale.field_product_template__to_weight
msgid ""
"Check if the product should be weighted using the hardware scale "
"integration."
msgstr "Huk av om produktet skal veies med integrert vekt."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__available_in_pos
#: model:ir.model.fields,help:point_of_sale.field_product_template__available_in_pos
msgid "Check if you want this product to appear in the Point of Sale."
msgstr "Huk av om dette produktet skal vises i kassasystemet."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_uom_category__is_pos_groupable
#: model:ir.model.fields,help:point_of_sale.field_uom_uom__is_pos_groupable
msgid ""
"Check if you want to group products of this category in point of sale orders"
msgstr ""
"Huk av om du ønsker å gruppere produkter i denne kategorien på kassaordrer."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__cash_control
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_cash_control
msgid "Check the amount of the cashbox at opening and closing."
msgstr "Tell opp kontantbeholdning ved åpning og lukking av økt."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
msgid ""
"Check the internet connection then try to sync again by clicking on the red "
"wifi button (upper right of the screen)."
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_cheese_croissant_product_template
msgid "Cheese Croissant"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_cherry_pie_product_template
msgid "Cherry Pie"
msgstr ""

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.product_bagel_product_template
msgid "Chewy and dense bagel, perfect for sandwiches or with cream cheese."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__child_ids
msgid "Children Categories"
msgstr "Underkategorier"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Choose a specific fiscal position at the order depending on the kind of "
"customer (tax exempt, onsite vs. takeaway, etc.)."
msgstr ""
"Velg en spesifikk avgiftsregel på ordren, avhenging av type kunde "
"(avgiftsfri, spise inne, takeaway osv)."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/list_container/list_container.js:0
msgid "Choose an order"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_company__point_of_sale_ticket_portal_url_display_mode
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__point_of_sale_ticket_portal_url_display_mode
msgid "Choose how the URL to the portal will be print on the receipt."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/pos_kanban_view/pos_kanban_view.xml:0
msgid "Choose your store"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_cinnamon_roll_product_template
msgid "Cinnamon Roll"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_classic_brown_jacket_product_template
msgid "Classic Brown Jacket"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.classic_leather_belt_product_template
msgid "Classic Leather Belt"
msgstr ""

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.classic_leather_belt_product_template
msgid "Classic leather belt, a must-have accessory for any wardrobe."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
msgid "Clear Cache"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Click here to close the session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__client
msgid "Client"
msgstr "Kunde"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/components/tour_selector_popup/tour_selector_popup.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sync_popup/sync_popup.xml:0
#: code:addons/point_of_sale/static/src/app/screens/scale_screen/scale_screen.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Close"
msgstr "Lukk"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
msgid "Close Register"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
msgid "Close Session"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Close Session & Post Entries"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_close_session_wizard
msgid "Close Session Wizard"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__closed
msgid "Closed & Posted"
msgstr "Lukket og postert"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid "Closed Register"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_tree
msgid "Closing"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__closing_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Closing Control"
msgstr "Avslutningskontroll"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__stop_at
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Closing Date"
msgstr "Avslutningsdato"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_closing_entry_by_product
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_closing_entry_by_product
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Closing Entry by product"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__closing_notes
msgid "Closing Notes"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
msgid "Closing Register"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid "Closing Session"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid "Closing difference in %(payment_method)s (%(session)s)"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
msgid "Closing note"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
msgid "Closing session error"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/pos_kanban_view/pos_kanban_view.js:0
msgid "Clothes"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid "Clothes Shop"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__value
msgid "Coin/Bill Value"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_bill
#: model:ir.model,name:point_of_sale.model_pos_bill
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__default_bill_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_default_bill_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_bill
msgid "Coins/Bills"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/money_details_popup/money_details_popup.xml:0
msgid "Coins/Notes"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__color
#: model:product.attribute,name:point_of_sale.product_attribute_color
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_product_view_form_normalized_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
msgid "Color"
msgstr "Farge"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__color
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__color
msgid "Color Index"
msgstr "Fargeindeks"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid "Combine %(payment_method)s POS payments from %(session)s"
msgstr ""

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.product_t_shirt_pants_product_template
msgid "Combo"
msgstr ""

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_product_combo
msgid "Combo Choices"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__combo_item_id
msgid "Combo Item"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__combo_line_ids
msgid "Combo Lines"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__combo_parent_id
msgid "Combo Parent"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_company
msgid "Companies"
msgstr "Firmaer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__company_id
msgid "Company"
msgstr "Firma"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__company_has_template
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_company_has_template
msgid "Company has chart of accounts"
msgstr "Firma har kontoplan"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/combo_configurator_popup/combo_configurator_popup.xml:0
msgid "Complete the selection to proceed"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_config_settings
msgid "Config Settings"
msgstr "Innstillinger"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_config_product
msgid "Configuration"
msgstr "Konfigurasjon"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Configurations &gt; Settings"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_tree
msgid "Configure at least one Point of Sale."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/components/tour_selector_popup/tour_selector_popup.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#: code:addons/point_of_sale/static/src/app/utils/date_picker_popup/date_picker_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/money_details_popup/money_details_popup.xml:0
msgid "Confirm"
msgstr "Bekreft"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/qr_code_popup/qr_code_popup.js:0
msgid "Confirm Payment"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/input_popups/number_popup.js:0
msgid "Confirm?"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Connect device to your PoS without an IoT Box"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__other_devices
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_other_devices
msgid "Connect devices to your PoS without an IoT Box."
msgstr "Koble enheter til kassasystemet uten en IoT Box."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Connect devices using an IoT Box"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Connected Devices"
msgstr "Tilkoblede enheter"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
msgid "Connecting to Proxy"
msgstr "Kobler til proxy"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/customer_display/utils.js:0
msgid "Connecting to the IoT Box"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/error_handlers.js:0
msgid "Connection Lost"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
msgid "Connection error"
msgstr "Tilkoblingsfeil"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/customer_display/utils.js:0
msgid "Connection failed"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/customer_display/utils.js:0
msgid "Connection successful"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/base_printer.js:0
msgid "Connection to IoT Box failed"
msgstr "Tilkobling til IoT Box feilet"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/base_printer.js:0
msgid "Connection to the printer failed"
msgstr "Tilkobling til skriveren feilet"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid ""
"Connection to the server has been lost. Please check your internet "
"connection."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: model:ir.model,name:point_of_sale.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Contact Info"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Continue Selling"
msgstr "Fortsett å selge"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/error_handlers.js:0
msgid "Continue with limited functionality"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_picking.py:0
msgid ""
"Conversion Error: The following unit of measure conversions result in a zero"
" quantity due to rounding:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__currency_rate
msgid "Conversion Rate"
msgstr "Konverteringsfrekvens"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__currency_rate
msgid "Conversion rate from company currency to order currency."
msgstr "Konverteringssats mellom firmavaluta og ordevaluta."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
msgid "Cost:"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sync_popup/sync_popup.xml:0
msgid "Count"
msgstr "Antall"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Counted"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_country
msgid "Country"
msgstr "Land"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__country_code
msgid "Country Code"
msgstr "Landskode"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_country_state
msgid "Country state"
msgstr "Navn på stat"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.cozy_sweater_product_template
msgid "Cozy Sweater"
msgstr ""

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.cozy_sweater_product_template
msgid "Cozy sweater, perfect for colder weather. Available in various colors."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
msgid "Create"
msgstr "Opprett"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
msgid "Create Product"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_sale_graph
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all_filtered
msgid "Create a new POS order"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_tree
msgid "Create a new PoS"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_product_action
msgid "Create a new product variant"
msgstr "Opprett en ny produktvariant"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/pos_kanban_view/pos_kanban_view.xml:0
msgid "Create my own products"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_template_action_pos_product
msgid "Create new product"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_note__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__create_uid
msgid "Created by"
msgstr "Opprettet av"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_note__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__create_date
msgid "Created on"
msgstr "Opprettet den"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_crocheted_poncho_unisize_product_template
msgid "Crocheted Poncho Unisize"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_currency
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__currency_rate
msgid "Currency Rate"
msgstr "Valutakurs"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_session_id
msgid "Current Session"
msgstr "Aktiv økt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_user_id
msgid "Current Session Responsible"
msgstr "Ansvarlig for økten"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_session_state
msgid "Current Session State"
msgstr "Status for aktiv økt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_header_or_footer
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_header_or_footer
msgid "Custom Header & Footer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__custom_attribute_value_ids
msgid "Custom Values"
msgstr "Tilpassede verdier"

#. module: point_of_sale
#. odoo-javascript
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/select_partner_button/select_partner_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__partner_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__partner_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__partner_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Customer"
msgstr "Kunde"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__pay_later
msgid "Customer Account"
msgstr "Kundekonto"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Customer Display"
msgstr "Kundeskjerm"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__customer_display_type
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_customer_display_type
msgid "Customer Facing Display"
msgstr "Kundeskjerm"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "Customer Invoice"
msgstr "Kundefaktura"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_note_button/customer_note_button.js:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__customer_note
msgid "Customer Note"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__access_url
msgid "Customer Portal URL"
msgstr "Link til kundeportal"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid "Customer Required"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#. odoo-python
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#: code:addons/point_of_sale/wizard/pos_payment.py:0
msgid "Customer is required for %s payment method."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/order_summary/order_summary.js:0
msgid "Customer tips, cannot be modified directly"
msgstr ""

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale_customer
msgid "Customers"
msgstr "Kunder"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
msgid "Daily Sale"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Daily Sales Report"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session_filtered
msgid "Daily sessions hold sales from your Point of Sale."
msgstr ""

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_pos_dashboard
msgid "Dashboard"
msgstr "Dashbord"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sync_popup/sync_popup.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__date_order
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_date
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Date"
msgstr "Dato"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/date_picker_popup/date_picker_popup.js:0
msgid "DatePicker"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
msgid "Days"
msgstr "Dager"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
msgid "Debug Window"
msgstr "Debug-vindu"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_decimal_precision
msgid "Decimal Precision"
msgstr "Desimalpresisjon"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default"
msgstr "Standard"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__account_default_pos_receivable_account_id
msgid "Default Account Receivable (PoS)"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__default_fiscal_position_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_default_fiscal_position_id
msgid "Default Fiscal Position"
msgstr "Standard avgiftsregel"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Journals"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/control_buttons.js:0
msgid "Default Price"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pricelist_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_pricelist_id
msgid "Default Pricelist"
msgstr "Standard prisliste"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__default_qr
msgid "Default Qr"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__sale_tax_id
msgid "Default Sale Tax"
msgstr "Standard utgående avgift"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Sales Tax"
msgstr "Standard utgående avgift"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Temporary Account"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default journals for orders and invoices"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default sales tax for products"
msgstr "Standard avgift for produkter"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "Standardenhet for alle lageroperasjoner."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid "Define a new category"
msgstr "Definer en ny kategori"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_template_action_pos_product
msgid "Define products and categories for POS retail and restaurant"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Define the smallest coinage of the currency used to pay by cash"
msgstr ""
"Bruk den minste verdien for valutaen som brukes til betaling med kontanter"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__name
msgid ""
"Defines the name of the payment method that will be displayed in the Point "
"of Sale when the payments are selected."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__delay_validation
msgid "Delay Validation"
msgstr "Utsett validering"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sync_popup/sync_popup.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
msgid "Delete"
msgstr "Slett"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.js:0
msgid "Delete Orders?"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
msgid "Delete Paid Orders"
msgstr "Slett betalte ordrer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
msgid "Delete Unpaid Orders"
msgstr "Slett ubetalte ordrer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sync_popup/sync_popup.js:0
msgid "Delete pending record?"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Demo 3-03-2000 5:00 PM"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Demo Name"
msgstr ""

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.product_rye_bread_product_template
msgid "Dense and dark, made with rye flour, perfect for sandwiches."
msgstr ""

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_desks
msgid "Desks"
msgstr "Arbeidspulter"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__account_id
msgid "Destination account"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__account_readonly
msgid "Destination account is readonly"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
msgid "Details"
msgstr "Detaljer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_difference
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Difference"
msgstr "Differanse"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid "Difference at closing PoS session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_difference
msgid ""
"Difference between the theoretical closing balance and the real closing "
"balance."
msgstr "Differanse mellom teoretisk og opptalt kassebeholdning."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_digest_digest
msgid "Digest"
msgstr "Digest"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/order_change_receipt_template.xml:0
msgid "Dine In"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/order_change_receipt_template.xml:0
msgid "Dine In -> Take Out"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/control_buttons.js:0
msgid "Dine in"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Disc.%"
msgstr "Rab.%"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Disc:"
msgstr "Rab:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: code:addons/point_of_sale/static/src/app/store/opening_control_popup/opening_control_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/product_configurator_popup/product_configurator_popup.xml:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/text_input_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Discard"
msgstr "Avbryt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__discount
msgid "Discount (%)"
msgstr "Rabatt (%)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__notice
msgid "Discount Notice"
msgstr "Rabattnotat"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
msgid "Discount:"
msgstr "Rabatt:"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__discount
msgid "Discounted Product"
msgstr "Rabattert Produkt"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/models/pos_order.js:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Discounts"
msgstr "Rabatter"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
msgid "Dismiss"
msgstr "Avvis"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_note__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__display_name
msgid "Display Name"
msgstr "Visningsnavn"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Display and update your products information through electronic price tags"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Display orders on the preparation display"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__is_closing_entry_by_product
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_is_closing_entry_by_product
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Display the breakdown of sales lines by product in the automatically "
"generated closing entry."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_pricer
msgid "Display the price of your products through electronic price tags"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/digest.py:0
msgid "Do not have access, skip this data for user's digest email"
msgstr "Har ikke tilgang, hopp over denne dataen for brukerens digest e-post"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
msgid "Do you want to open the customer list to select customer?"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/pos_printer_service.js:0
msgid "Do you want to print using the web printer? "
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
msgid "Download a report with all the sales of the current PoS Session"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_printer_form
msgid ""
"Each Order Printer has an IP Address that defines the IoT Box/Hardware\n"
"            Proxy where the printer can be found, and a list of product categories.\n"
"            An Order Printer will only print updates for products belonging to one of\n"
"            its categories."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Edit"
msgstr "Rediger"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_line/partner_line.xml:0
msgid "Edit Details"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.res_partner_action_edit_pos
msgid "Edit Partner"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
msgid "Edit Payment"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_product_action_edit_pos
msgid "Edit Product"
msgstr "Endre produkt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_edited
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__is_edited
msgid "Edited"
msgstr "Endret"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid "Edited order(s) during the session:%s"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_electronic_scale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_electronic_scale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Electronic Scale"
msgstr "Elektronisk vekt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__email
msgid "Email"
msgstr "E-post"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Veiviser for utforming av e-post"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/mail_compose_message.py:0
msgid "Email triggered successfully!"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__user_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__user_id
msgid "Employee"
msgstr "Ansatt"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__user_id
msgid "Employee who uses the cash register."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Employees can scan their badge or enter a PIN to log in to a PoS session. "
"These credentials are configurable in the *HR Settings* tab of the employee "
"form."
msgstr ""
"Ansatte kan skanne kortet sitt, eller taste inn pin-kode for å logge seg inn"
" i en kassaøkt. Dette kan konfigureres under fanen for HR-innstillinger i "
"skjemaet for ansatte."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid "Empty Order"
msgstr "Tom ordre"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_scan_via_proxy
msgid ""
"Enable barcode scanning with a remotely connected barcode scanner and card "
"swiping with a Vantiv card reader."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_electronic_scale
msgid "Enables Electronic Scale integration."
msgstr "Aktiverer integrasjon med elektronisk vekt."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__end_date
msgid "End Date"
msgstr "Avslutningsdato"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_end_real
msgid "Ending Balance"
msgstr "Utgående saldo"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid ""
"Ensure that there is an existing bank journal. Check if chart of accounts is"
" installed in your company."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid "Error"
msgstr "Feil"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_category.py:0
msgid "Error! You cannot create recursive categories."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid "Error: no internet connection."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid "Existing orderlines"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Expected"
msgstr "Forventet"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
msgid "Expected delivery:"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
msgid "Export Paid Orders"
msgstr "Eksporter betalte ordrer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
msgid "Export Unpaid Orders"
msgstr "Eksporter ubetalte ordrer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Extra Info"
msgstr "Tilleggsinformasjon"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__failed_pickings
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__failed_pickings
msgid "Failed Pickings"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid "Failed in printing %s changes of the order"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid "Failure to generate Payment QR Code"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_ir_binary
msgid "File streaming helper model for controllers"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
msgid "Financials"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_fiscal_position
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__fiscal_position_id
msgid "Fiscal Position"
msgstr "Avgiftsregel"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
msgid "Fiscal Position not found"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__fiscal_position_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_fiscal_position_ids
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Fiscal Positions"
msgstr "Avgiftsregler"

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.product_butter_croissant_product_template
msgid "Flaky all butter pastry, layered for a light and golden croissant."
msgstr ""

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.product_cheese_croissant_product_template
msgid ""
"Flaky croissant filled with creamy cheese, a savory twist on a classic."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Flexible Pricelists"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Flexible Taxes"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/pos_kanban_view/pos_kanban_view.js:0
msgid "Floor plan, tips, self order, etc."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__message_follower_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_follower_ids
msgid "Followers"
msgstr "Følgere"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__message_partner_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_partner_ids
msgid "Followers (Partners)"
msgstr "Følgere (partnere)"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font Awesome-ikon, for eksempel fa-tasks"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/pos_kanban_view/pos_kanban_view.js:0
msgid "Food, but over the counter"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Footer"
msgstr "Bunntekst"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__for_all_config
msgid "For All PoS"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_big_scrollbars
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_iface_big_scrollbars
msgid "For imprecise industrial touchscreens."
msgstr "For upresise industrielle berøringsskjermer."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
msgid "Force Close Session"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
msgid "Force Done"
msgstr "Tving ferdigstilt"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
msgid "Force done"
msgstr "Tving ferdigstilt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__force_outstanding_account_id
msgid "Forced Outstanding Account"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__split_transactions
msgid ""
"Forces to set a customer when using this payment method and splits the "
"journal entries for each customer. It could slow down the closing process."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#: code:addons/point_of_sale/static/src/app/models/pos_order_line.js:0
msgid "Free"
msgstr "Gratis"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid "From invoice payments"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__full_product_name
msgid "Full Product Name"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/pos_kanban_view/pos_kanban_view.js:0
msgid "Furniture"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid "Furniture Shop"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/order_widget/order_widget.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/control_buttons.js:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_note_button/customer_note_button.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__general_note
msgid "General Note"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "General Notes"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_company__point_of_sale_ticket_unique_code
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__point_of_sale_ticket_unique_code
msgid "Generate a code on ticket"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Generation of your order references"
msgstr "Generering av ordrereferanser"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/scale_screen/scale_screen.xml:0
msgid "Get Weight"
msgstr "Les vekt"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Get my invoice"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_category__sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr "Setter rekkefølgen når en liste med produktkategorier vises."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_discount
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_module_pos_discount
msgid "Global Discounts"
msgstr "Globale rabatter"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/back_button/back_button.xml:0
msgid "Go Back"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Go to"
msgstr "Gå til"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/models/pos_order_line.js:0
msgid "Greater than allowed"
msgstr ""

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.product_attribute_value_green
msgid "Green"
msgstr "Grønn"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_green_hood_product_template
msgid "Green Hood"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/scale_screen/scale_screen.xml:0
#: code:addons/point_of_sale/static/src/customer_display/customer_display.xml:0
msgid "Gross Weight:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Group By"
msgstr "Grupper etter"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_uom_category__is_pos_groupable
#: model:ir.model.fields,field_description:point_of_sale.field_uom_uom__is_pos_groupable
msgid "Group Products in POS"
msgstr "Grupper produkter i kassasystemet"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Group items in the cart according to their category"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-ruting"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid "HTTPS connection to IoT Box failed"
msgstr "Sikker tilkobling til IoT Box feilet"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
msgid "Hardware Events"
msgstr "Maskinvarehandlinger"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
msgid "Hardware Status"
msgstr "Maskinvarestatus"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__has_active_session
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_has_active_session
msgid "Has Active Session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_control
msgid "Has Cash Control"
msgstr "Har kontantkontroll"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__has_deleted_line
msgid "Has Deleted Line"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__has_image
msgid "Has Image"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__has_message
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__has_message
msgid "Has Message"
msgstr "Har melding"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__has_refundable_lines
msgid "Has Refundable Lines"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Header"
msgstr "Topptekst"

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.product_multigrain_bread_product_template
msgid "Hearty multigrain loaf with seeds and grains for extra nutrition."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__hide_qr_code_method
msgid "Hide Qr Code Method"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__hide_use_payment_terminal
msgid "Hide Use Payment Terminal"
msgstr "Gjem bruk betalingsterminal"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Hide pictures in POS"
msgstr ""

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.product_wholemeal_loaf_product_template
msgid ""
"High fibre loaf. Baked with fine wholemeal flour to give a soft texture."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "How to manage tax-included prices"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_note__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__id
msgid "ID"
msgstr "ID"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__proxy_ip
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_proxy_ip
msgid "IP Address"
msgstr "IP-adresse"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon for å indikere aktivitetsunntak."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__split_transactions
msgid "Identify Customer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__message_needaction
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Hvis haket av, vil nye meldinger kreve din oppmerksomhet."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__message_has_error
#: model:ir.model.fields,help:point_of_sale.field_pos_order__message_has_sms_error
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_error
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Hvis haket av, har enkelte meldinger leveringsfeil."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_bill__for_all_config
msgid "If checked, this coin/bill will be available in all PoS."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refunded_orderline_id
msgid ""
"If this orderline is a refund, then the refunded orderline is specified in "
"this field."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__picking_policy
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_picking_policy
msgid ""
"If you deliver all products at once, the delivery order will be scheduled "
"based on the greatest product lead time. Otherwise, it will be based on the "
"shortest."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__image_128
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__image
msgid "Image"
msgstr "Bilde"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
msgid "Import Orders"
msgstr "Importer ordrer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Improve navigation for imprecise industrial touchscreens"
msgstr "Forbedre navigasjonen for upresise industrielle berøringsskjermer "

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__opened
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "In Progress"
msgstr "Pågår"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "In order to delete a sale, it must be new or cancelled."
msgstr "For å slette en ordre, må den ha status som ny eller avbrutt."

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_update_stock_quantities__real
msgid "In real time"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid "Incorrect address for shipping"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
msgid "Information about your product."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__message
msgid "Information message"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
msgid "Install App"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_method_form
msgid ""
"Installing chart of accounts from the General Settings of\n"
"                Invocing/Accounting app will create Bank and Cash payment\n"
"                methods automatically."
msgstr ""
"Installasjon av kontoplan fra Generelle Innstillinger for\n"
"Fakturerings/Regnskaps-appen, vil automatisk opprette betalingsmetoder\n"
"for bank og kontant."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Integrate with"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__payment_method_type
msgid "Integration"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__receivable_account_id
msgid "Intermediary Account"
msgstr "Motpartskonto"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Intermediary account used for unidentified customers."
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_category_action
msgid "Internal Categories"
msgstr "Interne kategorier"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/control_buttons.js:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_note_button/customer_note_button.js:0
msgid "Internal Note"
msgstr "Internt notat"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Internal Notes"
msgstr "Interne notater"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/order_summary/order_summary.js:0
msgid "Invalid action"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/components/product_info_banner/product_info_banner.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Inventory"
msgstr "Lager"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Inventory Management"
msgstr "Administrasjon av lagerbeholdning"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__account_move
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Invoice"
msgstr "Faktura"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__invoice_journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_invoice_journal_id
msgid "Invoice Journal"
msgstr "Fakturajournal"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Invoice Name"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Invoice Request"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment.py:0
msgid ""
"Invoice payment for %(order)s (%(account_move)s) using %(payment_method)s"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__invoiced
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__invoiced
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__invoiced
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Invoiced"
msgstr "Fakturert"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Invoices"
msgstr "Fakturaer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Invoicing confirmation"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "IoT Box"
msgstr "IoT-boks"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "IoT Box IP Address"
msgstr "IoT Box IP-adresse"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/customer_display/customer_display_data_service.js:0
msgid "IoT Customer Display Error"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__message_is_follower
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_is_follower
msgid "Is Follower"
msgstr "Er følger"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_invoiced
msgid "Is Invoiced"
msgstr "Er fakturert"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__is_kiosk_mode
msgid "Is Kiosk Mode"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_total_cost_computed
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__is_total_cost_computed
msgid "Is Total Cost Computed"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__is_in_company_currency
msgid "Is Using Company Currency"
msgstr "Bruker firmaets valuta"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_restaurant
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_module_pos_restaurant
msgid "Is a Bar/Restaurant"
msgstr "Er en bar/restaurant"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_installed_account_accountant
msgid "Is the Full Accounting Installed"
msgstr "Regnskapsmodul installert"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_tipped
msgid "Is this already tipped?"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__is_change
msgid "Is this payment change?"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/account_tax.py:0
msgid ""
"It is forbidden to modify a tax used in a POS order not posted. You must "
"close the POS sessions before modifying the tax."
msgstr ""
"Du kan ikke endre avgifter som er brukt i en upostert kassaordre. Du må "
"lukke kassaøkten før du kan endre avgiften."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid "It is not allowed to mix refunds and sales"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/pos_printer_service.js:0
msgid "It is possible to print your tickets by making use of an IoT Box."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/pos_kanban_view/pos_kanban_view.js:0
msgid ""
"It seems like you don't have enough rights to create point of sale "
"configurations."
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.jean_jacket_product_template
msgid "Jean Jacket"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_journal
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__journal_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
msgid "Journal"
msgstr "Journal"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_move
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__move_id
msgid "Journal Entry"
msgstr "Bilag"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_move_line
msgid "Journal Item"
msgstr "Bilagslinje"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid "Journal Items"
msgstr "Bilagslinjer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/control_buttons.js:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_note_button/customer_note_button.js:0
msgid "Kitchen Note"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_digest_digest__kpi_pos_total_value
msgid "Kpi Pos Total Value"
msgstr "Kpi Kasse Total Verdi"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.led_lamp_product_template
msgid "LED Lamp"
msgstr "LED-lampe"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__name
msgid "Label"
msgstr "Tekst"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_lang
msgid "Languages"
msgstr "Språk"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Laptop"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Laptop model x"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_big_scrollbars
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_big_scrollbars
msgid "Large Scrollbars"
msgstr "Store rullefelt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__last_session_closing_cash
msgid "Last Session Closing Cash"
msgstr "Kontantbeholdning for siste økt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__last_session_closing_date
msgid "Last Session Closing Date"
msgstr "Dato for avslutning av siste økt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_note__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__write_uid
msgid "Last Updated by"
msgstr "Sist oppdatert av"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_note__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__write_date
msgid "Last Updated on"
msgstr "Sist oppdatert"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__last_order_preparation_change
msgid "Last preparation change"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__last_order_preparation_change
msgid "Last printed state of the order"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/components/tour_selector_popup/tour_selector_popup.xml:0
msgid "Launch fake tours"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.leather_jacket_product_template
msgid "Leather Jacket"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
msgid "Leave a reason here"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Leave empty to use the default account from the company setting"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__receivable_account_id
msgid ""
"Leave empty to use the default account from the company setting.\n"
"Overrides the company's receivable account (for Point of Sale) used in the journal entries."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Leave empty to use the receivable account of customer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__journal_id
msgid ""
"Leave empty to use the receivable account of customer.\n"
"Defines the journal where to book the accumulated payments (or individual payment if Identify Customer is true) after closing the session.\n"
"For cash journal, we directly write to the default account in the journal via statement lines.\n"
"For bank journal, we write to the outstanding account specified in this payment method.\n"
"Only cash and bank journals are allowed."
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.letter_tray_product_template
msgid "Letter Tray"
msgstr "Brevkurv"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.blue_denim_jeans_slim_template
msgid "Light Blue Jeans Slim"
msgstr ""

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.product_classic_brown_jacket_product_template
msgid ""
"Lightweight bomber jacket in linen with a ribbed stand-up collar and zip "
"down the front."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__manual_discount
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_manual_discount
msgid "Line Discounts"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__name
msgid "Line No"
msgstr "Linjenr."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
msgid "Load Order"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.client,name:point_of_sale.action_client_product_menu
msgid "Load Product Menu"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Log in with Employees"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__login_number
msgid "Login Sequence Number"
msgstr "Sekvensnummer for innlogging"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#: code:addons/point_of_sale/static/src/app/screens/login_screen/login_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/receipt_header/receipt_header.xml:0
#: code:addons/point_of_sale/static/src/app/screens/saver_screen/saver_screen.xml:0
msgid "Logo"
msgstr "Logo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__lot_name
msgid "Lot Name"
msgstr "Partinavn"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid "Lot/Serial Number(s) Required"
msgstr "Parti-/serienummer påkrevd"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__pack_lot_ids
msgid "Lot/serial Number"
msgstr "Parti-/serienummer"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_lower
msgid "Lower body"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.magnetic_board_product_template
msgid "Magnetic Board"
msgstr "Magnettavle"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Make Payment"
msgstr "Utfør betaling"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__available_pricelist_ids
msgid ""
"Make several pricelists available in the Point of Sale. You can also apply a"
" pricelist to specific customers from their contact form (in Sales tab). To "
"be valid, this pricelist must be listed here as an available pricelist. "
"Otherwise the default pricelist will apply."
msgstr ""
"Gjør flere prislister tilgjengelig for kassasystemet. Du kan også knytte en "
"prisliste til en spesifikk kunde i kundeskjemaet (i salgsfanen). For at "
"dette skal fungere må prislisten være i denne listen. Ellers vil standard "
"prisliste brukes."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/customer_display/customer_display_data_service.js:0
msgid ""
"Make sure there is an IoT Box subscription associated with your Odoo "
"database, then restart the IoT Box."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid ""
"Make sure you are using IoT Box v18.12 or higher. Navigate to %s to accept "
"the certificate of your IoT Box."
msgstr ""
"Forsikre deg om at du bruker IoT Box v18.12 eller høyere. Naviger til %s for"
" å akseptere sertifikatet til IoT-boksen."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage promotion that will grant customers discounts or gifts"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order_line__price_type__manual
msgid "Manual"
msgstr "Manuell"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_userlabel
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__margin
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__margin
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__margin
msgid "Margin"
msgstr "Margin"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__margin_percent
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__margin_percent
msgid "Margin (%)"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
msgid "Margin:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_margins_costs_accessible_to_every_user
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_margins_costs_accessible_to_every_user
msgid "Margins & Costs"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
msgid "Maximum Exceeded"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid "Maximum value reached"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Mercado Pago"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_mercado_pago
msgid "Mercado Pago Payment Terminal"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__message_has_error
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_error
msgid "Message Delivery error"
msgstr "Melding ved leveringsfeil"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__message_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_ids
msgid "Messages"
msgstr "Meldinger"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__name
msgid "Method"
msgstr "Metode"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
msgid "Method Name"
msgstr ""

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_miscellaneous
msgid "Misc"
msgstr "Diverse"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__mobile
msgid "Mobile"
msgstr "Mobil"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sync_popup/sync_popup.xml:0
msgid "Model"
msgstr "Modell"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_ir_module_module
msgid "Module"
msgstr "Modul"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_hr
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_module_pos_hr
msgid "Module Pos Hr"
msgstr "Modul Pos Hr"

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.product_blueberry_muffin_product_template
msgid "Moist and fluffy muffins bursting with blueberries."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "More settings:"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/pos_kanban_view/pos_kanban_view.js:0
msgid "Multi colors and sizes"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_multigrain_bread_product_template
msgid "Multigrain Bread"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "MIn aktivitets tidsfrist"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "My Sessions"
msgstr "Mine økter"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/components/product_info_banner/product_info_banner.xml:0
msgid "N/A"
msgstr "N/A"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_note__name
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_userlabel
msgid "Name"
msgstr "Navn"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.js:0
msgid "Navigate to your PoS Customer Display on the other computer"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
msgid "Need an invoice for your purchase ?"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
msgid "Need customer to invoice"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid ""
"Need loss account for the following journals to post the lost amount: %s\n"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid ""
"Need profit account for the following journals to post the gained amount: %s"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/scale_screen/scale_screen.xml:0
#: code:addons/point_of_sale/static/src/customer_display/customer_display.xml:0
msgid "Net Weight:"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
msgid "Network Error"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__draft
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__draft
msgid "New"
msgstr "Ny"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
msgid "New Order"
msgstr "Ny ordre"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_product_action_add_pos
msgid "New Product"
msgstr "Nytt produkt"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.js:0
msgid "New amount"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.newspaper_rack_product_template
msgid "Newspaper Rack"
msgstr "Avisstativ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Neste kalender aktivitet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Frist for neste aktivitet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_summary
msgid "Next Activity Summary"
msgstr "Oppsummering av neste aktivitet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_type_id
msgid "Next Activity Type"
msgstr "Neste aktivitetstype"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
msgid "Next Order List"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid "No"
msgstr "Nei"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "No PoS configuration found"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "No Point of Sale selected"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/report_sale_details.py:0
msgid "No Taxes"
msgstr "Ingen avgifter"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/barcode_reader_service.js:0
msgid ""
"No barcode nomenclature has been configured. This can be changed in the "
"configuration settings."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid ""
"No cash statement found for this session. Unable to record returned cash."
msgstr ""
"Ingen kontantuttalelse funnet for denne økten. Kan ikke registrere "
"returnerte kontanter."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid ""
"No chart of account configured, go to the \"configuration / settings\" menu,"
" and install one from the Invoicing tab."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_sale_graph
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all_filtered
msgid "No data yet!"
msgstr "Ingen data ennå"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid "No existing serial/lot number"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/edit_list_input/edit_list_input.xml:0
msgid "No existing serial/lot number matching..."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/report/pos_invoice.py:0
msgid "No link to an invoice for %s."
msgstr "Ingen tilknyttet faktura for %s."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.js:0
msgid "No more customer found for \"%s\"."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid ""
"No open session available. Please open a new session to capture the order."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_form
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
msgid "No orders found"
msgstr "Ingen ordre funnet"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
msgid "No other products found for \"%s\"."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
msgid "No products found for"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
msgid "No sale order found."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session_filtered
msgid "No sessions found"
msgstr "Ingen økter funnet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Not Cancelled"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/report_sale_details.py:0
msgid "Not Categorized"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Not Invoiced"
msgstr "Ikke fakturert"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_note_model
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__note_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_note_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_note_model
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_note_tree
msgid "Note Models"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Notes"
msgstr "Notater"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__message_needaction_counter
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_needaction_counter
msgid "Number of Actions"
msgstr "Antall handlinger"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__nb_print
msgid "Number of Print"
msgstr "Antall utskrifter"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refund_orders_count
msgid "Number of Refund Orders"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__number_of_rescue_session
msgid "Number of Rescue Session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__message_has_error_counter
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_error_counter
msgid "Number of errors"
msgstr "Antall feil"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refunded_qty
msgid "Number of items refunded in this orderline."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__message_needaction_counter
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Antall beskjeder som trenger oppfølging"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__message_has_error_counter
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Antall meldinger med leveringsfeil"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__refund_orders_count
msgid "Number of orders where items from this order were refunded"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Number of transactions:"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/error_handlers.js:0
msgid "Odoo Server Error"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_odoo_sneakers_product_template
msgid "Odoo Sneakers"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid "Offline Orders"
msgstr "Offline ordrer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/select_lot_popup.xml:0
msgid "Ok"
msgstr "Ok"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/components/product_info_banner/product_info_banner.xml:0
msgid "On hand:"
msgstr "På lager: "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
msgid "Ongoing"
msgstr "Pågående"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/models/pos_order_line.js:0
msgid ""
"Only a negative quantity is allowed for this refund line. Click on +/- to "
"modify the quantity to be refunded."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid "Only administrators can edit receipt headers and footers"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__only_round_cash_method
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_only_round_cash_method
msgid "Only apply rounding on cash"
msgstr "Bruk avrunding bare med kontanter"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment_method.py:0
msgid ""
"Only journals of type 'Cash' or 'Bank' could be used with payment methods."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Only on cash methods"
msgstr "Kun på kontanter"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__restrict_price_control
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_restrict_price_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Only users with Manager access rights for PoS app can modify the product "
"prices on orders."
msgstr ""
"Kun brukere med lederrettigheter for kassasystemet kan endre priser på "
"produkter."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
msgid "Open Cashbox"
msgstr "Åpne kassaskuff"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__open_session_ids
msgid "Open PoS sessions that are using this payment method."
msgstr "Åpne økter som bruker denne betalingsmetoden"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/login_screen/login_screen.xml:0
#: code:addons/point_of_sale/static/src/app/store/opening_control_popup/opening_control_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Open Register"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/opening_control_popup/opening_control_popup.xml:0
msgid "Open the money details popup"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__user_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Opened By"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Opened by"
msgstr "Åpnet av"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
msgid "Opening"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/opening_control_popup/opening_control_popup.xml:0
msgid "Opening Balance Eg: 123"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/opening_control_popup/opening_control_popup.xml:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__opening_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Opening Control"
msgstr "Åpningskontroll"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__start_at
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Opening Date"
msgstr "Åpningsdato"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__opening_notes
msgid "Opening Notes"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_balance_end
msgid "Opening balance summed to all cash transactions."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/opening_control_popup/opening_control_popup.xml:0
msgid "Opening cash"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid "Opening control message: "
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/opening_control_popup/opening_control_popup.xml:0
msgid "Opening note"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__picking_type_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_type_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_picking_type_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Operation Type"
msgstr "Operasjonstype"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Operation types show up in the Inventory dashboard."
msgstr "Operasjonstyper vises i dashbordet for Lager."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#: code:addons/point_of_sale/static/src/app/screens/scale_screen/scale_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__pos_order_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__order_id
msgid "Order"
msgstr "Ordre"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid "Order %s"
msgstr "Ordre %s"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "Order %s is not fully paid."
msgstr "Ordre %s er ikke ferdig betalt."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__order_count
msgid "Order Count"
msgstr "Ordreantall"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__date
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Order Date"
msgstr "Ordredato"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__sequence_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_sequence_id
msgid "Order IDs Sequence"
msgstr "Sekvens for Ordre-IDer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__sequence_line_id
msgid "Order Line IDs Sequence"
msgstr "Sekvens for ordrelinje-IDer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__lines
msgid "Order Lines"
msgstr "Ordrelinjer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__floating_order_name
msgid "Order Name"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__tracking_number
msgid "Order Number"
msgstr "Ordrenummer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_order_printer
msgid "Order Printer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__printer_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_printer_ids
msgid "Order Printers"
msgstr "Bestill skrivere"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_printer_form
msgid ""
"Order Printers are used by restaurants and bars to print the\n"
"            order updates in the kitchen/bar when the waiter updates the order."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__order_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__order_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Order Ref"
msgstr "Ordreref."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Order Reference"
msgstr "Ordrereferanse"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__sequence_number
msgid "Order Sequence Number"
msgstr "Sekvensnummer for ordrer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__orderlines_sequence_in_cart_by_category
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_orderlines_sequence_in_cart_by_category
msgid "Order cart by category's sequence"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__refunded_order_id
msgid "Order from which items were refunded in this order"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Order lines"
msgstr "Ordrelinjer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
msgid "Order number"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid "Order saved for later"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refund_orderline_ids
msgid "Orderlines in this field are the lines that refunded this orderline."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_filtered
#: model:ir.actions.act_window,name:point_of_sale.action_pos_pos_form
#: model:ir.actions.act_window,name:point_of_sale.action_pos_sale_graph
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__order_ids
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_ofsale
#: model:ir.ui.menu,name:point_of_sale.menu_report_pos_order_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Orders"
msgstr "Ordrer"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all_filtered
msgid "Orders Analysis"
msgstr "Ordreanalyse"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order_line__price_type__original
msgid "Original"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/control_buttons.js:0
msgid "Original Tax"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__other_devices
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_other_devices
msgid "Other Devices"
msgstr "Andre enheter"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Other Information"
msgstr "Annen informasjon"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: model:pos.category,name:point_of_sale.pos_category_others
msgid "Others"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__outstanding_account_id
msgid "Outstanding Account"
msgstr "Utesteående beløp"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_product_view_form_normalized_pos
msgid "POS Category"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_procurement_group__pos_order_id
msgid "POS Order"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid "POS Order %s"
msgstr "Kassaordre %s"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line_form
msgid "POS Order line"
msgstr "Kassaordrelinje"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "POS Order lines"
msgstr "Kassaordrelinjer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "POS Orders"
msgstr "Kasseordrer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree_all_sales_lines
msgid "POS Orders lines"
msgstr "Kassaordrelinjer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_payment_method_id
msgid "POS Payment Method"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_printer_form
msgid "POS Printer"
msgstr "PoS-skriver"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_search_view_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_tree_view
msgid "POS Product Category"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_digest_digest__kpi_pos_total
msgid "POS Sales"
msgstr "Kassasalg"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_session_id
msgid "POS Session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_session_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_session_ids
msgid "POS Sessions"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid "POS order line %s"
msgstr "Kassaordrelinje %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_paid
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__paid
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__paid
msgid "Paid"
msgstr "Betalt"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_pain_au_chocolat_product_template
msgid "Pain au Chocolat"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__parent_id
msgid "Parent Category"
msgstr "Overordnet kategori"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_pastries
msgid "Pastries"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
msgid "Pay"
msgstr "Betal"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Pay Order"
msgstr "Betal ordre"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "PayTM"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_paytm
msgid "PayTM Payment Terminal"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#. odoo-python
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/wizard/pos_payment.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Payment"
msgstr "Betaling"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_method_authcode
msgid "Payment APPR Code"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_date
msgid "Payment Date"
msgstr "Betalingsdato"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_method_issuer_bank
msgid "Payment Issuer Bank"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_method_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_method_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__payment_method_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Payment Method"
msgstr "Betalingsmetode"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment_method_form
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__payment_method_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__payment_method_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_payment_method_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_payment_method
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Payment Methods"
msgstr "Betalingsmetoder"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_method_payment_mode
msgid "Payment Mode"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Payment Name Demo"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__ticket
msgid "Payment Receipt Info"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_name
msgid "Payment Reference"
msgstr "Betalingsreferanse"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_status
msgid "Payment Status"
msgstr "Betalingsstatus"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
msgid "Payment Successful"
msgstr "Betaling gjennomført"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Payment Terminals"
msgstr "Betalingsterminaler"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__transaction_id
msgid "Payment Transaction ID"
msgstr "Betalingstransaksjon ID"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "Payment changes:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Payment methods available"
msgstr "Tilgjengelige betalingsmetoder"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_ref_no
msgid "Payment reference number"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__payment_ref_no
msgid "Payment reference number from payment provider terminal"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
msgid "Payment request pending"
msgstr "Betalingsforespørsel avventer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
msgid "Payment reversed"
msgstr "Betaling reversert"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment_form
#: model:ir.model,name:point_of_sale.model_account_payment
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__payment_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Payments"
msgstr "Betalinger"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
msgid "Payments Difference"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_payment_methods_tree
msgid "Payments Methods"
msgstr "Betalingsmetoder"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
msgid "Payments in"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
msgid "Payments:"
msgstr "Betalinger:"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_pecan_pie_product_template
msgid "Pecan Pie"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pick which product PoS categories are available"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__picking_ids
msgid "Picking"
msgstr "Plukk"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_count
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__picking_count
msgid "Picking Count"
msgstr "Plukkantall"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_picking_type
msgid "Picking Type"
msgstr "Plukktype"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Pickings"
msgstr "Plukkinger"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/pos_kanban_view/pos_kanban_view.xml:0
msgid "Please"
msgstr "Vennligst"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid "Please Confirm Large Amount"
msgstr "Bekreft stort beløp"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/base_printer.js:0
msgid "Please check if the IoT Box is still connected."
msgstr "Forsikre deg om at IoT-boksen er tilkoblet."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/base_printer.js:0
msgid ""
"Please check if the printer is still connected. \n"
"Some browsers don't allow HTTP calls from websites to devices in the network (for security reasons). If it is the case, you will need to follow Odoo's documentation for 'Self-signed certificate for ePOS printers' and 'Secure connection (HTTPS)' to solve the issue. "
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/res_company.py:0
msgid ""
"Please close all the point of sale sessions in this period before closing "
"it. Open sessions are: %s "
msgstr ""
"Lukk alle kassaøkter i denne perioden før du lukker den. Åpne økter er: %s"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment_method.py:0
msgid ""
"Please close and validate the following open PoS Sessions before modifying this payment method.\n"
"Open sessions: %s"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Please create/select a Point of Sale above to show the configuration "
"options."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid ""
"Please define income account for this product: '%(product)s' (id:%(id)d)."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid ""
"Please enter your billing information <small class=\"text-muted\">or</small>"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
msgid "Please fill all the required fields."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid ""
"Please go on the %s journal and define a Loss Account. This account will be "
"used to record cash difference."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid ""
"Please go on the %s journal and define a Profit Account. This account will "
"be used to record cash difference."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sync_popup/sync_popup.js:0
msgid ""
"Please note that this operation will result in the loss of any data not "
"saved on the server."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
msgid "Please print the invoice from the backend"
msgstr "Skriv ut fakturaen fra backend"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "Please provide a partner for the sale."
msgstr "Oppgi en partner for salget."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/control_buttons.js:0
msgid "Please register the voucher number"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/qr_code_popup/qr_code_popup.js:0
msgid "Please scan the QR code with %s"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_status/payment_status.xml:0
msgid "Please select a payment method"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid "Please select the Customer"
msgstr "Velg kunde"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.js:0
msgid "PoS Customer Display opened in a new window"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "PoS Interface"
msgstr "Kassagrensesnitt"

#. module: point_of_sale
#: model:mail.template,subject:point_of_sale.pos_email_marketing_template
msgid "PoS Marketing"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_note
msgid "PoS Note"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_attribute_custom_value__pos_order_line_id
msgid "PoS Order Line"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_warehouse.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_pivot
msgid "PoS Orders"
msgstr "Kasseordrer"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_pos_category_action
#: model:ir.ui.menu,name:point_of_sale.menu_products_pos_category
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "PoS Product Categories"
msgstr "Produktkategorier for kasse"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "PoS Product Category"
msgstr "Produktkategori for kasse"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_load_mixin
msgid "PoS data loading mixin"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
msgid "Point Of Sale"
msgstr "Kasse"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/account_journal.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_kanban
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__config_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_config_id
#: model:ir.ui.menu,name:point_of_sale.menu_point_root
#: model_terms:ir.ui.view,arch_db:point_of_sale.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Point of Sale"
msgstr "Kasse"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_pos_order_view_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_graph
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_pivot
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Point of Sale Analysis"
msgstr "Kassaanalyse"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_category
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__pos_categ_ids
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__pos_categ_ids
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__pos_categ_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Point of Sale Category"
msgstr "Kassakategori"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Point of Sale Config"
msgstr "Kassapunkt"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_config
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__config_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_tree
msgid "Point of Sale Configuration"
msgstr "Kassapunkt"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_daily_sales_reports_wizard
msgid "Point of Sale Daily Report"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_saledetails
msgid "Point of Sale Details"
msgstr "Kassadetaljer"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_details_wizard
msgid "Point of Sale Details Report"
msgstr "Detaljrapport for kasse"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_invoice
msgid "Point of Sale Invoice Report"
msgstr "Fakturarapport for kasse"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_journal_id
msgid "Point of Sale Journal"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_tree
msgid "Point of Sale List"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_make_payment
msgid "Point of Sale Make Payment Wizard"
msgstr "Kasse Opprett Betaling Veiviser"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_pos_manager_id
msgid "Point of Sale Manager Group"
msgstr "Gruppe for kasseledere"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_stock_warehouse__pos_type_id
msgid "Point of Sale Operation Type"
msgstr "Kasse Operasjonstype"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Kassaordrelinjer"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Point of Sale Orders"
msgstr "Kassaordrer"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_pos_order
msgid "Point of Sale Orders Report"
msgstr "Ordrerapport for kasse"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_payment_method
#: model:ir.model.fields,field_description:point_of_sale.field_account_journal__pos_payment_method_ids
msgid "Point of Sale Payment Methods"
msgstr "Betalingsmetoder for Kassasystem"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_payment
msgid "Point of Sale Payments"
msgstr "Kassabetalinger"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_printer
msgid "Point of Sale Printer"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_session
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_tree
msgid "Point of Sale Session"
msgstr "Kasseøkt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_pos_user_id
msgid "Point of Sale User Group"
msgstr "Brukergruppe for kasse"

#. module: point_of_sale
#: model:mail.template,name:point_of_sale.pos_email_marketing_template
msgid "Point of Sale: Marketing"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__pos_config_ids
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale_list
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Point of Sales"
msgstr "Kasse"

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.product_crocheted_poncho_unisize_product_template
msgid ""
"Poncho in a soft, crochet-look knit with a round, gently draped neckline."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__access_url
msgid "Portal Access URL"
msgstr "Tilgangslink for portal"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
msgid "Portal URL:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_allowed_pricelist_ids
msgid "Pos Allowed Pricelist"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__pos_config_ids
msgid "Pos Config"
msgstr "Kassaoppsett"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_order_printer
msgid "Pos Is Order Printer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_partner__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_users__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_stock_picking__pos_order_id
msgid "Pos Order"
msgstr "Kassaordre"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_partner__pos_order_count
#: model:ir.model.fields,field_description:point_of_sale.field_res_users__pos_order_count
msgid "Pos Order Count"
msgstr "Antall kassaordre"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__pos_order_line_id
msgid "Pos Order Line"
msgstr "Kassaordrelinje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_payment_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_payment_ids
msgid "Pos Payment"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "Pos Product Categories"
msgstr "Kategorier for kasse"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_refunded_invoice_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_refunded_invoice_ids
msgid "Pos Refunded Invoice"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_selectable_categ_ids
msgid "Pos Selectable Categ"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__pos_session_id
#: model:ir.model.fields,field_description:point_of_sale.field_stock_picking__pos_session_id
msgid "Pos Session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_duration
msgid "Pos Session Duration"
msgstr "Kassaøkt-varighet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_state
msgid "Pos Session State"
msgstr "Status for kassaøkt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_username
msgid "Pos Session Username"
msgstr "Brukernavn for kassaøkt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__open_session_ids
msgid "Pos Sessions"
msgstr "Kassaøkter"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_posbox
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_posbox
msgid "PosBox"
msgstr "PosBox"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/models/pos_order_line.js:0
msgid "Positive quantity not allowed"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__done
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__done
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Posted"
msgstr "Postert"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/customer_display/customer_display.xml:0
msgid "Powered by"
msgstr "Drevet av"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
msgid "Powered by Odoo"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Preparation"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_preparation_display
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Preparation Display"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_printer_form
#: model:ir.ui.menu,name:point_of_sale.menu_pos_preparation_printer
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_printer
msgid "Preparation Printers"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
msgid "Previous Order List"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
msgid "Price"
msgstr "Pris"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Price Control"
msgstr "Priskontroll"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_type
msgid "Price Type"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "Price discount from %(original_price)s to %(discounted_price)s"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
msgid "Price excl. Tax:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_extra
msgid "Price extra"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/control_buttons.xml:0
msgid "Price list"
msgstr "Prisliste"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__price
msgid "Priced Product"
msgstr "Priset produkt"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/control_buttons.xml:0
#: model:ir.model,name:point_of_sale.model_product_pricelist
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__pricelist_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__pricelist_id
msgid "Pricelist"
msgstr "Prisliste"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_pricelist_item
msgid "Pricelist Rule"
msgstr "Prislisteregel"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_action_product_pricelist
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Prislister"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_pricer
msgid "Pricer electronic price tags"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricer tags"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricing"
msgstr "Priser"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sale_details_button.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_res_company__point_of_sale_ticket_portal_url_display_mode
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__point_of_sale_ticket_portal_url_display_mode
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_daily_sales_reports_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
msgid "Print"
msgstr "Skriv ut"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
msgid "Print Basic Receipt"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
msgid "Print Full Receipt"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
msgid "Print Receipt"
msgstr "Skriv ut kvittering"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
msgid "Print Report"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sale_details_button.xml:0
msgid "Print a report with all the sales of the current PoS Session"
msgstr "Skriv ut salgsrapport for gjeldende kassaøkt"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__basic_receipt
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_basic_receipt
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Print basic ticket without prices. Can be used for gifts."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_company__point_of_sale_use_ticket_qr_code
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__point_of_sale_use_ticket_qr_code
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Print information on the receipt to allow the customer to easily access the "
"invoice anytime, from Odoo's portal."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Print orders at the kitchen, at the bar, etc."
msgstr "Skriv ut ordrer i kjøkkenet, i baren, osv."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Print receipts automatically once the payment is registered"
msgstr "Skriv ut kvitteringer automatisk når betaling er registrert"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_via_proxy
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_print_via_proxy
msgid "Print via Proxy"
msgstr "Skriv ut via proxy"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__product_categories_ids
msgid "Printed Product Categories"
msgstr "Utskrevne produktkategorier"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.js:0
msgid "Printer"
msgstr "Skriver"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__name
msgid "Printer Name"
msgstr "Skrivernavn"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__printer_type
msgid "Printer Type"
msgstr "Skriver type"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Printers"
msgstr "Skrivere"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/pos_printer_service.js:0
msgid "Printing error"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid "Printing failed"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/pos_printer_service.js:0
msgid "Printing is not supported on some browsers"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
msgid "Proceed Anyway"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_procurement_group
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__procurement_group_id
msgid "Procurement Group"
msgstr "Anskaffelsesgruppe"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_template
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Product"
msgstr "Produkt"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Product & PoS categories"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_attribute
msgid "Product Attribute"
msgstr "Produktattributt"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_attribute_custom_value
msgid "Product Attribute Custom Value"
msgstr "Tilpasset produktattributtverdi"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_category
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_categ_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Product Category"
msgstr "Produktkategori"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_combo
msgid "Product Combo"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_combo_item
msgid "Product Combo Item"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__public_description
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__public_description
msgid "Product Description"
msgstr "Produkt beskrivelse"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/product_card/product_card.xml:0
msgid "Product Information"
msgstr "Produktinformasjon"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__note
msgid "Product Note"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_packaging
msgid "Product Packaging"
msgstr "Produktemballasje"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Product Prices"
msgstr "Produktpriser"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "Product Product Categories"
msgstr "Kategorier for Produktvariant"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_qty
msgid "Product Quantity"
msgstr "Produktantall"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_tag
msgid "Product Tag"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_tmpl_id
msgid "Product Template"
msgstr "Produktmal"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_template_attribute_line
msgid "Product Template Attribute Line"
msgstr "Attributtlinje for Produktmal"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_template_attribute_value
msgid "Product Template Attribute Value"
msgstr "Attributtverdi for Produktmal"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Produktenhet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__product_uom_id
msgid "Product UoM"
msgstr "Produkt-enhet"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_uom_category
msgid "Product UoM Categories"
msgstr "Kategorier for Produktenhet"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_product
msgid "Product Variant"
msgstr "Produktvariant"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_product_action
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_action_product_product
msgid "Product Variants"
msgstr "Produktvarianter"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
msgid "Product information"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Product prices on receipts"
msgstr "Produktpriser på kvitteringer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_tipproduct
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_tipproduct
msgid "Product tips"
msgstr "Produkt for Tips"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
#: model:ir.actions.act_window,name:point_of_sale.product_template_action_pos_product
#: model:ir.ui.menu,name:point_of_sale.menu_pos_products
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_catalog
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_configuration
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Products"
msgstr "Produkter"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Products:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Promotions, Coupons, Gift Card & Loyalty Program"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
msgid "Proxy Connected"
msgstr "Proxy tilkoblet"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
msgid "Proxy Disconnected"
msgstr "Proxy frakoblet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__proxy_ip
msgid "Proxy IP Address"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
msgid "Proxy Warning"
msgstr "Proxy-advarsel"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
msgid "Public Description"
msgstr ""

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.product_attribute_value_purple
msgid "Purple"
msgstr "Lilla"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/qr_code_popup/qr_code_popup.xml:0
msgid "QR Code"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__qr_code_method
msgid "QR Code Format"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/qr_code_popup/qr_code_popup.js:0
msgid "QR Code Payment"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_ticket_portal_url_display_mode__qr_code
msgid "QR code"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_ticket_portal_url_display_mode__qr_code_and_url
msgid "QR code + URL"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
msgid "Qty"
msgstr "Ant"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__qty
msgid "Quantity"
msgstr "Antall"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Quantity:"
msgstr "Antall:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_receipt/cash_move_receipt.xml:0
msgid "REASON"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
msgid "REFUNDED:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__rating_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__rating_ids
msgid "Ratings"
msgstr "Vurderinger"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Razorpay"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_razorpay
msgid "Razorpay Payment Terminal"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
msgid "Read Weighing Scale"
msgstr "Les vekt"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/tours/point_of_sale.js:0
msgid "Ready to launch your <b>point of sale</b>?"
msgstr "Er du klar for å sette igang <b>kassaløsningen</b>?"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
msgid "Reason"
msgstr "Årsak"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
msgid "Receipt"
msgstr "Mottak"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "Receipt %s"
msgstr "Kvittering %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__receipt_footer
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_receipt_footer
msgid "Receipt Footer"
msgstr "Bunntekst for kvittering"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__receipt_header
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_receipt_header
msgid "Receipt Header"
msgstr "Topptekst for kvittering"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__pos_reference
msgid "Receipt Number"
msgstr "Kvitteringsnummer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Receipt Printer"
msgstr "Kvitteringsskriver"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__use_payment_terminal
msgid "Record payments with a terminal on this journal."
msgstr "Registrer betalinger med en betalingsterminal i denne journalen."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sync_popup/sync_popup.xml:0
msgid "Records to synchronize"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__rescue
msgid "Recovery Session"
msgstr "Gjennopprettingsøkt"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Ref 876787"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
msgid "Refresh Display"
msgstr "Oppdater skjerm"

#. module: point_of_sale
#. odoo-javascript
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/control_buttons.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
msgid "Refund"
msgstr "Kreditnota"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refund_orderline_ids
msgid "Refund Order Lines"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "Refund Orders"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid "Refund and Sales not allowed"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
msgid "Refunded"
msgstr "Refundert"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refunded_order_id
msgid "Refunded Order"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refunded_orderline_id
msgid "Refunded Order Line"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Refunded Orders"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refunded_qty
msgid "Refunded Quantity"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
msgid "Refunding"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Refunds"
msgstr "Kreditnotaer"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid "Related Session: %(link)s"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.client,name:point_of_sale.action_client_pos_menu
msgid "Reload POS Menu"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_status/payment_status.xml:0
msgid "Remaining"
msgstr "Gjenstående"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/edit_list_input/edit_list_input.xml:0
msgid "Remove"
msgstr "Fjern"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "Removed %(payment_method)s with %(amount)s"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
msgid "Replenishment"
msgstr "Påfylning"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_rep
msgid "Reporting"
msgstr "Rapportering"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
msgid "Reprint Invoice"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Request Invoice"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
msgid "Request sent"
msgstr "Forespørsel sendt"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid "Rescue Sessions"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_user_id
msgid "Responsible User"
msgstr "Ansvarlig bruker"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/pos_kanban_view/pos_kanban_view.js:0
msgid "Restaurant"
msgstr "Restaurant"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Restaurant Mode"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limit_categories
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_limit_categories
msgid "Restrict Categories"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__restrict_price_control
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_restrict_price_control
msgid "Restrict Price Modifications to Managers"
msgstr "Gjør prisendringer mulig kun for ledere"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Restrict price modification to managers"
msgstr "Gjør prisendringer mulig kun for ledere"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sync_popup/sync_popup.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
msgid "Retry"
msgstr "Prøv igjen"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Return Products"
msgstr "Returner produkter"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_return
msgid "Returned"
msgstr "Returnert"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid ""
"Reversal of POS closing entry %(entry)s for order %(order)s from session "
"%(session)s"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "Reversal of: %s"
msgstr "Kreditering av %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
msgid "Reversal request sent to terminal"
msgstr "Forespørsel om reversering er sendt til terminalen"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
msgid "Reverse"
msgstr "Reverser"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
msgid "Reverse Payment"
msgstr "Reverser betaling"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__reversed_pos_order_id
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__reversed_pos_order_id
msgid "Reversed POS Order"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
msgid "Review"
msgstr "Gjennomgå"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
msgid "Review Orders"
msgstr ""

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.product_pain_au_chocolat_product_template
msgid "Rich buttery pastry with a dark chocolate center."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/models/pos_order.js:0
msgid "Rounding"
msgstr "Avrunding"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Rounding Method"
msgstr "Avrundingsmetode"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid "Rounding error in payment lines"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/components/tour_selector_popup/tour_selector_popup.xml:0
msgid "Running a fake tour will create random orders. Use at your own risk."
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_rye_bread_product_template
msgid "Rye Bread"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__message_has_sms_error
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS Leveringsfeil"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_sms
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_module_pos_sms
msgid "SMS Enabled"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
msgid "SOLD:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__nbr_lines
msgid "Sale Line Count"
msgstr "Antall ordrelinjer"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_day
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_form
msgid "Sale line"
msgstr "Ordrelinje"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Sales"
msgstr "Salg"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_details
#: model:ir.actions.report,name:point_of_sale.sale_details_report
#: model:ir.ui.menu,name:point_of_sale.menu_report_order_details
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_daily_sales_reports_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
msgid "Sales Details"
msgstr "Salgsdetaljer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__sale_journal
msgid "Sales Journal"
msgstr "Salgsjournal"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Sample Closing Note"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Sample Config Name"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Sample Opening Note"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Save"
msgstr "Lagre"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Save this page and come back here to set up the feature."
msgstr "Lagre denne siden og kom tilbake hit for å sette opp funksjonen."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.js:0
msgid "Scale"
msgstr "Vekt"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/scale_screen/scale_screen.js:0
msgid "Scale error"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
msgid "Scan"
msgstr "Scan"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
msgid "Scan EAN-13"
msgstr "Skan EAN-13"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_scan_via_proxy
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_scan_via_proxy
msgid "Scan via Proxy"
msgstr "Scan via proxy"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.js:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Scanner"
msgstr "Skanner"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
msgid "Search Customers..."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
msgid "Search Orders..."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Search Sales Order"
msgstr "Søk i salgsordre"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
msgid "Search more"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
msgid "Search products..."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bus_mixin__access_token
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__access_token
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__access_token
msgid "Security Token"
msgstr "Sikkerhets-token"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/input_popups/selection_popup.js:0
msgid "Select"
msgstr "Velg"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Select PoS to start sharing orders"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid "Select a payment method to validate the order."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/control_buttons.js:0
msgid "Select the pricelist"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
msgid "Select the product(s) to refund and set the quantity"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid "Select the shipping date"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__attribute_value_ids
msgid "Selected Attributes"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Self-ordering interfaces are not impacted."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_company__point_of_sale_use_ticket_qr_code
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__point_of_sale_use_ticket_qr_code
msgid "Self-service invoicing"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Sell products and deliver them later."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
msgid "Send"
msgstr "Send"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: model:ir.actions.server,name:point_of_sale.action_send_mail
msgid "Send Email"
msgstr "Send e-post"

#. module: point_of_sale
#: model:mail.template,description:point_of_sale.pos_email_marketing_template
msgid "Send Marketing Emails from Point of Sale"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
msgid "Send Payment Request"
msgstr "Send betalingsforespørsel"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Send Text receipt Using sms"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
msgid "Sending"
msgstr "Sender"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__sequence
#: model:ir.model.fields,field_description:point_of_sale.field_pos_note__sequence
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__sequence_number
msgid "Sequence Number"
msgstr "Sekvensnummer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/edit_list_input/edit_list_input.xml:0
msgid "Serial/Lot Number"
msgstr "Serie-/partinummer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid "Served by %s"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid "Server communication problem"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__session_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__session_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
msgid "Session"
msgstr "Økt"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Session Control"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__name
msgid "Session ID"
msgstr "Økt-ID"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Session ID:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__session_move_id
msgid "Session Journal Entry"
msgstr "Bilag for økt"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_daily_sales_reports
#: model:ir.ui.menu,name:point_of_sale.menu_report_daily_details
msgid "Session Report"
msgstr ""

#. module: point_of_sale
#: model:mail.activity.type,name:point_of_sale.mail_activity_old_session
msgid "Session open over 7 days"
msgstr "Økt har vært open i over 7 dager"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session_filtered
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__session_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_session_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Sessions"
msgstr "Økter"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__set_maximum_difference
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_set_maximum_difference
msgid "Set Maximum Difference"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Set a maximum difference allowed between the expected and counted money "
"during the closing of the session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__set_maximum_difference
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_set_maximum_difference
msgid ""
"Set a maximum difference allowed between the expected and counted money "
"during the closing of the session."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/control_buttons.xml:0
msgid "Set fiscal position"
msgstr "Angi avgiftsregel"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Set multiple prices per product, automated discounts, etc."
msgstr "Angi flere priser per produkt, automatiserte rabatter, osv."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/order_summary/order_summary.js:0
msgid "Set the new discount"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/order_summary/order_summary.js:0
msgid "Set the new price"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/order_summary/order_summary.js:0
msgid "Set the new quantity"
msgstr ""

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_configuration
#: model:ir.ui.menu,name:point_of_sale.menu_pos_global_settings
msgid "Settings"
msgstr "Innstillinger"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Settings on this page will apply to this point of sale."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Share Open Orders"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__ship_later
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_ship_later
msgid "Ship Later"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__shipping_date
msgid "Shipping Date"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__picking_policy
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_picking_policy
msgid "Shipping Policy"
msgstr "Fraktpolicy"

#. module: point_of_sale
#: model:product.attribute,name:point_of_sale.product_attribute_size_number
msgid "Shoes size"
msgstr ""

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.product_apple_pie_product_template
msgid "Shortcrust pastry with a Bramley apple filling."
msgstr ""

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.product_cherry_pie_product_template
msgid "Shortcrust pastry with a Morello cherry filling."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__show_category_images
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_show_category_images
msgid "Show Category Images"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__show_product_images
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_show_product_images
msgid "Show Product Images"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Show category images"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__show_category_images
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_show_category_images
msgid "Show category images in the Point of Sale interface."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Show checkout to customers through a second display"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__customer_display_type
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_customer_display_type
msgid "Show checkout to customers."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__module_pos_hr
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_module_pos_hr
msgid "Show employee login screen"
msgstr "Vis skjerm for innlogging av ansatte"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Show margins & costs"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Show margins & costs on product information"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_preparation_display
msgid "Show orders on the preparation display screen."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Show product images"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__show_product_images
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_show_product_images
msgid "Show product images in the Point of Sale interface."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Sign in"
msgstr "Logg på"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Six"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_six
msgid "Six Payment Terminal"
msgstr ""

#. module: point_of_sale
#: model:product.attribute,name:point_of_sale.product_attribute_size
msgid "Size"
msgstr "Størrelse"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_skip_screen
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_print_skip_screen
msgid "Skip Preview Screen"
msgstr "Hopp over forhåndsvisning"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__skip_change
msgid "Skip line when sending ticket to kitchen printers."
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.small_shelf_product_template
msgid "Small Shelf"
msgstr "Liten hylle"

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.product_cinnamon_roll_product_template
msgid ""
"Soft dough with a buttery cinnamon filling, topped with cream cheese icing."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid "Some Serial/Lot Numbers are missing"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid ""
"Some orders could not be submitted to the server due to configuration "
"errors. You can exit the Point of Sale, but do not close the session before "
"the issue has been resolved."
msgstr ""
"Enkelte ordre kunne ikke sendes til serveren på grunn av feilkonfigurasjon. "
"Du kan gå ut av kassaløsningen, men ikke lukk økten før feilen er rettet."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid ""
"Some orders could not be submitted to the server due to internet connection "
"issues. You can exit the Point of Sale, but do not close the session before "
"the issue has been resolved."
msgstr ""
"Enkelte ordre kunne ikke sendes til serveren pga problemer med "
"internettilkoblingen. Du kan gå ut av kassaløsningen, men ikke lukk økten "
"før feilen er rettet."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid "Some, if not all, post-processing after syncing order failed."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Sort cart by category"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_sourdough_loaf_product_template
msgid "Sourdough Loaf"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Specific route"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_pack_operation_lot
msgid "Specify product lot/serial number in pos order line"
msgstr "Spesifiser parti-/serienummer for produkt i kassaordrelinje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__route_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_route_id
msgid "Spefic route for products delivered later."
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.sport_shoes_product_template
msgid "Sport Shoes"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__start_date
msgid "Start Date"
msgstr "Startdato"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/order_widget/order_widget.js:0
msgid "Start adding products"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_start
msgid "Starting Balance"
msgstr "Inngående saldo"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__state
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__state
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__state
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Status"
msgstr "Status"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status basert på aktiviteter\n"
"Utgått: Fristen er allerede passert\n"
"I dag: Aktiviteten skal gjøres i dag\n"
"Planlagt: Fremtidige aktiviteter."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_move
msgid "Stock Move"
msgstr "Lagerbevegelse"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_rule
msgid "Stock Rule"
msgstr "Regel lagring"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "Stock input for %s"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "Stock output for %s"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__update_stock_at_closing
msgid "Stock should be updated at closing"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/pos_kanban_view/pos_kanban_view.js:0
msgid "Stock, product configurator, replenishment, discounts"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
msgid "Stop"
msgstr "Stopp"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__order_edit_tracking
#: model:ir.model.fields,help:point_of_sale.field_pos_order__order_edit_tracking
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_order_edit_tracking
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Store edited orders in the backend"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Stripe"
msgstr "Stripe"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_stripe
msgid "Stripe Payment Terminal"
msgstr ""

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.leather_jacket_product_template
msgid "Stylish leather jacket, durable and fashionable for all occasions."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_subtotal_excl
msgid "Subtotal w/o Tax"
msgstr "Subtotal eks mva"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_sub_total
msgid "Subtotal w/o discount"
msgstr "Subtotal u/rabatt"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.js:0
msgid "Successfully made a cash %s of %s."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Sum of subtotals"
msgstr "Sum av subtotaler"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.summer_hat_product_template
msgid "Summer Hat"
msgstr ""

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.summer_hat_product_template
msgid "Summer hat, ideal for beach days and sunny weather."
msgstr ""

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.product_pecan_pie_product_template
msgid ""
"Sweet pie filled with pecans, with a rich, buttery filling and flaky saltt."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
msgid "Switch Product View"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sync_popup/sync_popup.xml:0
msgid "Synchronize"
msgstr "Synkroniser"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.t_shirt_slim_product_template
msgid "T shirt slim"
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_t_shirt_pants_product_template
msgid "T-shirt & Pants Combo"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/models/pos_order.js:0
msgid "TOTAL"
msgstr "TOTAL"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/order_change_receipt_template.xml:0
msgid "Table"
msgstr "Tabell"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/pos_kanban_view/pos_kanban_view.js:0
msgid "Tables, menus, kitchen display, etc."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/order_change_receipt_template.xml:0
msgid "Take Out"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/order_change_receipt_template.xml:0
msgid "Take Out -> Dine In"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/scale_screen/scale_screen.xml:0
msgid "Tare"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/customer_display/customer_display.xml:0
msgid "Tare Weight:"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/control_buttons.xml:0
#: model:ir.model,name:point_of_sale.model_account_tax
msgid "Tax"
msgstr "Avgift"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_tax_included
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_tax_included
msgid "Tax Display"
msgstr "Visning av avgifter"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_subtotal
msgid "Tax Excl."
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_tax_group
msgid "Tax Group"
msgstr "Avgiftsgruppe"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/receipt_header/receipt_header.js:0
msgid "Tax ID: %(vatId)s"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_subtotal_incl
msgid "Tax Incl."
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_fiscal_position_tax
msgid "Tax Mapping of Fiscal Position"
msgstr "Skattekartlegging av fiskal posisjon"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Tax Name"
msgstr "Avgiftsnavn"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tax_regime_selection
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_tax_regime_selection
msgid "Tax Regime Selection value"
msgstr "Avgiftsregel Valg Verdi"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__iface_tax_included__subtotal
msgid "Tax-Excluded Price"
msgstr "Pris eks mva"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__iface_tax_included__total
msgid "Tax-Included Price"
msgstr "Pris inkl mva"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/order_widget/order_widget.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_tax
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__tax_ids
#: model:ir.ui.menu,name:point_of_sale.menu_action_tax_form_open
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Taxes"
msgstr "Avgifter"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Taxes on refunds"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Taxes on sales"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__tax_ids_after_fiscal_position
msgid "Taxes to Apply"
msgstr "Avgifter å legge til"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
msgid "Taxes:"
msgstr "Avgifter:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Technical Stuff"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Technical Stuffs"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/receipt_header/receipt_header.xml:0
msgid "Tel:"
msgstr "Tlf:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/customer_display/customer_display.xml:0
msgid "Thank you."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
msgid "The %s must be filled in your details."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_printer__proxy_ip
msgid "The IP Address or hostname of the Printer's hardware proxy"
msgstr "IP-adressen eller vertsnavnet til skriverens maskinvare-proxy"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"ISO-landkoden med to tegn.\n"
"Du kan bruke dette feltet til et raskt søk."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/barcode_reader_service.js:0
msgid ""
"The Point of Sale could not find any product, customer, employee or action "
"associated with the scanned barcode."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_rounding_form_view_inherited
msgid ""
"The Point of Sale only supports the \"add a rounding line\" rounding "
"strategy."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
msgid "The Ticket Number should be at least 14 characters long."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid ""
"The amount cannot be higher than the due amount if you don't have a cash "
"payment method configured."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid ""
"The amount of your payment lines must be rounded to validate the transaction.\n"
"The rounding precision is %s so you should set %s as payment amount instead of %s."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__card_brand
msgid "The brand of the payment card (e.g. Visa, AMEX, ...)"
msgstr "Typen betalingskort (for eksempel Visa, American Express ...)"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid ""
"The cash rounding strategy of the point of sale %(pos)s must be: '%(value)s'"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid "The default pricelist must be included in the available pricelists."
msgstr "Standard prisliste må være inkludert i de tilgjengelige prislistene."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid ""
"The default pricelist must belong to no company or the company of the point "
"of sale."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid ""
"The default tip product is missing. Please manually specify the tip product."
" (See Tips field.)"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid ""
"The existing serial/lot numbers could not be retrieved. \n"
"Continue without checking the validity of serial/lot numbers ?"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
msgid ""
"The fiscal position used in the original order is not loaded. Make sure it "
"is loaded by adding it in the pos configuration."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__proxy_ip
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_proxy_ip
msgid ""
"The hostname or ip address of the hardware proxy, Will be autodetected if "
"left empty."
msgstr ""
"Domenet eller ip-adressen til IoT-boksen. Vil automatisk bli funnet hvis "
"utelatt."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid ""
"The invoice journal must be in the same currency as the Sales Journal or the"
" company currency if that is not set."
msgstr ""
"Fakturajournalen må være i samme valuta som salgsjournalen, eventuelt "
"firmavaluta om salgsjournal ikke er satt."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
msgid ""
"The maximum difference allowed is %s.\n"
"Please contact your manager to accept the closing difference."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
msgid ""
"The money counted doesn't match what we expected. Want to log the difference"
" for the books?"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_bill.py:0
msgid "The name of the Coins/Bills must be a number."
msgstr ""

#. module: point_of_sale
#: model:ir.model.constraint,message:point_of_sale.constraint_pos_session_uniq_name
msgid "The name of this POS Session must be unique!"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_partner__pos_order_count
#: model:ir.model.fields,help:point_of_sale.field_res_users__pos_order_count
msgid "The number of point of sales orders related to this customer"
msgstr "Antall kassaordrer relatert til denne kunden"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
msgid "The order could not be sent to the server due to an unknown error"
msgstr "Ordren kunne ikke sendes til server grunnet en ukjent feil"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
msgid ""
"The order has been synchronized earlier. Please make the invoice from the "
"backend for the order: "
msgstr ""
"Ordren er tidligere synkronisert. Opprett faktura for ordren fra backend."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "The paid amount is different from the total amount of the order."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment.py:0
msgid ""
"The payment method selected is not allowed in the config of the POS session."
msgstr "Den valgte betalingsmetoden er ikke tillatt i kassaøkten."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid ""
"The payment methods for the point of sale %s must belong to its company."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_available_categ_ids
msgid ""
"The point of sale will only display products which are within one of the "
"selected category trees. If no category is specified, all available products"
" will be shown"
msgstr ""
"Kassaløsningen vil kun vise produkter som er i en av de valgte kategoriene "
"(og dens underkategorier). Hvis ingen kategori er spesifisert, vil alle "
"tilgjengelige produkter vises."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_account_bank_statement_line__reversed_pos_order_id
#: model:ir.model.fields,help:point_of_sale.field_account_move__reversed_pos_order_id
msgid ""
"The pos order that was reverted after closing the session to create an "
"invoice for it."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__note_ids
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_note_ids
msgid "The predefined notes of this point of sale."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__pricelist_id
msgid ""
"The pricelist used if no customer is selected or if the customer has no Sale"
" Pricelist configured if any."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__currency_rate
msgid ""
"The rate of the currency to the currency of rate applicable at the date of "
"the order"
msgstr "Valutakurs ved ordredato"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_skip_screen
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_iface_print_skip_screen
msgid ""
"The receipt screen will be skipped if the receipt can be printed "
"automatically."
msgstr ""
"Kvitteringsskjermen vil hoppes over hvis kvitteringen kan skrives ut "
"automatisk."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_auto
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_iface_print_auto
msgid "The receipt will automatically be printed at the end of each order."
msgstr "Kvittering vil skrives ut automatisk ved avslutning av ordre."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
msgid ""
"The requested quantity to be refunded is higher than the ordered quantity. "
"%s is requested while only %s can be refunded."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/models/pos_order_line.js:0
msgid ""
"The requested quantity to be refunded is higher than the refundable "
"quantity."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid "The selected customer needs an address."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid ""
"The selected pricelists must belong to no company or the company of the "
"point of sale."
msgstr ""
"Den valgte prislisten må tilhøre samme firma som kassapunktet, eventuelt "
"ikke tilhøre et firma."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
msgid "The server encountered an error while receiving your order."
msgstr "Serveren støtte på en feil under mottak av ordren."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid ""
"The session has been already closed by another User. All sales completed in "
"the meantime have been saved in a Rescue Session, which can be reviewed "
"anytime and posted to Accounting from Point of Sale's dashboard."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid ""
"The session has been opened for an unusually long period. Please consider "
"closing."
msgstr "Økten har vært åpen i uvanlig lang tid. Vurder å lukke den."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid ""
"The session is being closed by another user. The page will be reloaded."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_adyen
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Adyen. Set your Adyen credentials on the "
"related payment method."
msgstr ""
"Transaksjonene blir behandlet av Aydyen. Angi påloggingsinformasjon på "
"relatert betalingsmetode."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "The transactions are processed by Mercado Pago on terminal"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_mercado_pago
msgid ""
"The transactions are processed by Mercado Pago. Set your Mercado Pago "
"credentials on the related payment method."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_paytm
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by PayTM. Set your PayTM credentials on the "
"related payment method."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_razorpay
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Razorpay. Set your Razorpay credentials on"
" the related payment method."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_six
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Six. Set the IP address of the terminal on"
" the related payment method."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_stripe
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Stripe. Set your Stripe credentials on the"
" related payment method."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_viva_wallet
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Viva Wallet on terminal or tap on phone."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__card_type
msgid "The type of the payment card (e.g. CREDIT CARD OR DEBIT CARD)"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_end
msgid "Theoretical Closing Balance"
msgstr "Teoretisk beholdning ved avslutning"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
msgid "There are no products in this category."
msgstr "Det finnes ingen produkter i denne kategorien."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid ""
"There are still orders in draft state in the session. Pay or cancel the following orders to validate the session:\n"
"%s"
msgstr ""
"Det er fremdeles ordre med status kladd i denne økten. Betal eller avbryt følgende ordre for å validere økten:\n"
"%s"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid ""
"There is a difference between the amounts to post and the amounts of the "
"orders, it is probably caused by taxes or accounting configurations changes."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid "There is already an electronic payment in progress."
msgstr "Det pågår allerede en betaling."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"There is no Chart of Accounts configured on the company. Please go to the "
"invoicing settings to install a Chart of Accounts."
msgstr ""
"Ingen kontoplan er konfigurert for firmaet. Gå til innstillingene for "
"fakturering, og velg en kontoplan."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid ""
"There is no cash payment method available in this point of sale to handle the change.\n"
"\n"
" Please pay the exact amount or add a cash payment method in the point of sale configuration"
msgstr ""
"Det finnes ingen kontant betalingsmetode for denne kassen, som kan behandle veksel.\n"
"\n"
"Betal eksakt beløp, eller legg til en kontant betalingsmetode i innstillingene for kassapunktet."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid "There is no cash payment method for this PoS Session"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid "There is no cash register in this session."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid ""
"There is no serial/lot number for the selected product, and their creation "
"is not allowed from the Point of Sale app."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid ""
"There must be at least one product in your order before it can be validated "
"and invoiced."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid ""
"This cash payment method is already used in another Point of Sale.\n"
"A new cash payment method should be created for this Point of Sale."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/combo_configurator_popup/combo_configurator_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/product_configurator_popup/product_configurator_popup.xml:0
msgid "This combination does not exist."
msgstr "Denne kombinasjonen eksisterer ikke."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__amount_authorized_diff
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_amount_authorized_diff
msgid ""
"This field depicts the maximum difference allowed between the ending balance"
" and the theoretical cash when closing a session, for non-POS managers. If "
"this maximum is reached, the user will have an error message at the closing "
"of his session saying that he needs to contact his manager."
msgstr ""
"Dette feltet definerer maks tillatt differanse mellom teoretisk "
"kassabeholdning og opptalt kassabeholdning, for brukere som ikke er ledere. "
"Hvis tillatt differanse overskrides, vil brukeren få en feilmelding med "
"beskjed om å kontakte sin leder."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_pos_manager_id
msgid ""
"This field is there to pass the id of the pos manager group to the point of "
"sale client."
msgstr "Dette feltet sender id-en til kassaleder-gruppen til kassaklienten."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_pos_user_id
msgid ""
"This field is there to pass the id of the pos user group to the point of "
"sale client."
msgstr "Dette feltet sender id-en for kassabruker-gruppen til kassaklienten."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "This invoice has been created from the point of sale session: %s"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__fiscal_position_ids
msgid ""
"This is useful for restaurants with onsite and take-away services that imply"
" specific tax rates."
msgstr ""
"Dette er nyttig for å sette riktig mva for restauranter som tilbyr både spis"
" her og ta med."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/account_journal.py:0
msgid ""
"This journal is associated with a payment method. You cannot modify its type"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/account_journal.py:0
msgid ""
"This journal is associated with payment method %(payment_method)s that is "
"being used by order %(pos_order)s in the active pos session %(pos_session)s"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.js:0
msgid ""
"This operation will destroy all ${\n"
"                    paid ? \"paid\" : \"unpaid\"\n"
"                } orders in the browser. You will lose all the data. This operation cannot be undone."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid ""
"This order already has refund lines for %s. We can't change the customer "
"associated to it. Create a new order for the new customer."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
msgid ""
"This order is not yet synced to server. Make sure it is synced then try "
"again."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment_method.py:0
msgid "This payment method is not configured to generate QR codes."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__tip_product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "This product is used as reference on customer receipts."
msgstr "Dette produktet brukes som referanse på kundekvitteringer."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__sequence_line_id
msgid ""
"This sequence is automatically created by Odoo but you can change it to "
"customize the reference numbers of your orders lines."
msgstr ""
"Denne sekvensen er automatisk opprettet av Odoo, men du kan endre den for å "
"tilpasse referansenummer for ordrelinjer."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__sequence_id
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_sequence_id
msgid ""
"This sequence is automatically created by Odoo but you can change it to "
"customize the reference numbers of your orders."
msgstr ""
"Denne sekvensen er automatisk opprettet av Odoo, men du kan endre den for å "
"tilpasse referansenummer for ordrer."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid "This session is already closed."
msgstr "Denne økten er allerede lukket."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "This tax is applied to any new product created in the catalog."
msgstr ""
"Denne avgiften legges til på alle nye produkter som opprettes i katalogen."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Those settings are common to all PoS."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__ticket_code
msgid "Ticket Code"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Ticket Nr"
msgstr ""

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.product_tiger_white_loaf_product_template
msgid ""
"Tiger bloomer. Baked for a soft inside, with its distinctive savoury "
"crackled crust."
msgstr ""

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_tiger_white_loaf_product_template
msgid "Tiger white loaf"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
msgid "Tip"
msgstr "Tips"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__tip_amount
msgid "Tip Amount"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tip_product_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_tip_product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Tip Product"
msgstr "Tipsprodukt"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_product_tip_product_template
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Tips"
msgstr "Tips"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Tips:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "To Close"
msgstr "Til lukking"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
msgid "To Pay"
msgstr "Å betale"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
msgid "To Refund:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__to_weight
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__to_weight
msgid "To Weigh With Scale"
msgstr "Veies med vekt"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/product.py:0
msgid ""
"To delete a product, make sure all point of sale sessions are closed.\n"
"\n"
"Deleting a product available in a session would be like attempting to snatch a hamburger from a customer’s hand mid-bite; chaos will ensue as ketchup and mayo go flying everywhere!"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid ""
"To ensure due balance follow-up, generate an invoice or download the "
"accounting application. "
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__to_invoice
msgid "To invoice"
msgstr "Å fakturere"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_form
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
msgid "To record new orders, start a new session."
msgstr "Start en ny økt for å registrere nye ordre."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "To return product(s), you need to open a session in the POS %s"
msgstr "For å returnere et produkt, må du åpne en kassaøkt i kassapunktet %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/order_widget/order_widget.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sync_popup/sync_popup.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: code:addons/point_of_sale/static/src/app/utils/money_details_popup/money_details_popup.xml:0
#: code:addons/point_of_sale/static/src/customer_display/customer_display.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_total
msgid "Total"
msgstr "Totalt"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
msgid "Total Cost:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__total_discount
msgid "Total Discount"
msgstr "Total rabatt"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
msgid "Total Margin:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Total Paid (with rounding)"
msgstr "Totalt Betalt (avrundet)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__total_payments_amount
msgid "Total Payments Amount"
msgstr "Totalt betalingsbeløp"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_total
msgid "Total Price"
msgstr "Totalbeløp"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
msgid "Total Price excl. Tax:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__amount
msgid "Total amount of the payment."
msgstr "Totalt betalingsbeløp"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__total_cost
msgid "Total cost"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Total qty"
msgstr "Totalt ant"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Total:"
msgstr "Total:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/money_details_popup/money_details_popup.js:0
msgid "Total: %s"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__order_edit_tracking
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__order_edit_tracking
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_order_edit_tracking
msgid "Track orders edits"
msgstr ""

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.product_odoo_sneakers_product_template
msgid ""
"Trainers in cotton canvas with a padded top edge, tongue and lacing at the "
"front."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_real_transaction
msgid "Transaction"
msgstr "Transaksjon"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
msgid "Transaction cancelled"
msgstr "Transaksjon avbrutt"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_picking
msgid "Transfer"
msgstr "Overføring"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Trusted POS"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__trusted_config_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_trusted_config_ids
msgid "Trusted Point of Sale Configurations"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_barcode_rule__type
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__type
msgid "Type"
msgstr "Type"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__qr_code_method
msgid "Type of QR-code to be generated for this payment method."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__card_type
msgid "Type of card used"
msgstr "Type kort brukt"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type unntaks-aktivitet på posten."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_line/partner_line.xml:0
msgid "UNSELECT"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_ticket_portal_url_display_mode__url
msgid "URL"
msgstr "URL"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid ""
"Unable to close and validate the session.\n"
"Please set corresponding tax account in each repartition line of the following taxes: \n"
"%s"
msgstr ""
"Kan ikke lukke og validere økten.\n"
"Du må angi samsvarende avgiftskonto i hver fordelingslinje på følgende avgifter: \n"
"%s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
msgid "Unable to download invoice."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid ""
"Unable to modify this PoS Configuration because you can't modify %s while a "
"session is open."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/barcode_reader_service.js:0
msgid "Unable to parse barcode"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/error_handlers.js:0
msgid "Unable to show information about this error."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
msgid "Unable to sync order"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
msgid "Unique Code:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Unique code"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_session_sales_details
msgid "Unit"
msgstr "Enhet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_unit
msgid "Unit Price"
msgstr "Enhetspris"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Unit price:"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/components/product_info_banner/product_info_banner.xml:0
msgid "Units"
msgstr "Stk"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/barcode_reader_service.js:0
msgid "Unknown Barcode"
msgstr "Ukjent strekkode"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/error_handlers.js:0
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
msgid "Unknown Error"
msgstr "Ukjent feil"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/scale_screen/scale_service.js:0
msgid "Unnamed Product"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_product_view_form_normalized_pos
msgid "Unsaleable"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "Unsupported search operation"
msgstr "Ikke støttet søksoperasjon"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
msgid "Unsynced order"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/error_handlers.js:0
msgid ""
"Until the connection is reestablished, Odoo Point of Sale will operate with "
"limited functionality."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "UoM"
msgstr "Enhet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_company__point_of_sale_update_stock_quantities
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__update_stock_quantities
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Update quantities in stock"
msgstr ""

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_upper
msgid "Upper body"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__use_payment_terminal
msgid "Use a Payment Terminal"
msgstr "Bruk betalingsterminal"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__use_pricelist
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_use_pricelist
msgid "Use a pricelist."
msgstr "Bruk prisliste."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Use automatic taxes mapping with Avatax"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__module_pos_avatax
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_module_pos_avatax
msgid "Use automatic taxes mapping with Avatax in PoS"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Use barcodes to scan products, customer cards, etc."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Use fiscal positions to get different taxes by order"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Used to record product pickings. Products are consumed from its default "
"source location."
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_users
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__user_id
#: model:res.groups,name:point_of_sale.group_pos_user
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "User"
msgstr "Bruker"

#. module: point_of_sale
#: model:ir.actions.report,name:point_of_sale.report_user_label
msgid "User Labels"
msgstr "Navneskilt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__uuid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__uuid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__uuid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__uuid
msgid "Uuid"
msgstr "Uuid"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/components/product_info_banner/product_info_banner.xml:0
msgid "VAT:"
msgstr "MVA:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
msgid "Validate"
msgstr "Valider"

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.product_green_hood_product_template
msgid ""
"Versatile cotton hoodie with an adjustable drawstring hood and front zipper "
"closure."
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_ir_ui_view
msgid "View"
msgstr "Vis"

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.jean_jacket_product_template
msgid "Vintage jean jacket, a timeless piece for any casual outfit."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Viva Wallet"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_viva_wallet
msgid "Viva Wallet Payment Terminal"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
msgid "Waiting for card"
msgstr "Venter på kort"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.wall_shelf_product_template
msgid "Wall Shelf Unit"
msgstr "Hylle"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/pos_kanban_view/pos_kanban_view.xml:0
msgid "Want to try with sample products?"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_warehouse
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__warehouse_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Warehouse"
msgstr "Lager"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_warehouse_id
msgid "Warehouse (PoS)"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid "Warning"
msgstr "Advarsel"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid ""
"Warning, the paid amount is higher than the total amount. (Difference: %s)"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/models/utils/indexed_db.js:0
msgid ""
"Warning: Your browser doesn't support IndexedDB. The data won't be saved. "
"Please use a modern browser."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__website_message_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__website_message_ids
msgid "Website Messages"
msgstr "Meldinger fra nettsted"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__website_message_ids
#: model:ir.model.fields,help:point_of_sale.field_pos_session__website_message_ids
msgid "Website communication history"
msgstr " Kommunikasjonshistorikk for nettsted"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/customer_display/customer_display.xml:0
msgid "Weighing Product:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__weight
msgid "Weighted Product"
msgstr "Produkt til veiing"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/customer_display/customer_display.xml:0
msgid "Welcome."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__orderlines_sequence_in_cart_by_category
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_orderlines_sequence_in_cart_by_category
msgid ""
"When active, orderlines will be sorted based on product category and "
"sequence in the product screen's order cart."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__picking_policy__one
msgid "When all products are ready"
msgstr "Når alle produkter er klare"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__is_margins_costs_accessible_to_every_user
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_is_margins_costs_accessible_to_every_user
msgid ""
"When disabled, only PoS manager can view the margin and cost of product "
"among the Product info."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Whenever you close a session, one entry is generated in the following "
"accounting journal for all the orders not invoiced. Invoices are recorded in"
" accounting separately."
msgstr ""
"Når du lukker en økt, vil det for alle ordrer som ikke er fakturert, "
"genereres ett bilag i følgende regnskapsjournal. Fakturaer bilagsføres hver "
"for seg."

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.product_attribute_value_white
msgid "White"
msgstr "Hvit"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.whiteboard_product_template
msgid "Whiteboard"
msgstr "Whiteboard"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.whiteboard_pen_product_template
msgid "Whiteboard Pen"
msgstr "Whiteboard-penn"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_wholemeal_loaf_product_template
msgid "Wholemeal loaf"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
msgid "With a"
msgstr "Med"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.product_attribute_value_xl
msgid "XL"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid "Yes"
msgstr "Ja"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/main.js:0
msgid ""
"You are currently offline. Reloading the page may cause you to lose unsaved "
"data."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid ""
"You are not allowed to change the cash rounding configuration while a pos "
"session using it is already opened."
msgstr ""
"Du kan ikke endre innstillinger for avrunding mens en kassaøkt er åpen."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/order_summary/order_summary.js:0
msgid "You are not allowed to change this quantity"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
msgid ""
"You are trying to sell products with serial/lot numbers, but some of them are not set.\n"
"Would you like to proceed anyway?"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid ""
"You can only cancel a session that is in opening control state and has no "
"orders."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "You can only refund products from the same order."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid ""
"You can only unlink PoS order lines that are related to orders in new or "
"cancelled state."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid ""
"You can't: create a pos order from the backend interface, or unset the "
"pricelist, or create a pos.order in a python test with Form tool, or edit "
"the form view in studio if no PoS order exist"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_picking.py:0
msgid ""
"You cannot archive '%(picking_type)s' as it is used by POS configuration "
"'%(config)s'."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "You cannot change the payment of a printed order."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid ""
"You cannot close the POS when invoices are not posted.\n"
"Invoices: %s"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid "You cannot close the POS when orders are still in draft"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid "You cannot create a session starting before: %(lock_date_info)s"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_category.py:0
msgid ""
"You cannot delete a point of sale category while a session is still opened."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment.py:0
msgid "You cannot edit a payment for a posted order."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "You cannot invoice orders belonging to different companies."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/account_cash_rounding.py:0
#, python-format
msgid ""
"You cannot delete a rounding method that is used in a Point of Sale "
"configuration."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid ""
"You cannot share open orders with configuration that does not use the same "
"currency."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid "You cannot use the same journal on multiples cash payment methods."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid ""
"You do not have permission to open a POS session. Please try opening a "
"session with a different user"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid ""
"You don't have the access rights to get the point of sale closing control "
"data."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid ""
"You have enabled the \"Identify Customer\" option for %(payment_method)s "
"payment method,but the order %(order)s does not contain a customer."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_product_action
msgid ""
"You must define a product for everything you sell through\n"
"                the point of sale interface."
msgstr ""
"Du må definere produkter for alt du skal selge\n"
"med kassasystemet."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/product.py:0
msgid "You must first remove this product from the %s combo"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid "You must have 'Administration Settings' access to load clothes data."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid "You must have 'Administration Settings' access to load furniture data."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid ""
"You must have at least one payment method configured to launch a session."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment_method.py:0
msgid ""
"You must select a QR-code method to generate QR-codes for this payment "
"method."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid ""
"You must set the iot box's IP address to use an IoT-connected screen. You'll"
" find the field under the 'IoT Box' option."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
msgid "You need a loss and profit account on your cash journal."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid ""
"You need to select the customer before you can invoice or ship an order."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid "You should assign a Point of Sale to your session."
msgstr "Du må angi et kassapunkt for økten."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
msgid ""
"Your PoS Session is open since %(date)s, we advise you to close it and to "
"create a new one."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid ""
"Your address is missing or incomplete. <br/>\n"
"                                Please make sure to"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/components/product_info_banner/product_info_banner.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
msgid "available,"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "before continuing."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
msgid "by Email"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
msgid "discount"
msgstr "rabatt"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
msgid "e.g. 0123456789"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "e.g. Cash"
msgstr "e.g. Cash"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "e.g. Company Address, Website"
msgstr "for eksempel firmaadresse, nettsted"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "e.g. NYC Shop"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "e.g. Return Policy, Thanks for shopping with us!"
msgstr "for eks: Returpolicy, eller: Takk for at du handler hos oss!"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "e.g. Soft Drinks"
msgstr "f.eks mineralvann"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
msgid "e.g. <EMAIL>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "ePos Printer"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "email"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
msgid "failed. Please try again"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "fill all relevant information"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "for"
msgstr "for "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
msgid "for an order of"
msgstr "for en ordre på"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/components/product_info_banner/product_info_banner.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
msgid "forecasted"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
msgid "in"
msgstr "i"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
msgid "in progress"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
msgid "in this category."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/pos_kanban_view/pos_kanban_view.xml:0
msgid "install a chart of accounts"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
msgid "is sent successfully"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
msgid "items"
msgstr ""

#. module: point_of_sale
#: model:mail.activity.type,summary:point_of_sale.mail_activity_old_session
msgid "note"
msgstr "notat"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
msgid "on"
msgstr "på"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/pos_kanban_view/pos_kanban_view.xml:0
msgid "or"
msgstr "eller"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
msgid "orders:"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "outstanding rescue session"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "qx9h1"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
msgid "return"
msgstr "retur"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/models/pos_order.js:0
msgid "the invoice"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/models/pos_order.js:0
msgid "the receipt"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/pos_kanban_view/pos_kanban_view.xml:0
msgid "to activate the buttons."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_invoice_document
msgid "using"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
msgid "{{ !barcodeReader ? 'No nomenclature' : '' }}"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/order_summary/order_summary.xml:0
msgid ""
"{{ line.has_valid_product_lot() ? 'Valid product lot' : 'Invalid product "
"lot'}}"
msgstr ""
