<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="hr.BackgroundImage">
        <img
            loading="lazy"
            t-att-data-tooltip-template="hasTooltip and tooltipAttributes.template"
            t-att-data-tooltip-info="hasTooltip and tooltipAttributes.info"
            t-att-data-tooltip-delay="hasTooltip and props.zoomDelay"
            t-attf-src="#{getUrl(props.previewImage or props.name)}"
            alt="Binary file"
           />
    </t>
</templates>
