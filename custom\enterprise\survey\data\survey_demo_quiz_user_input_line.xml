<?xml version="1.0" encoding="utf-8"?>
<odoo><data noupdate="0">
    <!-- Page 1: general informations -->
    <record id="survey_demo_quiz_answer_1_p1_q1_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p1_q1"/>
        <field name="answer_type">char_box</field>
        <field name="value_char_box"><EMAIL></field>
    </record>
    <record id="survey_demo_quiz_answer_1_p1_q2_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p1_q2"/>
        <field name="answer_type">char_box</field>
        <field name="value_char_box"><PERSON></field>
    </record>
    <record id="survey_demo_quiz_answer_1_p1_q3_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p1_q3"/>
        <field name="answer_type">char_box</field>
        <field name="value_char_box">Brussels</field>
    </record>
    <record id="survey_demo_quiz_answer_1_p1_q4_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p1_q4"/>
        <field name="answer_type">numerical_box</field>
        <field name="value_numerical_box">36</field>
    </record>
    <!-- Page 2: quiz about company -->
    <record id="survey_demo_quiz_answer_1_p2_q1_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p2_q1"/>
        <field name="answer_type">date</field>
        <field name="value_date" eval="DateTime.today() - relativedelta(years=36)"/>
    </record>
    <record id="survey_demo_quiz_answer_1_p2_q2_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p2_q2"/>
        <field name="answer_type">datetime</field>
        <field name="value_datetime" eval="DateTime.now().replace(year=2017, month=10, day=2, hour=2, minute=27, second=0)"/>
    </record>
    <record id="survey_demo_quiz_answer_1_p2_q3_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p2_q3"/>
        <field name="answer_type">text_box</field>
        <field name="value_text_box">Oak, ash, pine</field>
    </record>
    <!-- Page 3: quiz about fruits and vegetables -->
    <record id="survey_demo_quiz_answer_1_p3_q1_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q1"/>
        <field name="answer_is_correct" eval="True"/>
        <field name="answer_score">20</field>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q1_sug1"/>
    </record>
    <record id="survey_demo_quiz_answer_1_p3_q2_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q2"/>
        <field name="answer_is_correct" eval="True"/>
        <field name="answer_score">20</field>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q2_sug1"/>
    </record>
    <record id="survey_demo_quiz_answer_1_p3_q2_l2" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q2"/>
        <field name="answer_type">char_box</field>
        <field name="value_char_box">Mooses?? Really?</field>
    </record>
    <record id="survey_demo_quiz_answer_1_p3_q3_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q3"/>
        <field name="answer_is_correct" eval="True"/>
        <field name="answer_score">10</field>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q3_sug2"/>
    </record>
    <record id="survey_demo_quiz_answer_1_p3_q3_l2" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q3"/>
        <field name="answer_score">-10</field>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q3_sug3"/>
    </record>
    <record id="survey_demo_quiz_answer_1_p3_q4_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q4"/>
        <field name="answer_is_correct" eval="True"/>
        <field name="answer_score">20</field>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q4_sug1"/>
    </record>
    <record id="survey_demo_quiz_answer_1_p3_q4_l2" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q4"/>
        <field name="answer_score">-10</field>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q4_sug3"/>
    </record>
    <record id="survey_demo_quiz_answer_1_p3_q5_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q5"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q5_sug1"/>
        <field name="matrix_row_id" ref="survey_demo_quiz_p3_q5_row1"/>
    </record>
    <record id="survey_demo_quiz_answer_1_p3_q5_l2" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q5"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q5_sug1"/>
        <field name="matrix_row_id" ref="survey_demo_quiz_p3_q5_row2"/>
    </record>
    <record id="survey_demo_quiz_answer_1_p3_q6_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q6"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q6_sug1"/>
        <field name="matrix_row_id" ref="survey_demo_quiz_p3_q6_row1"/>
    </record>
    <record id="survey_demo_quiz_answer_1_p3_q6_l2" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q6"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q6_sug1"/>
        <field name="matrix_row_id" ref="survey_demo_quiz_p3_q6_row2"/>
    </record>
    <record id="survey_demo_quiz_answer_1_p3_q6_l3" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q6"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q6_sug1"/>
        <field name="matrix_row_id" ref="survey_demo_quiz_p3_q6_row3"/>
    </record>
    <!-- Page 4: trees -->
    <record id="survey_demo_quiz_answer_1_p4_q1_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p4_q1"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p4_q1_sug1"/>
    </record>
    <record id="survey_demo_quiz_answer_1_p4_q2_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p4_q2"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p4_q2_sug1"/>
    </record>
    <record id="survey_demo_quiz_answer_1_p4_q3_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p4_q3"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p4_q3_sug1"/>
    </record>
    <record id="survey_demo_quiz_answer_1_p4_q4_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p4_q4"/>
        <field name="answer_is_correct" eval="True"/>
        <field name="answer_score">20</field>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p4_q4_sug2"/>
    </record>
    <record id="survey_demo_quiz_answer_1_p4_q5_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p4_q5"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p4_q5_sug1"/>
    </record>
    <record id="survey_demo_quiz_answer_1_p4_q5_l2" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p4_q5"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p4_q5_sug2"/>
    </record>
    <record id="survey_demo_quiz_answer_1_p4_q6_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_1"/>
        <field name="question_id" ref="survey_demo_quiz_p4_q6"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p4_q6_sug1"/>
    </record>


    <!-- Page 1: general informations -->
    <record id="survey_demo_quiz_answer_2_p1_q1_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p1_q1"/>
        <field name="answer_type">char_box</field>
        <field name="value_char_box"><EMAIL></field>
    </record>
    <record id="survey_demo_quiz_answer_2_p1_q2_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p1_q2"/>
        <field name="answer_type">char_box</field>
        <field name="value_char_box">Mitchell Admin</field>
    </record>
    <record id="survey_demo_quiz_answer_2_p1_q3_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p1_q3"/>
        <field name="answer_type">char_box</field>
        <field name="value_char_box">Ottawa</field>
    </record>
    <record id="survey_demo_quiz_answer_2_p1_q4_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p1_q4"/>
        <field name="answer_type">numerical_box</field>
        <field name="value_numerical_box">48</field>
    </record>
    <!-- Page 2: quiz about company -->
    <record id="survey_demo_quiz_answer_2_p2_q1_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p2_q1"/>
        <field name="answer_type">date</field>
        <field name="value_date" eval="DateTime.today() + relativedelta(years=24)"/>
    </record>
    <record id="survey_demo_quiz_answer_2_p2_q2_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p2_q2"/>
        <field name="answer_type">datetime</field>
        <field name="value_datetime" eval="DateTime.now().replace(year=2011, month=8, day=21, hour=15, minute=34, second=0)"/>
    </record>
    <record id="survey_demo_quiz_answer_2_p2_q3_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p2_q3"/>
        <field name="skipped" eval="True"/>
    </record>
    <!-- Page 3: quiz about fruits and vegetables -->
    <record id="survey_demo_quiz_answer_2_p3_q1_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q1"/>
        <field name="answer_is_correct" eval="True"/>
        <field name="answer_score">10</field>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q1_sug2"/>
    </record>
    <record id="survey_demo_quiz_answer_2_p3_q2_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q2"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q2_sug3"/>
    </record>
    <record id="survey_demo_quiz_answer_2_p3_q2_l2" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q2"/>
        <field name="answer_type">char_box</field>
        <field name="value_char_box">Mooses are best pollinators of the world!</field>
    </record>
    <record id="survey_demo_quiz_answer_2_p3_q3_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q3"/>
        <field name="answer_is_correct" eval="True"/>
        <field name="answer_score">20</field>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q3_sug1"/>
    </record>
    <record id="survey_demo_quiz_answer_2_p3_q3_l2" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q3"/>
        <field name="answer_type">char_box</field>
        <field name="value_char_box">I sold a 30K raspberry tree once.</field>
    </record>
    <record id="survey_demo_quiz_answer_2_p3_q4_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q4"/>
        <field name="answer_is_correct" eval="True"/>
        <field name="answer_score">20</field>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q4_sug1"/>
    </record>
    <record id="survey_demo_quiz_answer_2_p3_q5_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q5"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q5_sug1"/>
        <field name="matrix_row_id" ref="survey_demo_quiz_p3_q5_row1"/>
    </record>
    <record id="survey_demo_quiz_answer_2_p3_q5_l2" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q5"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q5_sug2"/>
        <field name="matrix_row_id" ref="survey_demo_quiz_p3_q5_row2"/>
    </record>
    <record id="survey_demo_quiz_answer_2_p3_q6_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q6"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q6_sug1"/>
        <field name="matrix_row_id" ref="survey_demo_quiz_p3_q6_row1"/>
    </record>
    <record id="survey_demo_quiz_answer_2_p3_q6_l2" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q6"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q6_sug2"/>
        <field name="matrix_row_id" ref="survey_demo_quiz_p3_q6_row2"/>
    </record>
    <record id="survey_demo_quiz_answer_2_p3_q6_l3" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q6"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q6_sug3"/>
        <field name="matrix_row_id" ref="survey_demo_quiz_p3_q6_row3"/>
    </record>
    <!-- Page 4: trees -->
    <record id="survey_demo_quiz_answer_2_p4_q1_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p4_q1"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p4_q1_sug1"/>
    </record>
    <record id="survey_demo_quiz_answer_2_p4_q2_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p4_q2"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p4_q2_sug1"/>
    </record>
    <record id="survey_demo_quiz_answer_2_p4_q3_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p4_q3"/>
        <field name="answer_is_correct" eval="True"/>
        <field name="answer_score">10</field>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p4_q3_sug2"/>
    </record>
    <record id="survey_demo_quiz_answer_2_p4_q4_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p4_q4"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p4_q4_sug1"/>
    </record>
    <record id="survey_demo_quiz_answer_2_p4_q5_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p4_q5"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p4_q5_sug1"/>
    </record>
    <record id="survey_demo_quiz_answer_2_p4_q5_l2" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p4_q5"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p4_q5_sug3"/>
    </record>
    <record id="survey_demo_quiz_answer_2_p4_q6_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_2"/>
        <field name="question_id" ref="survey_demo_quiz_p4_q6"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p4_q6_sug1"/>
    </record>


    <!-- Page 1: general informations -->
    <record id="survey_demo_quiz_answer_3_p1_q1_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p1_q1"/>
        <field name="answer_type">char_box</field>
        <field name="value_char_box"><EMAIL></field>
    </record>
    <record id="survey_demo_quiz_answer_3_p1_q2_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p1_q2"/>
        <field name="answer_type">char_box</field>
        <field name="value_char_box">Joël Willis</field>
    </record>
    <record id="survey_demo_quiz_answer_3_p1_q3_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p1_q3"/>
        <field name="answer_type">char_box</field>
        <field name="value_char_box">Brussels</field>
    </record>
    <record id="survey_demo_quiz_answer_3_p1_q4_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p1_q4"/>
        <field name="answer_type">numerical_box</field>
        <field name="value_numerical_box">28</field>
    </record>
    <!-- Page 2: quiz about company -->
    <record id="survey_demo_quiz_answer_3_p2_q1_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p2_q1"/>
        <field name="answer_type">date</field>
        <field name="value_date" eval="DateTime.today() - relativedelta(years=38)"/>
    </record>
    <record id="survey_demo_quiz_answer_3_p2_q2_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p2_q2"/>
        <field name="answer_type">datetime</field>
        <field name="value_datetime" eval="DateTime.now().replace(year=2005, month=4, day=18, hour=10, minute=0, second=0)"/>
    </record>
    <record id="survey_demo_quiz_answer_3_p2_q3_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p2_q3"/>
        <field name="answer_type">text_box</field>
        <field name="value_text_box">Oak, fur, pine, red pine</field>
    </record>
    <!-- Page 3: quiz about fruits and vegetables -->
    <record id="survey_demo_quiz_answer_3_p3_q1_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q1"/>
        <field name="answer_type">char_box</field>
        <field name="value_char_box">Both fruit (seeds, par of the plant) and vegetable (culinary use), obviously.</field>
    </record>
    <record id="survey_demo_quiz_answer_3_p3_q2_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q2"/>
        <field name="answer_is_correct" eval="True"/>
        <field name="answer_score">20</field>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q2_sug1"/>
    </record>
    <record id="survey_demo_quiz_answer_3_p3_q3_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q3"/>
        <field name="answer_is_correct" eval="True"/>
        <field name="answer_score">20</field>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q3_sug1"/>
    </record>
    <record id="survey_demo_quiz_answer_3_p3_q3_l2" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q3"/>
        <field name="answer_is_correct" eval="True"/>
        <field name="answer_score">10</field>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q3_sug2"/>
    </record>
    <record id="survey_demo_quiz_answer_3_p3_q3_l3" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q3"/>
        <field name="answer_type">char_box</field>
        <field name="value_char_box">You forgot the strawberry tree.</field>
    </record>
    <record id="survey_demo_quiz_answer_3_p3_q4_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q4"/>
        <field name="answer_is_correct" eval="True"/>
        <field name="answer_score">20</field>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q4_sug1"/>
    </record>
    <record id="survey_demo_quiz_answer_3_p3_q4_l2" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q4"/>
        <field name="answer_is_correct" eval="True"/>
        <field name="answer_score">20</field>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q4_sug2"/>
    </record>
    <record id="survey_demo_quiz_answer_3_p3_q4_l3" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q4"/>
        <field name="answer_score">-10</field>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q4_sug3"/>
    </record>
    <record id="survey_demo_quiz_answer_3_p3_q4_l4" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q4"/>
        <field name="answer_type">char_box</field>
        <field name="value_char_box">Gives the beeest cosmics rays man. So juicy.</field>
    </record>
    <record id="survey_demo_quiz_answer_3_p3_q5_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q5"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q5_sug1"/>
        <field name="matrix_row_id" ref="survey_demo_quiz_p3_q5_row1"/>
    </record>
    <record id="survey_demo_quiz_answer_3_p3_q5_l2" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q5"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q5_sug2"/>
        <field name="matrix_row_id" ref="survey_demo_quiz_p3_q5_row2"/>
    </record>
    <record id="survey_demo_quiz_answer_3_p3_q5_l3" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q5"/>
        <field name="answer_type">char_box</field>
        <field name="value_char_box">Well sometimes I forget them, they survived. Almost.</field>
    </record>
    <record id="survey_demo_quiz_answer_3_p3_q6_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q6"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q6_sug3"/>
        <field name="matrix_row_id" ref="survey_demo_quiz_p3_q6_row1"/>
    </record>
    <record id="survey_demo_quiz_answer_3_p3_q6_l2" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q6"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q6_sug4"/>
        <field name="matrix_row_id" ref="survey_demo_quiz_p3_q6_row1"/>
    </record>
    <record id="survey_demo_quiz_answer_3_p3_q6_l3" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q6"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q6_sug1"/>
        <field name="matrix_row_id" ref="survey_demo_quiz_p3_q6_row2"/>
    </record>
    <record id="survey_demo_quiz_answer_3_p3_q6_l4" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q6"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q6_sug2"/>
        <field name="matrix_row_id" ref="survey_demo_quiz_p3_q6_row2"/>
    </record>
    <record id="survey_demo_quiz_answer_3_p3_q6_l5" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p3_q6"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p3_q6_sug4"/>
        <field name="matrix_row_id" ref="survey_demo_quiz_p3_q6_row3"/>
    </record>
    <!-- Page 4: trees -->
    <record id="survey_demo_quiz_answer_3_p4_q1_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p4_q1"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p4_q1_sug3"/>
    </record>
    <record id="survey_demo_quiz_answer_3_p4_q2_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p4_q2"/>
        <field name="answer_score">20</field>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p4_q2_sug2"/>
    </record>
    <record id="survey_demo_quiz_answer_3_p4_q3_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p4_q3"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p4_q3_sug1"/>
    </record>
    <record id="survey_demo_quiz_answer_3_p4_q4_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p4_q4"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p4_q4_sug3"/>
    </record>
    <record id="survey_demo_quiz_answer_3_p4_q5_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p4_q5"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p4_q5_sug2"/>
    </record>
    <record id="survey_demo_quiz_answer_3_p4_q5_l2" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p4_q5"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p4_q5_sug3"/>
    </record>
    <record id="survey_demo_quiz_answer_3_p4_q6_l1" model="survey.user_input.line">
        <field name="user_input_id" ref="survey_demo_quiz_answer_3"/>
        <field name="question_id" ref="survey_demo_quiz_p4_q6"/>
        <field name="answer_type">suggestion</field>
        <field name="suggested_answer_id" ref="survey_demo_quiz_p4_q6_sug1"/>
    </record>

</data></odoo>
