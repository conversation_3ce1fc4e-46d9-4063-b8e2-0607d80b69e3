<?xml version="1.0" encoding="UTF-8"?>
<odoo noupdate="1">

    <record id="goal_gamification_hr_user_visibility" model="ir.rule">
        <field name="name">HR Officer can see any goal</field>
        <field name="model_id" ref="gamification.model_gamification_goal"/>
        <field name="groups" eval="[(4, ref('hr.group_hr_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

</odoo>
