# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* snailmail
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__attachment_error
msgid "ATTACHMENT_ERROR"
msgstr "ATTACHMENT_ERROR"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_cover
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_cover
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__snailmail_cover
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Add a Cover Page"
msgstr "เพิ่มหน้าปก"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Address"
msgstr "ที่อยู่"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core/failure_model_patch.js:0
msgid ""
"An error occurred when sending a letter with Snailmail on “%(record_name)s”"
msgstr "เกิดข้อผิดพลาดขณะส่งจดหมายด้วย Snailmail ใน “%(record_name)s”"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core/failure_model_patch.js:0
msgid "An error occurred when sending a letter with Snailmail."
msgstr "เกิดข้อผิดพลาดขณะส่งจดหมายด้วย Snailmail"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "An error occurred when sending the document by post.<br>Error: %s"
msgstr "เกิดข้อผิดพลาดขณะส่งเอกสารทางไปรษณีย์ <br>ข้อผิดพลาด:%s"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "An unknown error happened. Please contact the support."
msgstr "เกิดข้อผิดพลาดโดยไม่ทราบสาเหตุ โปรดติดต่อฝ่ายช่วยเหลือ"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid "An unknown error occurred. Please contact our"
msgstr "เกิดข้อผิดพลาดโดยไม่ทราบสาเหตุ กรุณาติดต่อเรา"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_id
msgid "Attachment"
msgstr "การแนบ"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_fname
msgid "Attachment Filename"
msgstr "ชื่อไฟล์แนบ"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core/notification_model_patch.js:0
msgid "Awaiting Dispatch"
msgstr "รอจัดส่ง"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__duplex
msgid "Both side"
msgstr "ทั้งสองด้าน"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_duplex
msgid "Both sides"
msgstr "ทั้งสองด้าน"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid "Buy credits"
msgstr "ซื้อเครดิต"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__credit_error
msgid "CREDIT_ERROR"
msgstr "CREDIT_ERROR"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_form
msgid "Cancel"
msgstr "ยกเลิก"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Cancel Letter"
msgstr "ยกเลิกจดหมาย"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Cancel letter"
msgstr "ยกเลิกจดหมาย"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Cancel notification in failure"
msgstr "ยกเลิกการแจ้งเตือนเมื่อล้มเหลว"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core/notification_model_patch.js:0
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__canceled
msgid "Cancelled"
msgstr "ยกเลิก"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__city
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__city
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "City"
msgstr "เมือง"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Close"
msgstr "ปิด"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__color
msgid "Color"
msgstr "สี"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__company_id
msgid "Company"
msgstr "บริษัท"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__country_id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__country_id
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Country"
msgstr "ประเทศ"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__cover
msgid "Cover Page"
msgstr "หน้าปก"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__create_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__create_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__create_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__create_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__display_name
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__display_name
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_datas
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_list
msgid "Document"
msgstr "เอกสาร"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__res_id
msgid "Document ID"
msgstr "ไอดีเอกสาร"

#. module: snailmail
#: model:ir.model,name:snailmail.model_mail_thread
msgid "Email Thread"
msgstr "เธรดอีเมล"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core/notification_model_patch.js:0
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__error_code
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__error
msgid "Error"
msgstr "ผิดพลาด"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__format_error
msgid "FORMAT_ERROR"
msgstr "FORMAT_ERROR"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
#: model:ir.actions.act_window,name:snailmail.snailmail_letter_missing_required_fields_action
msgid "Failed letter"
msgstr "จดหมายล้มเหลว"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_notification__failure_type
msgid "Failure type"
msgstr "ประเภทความล้มเหลว"

#. module: snailmail
#: model:ir.actions.act_window,name:snailmail.snailmail_letter_format_error_action
msgid "Format Error"
msgstr "ข้อผิดพลาดของรูปแบบ"

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter_format_error
msgid "Format Error Sending a Snailmail Letter"
msgstr "ข้อผิดพลาดของรูปแบบในการส่งจดหมาย Snailmail"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__id
msgid "ID"
msgstr "ไอดี"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__pending
msgid "In Queue"
msgstr "อยู่ในคิว"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__info_msg
msgid "Information"
msgstr "ข้อมูล"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "Invalid recipient name."
msgstr "ชื่อผู้รับไม่ถูกต้อง"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__write_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__write_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__write_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__write_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_mail__letter_ids
#: model:ir.model.fields,field_description:snailmail.field_mail_message__letter_ids
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__letter_id
msgid "Letter"
msgstr "ซองจดหมาย"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "Letter sent by post with Snailmail"
msgstr "จดหมายที่ส่งทางไปรษณีย์ด้วย Snailmail"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_list
msgid "Letters"
msgstr "จดหมาย"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__missing_required_fields
msgid "MISSING_REQUIRED_FIELDS"
msgstr "MISSING_REQUIRED_FIELDS"

#. module: snailmail
#: model:ir.model,name:snailmail.model_mail_message
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__message_id
msgid "Message"
msgstr "ข้อความ"

#. module: snailmail
#: model:ir.model,name:snailmail.model_mail_notification
msgid "Message Notifications"
msgstr "การแจ้งเตือนข้อความ"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__model
msgid "Model"
msgstr "โมเดล"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__no_price_available
msgid "NO_PRICE_AVAILABLE"
msgstr "NO_PRICE_AVAILABLE"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "Not enough credits for Snail Mail"
msgstr "เครดิตไม่เพียงพอสำหรับ Snail Mail"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "ประเภทการแจ้งเตือน"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__notification_ids
msgid "Notifications"
msgstr "การแจ้งเตือน"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "One or more required fields are empty."
msgstr "มีช่องที่ต้องกรอกอย่างน้อยหนึ่งช่อง"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__report_template
msgid "Optional report to print and attach"
msgstr "รายงานเพิ่มเติมที่จะพิมพ์และแนบ"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid ""
"Our service cannot read your letter due to its format.<br/>\n"
"                Please modify the format of the template or update your settings\n"
"                to automatically add a blank cover page to all letters."
msgstr ""
"บริการของเราไม่สามารถอ่านจดหมายของคุณได้เนื่องจากรูปแบบของจดหมาย<br/>\n"
"                โปรดแก้ไขรูปแบบของเทมเพลตหรืออัปเดตการตั้งค่าของคุณ\n"
"                เพื่อเพิ่มหน้าปกเปล่าให้กับตัวอักษรทั้งหมดโดยอัตโนมัติ"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__partner_id
msgid "Partner"
msgstr "พาร์ทเนอร์"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "Please use an A4 Paper format."
msgstr "กรุณาใช้รูปแบบกระดาษ A4"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_duplex
msgid "Print Both sides"
msgstr "พิมพ์ทั้งสองด้าน"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_color
msgid "Print In Color"
msgstr "พิมพ์เป็นสี"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid "Re-send letter"
msgstr "ส่งจดหมายอีกครั้ง"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__partner_id
msgid "Recipient"
msgstr "ผู้รับ"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__reference
msgid "Related Record"
msgstr "บันทึกที่เกี่ยวข้อง"

#. module: snailmail
#: model:ir.model,name:snailmail.model_ir_actions_report
msgid "Report Action"
msgstr "การดําเนินการรายงาน"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_form
msgid "Send Now"
msgstr "ส่งทันที"

#. module: snailmail
#: model:iap.service,description:snailmail.iap_service_snailmail
msgid "Send your customer invoices and follow-up reports by post, worldwide."
msgstr "ส่งใบแจ้งหนี้ลูกค้าของคุณและรายงานการติดตามทางไปรษณีย์ทั่วโลก"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core/notification_model_patch.js:0
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__sent
msgid "Sent"
msgstr "ส่งแล้ว"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__user_id
msgid "Sent by"
msgstr "ส่งโดย"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "Snail Mails are successfully sent"
msgstr "Snail Mails ถูกส่งเรียบร้อยแล้ว"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_message__message_type__snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__notification_type__snail
msgid "Snailmail"
msgstr "Snailmail"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_color
msgid "Snailmail Color"
msgstr "สี Snailmail"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_cover_readonly
msgid "Snailmail Cover Readonly"
msgstr ""

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_credit
msgid "Snailmail Credit Error"
msgstr "ข้อผิดพลาดด้านเครดิต Snailmail"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/messaging_menu/messaging_menu_patch.js:0
msgid "Snailmail Failure: %(modelName)s"
msgstr ""

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/messaging_menu/messaging_menu_patch.js:0
msgid "Snailmail Failures"
msgstr "Snailmail ล้มเหลว"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_format
msgid "Snailmail Format Error"
msgstr "เกิดข้อผิดพลาดของรูปแบบ Snailmail"

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter
#: model:ir.model.fields,field_description:snailmail.field_mail_notification__letter_id
msgid "Snailmail Letter"
msgstr "จดหมาย Snailmail"

#. module: snailmail
#: model:ir.actions.act_window,name:snailmail.action_mail_letters
#: model:ir.ui.menu,name:snailmail.menu_snailmail_letters
msgid "Snailmail Letters"
msgstr "จดหมาย Snailmail"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_fields
msgid "Snailmail Missing Required Fields"
msgstr "Snailmail ขาดฟิลด์ที่จำเป็น"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_price
msgid "Snailmail No Price Available"
msgstr "Snailmail ไม่มีราคาจำหน่าย"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__message_id
msgid "Snailmail Status Message"
msgstr "ข้อความสถานะ Snailmail"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_trial
msgid "Snailmail Trial Error"
msgstr "ข้อผิดพลาดในการทดลองใช้ Snailmail"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_error
msgid "Snailmail Unknown Error"
msgstr "ข้อผิดพลาดที่ไม่รู้สาเหตุ Snailmail"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_mail__snailmail_error
#: model:ir.model.fields,field_description:snailmail.field_mail_message__snailmail_error
msgid "Snailmail message in error"
msgstr "ข้อผิดพลาดของข้อความ Snailmail"

#. module: snailmail
#: model:ir.actions.server,name:snailmail.snailmail_print_ir_actions_server
msgid "Snailmail: process letters queue"
msgstr "Snailmail: ประมวลผลคิวจดหมาย"

#. module: snailmail
#: model:iap.service,unit_name:snailmail.iap_service_snailmail
msgid "Stamps"
msgstr "แสตมป์"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__state_id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__state_id
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "State"
msgstr "รัฐ"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__state
msgid "Status"
msgstr "สถานะ"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__street
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__street
msgid "Street"
msgstr "ถนน"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Street 2..."
msgstr "ถนน 2..."

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Street..."
msgstr "ถนน..."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__street2
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__street2
msgid "Street2"
msgstr "ถนน2"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__trial_error
msgid "TRIAL_ERROR"
msgstr "TRIAL_ERROR"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "The address of the recipient is not complete"
msgstr "ที่อยู่ของผู้รับไม่ครบถ้วน"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid ""
"The attachment of the letter could not be sent. Please check its content and"
" contact the support if the problem persists."
msgstr ""
"ไม่สามารถส่งเอกสารแนบในจดหมายได้ "
"โปรดตรวจสอบเนื้อหาและติดต่อฝ่ายช่วยเหลือหากยังคงมีปัญหาอยู่"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "The country of the partner is not covered by Snailmail."
msgstr "ประเทศของพาร์ทเนอร์ไม่ครอบคลุมโดย Snailmail"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid ""
"The country to which you want to send the letter is not supported by our "
"service."
msgstr "บริการของเราไม่รองรับประเทศที่คุณต้องการส่งจดหมายไป"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid ""
"The customer address is not complete. Update the address here and re-send "
"the letter."
msgstr ""
"ที่อยู่ของลูกค้าไม่ครบถ้วน อัปเดตที่อยู่ที่นี่และส่งจดหมายใหม่อีกครั้ง"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "The document was correctly sent by post.<br>The tracking id is %s"
msgstr "เอกสารถูกส่งอย่างถูกต้องทางไปรษณีย์<br> รหัสติดตามคือ %s"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid ""
"The letter could not be sent due to insufficient credits on your IAP "
"account."
msgstr "ไม่สามารถส่งจดหมายได้ เนื่องจากเครดิตไม่เพียงพอในบัญชี IAP ของคุณ"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:snailmail.field_mail_message__message_type
msgid "Type"
msgstr "ประเภท"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__unknown_error
msgid "UNKNOWN_ERROR"
msgstr "UNKNOWN_ERROR"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Update Config and Re-send"
msgstr "อัปเดตการกำหนดค่าและส่งอีกครั้ง"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Update address and re-send"
msgstr "อัปเดตที่อยู่แล้วส่งใหม่"

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter_missing_required_fields
msgid "Update address of partner"
msgstr "อัปเดตที่อยู่ของพาร์ทเนอร์"

#. module: snailmail
#: model:ir.model.fields,help:snailmail.field_mail_mail__message_type
#: model:ir.model.fields,help:snailmail.field_mail_message__message_type
msgid ""
"Used to categorize message generator\n"
"'email': generated by an incoming email e.g. mailgateway\n"
"'comment': generated by user input e.g. through discuss or composer\n"
"'email_outgoing': generated by a mailing\n"
"'notification': generated by system e.g. tracking messages\n"
"'auto_comment': generated by automated notification mechanism e.g. acknowledgment\n"
"'user_notification': generated for a specific recipient"
msgstr ""
"ใช้เพื่อจัดหมวดหมู่ตัวสร้างข้อความ\n"
"'อีเมล': สร้างโดยอีเมลขาเข้า เช่น mailgateway\n"
"'ความคิดเห็น': สร้างโดยการป้อนข้อมูลของผู้ใช้ เช่น ผ่านการพูดคุยหรือการแต่ง\n"
"'อีเมลขาออก': สร้างโดยการส่งจดหมาย\n"
"'การแจ้งเตือน': สร้างโดยระบบ เช่น ติดตามข้อความ\n"
"'ความคิดเห็นอัตโนมัติ': สร้างโดยกลไกการแจ้งเตือนอัตโนมัติ เช่น การรับทราบ\n"
"'การแจ้งเตือนผู้ใช้': สร้างขึ้นสำหรับผู้รับที่เฉพาะเจาะจง"

#. module: snailmail
#: model:ir.model.fields,help:snailmail.field_snailmail_letter__state
msgid ""
"When a letter is created, the status is 'Pending'.\n"
"If the letter is correctly sent, the status goes in 'Sent',\n"
"If not, it will got in state 'Error' and the error message will be displayed in the field 'Error Message'."
msgstr ""
"เมื่อสร้างจดหมายแล้ว สถานะจะเป็น 'รอดำเนินการ'\n"
"หากจดหมายถูกส่งอย่างถูกต้อง สถานะจะเป็น 'ส่งแล้ว'\n"
"ถ้าไม่เช่นนั้น จะอยู่ในสถานะ 'ข้อผิดพลาด' และข้อความแสดงข้อผิดพลาดจะแสดงขึ้นในช่อง 'ข้อความแสดงข้อผิดพลาด'"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid ""
"You don't have an IAP account registered for this service.<br>Please go to "
"<a href=%s target=\"new\">iap.odoo.com</a> to claim your free credits."
msgstr ""
"คุณไม่มีบัญชี IAP ที่ลงทะเบียนสำหรับบริการนี้ <br>โปรดไปที่ <a href=%s "
"target=\"new\">iap.odoo.com</a> เพื่อรับเครดิตฟรีของคุณ"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid ""
"You don't have enough credits to perform this operation.<br>Please go to "
"your <a href=%s target=\"new\">iap account</a>."
msgstr ""
"คุณมีเครดิตไม่เพียงพอที่จะดำเนินการนี้ <br>โปรดไปที่<a href=%s "
"target=\"new\">บัญชี IAP </a>ของคุณ"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid "You need credits on your IAP account to send a letter."
msgstr "คุณต้องมีเครดิตในบัญชี IAP ของคุณเพื่อส่งจดหมาย"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "ZIP"
msgstr "รหัสไปรษณีย์"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__zip
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__zip
msgid "Zip"
msgstr "รหัสไปรษณีย์"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid "for further assistance."
msgstr "เพื่อขอความช่วยเหลือต่อไป"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid "support"
msgstr "ช่วยเหลือ"
