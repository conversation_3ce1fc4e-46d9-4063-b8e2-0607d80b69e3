.max-width-325px {
  max-width: 325px;
}

.numpad-4-cols {
  grid-template-columns: repeat(4, 1fr);

  .numpad-button:nth-child(4n) {
    margin-left: map-get($spacers, 2) !important;
  }
}

.modal-body .numpad-button {
  @include media-breakpoint-down(xl) {
    padding: map-get($spacers, 2) 0;
  }
}

.numpad-button {
  @include media-breakpoint-down(xl) {
    padding: 0;
  }

  &:nth-child(4n) {
    margin-left: map-get($spacers, 2);
  }
}

.numpad-3-cols {
  grid-template-columns: repeat(3, 1fr);
}

.numpad-discount.active {
  border-bottom: $border-width solid $o-component-active-border !important;
  border-top: $border-width solid $o-component-active-border !important;
}
