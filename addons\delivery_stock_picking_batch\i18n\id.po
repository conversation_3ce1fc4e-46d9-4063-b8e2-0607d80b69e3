# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_stock_picking_batch
# 
# Translators:
# Wil Odoo, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Abe Manyo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: delivery_stock_picking_batch
#: model:ir.model.fields,help:delivery_stock_picking_batch.field_stock_picking_type__batch_max_weight
msgid ""
"A transfer will not be automatically added to batches that will exceed this weight if the transfer is added to it.\n"
"Leave this value as '0' if no weight limit."
msgstr ""
"Transfer tidak akan secara otomatis ditambahkan ke batch bila dengan penambahan transfer berat ini akan dilampaui.\n"
"Biarkan nilai ini '0' bila tidak ada batas berat."

#. module: delivery_stock_picking_batch
#: model:ir.model.fields,help:delivery_stock_picking_batch.field_stock_picking_type__batch_group_by_carrier
msgid "Automatically group batches by carriers"
msgstr "Secara otomatis kelompokkan batch berdasarkan carrier"

#. module: delivery_stock_picking_batch
#: model:ir.model,name:delivery_stock_picking_batch.model_stock_picking_batch
msgid "Batch Transfer"
msgstr "Transfer Batch"

#. module: delivery_stock_picking_batch
#: model:ir.model.fields,field_description:delivery_stock_picking_batch.field_stock_picking_type__batch_group_by_carrier
msgid "Carrier"
msgstr "Pembawa"

#. module: delivery_stock_picking_batch
#: model:ir.model.fields,field_description:delivery_stock_picking_batch.field_stock_picking_type__batch_max_weight
msgid "Maximum weight"
msgstr "Bobot maksimum"

#. module: delivery_stock_picking_batch
#: model:ir.model,name:delivery_stock_picking_batch.model_stock_picking_type
msgid "Picking Type"
msgstr "Tipe Picking"

#. module: delivery_stock_picking_batch
#: model:ir.model,name:delivery_stock_picking_batch.model_stock_picking
msgid "Transfer"
msgstr "Transfer"

#. module: delivery_stock_picking_batch
#: model:ir.model.fields,field_description:delivery_stock_picking_batch.field_stock_picking_type__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Satuan berat dari label ukuran"
