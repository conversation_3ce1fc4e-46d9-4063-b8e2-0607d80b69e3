.o_location_selector_marker_icon {
    --LocationSelectorMarker-border-color: #{mix($body-bg, $body-color)};
    --LocationSelectorMarker-background: #{$body-bg};
    --LocationSelectorMarker-color: #{$body-color};
}

.o_location_selector_marker_icon_selected {
    --LocationSelectorMarker-border-color: #{mix(map-get($theme-colors, 'primary'), color-contrast(map-get($theme-colors, 'primary')))};
    --LocationSelectorMarker-background: #{map-get($theme-colors, 'primary')};
    --LocationSelectorMarker-color: #{color-contrast(map-get($theme-colors, 'primary'))};
}

.o_location_selector_map {
    min-height: 25vh;
}
