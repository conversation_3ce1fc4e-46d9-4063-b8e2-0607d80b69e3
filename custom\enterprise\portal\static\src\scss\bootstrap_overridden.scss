// The contrast ratio to reach against white, to determine if color changes from "light" to "dark". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.
// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast

// Overridden here as it is a dependency for some variables/functions afterwards
$min-contrast-ratio: $o-frontend-min-contrast-ratio !default;

//Restore BS4 Colors
$blue: #007bff !default;
$pink: #e83e8c !default;
$green: #28a745 !default;
$cyan: #17a2b8 !default;
$gray-900: #212529 !default;
//End Restore BS4 Colors

$gray-300: #dee2e6 !default;

$light: #eeeeee !default;

// Body
//
// Settings for the `<body>` element.

$body-bg: $o-portal-default-body-bg !default;

// Fonts
//
// Font, line-height, and color for body text, headings, and more.

$font-size-sm: (12 / 16) * 1rem !default;

// Table
$table-border-color: $gray-300 !default;
$table-group-separator-color: $gray-300 !default;

// Buttons
//
// For each of Bootstrap's buttons, define text, background, and border color.

$btn-padding-y-sm: (1 / 16) * 1rem !default;
$btn-padding-x-sm: (5 / 16) * 1rem !default;

// List group

$list-group-bg: $body-bg !default;

// Navbar

$navbar-dark-toggler-border-color: transparent;
$navbar-light-toggler-border-color: transparent;

// Modals

$modal-lg: $o-modal-lg;
$modal-md: $o-modal-md;
