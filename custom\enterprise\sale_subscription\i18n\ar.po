# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_subscription
# 
# Translators:
# Wil Odoo, 2025
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 15:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"\n"
"- You are trying to invoice recurring orders. Please verify the delivered quantity of product based on invoicing policy, next invoice date and end date of the contract."
msgstr ""
"\n"
"- أنت تحاول فوترة طلبات متكررة. يُرجى التحقق من كمية المنتج التي تم توصيلها بناءً على سياسة الفوترة وتاريخ الفاتورة التالي وتاريخ انتهاء العقد. "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__nbr
msgid "# of Lines"
msgstr "عدد البنود"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__percentage_satisfaction
msgid "% Happy"
msgstr "% سعيد"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/product.py:0
msgid "%(price)s %(billing_period_display_sentence)s"
msgstr "%(price)s %(billing_period_display_sentence)s"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order_line.py:0
msgid "%(start)s to %(next)s"
msgstr "%(start)s إلى %(next)s "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order_line.py:0
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
msgid "%s days"
msgstr "%s أيام "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order_line.py:0
msgid ""
"(*) These recurring products are discounted according to the prorated period"
" from %(start)s to %(end)s"
msgstr ""
"(*) يتم تخفيض تلك المنتجات التي يتم شراؤها بشكل دوري وفقاً للمدة التناسبية "
"من %(start)s إلى %(end)s "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order_line.py:0
msgid ""
"(*) These recurring products are entirely discounted as the next period has "
"not been invoiced yet."
msgstr ""
"(*) تلك المنتجات المتكررة مخفضة تماماً حيث إنه لم تتم فوترة الفترة القادمة "
"بعد. "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "(Change Plan)"
msgstr "(تغيير الخطة) "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "- You delivered %(delivered)s %(name)s and invoiced %(invoiced)s"
msgstr "- لقد قمت بتوصيل %(delivered)s %(name)s وفوترة %(invoiced)s "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__mrr_change_period__1month
msgid "1 Month"
msgstr "شهر 1 "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__mrr_change_period__3months
msgid "3 Months"
msgstr "3 أشهر"

#. module: sale_subscription
#: model:sale.subscription.plan,name:sale_subscription.subscription_plan_6_month
msgid "6 Months"
msgstr "6 أشهر "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "<b>Payment Method</b>"
msgstr "<b>طريقة العرض</b> "

#. module: sale_subscription
#: model:mail.template,body_html:sale_subscription.mail_template_subscription_alert
msgid ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"                <table style=\"width:600px;margin:5px auto;\">\n"
"                    <tbody>\n"
"                        <tr><td t-if=\"not object.company_id.uses_default_logo\">\n"
"                            <a href=\"/\"><img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"vertical-align:baseline;max-width:100px;\"/></a>\n"
"                        </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                                Subscription Renewal\n"
"                        </td></tr>\n"
"                    </tbody>\n"
"                </table>\n"
"                <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"                    <tbody>\n"
"                        <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                            <p>Dear <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,</p>\n"
"                            <p>Your subscription <strong t-out=\"object.name or ''\">Office Cleaning Service\"</strong> needs your attention.</p>\n"
"                            <p>We invite you to renew it by clicking on the following link.</p>\n"
"                            <p>Kind regards.</p>\n"
"                        </td></tr>\n"
"                        <tr data-o-mail-quote-container=\"1\"><td style=\"padding:15px 20px 10px 20px;\" t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</td></tr>\n"
"                    </tbody>\n"
"                </table>\n"
"            </div>\n"
"        "
msgstr ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"                <table style=\"width:600px;margin:5px auto;\">\n"
"                    <tbody>\n"
"                        <tr><td t-if=\"not object.company_id.uses_default_logo\">\n"
"                            <a href=\"/\"><img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"vertical-align:baseline;max-width:100px;\"/></a>\n"
"                        </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                                تجديد الاشتراك\n"
"                        </td></tr>\n"
"                    </tbody>\n"
"                </table>\n"
"                <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"                    <tbody>\n"
"                        <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                            <p>عزيزنا <t t-out=\"object.partner_id.name or ''\">براندن فريمان</t>،</p>\n"
"                            <p>اشتراكك <strong t-out=\"object.name or ''\">\"خدمة تنظيف المكاتب\"</strong> بحاجة إلى انتباهك.</p>\n"
"                            <p>ندعوك إلى تجديده عن طريق الضغط على الرابط التالي.</p>\n"
"                            <p>مع أطيب التحيات.</p>\n"
"                        </td></tr>\n"
"                        <tr data-o-mail-quote-container=\"1\"><td style=\"padding:15px 20px 10px 20px;\" t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>ميتشل آدمن</td></tr>\n"
"                    </tbody>\n"
"                </table>\n"
"            </div>\n"
"        "

#. module: sale_subscription
#: model:mail.template,body_html:sale_subscription.mail_template_subscription_rating
msgid ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"            <t t-set=\"access_token\" t-value=\"object._rating_get_access_token()\"/>\n"
"                <table style=\"width:600px;margin:5px auto;\">\n"
"                    <tbody>\n"
"                        <tr><td t-if=\"not object.company_id.uses_default_logo\">\n"
"                            <a href=\"/\"><img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"vertical-align:baseline;max-width:100px;\"/></a>\n"
"                        </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                                Satisfaction Survey\n"
"                        </td></tr>\n"
"                    </tbody>\n"
"                </table>\n"
"                <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"                    <tbody>\n"
"                        <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                            <p>Hello,</p>\n"
"                            <p>Please take a moment to rate our services related to your subscription \"<strong t-out=\"object.name or ''\">Office Cleaning Service\"</strong>\"\n"
"                               assigned to <strong t-out=\"object._rating_get_operator().name or ''\">Mitchell Admin</strong>.</p>\n"
"                            <p>We appreciate your feedback. It helps us to improve continuously.</p>\n"
"                        </td></tr>\n"
"                        <tr><td style=\"padding:10px 20px\">\n"
"                            <table summary=\"o_mail_notification\" style=\"width:100%;border-top:1px solid #e1e1e1;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align:center;\">\n"
"                                        <h2 style=\"font-weight:300;font-size:18px;\">\n"
"                                            Tell us how you feel about our services:\n"
"                                        </h2>\n"
"                                        <div style=\"text-color: #888888\">(click on one of these smileys)</div>\n"
"                                    </td>\n"
"                                </tr>\n"
"                                <tr>\n"
"                                    <td style=\"padding:10px 10px;\">\n"
"                                        <table style=\"width:100%;text-align:center;\">\n"
"                                            <tr>\n"
"                                                <td>\n"
"                                                    <a t-attf-href=\"/rate/{{ access_token }}/5\">\n"
"                                                        <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\"/>\n"
"                                                    </a>\n"
"                                                </td>\n"
"                                                <td>\n"
"                                                    <a t-attf-href=\"/rate/{{ access_token }}/3\">\n"
"                                                        <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\"/>\n"
"                                                    </a>\n"
"                                                </td>\n"
"                                                <td>\n"
"                                                    <a t-attf-href=\"/rate/{{ access_token }}/1\">\n"
"                                                        <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\"/>\n"
"                                                    </a>\n"
"                                                </td>\n"
"                                            </tr>\n"
"                                        </table>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </td></tr>\n"
"                        <tr data-o-mail-quote-container=\"1\"><td style=\"padding:15px 20px 10px 20px;\" t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</td></tr>\n"
"                    </tbody>\n"
"                </table>\n"
"                <table style=\"width:600px;margin:auto;text-align:center;font-size:12px;\">\n"
"                    <tbody>\n"
"                        <tr><td style=\"padding-top:10px;color:#afafaf;\">\n"
"                            <p>Email automatically sent by <a target=\"_blank\" href=\"https://www.odoo.com/app/subscriptions\" style=\"color:#875A7B;text-decoration:none;\">Odoo Subscription</a> for <a t-att-href=\"object.company_id.website\" style=\"color:#875A7B;text-decoration:none;\" t-out=\"object.company_id.name or ''\">YourCompany</a></p>\n"
"                        </td></tr>\n"
"                    </tbody>\n"
"                </table>\n"
"            </div>\n"
"        "
msgstr ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"            <t t-set=\"access_token\" t-value=\"object._rating_get_access_token()\"/>\n"
"                <table style=\"width:600px;margin:5px auto;\">\n"
"                    <tbody>\n"
"                        <tr><td t-if=\"not object.company_id.uses_default_logo\">\n"
"                            <a href=\"/\"><img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"vertical-align:baseline;max-width:100px;\"/></a>\n"
"                        </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                                استطلاع الرضا\n"
"                        </td></tr>\n"
"                    </tbody>\n"
"                </table>\n"
"                <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"                    <tbody>\n"
"                        <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                            <p>مرحباً،</p>\n"
"                            <p>يرجى أخذ دقيقة من وقتك لتقييم خدماتنا المتعلقة باشتراكك \"<strong t-out=\"object.name or ''\">خدمة تنظيف المكاتب\"</strong>\"\n"
"                               المسندة إلى <strong t-out=\"object._rating_get_operator().name or ''\">ميتشل آدمن</strong>.</p>\n"
"                            <p>نحن نقدر ملاحظاتك، حيث إنها تساعدنا على التحسن باستمرار.</p>\n"
"                        </td></tr>\n"
"                        <tr><td style=\"padding:10px 20px\">\n"
"                            <table summary=\"o_mail_notification\" style=\"width:100%;border-top:1px solid #e1e1e1;\">\n"
"                                <tr>\n"
"                                    <td style=\"text-align:center;\">\n"
"                                        <h2 style=\"font-weight:300;font-size:18px;\">\n"
"                                            أخبرنا عن رأيك حول خدماتنا:\n"
"                                        </h2>\n"
"                                        <div style=\"text-color: #888888\">(اضغط على إحدى تلك الوجوه الباسمة)</div>\n"
"                                    </td>\n"
"                                </tr>\n"
"                                <tr>\n"
"                                    <td style=\"padding:10px 10px;\">\n"
"                                        <table style=\"width:100%;text-align:center;\">\n"
"                                            <tr>\n"
"                                                <td>\n"
"                                                    <a t-attf-href=\"/rate/{{ access_token }}/5\">\n"
"                                                        <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\"/>\n"
"                                                    </a>\n"
"                                                </td>\n"
"                                                <td>\n"
"                                                    <a t-attf-href=\"/rate/{{ access_token }}/3\">\n"
"                                                        <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\"/>\n"
"                                                    </a>\n"
"                                                </td>\n"
"                                                <td>\n"
"                                                    <a t-attf-href=\"/rate/{{ access_token }}/1\">\n"
"                                                        <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\"/>\n"
"                                                    </a>\n"
"                                                </td>\n"
"                                            </tr>\n"
"                                        </table>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </td></tr>\n"
"                        <tr data-o-mail-quote-container=\"1\"><td style=\"padding:15px 20px 10px 20px;\" t-out=\"object.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>ميتشل آدمن</td></tr>\n"
"                    </tbody>\n"
"                </table>\n"
"                <table style=\"width:600px;margin:auto;text-align:center;font-size:12px;\">\n"
"                    <tbody>\n"
"                        <tr><td style=\"padding-top:10px;color:#afafaf;\">\n"
"                            <p>بريد إلكتروني يتم إرساله تلقائياً بواسطة <a target=\"_blank\" href=\"https://www.odoo.com/app/subscriptions\" style=\"color:#875A7B;text-decoration:none;\">اشتراك أودو</a> لـ<a t-att-href=\"object.company_id.website\" style=\"color:#875A7B;text-decoration:none;\" t-out=\"object.company_id.name or ''\">شركتك</a></p>\n"
"                        </td></tr>\n"
"                    </tbody>\n"
"                </table>\n"
"            </div>\n"
"        "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid ""
"<em class=\"text-muted\" colspan=\"2\" invisible=\"not id\">\n"
"                                Action data can not be updated to avoid unexpected behaviors. Create a new action instead.\n"
"                            </em>"
msgstr ""
"<em class=\"text-muted\" colspan=\"2\" invisible=\"not id\">\n"
"                                لا يمكن تحديث بيانات الإجراء لتجنب السلوكيات غير المتوقعة. قم بإنشاء إجراء جديد عوضاً عن ذلك.\n"
"                            </em> "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_base_automation_form
msgid ""
"<em class=\"text-muted\">\n"
"                        Action data can not be updated to avoid unexpected behaviors. Create a new automation rule instead.\n"
"                    </em>"
msgstr ""
"<em class=\"text-muted\">\n"
"                        لا يمكن تحديث بيانات الإجراء لتجنب السلوكيات غير المتوقعة. قم بإنشاء قاعدة أتمتة جديدة عوضاً عن ذلك.\n"
"                    </em>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "<i class=\"fa fa-download me-1\"/> Download"
msgstr "<i class=\"fa fa-download me-1\"/> تنزيل "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> In Progress"
msgstr "<i class=\"fa fa-fw fa-check\"/> قيد التنفيذ"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> Paused"
msgstr "<i class=\"fa fa-fw fa-check\"/> تم إيقافه مؤقتاً "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> Renewed"
msgstr "<i class=\"fa fa-fw fa-check\"/> تم التجديد "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "<i class=\"fa fa-fw fa-remove\"/> Closed"
msgstr "<i class=\"fa fa-fw fa-remove\"/> مغلق "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "<i class=\"fa fa-fw fa-remove\"/> Closing"
msgstr "<i class=\"fa fa-fw fa-remove\"/> الإغلاق "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "<i class=\"fa fa-fw fa-repeat\"/> To Renew"
msgstr "<i class=\"fa fa-fw fa-repeat\"/> بانتظار التجديد "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_so_portal_template
msgid "<i class=\"oi oi-arrow-right me-1\"/>Back to your subscription"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>الرجوع إلى اشتراكك "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "<option value=\"\">Choose a reason...</option>"
msgstr "<option value=\"\">اختر سبباً...</option> "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.payment_method_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.payment_token_form
msgid ""
"<small class=\"text-600\">Automate payments for the linked "
"subscriptions</small>"
msgstr ""
"<small class=\"text-600\">قم بأتمتة المدفوعات للاشتراكات المرتبطة</small> "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid ""
"<span class=\"badge rounded-pill text-bg-danger\"><i class=\"fa fa-fw fa-"
"repeat\"/> To Renew</span>"
msgstr ""
"<span class=\"badge rounded-pill text-bg-danger\"><i class=\"fa fa-fw fa-"
"repeat\"/> بانتظار التجديد</span>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid ""
"<span class=\"badge rounded-pill text-bg-dark\"><i class=\"fa fa-fw fa-"
"remove\"/> Closed</span>"
msgstr ""
"<span class=\"badge rounded-pill text-bg-dark\"><i class=\"fa fa-fw fa-"
"remove\"/> مغلق</span> "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid "<span class=\"badge rounded-pill text-bg-info\"> Renewal Quotation </span>"
msgstr "<span class=\"badge rounded-pill text-bg-info\"> عرض سعر التجديد </span> "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_portal_content_inherit
msgid ""
"<span class=\"badge rounded-pill text-bg-primary\"><i class=\"fa fa-fw fa-"
"check\"/> Upsell</span>"
msgstr ""
"<span class=\"badge rounded-pill text-bg-primary\"><i class=\"fa fa-fw fa-"
"check\"/> الارتقاء بالصفقة</span> "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid ""
"<span class=\"badge rounded-pill text-bg-secondary\"><i class=\"fa fa-fw fa-"
"check\"/> Renewed</span>"
msgstr ""
"<span class=\"badge rounded-pill text-bg-secondary\"><i class=\"fa fa-fw fa-"
"check\"/> تم التجديد</span> "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid ""
"<span class=\"badge rounded-pill text-bg-success\"><i class=\"fa fa-fw fa-"
"check\"/> In Progress</span>"
msgstr ""
"<span class=\"badge rounded-pill text-bg-success\"><i class=\"fa fa-fw fa-"
"check\"/> قيد التنفيذ</span> "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid ""
"<span class=\"badge rounded-pill text-bg-success\"><i class=\"fa fa-fw fa-"
"check\"/> Paused</span>"
msgstr ""
"<span class=\"badge rounded-pill text-bg-success\"><i class=\"fa fa-fw fa-"
"check\"/> تم إيقافه مؤقتاً</span> "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "<span class=\"badge rounded-pill text-bg-warning\">Closing</span>"
msgstr "<span class=\"badge rounded-pill text-bg-warning\">سيغلق</span> "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "<span class=\"o_stat_text\">MRR</span>"
msgstr "<span class=\"o_stat_text\">الإيرادات الشهرية المتكررة</span> "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "<span class=\"o_stat_text\">Subscription Items</span>"
msgstr "<span class=\"o_stat_text\">عناصر الاشتراك</span> "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "<span class=\"o_stat_text\">Subscriptions</span>"
msgstr "<span class=\"o_stat_text\">الاشتراكات</span> "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "<span class=\"text-muted\">Next Billing</span>"
msgstr "<span class=\"text-muted\">الفوترة التالية</span> "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.recurring_details
msgid "<span class=\"text-start\">Non Recurring</span>:"
msgstr "<span class=\"text-start\">غير المتكررة</span>: "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.recurring_details
msgid "<span class=\"text-start\">Recurring</span>:"
msgstr "<span class=\"text-start\">المتكررة</span>: "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid ""
"<span invisible=\"not plan_id or subscription_state == "
"'7_upsell'\">until</span>"
msgstr ""
"<span invisible=\"not plan_id or subscription_state == "
"'7_upsell'\">حتى</span> "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid ""
"<span>\n"
"                                There is already a pending payment for this subscription.\n"
"                            </span>"
msgstr ""
"<span>\n"
"                                توجد عملية دفع قيد الانتظار لهذا الاشتراك.\n"
"                            </span>"

#. module: sale_subscription
#: model_terms:web_tour.tour,rainbow_man_message:sale_subscription.sale_subscription_tour
msgid ""
"<span><b>Congratulations</b>, your first subscription quotation is ready to be sent!\n"
"        </span>"
msgstr ""
"<span><b>تهانينا</b>، لقد أصبح عرض سعر اشتراكك الأول جاهزاً لإرساله!\n"
"        </span>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "<strong invisible=\"not rating_operator\">%</strong>"
msgstr "<strong invisible=\"not rating_operator\">%</strong>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "<strong> and </strong>"
msgstr "<strong> و</strong>"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.report_saleorder_document
msgid "<strong>Recurring Plan</strong>"
msgstr "<strong>الخطة المتكررة</strong> "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.report_saleorder_document
msgid "<strong>Start Date</strong>"
msgstr "<strong>تاريخ البدء</strong> "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid ""
"<strong>Warning:</strong> the survey can only be shown if all information is"
" set. Please complete:"
msgstr ""
"<strong>تحذير:</strong> يمكن إظهار الاستطلاع فقط إذا كانت كافة المعلومات "
"موجودة. يرجى إكمال: "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "<strong>subscriptions</strong>"
msgstr "<strong>الاشتراكات</strong> "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "<strong>to</strong>"
msgstr "<strong>إلى</strong>"

#. module: sale_subscription
#: model:mail.template,body_html:sale_subscription.email_payment_close
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"/>\n"
"                    <span style=\"font-size: 10px;\">Your Subscription</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Office Cleaning Service</span>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"right\" t-if=\"not company.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"                    <t t-if=\"ctx.get('payment_token') and ctx.get('next_invoice_amount')\">\n"
"                        Our final attempt to process a payment for your subscription using your payment method\n"
"                        <t t-out=\"ctx.get('payment_token') or ''\">TOKEN</t>\n"
"                        for <t t-out=\"ctx['next_invoice_amount'] or ''\">100</t> <t t-out=\"ctx.get('currency') or ''\">$</t> failed.\n"
"                        <t t-if=\"ctx.get('error')\">\n"
"                            Your bank or credit institution gave the following details about the issue: <pre t-out=\"ctx['error'] or ''\"/>.\n"
"                        </t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        Our final attempt to process a payment for your subscription failed because we have no payment method recorded for you.\n"
"                    </t>\n"
"                    <br/><br/>\n"
"                    As your payment should have been made <strong><t t-out=\"ctx.get('auto_close_limit') or ''\">5</t> days ago</strong>, your subscription has been terminated.\n"
"                    Should you wish to resolve this issue, do not hesitate to contact us.<br/><br/>\n"
"                    Thank you for choosing <t t-out=\"company.name or ''\">YourCompany</t>!\n"
"                    <div t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                        <br/><br/>\n"
"                        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"company.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-if=\"company.phone\">\n"
"                        <t t-out=\"company.phone or ''\">******-123-4567</t> |\n"
"                    </t>\n"
"                    <t t-if=\"company.email\">\n"
"                        <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\"><EMAIL></a> |\n"
"                    </t>\n"
"                    <t t-if=\"company.website\">\n"
"                        <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"        "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"/>\n"
"                    <span style=\"font-size: 10px;\">اشتراكك</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">خدمة تنظيف المكاتب</span>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"right\" t-if=\"not company.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    مرحباً <t t-out=\"object.partner_id.name or ''\">براندن فريمان</t>،<br/><br/>\n"
"                    <t t-if=\"ctx.get('payment_token') and ctx.get('next_invoice_amount')\">\n"
"                        محاولتنا الأخيرة لمعالجة دفعة لاشتراكك باستخدام طريقة دفعك\n"
"                        <t t-out=\"ctx.get('payment_token') or ''\">الرمز</t>\n"
"                        لـ<t t-out=\"ctx['next_invoice_amount'] or ''\">100</t> <t t-out=\"ctx.get('currency') or ''\">$</t> فشلت.\n"
"                        <t t-if=\"ctx.get('error')\">\n"
"                            أعطى بنكك أو المؤسسة الائتمانية التفاصيل التالية حول المشكلة: <pre t-out=\"ctx['error'] or ''\"/>.\n"
"                        </t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        لقد فشلت المحاولة الأخيرة لمعالجة الدفع لاشتراكك لعدم وجود طريقة دفع مسجلة لديك.\n"
"                    </t>\n"
"                    <br/><br/>\n"
"                    بما أنه كان يجب إتمام الدفع منذ <strong><t t-out=\"ctx.get('auto_close_limit') or ''\">5</t> أيام مضت</strong>، لقد تم إنهاء اشتراكك.\n"
"                    إذا كنت ترغب في حل هذه المشكلة، لا تتردد في التواصل معنا.<br/><br/>\n"
"                    شكراً لاختيارك <t t-out=\"company.name or ''\">شركتك</t>!\n"
"                    <div t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                    <br/><br/>    \n"
"                        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>ميتشل آدمن</t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"company.name or ''\">شركتك</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-if=\"company.phone\">\n"
"                        <t t-out=\"company.phone or ''\">******-123-4567</t> |\n"
"                    </t>\n"
"                    <t t-if=\"company.email\">\n"
"                        <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\"><EMAIL></a> |\n"
"                    </t>\n"
"                    <t t-if=\"company.website\">\n"
"                        <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        مشغل بواسطة <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">أودو</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"        "

#. module: sale_subscription
#: model:mail.template,body_html:sale_subscription.email_payment_reminder
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"/>\n"
"                    <span style=\"font-size: 10px;\">Your Subscription</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Office Cleaning Service</span>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"right\" t-if=\"not company.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"                    <t t-if=\"ctx.get('payment_token') and ctx.get('next_invoice_amount')\">\n"
"                        We were unable to process a payment for your subscription using your payment method\n"
"                        <t t-out=\"ctx['payment_token'] or ''\">TOKEN</t>\n"
"                        for <t t-out=\"ctx['next_invoice_amount'] or ''\">10</t> <t t-out=\"ctx.get('currency_name') or ''\">$</t>.\n"
"                        <t t-if=\"ctx.get('error')\">\n"
"                            Your bank or credit institution gave the following details about the issue: <pre t-out=\"ctx['error'] or ''\"/>.\n"
"                        </t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        Your subscription <t t-out=\"ctx.get('code') or ''\">CODE</t> is due for renewal but we haven’t received your payment yet. To keep your subscription active, please make your payment as soon as possible.\n"
"                    </t>\n"
"                    <br/><br/>\n"
"                    Your subscription <t t-out=\"ctx.get('code') or ''\">CODE</t> is still valid but will be <strong>suspended</strong>\n"
"                    on <t t-out=\"format_date(ctx.get('date_close')) or ''\">05/05/2021</t><br/>\n"
"                    <div style=\"margin: 16px 0px; text-align: center;\">\n"
"                        <a t-attf-href=\"{{ ctx.get('payment_link', object.get_portal_url()) }}\" style=\"display: inline-block; padding: 10px 30px; text-decoration: none; background-color: #875A7B; color: #fff; border-radius: 5px;\">\n"
"                            Pay Now\n"
"                        </a>\n"
"                    </div>\n"
"                    <br/>\n"
"                    If you have any questions, do not hesitate to contact us.<br/><br/>\n"
"                    Thank you for choosing <t t-out=\"company.name or ''\">YourCompany</t>!\n"
"                    <div>\n"
"                    <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                        <br/><br/>\n"
"                        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                    </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"company.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-if=\"company.phone\">\n"
"                        <t t-out=\"company.phone or ''\">******-123-4567</t> |\n"
"                    </t>\n"
"                    <t t-if=\"company.email\">\n"
"                        <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\"><EMAIL></a> |\n"
"                    </t>\n"
"                    <t t-if=\"company.website\">\n"
"                        <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"        "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"/>\n"
"                    <span style=\"font-size: 10px;\">اشتراكك</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">خدمة تنظيف المكاتب</span>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"right\" t-if=\"not company.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    مرحباً <t t-out=\"object.partner_id.name or ''\">براندن فريمان</t>،<br/><br/>\n"
"                    <t t-if=\"ctx.get('payment_token') and ctx.get('next_invoice_amount')\">\n"
"                        لم نتمكن من معالجة الدفع لاشتراكك باستخدام طريقة الدفع الخاصة بك\n"
"                        <t t-out=\"ctx['payment_token'] or ''\">الرمز</t>\n"
"                        بسعر <t t-out=\"ctx['next_invoice_amount'] or ''\">10</t> <t t-out=\"ctx.get('currency_name') or ''\">$</t>.\n"
"                        <t t-if=\"ctx.get('error')\">\n"
"                            أعطى بنكك أو منشأتك الائتمانية التفاصيل التالية حول المشكلة: <pre t-out=\"ctx['error'] or ''\"/>.\n"
"                        </t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        حان وقت تجديد <t t-out=\"ctx.get('code') or ''\">رمز</t> الاشتراك الخاص بك ولكن لم تتم عملية الدفع بعد. حتى يبقى اشتراكك نشطاً، يرجى إتمام عملية الدفع في أسرع وقت ممكن.\n"
"                    </t>\n"
"                    <br/><br/>\n"
"                    <t t-out=\"ctx.get('code') or ''\">رمز</t> الاشتراك الخاص بك لا يزال صالحاً ولكن سيتم <strong>تعليقه</strong>\n"
"                    بتاريخ <t t-out=\"format_date(ctx.get('date_close')) or ''\">05/05/2021</t><br/>\n"
"                    <div style=\"margin: 16px 0px; text-align: center;\">\n"
"                        <a t-attf-href=\"{{ ctx.get('payment_link', object.get_portal_url()) }}\" style=\"display: inline-block; padding: 10px 30px; text-decoration: none; background-color: #875A7B; color: #fff; border-radius: 5px;\">\n"
"                            ادفع الآن\n"
"                        </a>\n"
"                    </div>\n"
"                    <br/>\n"
"                    لا تتردد في التواصل معنا إذا كانت لديك أي أسئلة أو استفسارات.<br/><br/>\n"
"                    شكراً لاختيارك <t t-out=\"company.name or ''\">شركتك</t>!\n"
"                    <div>\n"
"                    <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                        <br/><br/>\n"
"                        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>ميتشل آدمن</t>\n"
"                    </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"company.name or ''\">شركتك</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-if=\"company.phone\">\n"
"                        <t t-out=\"company.phone or ''\">******-123-4567</t> |\n"
"                    </t>\n"
"                    <t t-if=\"company.email\">\n"
"                        <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\"><EMAIL></a> |\n"
"                    </t>\n"
"                    <t t-if=\"company.website\">\n"
"                        <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        مُشغّل بواسطة <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">أودو</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"        "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"A cancelled SO cannot be in progress. You should close %s before cancelling "
"it."
msgstr ""
"لا يمكن أن يكون أمر البيع الملغي قيد التنفيذ. عليك إغلاق %s قبل إلغائه. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid "A renewal has been created by the client."
msgstr "تم إنشاء تجديد بواسطة العميل. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "A renewal quotation %s has been created"
msgstr "لقد تم إنشاء عرض سعر للتجديد %s "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/product.py:0
msgid "A subscription combo product can only contain subscription products."
msgstr "يمكن أن يحتوي منتج كومبو الاشتراك على منتجات الاشتراك فقط. "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_analysis_graph
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_graph
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_pivot
msgid "ARR Change"
msgstr "تغير معدل العائد المحاسبي "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__arr_change_normalized
msgid "ARR Change (normalized)"
msgstr "تغير معدل العائد المحاسبي (متطبع) "

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_account_move_send
msgid "Account Move Send"
msgstr "إرسال حركة الحساب "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Action"
msgstr "إجراء"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Action Name"
msgstr "اسم الإجراء"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_needaction
msgid "Action Needed"
msgstr "إجراء مطلوب"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__action
msgid "Action To Do"
msgstr "إجراء مطلوب"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__action_server_ids
msgid "Actions"
msgstr "الإجراءات"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__active
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__active
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__active
msgid "Active"
msgstr "نشط"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__contract_number
msgid "Active Subscriptions Change"
msgstr "تغير الاشتراك النشط "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Activity"
msgstr "النشاط"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.mail_activity_plan_action_subscription
#: model:ir.ui.menu,name:sale_subscription.mail_activity_plan_menu_config_subscription
msgid "Activity Plans"
msgstr "خطط النشاط "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__activity_type_id
msgid "Activity Type"
msgstr "نوع النشاط"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.mail_activity_type_action_config_subscription
#: model:ir.ui.menu,name:sale_subscription.subscription_menu_config_activity_type
msgid "Activity Types"
msgstr "أنواع الأنشطة "

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.mail_activity_plan_action_subscription
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                (e.g. \"Renewal Offer\", \"Yearly Satisfaction Review\", ...)"
msgstr ""
"تُستخدَم خطط الأنشطة لإسناد قائمة من الأنشطة ببضع نقرات فقط\n"
"                (مثال: \"عرض التجديد\"، \"تقييم الرضا السنوي\"، ...) "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__user_quantity
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__user_quantity
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_search
msgid "Add Products"
msgstr "إضافة المنتجات "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "Add Quantity"
msgstr "إضافة الكمية "

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Add a new rule"
msgstr "إضافة قاعدة جديدة "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:sale_subscription.product_template_view_form_recurring
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "Add a price rule"
msgstr "إضافة قاعدة سعر "

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid ""
"Add a recurring plan for this product, or create a new one with the desired "
"recurrence (e.g., Monthly)"
msgstr ""
"قم بإضافة خطة متكررة لهذا المنتج، أو أنشئ خطة جديدة بالتكرارا المطلوب (مثال:"
" شهرياً) "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_plan__billing_first_day
msgid ""
"Align all subscription invoices on the first day of each billing period."
msgstr "قم بمواءمة كافة فواتير الاشتراكات في اليوم الأول من كل فترة فوترة. "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__billing_first_day
msgid "Align to Period Start"
msgstr "المواءمة مع بداية الفترة "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid "All"
msgstr "الكل"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__user_quantity
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_plan__user_quantity
msgid ""
"Allow customers to create an Upsell quote to adjust the quantity of products"
" in their subscription.Only products that are listed as \"optional "
"products\" can be modified."
msgstr ""
"أتح للعملاء إنشاء عروض أسعار للارتقاء بالصفقة لضبط كميات المنتجات في "
"اشتراكهم. وحدها المنتجات المدرجة كـ \"منتجات اختيارية\" فقط هي التي يمكن "
"تعديلها. "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_plan__related_plan_id
msgid ""
"Allow your customers to switch from this plan to another on quotation (new "
"subscription or renewal)"
msgstr ""
"أتح لعملائك التبديل من هذه الخطة إلى أخرى في عرض السعر (اشتراك جديد أو "
"تجديد) "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid ""
"Allowing to be selected by customers in the portal when they are closing "
"their subscriptions."
msgstr ""
"السماح بأن يتم تحديدهم بواسطة العملاء عندما يقومون بإغلاق اشتراكاتهم. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid "An upsell has been created by the client."
msgstr "تم الارتقاء بالصفقة بواسطة العميل. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "An upsell quotation %s has been created"
msgstr "لقد تم إنشاء عرض سعر للارتقاء بالصفقة %s "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__recurring_yearly
msgid "Annual Recurring Revenue"
msgstr "الإيرادات المتكررة السنوية "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Anticipate payment"
msgstr "توقع الدفع "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__filter_domain
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Apply on"
msgstr "يُطبق على"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_template_view_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "Archived"
msgstr "مؤرشف"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__activity_user
msgid "Assign To"
msgstr "إسناد إلى "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__payment_transaction__subscription_action__assign_token
msgid "Assign Token"
msgstr "إسناد التذكرة "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_plan__user_closable_options__at_date
msgid "At date"
msgstr "بتاريخ "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__payment_transaction__renewal_state__authorized
msgid "Authorized"
msgstr "مصرح به "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "Automate Payment"
msgstr "أتمتة الدفع "

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_filtered
msgid ""
"Automate invoices and payments, simplify upsell quotes, reduce churn and get"
" insights (MRR, churn, CLTV, ...)"
msgstr ""
"يمكنك أتمتة الفواتير والمدفوعات، وتبسيط عروض الأسعار، وتقليل معدل توقف "
"العملاء عن استخدام الخدمات، والحصول على رؤى تفصيلية (الإيرادات الشهرية "
"المتكررة، ومعدل التوقف عن استخدام الخدمات، CLTV...). "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__auto_close_limit
msgid "Automatic Closing"
msgstr "الإغلاق التلقائي "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__auto_close_limit_display
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_search
msgid "Automatic Closing After"
msgstr "الإغلاق تلقائياً بعد "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Automatic payment failed after multiple attempts. Contract closed "
"automatically."
msgstr "فشل الدفع التلقائي بعد عدة محاولات. تم إغلاق العقد تلقائياً. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Automatic payment failed. Check the corresponding invoice %s. We can't "
"automatically process negative payment"
msgstr ""
"فشلت عملية الدفع التلقائي. يرجى التحقق من الفاتورة المقابلة له %s. لا يمكننا"
" معالجة عملية دفع سالبة تلقائياً "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Automatic payment failed. Email sent to customer. Error: %s"
msgstr ""
"فشلت عملية الدفع التلقائي. تم إرسال البريد الإلكتروني إلى العميل. الخطأ: %s "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Automatic payment failed. No country specified on payment_token's partner"
msgstr "فشلت عملية الدفع التلقائي. لا توجد دولة محددة في شريك payment_token "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Automatic payment failed. No email sent this time. Error: %s"
msgstr ""
"فشلت عملية الدفع التلقائي. لم يتم إرسال بريد إلكتروني هه المرة. الخطأ: %s "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Automatic payment succeeded. Payment reference: %(ref)s. Amount: %(amount)s."
" Contract set to: In Progress, Next Invoice: %(inv)s. Email sent to "
"customer."
msgstr ""
"لقد تمت عملية الدفع التلقائي بنجاح. مرجع الدفع: %(ref)s المبلغ: %(amount)s. "
"تم تعيين العقد إلى: قيد التنفيذ، الفاتورة التالية: %(inv)s. تم إرسال بريد "
"إلكتروني إلى العميل. "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__payment_exception
msgid ""
"Automatic payment with token failed. The payment provider configuration and "
"token should be checked"
msgstr ""
"فشلت عملية الدفع التلقائي باستخدام الرمز. يجب التحقق من تهيئة مزود الدفع "
"والرمز "

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_auto_close_limit_reached
msgid "Automatic renewal failed"
msgstr "فشلت عملية التجديد التلقائي "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Automatic renewal succeeded. Free subscription. Next Invoice: %(inv)s. No "
"email sent."
msgstr ""
"نجحت عملية التجديد التلقائي. الاشتراك المجاني. الفاتورة التالية: %(inv)s. لم"
" يتم إرسال بريد إلكتروني. "

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_base_automation
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__automation_id
msgid "Automation Rule"
msgstr "قاعدة الأتمتة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__name
msgid "Automation Rule Name"
msgstr "اسم قاعدة الأتمتة "

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_alert_action
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_alert
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_tree
msgid "Automation Rules"
msgstr "قواعد الأتمتة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_avg
msgid "Average Rating"
msgstr "متوسط التقييم "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__health__bad
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__health__bad
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__health__bad
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__health__bad
msgid "Bad"
msgstr "سيئ"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Bad Health"
msgstr "صحة سيئة"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__filter_pre_domain
msgid "Before Update Domain"
msgstr "نطاق ما قبل التحديث"

#. module: sale_subscription
#: model_terms:sale.order.close.reason,retention_message:sale_subscription.close_reason_1
msgid ""
"Before closing your subscription, we'd like to offer you to schedule a call "
"with Marc Demo, your account manager."
msgstr ""
"قبل أن تغلق اشتراكك، نود أن نعرض عليك جدولة مكالمة مع مارك ديمو، مدير "
"الحسابات المسند إليك. "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__billing_period_display
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__name
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "Billing Period"
msgstr "فترة الفوترة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__billing_period_value
msgid "Billing Period "
msgstr "فترة الفوترة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__billing_period_display_sentence
msgid "Billing Period Display"
msgstr "عرض فترة الفوترة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__retention_button_link
msgid "Button Link"
msgstr "رابط الزر "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__retention_button_text
msgid "Button Text"
msgstr "نص الزر "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__percentage_satisfaction
msgid ""
"Calculate the ratio between the number of the best ('great') ratings and the"
" total number of ratings"
msgstr "احتساب النسبة بين عدد أفضل التقييمات ('رائع') وإجمالي عدد التقييمات "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__campaign_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__campaign_id
msgid "Campaign"
msgstr "الحملة"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_change_customer_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_wizard_view_form
msgid "Cancel"
msgstr "إلغاء"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__order_state__cancel
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__state__cancel
#: model:sale.order.close.reason,name:sale_subscription.close_reason_cancel
msgid "Cancelled"
msgstr "تم الإلغاء "

#. module: sale_subscription
#: model:product.template,name:sale_subscription.product_car_leasing_product_template
msgid "Car Leasing (SUB)"
msgstr "تأجير سيارة (اشتراك) "

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_change_customer_wizard_action
msgid "Change Customer"
msgstr "تغيير العميل"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_so_portal_template
msgid "Change Plan"
msgstr "تغيير الخطة "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_so_portal_template
msgid "Change Plan for"
msgstr "تغيير الخطة لـ"

#. module: sale_subscription
#: model:ir.actions.server,name:sale_subscription.model_sale_order_subscription_change_customer
msgid "Change customer"
msgstr "تغيير العميل "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Check reopened subscription"
msgstr "تحقق من الاشتراكات التي تم إعادة فتحها "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "Choose a closing reason before submitting"
msgstr "اختر سبب الإغلاق قبل الإرسال "

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Choose a product name.<br/><i>(e.g. eLearning Access)</i>"
msgstr "اختر اسم المنتج.<br/><i>(مثلًا: الوصول للتعلّم الإلكتروني)</i>"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Choose the invoice duration for your subscription"
msgstr "اختر مدة الفاتورة لاشتراكك "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__event_type__2_churn
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__event_type__2_churn
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
msgid "Churn"
msgstr "توقف العميل عن استخدام الخدمات "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__subscription_state__6_churn
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state__6_churn
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state_from__6_churn
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__subscription_state__6_churn
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__subscription_state__6_churn
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__subscription_state__6_churn
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Churned"
msgstr "توقف عن استخدام الخدمات "

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Click here to add some products or services"
msgstr "اضغط هنا لإضافة بعض المنتجات أو الخدمات "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__user_closable
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__user_closable
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_search
msgid "Closable"
msgstr "قابل للإغلاق "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_so_portal_template
msgid "Close"
msgstr "إغلاق"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_close_reason_wizard_action
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__close_reason_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__close_reason_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_close_reason_wizard__close_reason_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__close_reason_id
msgid "Close Reason"
msgstr "سبب الإغلاق"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_close_reason_action
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_close_reason_action
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_tree
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_wizard_view_form
msgid "Close Reasons"
msgstr "أسباب الإغلاق"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "Close Subscription"
msgstr "إغلاق الاشتراك "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__user_closable_options
msgid "Closeable Plan Options"
msgstr "خيارات الخطة القابلة للإغلاق "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid "Closed"
msgstr "مغلق"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
msgid "Closed subscriptions"
msgstr "الاشتراكات المغلقة"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid "Closing text: %s"
msgstr "نص الإغلاق: %s "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__commercial_partner_id
msgid "Commercial Entity"
msgstr "الكيان التجاري"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__company_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__company_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__company_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__company_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__company_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__company_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
msgid "Company"
msgstr "الشركة "

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: sale_subscription
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_config
msgid "Configuration"
msgstr "التهيئة "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_res_config_settings__invoice_consolidation
#: model_terms:ir.ui.view,arch_db:sale_subscription.res_config_settings_view_form
msgid ""
"Consolidate all of a customer's subscriptions that are due to be billed on "
"the same day onto a single invoice."
msgstr ""
"قم بتجميع كافة اشتراكات العميل التي من المقرر أن تتم فوترتها في نفس اليوم في"
" فاتورة واحدة. "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_res_config_settings__invoice_consolidation
msgid "Consolidate subscriptions billing"
msgstr "تجميع فوترة الاشتراكات "

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_res_partner
msgid "Contact"
msgstr "جهة الاتصال"

#. module: sale_subscription
#: model:sale.order.close.reason,retention_button_text:sale_subscription.close_reason_1
msgid "Contact Marc"
msgstr "تواصل مع مارك "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_template__duration_unit
msgid "Contract duration"
msgstr "مدة العقد "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__payment_exception
msgid "Contract in exception"
msgstr "العقد المستثنى "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__event_type__15_contraction
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__event_type__15_contraction
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
msgid "Contraction"
msgstr "التقلص "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Contracts whose payment has failed"
msgstr "العقود التي قد فشلت عملية الدفع فيها "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__country_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
msgid "Country"
msgstr "الدولة"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Create Alternative"
msgstr "إنشاء بديل "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Create Invoice"
msgstr "إنشاء فاتورة"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.mail_activity_plan_action_subscription
msgid "Create a Subscription Activity Plan"
msgstr "إنشاء خطة نشاط للاشتراك "

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_plan_action
msgid "Create a new plan"
msgstr "إنشاء خطوة جديدة "

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.product_action_subscription
msgid "Create a new product"
msgstr "إنشاء منتج جديد"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_filtered
msgid "Create a new subscription"
msgstr "إنشاء اشتراك جديد"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_alert_action
msgid "Create a new subscription alert"
msgstr "إنشاء تنبيه عرض سعر جديد"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_template_action
msgid "Create a new template of subscription"
msgstr "إنشاء قالب اشتراك جديد"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_quotes
msgid "Create a subscription quotation"
msgstr "إنشاء عرض سعر للاشتراك "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__action__next_activity
msgid "Create next activity"
msgstr "إنشاء نشاط تالي"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_quotes
msgid ""
"Create subscriptions to manage recurring invoicing and payments from your "
"customers."
msgstr ""
"أنشئ الاشتراكات حتى تتمكن من إدارة الفواتير المتكررة والمدفوعات من عملائك. "

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Create your first subscription product here"
msgstr "أنشئ أول منتج اشتراك لك هنا "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__create_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__create_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__create_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__create_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_close_reason_wizard__create_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__create_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__create_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__create_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__create_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__create_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_close_reason_wizard__create_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__create_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__cron_nextcall
msgid "Cron Nextcall"
msgstr "تاريخ التشغيل التالي لـ Cron "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__currency_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__currency_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__currency_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__currency_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__currency_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__currency_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_tree
msgid "Currency"
msgstr "العملة"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_product_product__product_subscription_pricing_ids
#: model:ir.model.fields,field_description:sale_subscription.field_product_template__product_subscription_pricing_ids
msgid "Custom Subscription Pricings"
msgstr "أسعار الاشتراكات المخصصة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__partner_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__partner_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Customer"
msgstr "العميل"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__commercial_partner_id
msgid "Customer Company"
msgstr "شركة العميل "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__country_id
msgid "Customer Country"
msgstr "بلد العميل"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__commercial_partner_id
msgid "Customer Entity"
msgstr "الكيان التجاري للعميل"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__industry_id
msgid "Customer Industry"
msgstr "مجال عمل العميل "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__client_order_ref
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__client_order_ref
msgid "Customer Reference"
msgstr "رقم العميل المرجعي "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__state_id
msgid "Customer State"
msgstr "حالة العميل "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__partner_zip
msgid "Customer ZIP"
msgstr "الرمز البريدي للعميل "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__user_closable
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_plan__user_closable
msgid "Customer can close their subscriptions."
msgstr "بإمكان العميل إغلاق اشتراكه. "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__user_extend
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_plan__user_extend
msgid "Customer can create a renewal quotation for their subscription."
msgstr "بوسع العميل إنشاء عرض سعر لتجديد اشتراكه. "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__customer_ids
#: model:ir.ui.menu,name:sale_subscription.menu_orders_customers
msgid "Customers"
msgstr "العملاء"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Date:"
msgstr "التاريخ:"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "Days"
msgstr "الأيام"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Delay After Trigger"
msgstr "المهلة بعد التشغيل "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__trg_date_range
msgid ""
"Delay after the trigger date. You can put a negative number if you need a "
"delay before the trigger date, like sending a reminder 15 minutes before a "
"meeting."
msgstr ""
"التأخير بعد تاريخ التشغيل. يمكنك وضع عدد سالب إذا أردت أن يكون التأخير قبل "
"تاريخ التشغيل، كإرسال تذكير قبل 15 دقيقة من بدء الاجتماع. "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trg_date_range
msgid "Delay after trigger date"
msgstr "المهلة بعد تاريخ التشغيل "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trg_date_range_type
msgid "Delay type"
msgstr "نوع المهلة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__description
msgid "Description"
msgstr "الوصف"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Destination"
msgstr "الوجهة"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "Details"
msgstr "التفاصيل"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "Discard, I want to stay"
msgstr "إهمال، أود البقاء "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__discount
msgid "Discount %"
msgstr "الخصم %"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__discount_amount
msgid "Discount Amount"
msgstr "مبلغ الخصم "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__display_late
msgid "Display Late"
msgstr "عرض المتأخرة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__display_name
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__display_name
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__display_name
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__display_name
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__display_name
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_close_reason_wizard__display_name
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__display_name
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__display_name
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_product_product__display_subscription_pricing
#: model:ir.model.fields,field_description:sale_subscription.field_product_template__display_subscription_pricing
msgid "Display Price"
msgstr "عرض السعر "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "Download"
msgstr "تنزيل "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__payment_transaction__renewal_state__draft
msgid "Draft"
msgstr "مسودة"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__activity_date_deadline_range
msgid "Due Date In"
msgstr "تاريخ الاستحقاق في"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__activity_date_deadline_range_type
msgid "Due type"
msgstr "نوع الاستحقاق"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_template__duration_unit
msgid "Duration Unit"
msgstr "وحدة قياس المدة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__template_id
msgid "Email Template"
msgstr "قالب البريد الإلكتروني"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_plan__invoice_mail_template_id
msgid ""
"Email template used to send invoicing email automatically.\n"
"Leave it empty if you don't want to send email automatically."
msgstr ""
"قالب البريد الإلكتروني المستخدَم لإرسال الفواتير عبر البريد الإلكتروني تلقائياً.\n"
"اتركه فارغاً إذا كنت لا ترغب في إرسال رسائل البريد الإلكتروني تلقائياً. "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__empty_retention_message
msgid "Empty Retention Message"
msgstr "رسالة احتفاظ فارغة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_template__duration_value
msgid "End After"
msgstr "تنتهي بعد"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__end_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__subscription_end_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__end_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__end_date
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "End Date"
msgstr "تاريخ الانتهاء"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "End Date:"
msgstr "تاريخ الانتهاء "

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_end_of_contract
msgid "End of contract"
msgstr "نهاية العقد "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_plan__user_closable_options__end_of_period
msgid "End of period"
msgstr "نهاية الفترة "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Error during renewal of contract %(order_ids)s %(order_refs)s "
"%(payment_state)s"
msgstr ""
"حدث خطأ أثناء تجديد العقد %(order_ids)s %(order_refs)s %(payment_state)s "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Error during renewal of contract %s (Payment not recorded)"
msgstr "خطأ أثناء تجديد العقد %s (لم يتم تسجيل الدفع) "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__event_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__event_date
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
msgid "Event Date"
msgstr "تاريخ الفعالية "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
msgid "Event Type"
msgstr "نوع الفعالية"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__event_type__1_expansion
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__event_type__1_expansion
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
msgid "Expansion"
msgstr "التوسع "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Expiration Date:"
msgstr "تاريخ انتهاء الصلاحية "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Failed Payments"
msgstr "المدفوعات غير الناجحة "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__on_change_field_ids
msgid "Fields that trigger the onchange."
msgstr "الحقول التي تقوم بتشغيل onchange. "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
msgid "First Contract"
msgstr "العقد الأول "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__first_contract_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__first_contract_date
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "First Contract Date"
msgstr "تاريخ أول عقد"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__origin_order_id
msgid "First Order"
msgstr "الطلب الأول "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__origin_order_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__origin_order_id
msgid "First contract"
msgstr "العقد الأول "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__first_contract_date
msgid "First contract date"
msgstr "تاريخ أول عقد "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_follower_ids
msgid "Followers"
msgstr "المتابعين"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء) "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid "Future"
msgstr "المستقبل "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Future Activities"
msgstr "الأنشطة المستقبلية"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "إنشاء رابط دفع المبيعات "

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Go ahead and create a new product"
msgstr "هيا، أنشئ منتجًا جديدًا"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Go ahead and create a new subscription"
msgstr "هيا، أنشئ اشتراكاً جديداً "

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Go back to the subscription view"
msgstr "العودة إلى نافذة عرض الاشتراك "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__health__done
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__health__done
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__health__done
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__health__done
msgid "Good"
msgstr "جيد"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Good Health"
msgstr "صحة جيدة"

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_pending
msgid "Great, there are no subscriptions to renew!"
msgstr "رائع! لا توجد أي اشتراكات لتجديدها. "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__weight
msgid "Gross Weight"
msgstr "الوزن الإجمالي"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Group By"
msgstr "تجميع حسب"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid "Happy face"
msgstr "وجه سعيد"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__model_is_mail_thread
msgid "Has Mail Thread"
msgstr "يحتوي على محادثة البريد الإلكتروني "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__has_message
msgid "Has Message"
msgstr "يحتوي على رسالة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__has_recurring_line
msgid "Has Recurring Line"
msgstr "يحتوي على بند متكرر "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__health
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__health
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__health
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__health
msgid "Health"
msgstr "الصحة "

#. module: sale_subscription
#: model:ir.module.category,description:sale_subscription.module_category_subscription_management
msgid "Helps you handle subscriptions and recurring invoicing."
msgstr "يساعدك على التعامل مع الاشتراكات والفواتير المتكررة."

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_pending
msgid "Here you will find subscriptions overdue for renewal."
msgstr "ستجد هنا الاشتراكات المتأخر تجديدها. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "History"
msgstr "السجل"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__history_count
msgid "History Count"
msgstr "عدد السجلات "

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_4
msgid "I don't use it"
msgstr "لا أستخدمه"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_close_reason_wizard__id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__id
msgid "ID"
msgstr "المُعرف"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__message_needaction
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة عليك رؤيتها. "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__message_has_error
#: model:ir.model.fields,help:sale_subscription.field_sale_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__payment_token_id
msgid "If not set, the automatic payment will fail."
msgstr "إذا لم يكن محدداً، ستفشل عملية الدفع التلقائي. "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__filter_domain
msgid ""
"If present, this condition must be satisfied before executing the automation"
" rule."
msgstr "إذا كان موجودًا، يتوجب استيفاء هذا الشرط قبل تنفيذ قاعدة الأتمتة. "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__filter_pre_domain
msgid ""
"If present, this condition must be satisfied before the update of the "
"record. Not checked on record creation."
msgstr ""
"في حال وجوده، يجب استيفاء هذا الشرط قبل تحديث السجل. لا يتم التحقق منه عند "
"إنشاء السجل. "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__end_date
#: model:ir.model.fields,help:sale_subscription.field_sale_order_line__subscription_end_date
msgid ""
"If set in advance, the subscription will be set to renew 1 month before the "
"date and will be closed on the date set in this field."
msgstr ""
"إذا تم تعيين هذا الحقل مسبقاً، سيتم تعليم الاشتراك بانتظار التجديد قبل شهر "
"واحد من التاريخ وسيتم إغلاقه في التاريخ المحدد في هذا الحقل. "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_product_product__recurring_invoice
#: model:ir.model.fields,help:sale_subscription.field_product_template__recurring_invoice
#: model:ir.model.fields,help:sale_subscription.field_sale_order_line__recurring_invoice
#: model:ir.model.fields,help:sale_subscription.field_sale_order_template_line__recurring_invoice
#: model:ir.model.fields,help:sale_subscription.field_sale_order_template_option__recurring_invoice
msgid ""
"If set, confirming a sale order with this product will create a subscription"
msgstr "إذا كان معينًا، سيتم إنشاء اشتراك عند تأكيد أمر بيع لهذا المنتج"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_line__pricelist_id
msgid "If you change the pricelist, only newly added lines will be affected."
msgstr ""
"إذا قمت بتغيير قائمة الأسعار، وحدها البنود المضافة حديثاً سوف تتأثر بذلك. "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "If you wish to reopen it, the"
msgstr "إذا أردت إعادة فتحه، يمكنك"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid ""
"If you wish to reopen it, you can pay your invoice for the current invoicing"
" period."
msgstr ""
"إذا أردت إعادة فتحه، يمكنك سداد قيمة فاتورة فترة تحرير الفواتير الحالية."

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__subscription_state__3_progress
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state__3_progress
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state_from__3_progress
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__subscription_state__3_progress
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__subscription_state__3_progress
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__subscription_state__3_progress
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "In Progress"
msgstr "قيد التنفيذ"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__industry_id
msgid "Industry"
msgstr "مجال العمل"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Initial"
msgstr "الحرف الأول من الاسم"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__internal_note
msgid "Internal Note"
msgstr "ملاحظة داخلية"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__internal_note_display
msgid "Internal Note Display"
msgstr "عرض الملاحظة الداخلية "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Internal notes"
msgstr "ملاحظات داخلية"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid "Invalid access token."
msgstr "رمز الوصول غير صالح. "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__invoice_mail_template_id
msgid "Invoice Email Template"
msgstr "قالب البريد الإلكتروني للفاتورة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__line_invoice_status
msgid "Invoice Status"
msgstr "حالة الفاتورة"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid "Invoice not found."
msgstr "لم يتم العثور على الفاتورة. "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__is_batch
msgid "Is Batch"
msgstr "مجموعة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__is_protected
msgid "Is Protected"
msgstr "محمي "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__is_renewing
msgid "Is Renewing"
msgstr "جارِ التجديد "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_base_automation__is_sale_order_alert
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__is_sale_order_alert
msgid "Is Sale Order Alert"
msgstr "تنبيه أمر البيع "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_template__is_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__is_subscription
msgid "Is Subscription"
msgstr "اشتراك "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__is_upselling
msgid "Is Upselling"
msgstr "الارتقاء بالصفقة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__is_invoice_cron
msgid "Is a Subscription invoiced in cron"
msgstr "الاشتراك مفوتر في cron "

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_pending
msgid ""
"It could be due to the fail of the automatic payment\n"
"            or because the new invoice has not yet been issued."
msgstr ""
"قد يكون بسبب فشل عملية الدفع التلقائي\n"
"            أو لأنه لم يتم إصدار فاتورة العميل الجديدة بعد. "

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_account_move
msgid "Journal Entry"
msgstr "قيد اليومية"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_account_move_line
msgid "Journal Item"
msgstr "عنصر اليومية"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__kpi_1month_mrr_delta
msgid "KPI 1 Month MRR Delta"
msgstr "المؤشر الرئيسي لمتوسط الإيرادات الشهرية المتكررة لشهر واحد"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__kpi_1month_mrr_percentage
msgid "KPI 1 Month MRR Percentage"
msgstr "المؤشر الرئيسي لنسبة الإيرادات الشهرية المتكررة لشهر واحد"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__kpi_3months_mrr_percentage
msgid "KPI 3 Months MRR Percentage"
msgstr "المؤشر الرئيسي لنسبة الإيرادات الشهرية المتكررة لثلاث شهور"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__kpi_3months_mrr_delta
msgid "KPI 3 months MRR Delta"
msgstr "المؤشر الرئيسي لمتوسط الإيرادات الشهرية المتكررة لثلاث شهور"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_template__is_unlimited
msgid "Last Forever"
msgstr "للأبد "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__last_invoiced_date
msgid "Last Invoiced Date"
msgstr "تاريخ آخر فاتورة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__last_reminder_date
msgid "Last Reminder Date"
msgstr "تاريخ آخر تذكير "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__last_run
msgid "Last Run"
msgstr "آخر تشغيل "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__write_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__write_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__write_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__write_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_close_reason_wizard__write_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__write_uid
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__write_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__write_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__write_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__write_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_close_reason_wizard__write_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__write_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__last_invoice_date
msgid "Last invoice date"
msgstr "تاريخ آخر فاتورة "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__last_reminder_date
msgid "Last time when we sent a payment reminder"
msgstr "آخر مرة أرسلنا فيها تذكيراً بالدفع "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Late Activities"
msgstr "الأنشطة المتأخرة"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid "Latest Rating: Dissatisfied"
msgstr "آخر تقييم: غير راضٍ "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid "Latest Rating: Okay"
msgstr "آخر تقييم: جيد "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid "Latest Rating: Satisfied"
msgstr "آخر تقييم: راضٍ "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__least_delay_msg
msgid "Least Delay Msg"
msgstr "رسالة أقل تأخير "

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Let's add a pricing with a recurrence"
msgstr "فلنقم بإضافة سعر مع تكرار "

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Let's add price for selected recurrence"
msgstr "فلنقم بإضافة سعر للتكرار المحدد "

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Let's choose the customer for your subscription"
msgstr "فلنقم باختيار العميل لاشتراكك "

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Let's go to the catalog to create our first subscription product"
msgstr "فلنذهب إلى الكتالوج لإنشاء أول منتجات الاشتراك"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__log_webhook_calls
msgid "Log Calls"
msgstr "تسجيل المكالمات "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__log_currency_id
msgid "Log Currency"
msgstr "عملة السجل "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Log a note..."
msgstr "تسجيل ملاحظة..."

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_tree
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_tree
msgid "MRR"
msgstr "الإيرادات الشهرية المتكررة "

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_order_log_analysis_action
msgid "MRR Analysis"
msgstr "تحليل الإيرادات الشهرية المتكررة "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "MRR Between"
msgstr "الإيرادات الشهرية المتكررة ما بين"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_order_log_growth_action
#: model:ir.ui.menu,name:sale_subscription.menu_sale_order_log_growth_report
msgid "MRR Breakdown"
msgstr "تحليل الإيرادات الشهرية المتكررة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__amount_signed
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_analysis_graph
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_graph
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_pivot
msgid "MRR Change"
msgstr "تغير الإيرادات الشهرية المتكررة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__mrr_change_normalized
msgid "MRR Change (normalized)"
msgstr "تغير الإيرادات الشهرية المتكررة (متطبع) "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__mrr_change_amount
msgid "MRR Change Amount"
msgstr "تغيير قيمة الإيرادات الشهرية المتكررة"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "MRR Change More"
msgstr "الإيرادات الشهرية المتكررة تغيير المزيد"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__mrr_change_period
msgid "MRR Change Period"
msgstr "فترة تغير الإيرادات الشهرية المتكررة"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__mrr_change_unit
msgid "MRR Change Unit"
msgstr "وحدة تغير الإيرادات الشهرية المتكررة"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__mrr_max
msgid "MRR Range Max"
msgstr "الحد الأقصى لنطاق الإيرادات الشهرية المتكررة"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__mrr_min
msgid "MRR Range Min"
msgstr "الحد الأدنى لنطاق الإيرادات الشهرية المتكررة"

#. module: sale_subscription
#: model:ir.ui.menu,name:sale_subscription.menu_sale_order_log_analysis_report
msgid "MRR Timeline"
msgstr "المخطط الزمني للإيرادات الشهرية المتكررة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__amount_signed
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_tree
msgid "MRR change"
msgstr "تغير الإيرادات الشهرية المكررة "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "MRR changes"
msgstr "تغييرات الإيرادات الشهرية المتكررة "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_log__recurring_monthly
msgid "MRR, after applying the changes of that particular event"
msgstr "الإيرادات المتكررة الشهرية، بعد تطبيق تغييرات تلك الفعالية المحددة "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_digest
msgid "MRR:"
msgstr "الإيرادات الشهرية المتكررة: "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_home_subscription
msgid "Manage your subscriptions"
msgstr "أدر اشتراكاتك "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid ""
"Managing payment methods requires to be logged in under the customer order."
msgstr "تتطلب إدارة طرق الدفع أن تقوم بتسجيل الدخول إلى طلب العميل. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Manual payment succeeded. Payment reference: %(ref)s. Amount: %(amount)s. "
"Contract set to: In Progress, Next Invoice: %(inv)s. Email sent to customer."
msgstr ""
"لقد تمت عملية الدفع اليدوي بنجاح. مرجع الدفع: %(ref)s. المبلغ: %(amount)s. "
"تم تعيين العقد إلى: قيد التنفيذ، الفاتورة التالية: %(inv)s. تم إرسال بريد "
"إلكتروني إلى العميل. "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__margin
msgid "Margin"
msgstr "الهامش"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__medium_id
msgid "Medium"
msgstr "متوسط "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__starred_user_ids
msgid "Members"
msgstr "الأعضاء"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__retention_message
msgid "Message"
msgstr "الرسالة"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Mix of negative recurring lines and non-recurring line. The contract should "
"be fixed manually"
msgstr ""
"مزيج بين البنود المتكررة السالبة والبنود غير المتكررة. يجب تصحيح الحقد "
"يدوياً "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__model_id
msgid "Model"
msgstr "النموذج "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__model_name
msgid "Model Name"
msgstr "اسم النموذج "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__model_id
msgid "Model on which the automation rule runs."
msgstr "النموذج الذي تعمل فيه قواعد الأتمتة. "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__trigger_condition__on_create_or_write
msgid "Modification"
msgstr "تعديل"

#. module: sale_subscription
#: model:sale.subscription.plan,name:sale_subscription.subscription_plan_month
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "Monthly"
msgstr "شهرياً"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__recurring_monthly
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__recurring_monthly
msgid "Monthly Recurring"
msgstr "الشهرية المتكررة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_account_move_line__subscription_mrr
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__recurring_monthly
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__recurring_monthly
msgid "Monthly Recurring Revenue"
msgstr "الإيرادات الشهرية المتكررة"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_template__duration_unit__month
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_plan__billing_period_unit__month
msgid "Months"
msgstr "شهور"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "My Subscriptions"
msgstr "اشتراكاتي"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__name
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_tree
msgid "Name"
msgstr "الاسم"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__health__normal
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__health__normal
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__health__normal
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__health__normal
msgid "Neutral"
msgstr "محايد"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid "Neutral face"
msgstr "وجه محايد"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__event_type__0_creation
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__event_type__0_creation
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
msgid "New"
msgstr "جديد"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__partner_id
msgid "New Customer"
msgstr "عميل جديد "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_change_customer_view_form
msgid "New Customer Information"
msgstr "معلومات العميل الجديد "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__partner_shipping_id
msgid "New Delivery Address"
msgstr "عنوان التوصيل الجديد "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_change_customer_wizard__partner_invoice_id
msgid "New Invoice Address"
msgstr "عنوان الفاتورة الجديد "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__recurring_monthly
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_tree
msgid "New MRR"
msgstr "الإيرادات الشهرية المتكررة الجديدة"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid "Newest"
msgstr "الأحدث"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid "Next Billing Date"
msgstr "تاريخ الفوترة التالي "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__next_invoice_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__next_invoice_date
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_tree
msgid "Next Invoice"
msgstr "الفاتورة التالية "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__next_invoice_date
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Next Invoice Date"
msgstr "تاريخ الفاتورة القادمة "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_digest
msgid "Next Invoice:"
msgstr "الفاتورة التالية: "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Next invoice Date"
msgstr "تاريخ الفاتورة التالي "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "No Payment Method"
msgstr "لا توجد طريقة دفع "

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_upsell
msgid "No Upsell Found"
msgstr "لم يتم العثور على فرصة للارتقاء بالصفقة "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "No automatic mail"
msgstr "لا يوجد بريد إلكتروني تلقائي "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "No thanks, close my account"
msgstr "لا شكراً، قم بإغلاق حسابي "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "No valid Payment Method"
msgstr "لا توجد طريقة دفع صالحة "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_tree
msgid "Non Recurring"
msgstr "غير متكرر "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_order_product_search_inherit
msgid "Non-recurring"
msgstr "غير المتكررة "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_template_view_form
msgid "None"
msgstr "لا شيء"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sales_order_filter_subscription
msgid "Not Recurring"
msgstr "غير متكرر "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__activity_note
msgid "Note"
msgstr "الملاحظات"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__note_order
msgid "Note Order"
msgstr "ملاحظة الطلب "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Notes"
msgstr "الملاحظات"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_tree
msgid "Number"
msgstr "عدد "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "عدد الرسائل التي تتطلب اتخاذ إجراء"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: sale_subscription
#: model:product.template,name:sale_subscription.product_office_cleaning_product_template
#: model:sale.order.template.line,name:sale_subscription.montly_template_line
#: model:sale.order.template.line,name:sale_subscription.yearly_template_line
msgid "Office Cleaning Service (SUB)"
msgstr "خدمة تنظيف المكاتب (اشتراك) "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__on_change_field_ids
msgid "On Change Fields Trigger"
msgstr "التشغيل عند تغير قيمة الحقول "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order_template.py:0
msgid "Operation not supported"
msgstr "العملية غير مدعومة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__related_plan_id
msgid "Optional Plans"
msgstr "الخطط الاختيارية "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__order_reference
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Order"
msgstr "الطلب"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__date
msgid "Order Date"
msgstr "تاريخ الطلب "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__invoice_status
msgid "Order Invoice Status"
msgstr "حالة فاتورة الطلب "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__name
msgid "Order Reference"
msgstr "مرجع الطلب "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_tree
msgid "Order Status"
msgstr "حالة الطلب "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "Orders"
msgstr "الطلبات "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__origin_order_id
msgid "Origin Contract"
msgstr "العقد الأصلي"

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_5
msgid "Other"
msgstr "غير ذلك"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Over"
msgstr "انتهى "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "Overdue"
msgstr "متأخر"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__subscription_id
msgid "Parent Contract"
msgstr "العقد الأصلي "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__parent_line_id
msgid "Parent Line"
msgstr "البند الرئيسي "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_tree
msgid "Parent Subscription"
msgstr "الاشتراك الرئيسي "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/res_partner.py:0
msgid "Partner Subscription"
msgstr "اشتراك الشريك "

#. module: sale_subscription
#: model:ir.actions.server,name:sale_subscription.model_sale_order_subscription_pause_record
msgid "Pause Subscription"
msgstr "إيقاف الاشتراك مؤقتاً "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__subscription_state__4_paused
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state__4_paused
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state_from__4_paused
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__subscription_state__4_paused
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__subscription_state__4_paused
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__subscription_state__4_paused
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Paused"
msgstr "متوقف مؤقتاً "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Pay now"
msgstr "ادفع الآن "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid "Payment Failure"
msgstr "فشل الدفع "

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_payment_provider
msgid "Payment Provider"
msgstr "مزود الدفع "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__payment_term_id
msgid "Payment Terms"
msgstr "شروط السداد"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_payment_token
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__payment_token_id
msgid "Payment Token"
msgstr "رمز الدفع "

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_payment_transaction
msgid "Payment Transaction"
msgstr "معاملة الدفع "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Payment not recorded"
msgstr "الدفع غير مسجل "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Payment recorded: %s"
msgstr "تم تسجيل الدفع: %s "

#. module: sale_subscription
#: model:mail.template,subject:sale_subscription.email_payment_reminder
msgid ""
"Payment reminder for subscription {{ object.client_order_ref or object.name "
"}}"
msgstr "تذكير بالدفع للاشتراك {{ object.client_order_ref or object.name }} "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__payment_transaction__renewal_state__pending
msgid "Pending"
msgstr "قيد الانتظار "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__pending_transaction
msgid "Pending Transaction"
msgstr "معاملة قيد الانتظار "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Pending transaction"
msgstr "معاملة قيد الانتظار "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__mrr_change_period
msgid "Period over which the KPI is calculated"
msgstr "المدة التي يتم حساب مؤشرات الأداء الرئيسية خلالها "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__plan_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__plan_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
msgid "Plan"
msgstr "الخطة "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Plan:"
msgstr "الخطة: "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Please add a recurring plan on the subscription or remove the recurring "
"product."
msgstr "يرجى إضافة خطة دورية في الاشتراك أو إزالة المنتج الدوري. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Please add a recurring product in the subscription or remove the recurring "
"plan."
msgstr "يرجى إضافة منتج دوري في الاشتراك أو إزالة الخطة الدورية. "

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/xml/payment_form_templates.xml:0
msgid "Please provide another payment method for these subscriptions first."
msgstr "يرجى أولاً تقديم طريقة دفع أخرى لهذه الاشتراكات. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Please remove the recurring plan on the subscription before sending the "
"email."
msgstr "يرجى إزالة الخطة الدورية من الاشتراك قبل إرسال البريد الإلكتروني. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Please set a recurring plan on the subscription before sending the email."
msgstr "يرجى إعداد خطة دوية في الاشتراك قبل إرسال البريد الإلكتروني. "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "Portal: retention step"
msgstr "البوابة: خطوة الاحتفاظ "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sale_order_line_list
msgid "Price"
msgstr "السعر"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_product_pricelist
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__pricelist_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__pricelist_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__pricelist_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__pricelist_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
msgid "Pricelist"
msgstr "قائمه الأسعار"

#. module: sale_subscription
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_pricelist
msgid "Pricelists"
msgstr "قوائم الأسعار"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "Pricing"
msgstr "الأسعار "

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_subscription_pricing
msgid "Pricing rule of subscription products"
msgstr "قاعدة التسعير لمنتجات الاشتراك "

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_product_template
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__product_tmpl_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sale_order_line_list
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Product"
msgstr "المنتج"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__categ_id
msgid "Product Category"
msgstr "فئة المنتج"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__product_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
msgid "Product Variant"
msgstr "متغير المنتج "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__product_variant_ids
msgid "Product Variants"
msgstr "متغيرات المنتج "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sale_order_line_list
msgid "Product Varient"
msgstr "متغير المنتج "

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.product_action_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__product_template_id
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_product
#: model:ir.ui.menu,name:sale_subscription.product_menu_catalog
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Products"
msgstr "المنتجات"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__qty_delivered
msgid "Qty Delivered"
msgstr "الكمية التي قد تم توصيلها "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__qty_invoiced
msgid "Qty Invoiced"
msgstr "الكمية المفوترة"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__product_uom_qty
msgid "Qty Ordered"
msgstr "الكمية المطلوبة"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__qty_to_deliver
msgid "Qty To Deliver"
msgstr "الكمية بانتظار توصيلها "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__qty_to_invoice
msgid "Qty To Invoice"
msgstr "الكمية المطلوب فوترتها"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_analysis_view_graph
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_analysis_view_pivot
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_analysis_view_tree
msgid "Quantity"
msgstr "الكمية"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__subscription_state__1_draft
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__order_state__draft
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state__1_draft
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state_from__1_draft
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__subscription_state__1_draft
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__state__draft
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__subscription_state__1_draft
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__subscription_state__1_draft
msgid "Quotation"
msgstr "عرض سعر"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__order_state__sent
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__state__sent
msgid "Quotation Sent"
msgstr "تم إرسال عرض السعر"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_template
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_tree
msgid "Quotation Template"
msgstr "قالب عرض السعر"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr "بند قالب عرض السعر"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_template_option
msgid "Quotation Template Option"
msgstr "خيار قالب عرض السعر"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_digest
msgid "Quotation Template:"
msgstr "قالب عرض السعر: "

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_template_action
#: model:ir.ui.menu,name:sale_subscription.menu_template_of_subscription
msgid "Quotation Templates"
msgstr "قوالب عرض السعر"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_action_quotes
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_quotes
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Quotations"
msgstr "عروض الأسعار"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_avg_text
msgid "Rating Avg Text"
msgstr "متوسط نص التقييم "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "آخر ملاحظات التقييم"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_last_image
msgid "Rating Last Image"
msgstr "آخر صورة للتقييم"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_last_value
msgid "Rating Last Value"
msgstr "آخر قيمة للتقييم"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__rating_operator
msgid "Rating Operator"
msgstr "تقييم موظف الدعم"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__rating_percentage
msgid "Rating Percentage"
msgstr "نسبة التقييم"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_percentage_satisfaction
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Rating Satisfaction"
msgstr "رضا التقييم "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__rating_percentage
msgid ""
"Rating Satisfaction is the ratio of positive rating to total number of "
"rating."
msgstr "تقييم الرضا هو نسبة التقييمات الإيجابية إلى إجمالي عدد التقييمات. "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_last_text
msgid "Rating Text"
msgstr "نص التقييم "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_count
msgid "Rating count"
msgstr "عدد التقييمات"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__rating_ids
msgid "Ratings"
msgstr "التقييمات "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__name
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_wizard_view_form
msgid "Reason"
msgstr "السبب"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__record_getter
msgid "Record Getter"
msgstr "أداة جلب السجلات "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__is_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_tree
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_order_product_search_inherit
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sales_order_filter_subscription
msgid "Recurring"
msgstr "متكررة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__recurring_details
msgid "Recurring Details"
msgstr "التفاصيل المتكررة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__plan_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__subscription_plan_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__plan_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_template__plan_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__plan_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Recurring Plan"
msgstr "خطة متكررة "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_portal_content_inherit
msgid "Recurring Plan:"
msgstr "الخطة المتكررة: "

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_plan_action
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_plans
msgid "Recurring Plans"
msgstr "الخطط المتكررة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_pricing__price
msgid "Recurring Price"
msgstr "السعر المتكرر"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:sale_subscription.product_template_view_form_recurring
msgid "Recurring Prices"
msgstr "الأسعار المتكررة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_product_pricelist__product_subscription_pricing_ids
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__product_subscription_pricing_ids
msgid "Recurring Pricing"
msgstr "الأسعار المتكررة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__recurring_total
msgid "Recurring Revenue"
msgstr "الإيرادات المتكررة "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
#: model:ir.model.constraint,message:sale_subscription.constraint_sale_subscription_plan_check_for_valid_billing_period_value
msgid ""
"Recurring period must be a positive number. Please ensure the input is a "
"valid positive numeric value."
msgstr ""
"يجب أن تكون الفترة المتكررة رقماً موجباً. يرجى التأكد من أن القيمة المدخلة "
"هي قيمة عددية موجبة صالحة. "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Reference:"
msgstr "المرجع: "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__payment_transaction__renewal_state__cancel
msgid "Refused"
msgstr "تم الرفض "

#. module: sale_subscription
#: model:product.template,name:sale_subscription.product_fee_product_template
#: model:sale.order.template.line,name:sale_subscription.montly_template_line2
msgid "Registration fee"
msgstr "رسوم التسجيل "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__user_extend
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__user_extend
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "Renew"
msgstr "تجديد "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__renewal_count
msgid "Renewal Count"
msgstr "عدد التجديدات "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__subscription_state__2_renewal
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state__2_renewal
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state_from__2_renewal
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__subscription_state__2_renewal
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__subscription_state__2_renewal
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__subscription_state__2_renewal
msgid "Renewal Quotation"
msgstr "عرض سعر التجديد "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Renewal Quotations"
msgstr "عروض أسعار التجديدات "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Renewal Quote"
msgstr "عرض سعر التجديد "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_payment_transaction__renewal_state
msgid "Renewal State"
msgstr "حالة التجديد "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__subscription_state__5_renewed
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state__5_renewed
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state_from__5_renewed
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__subscription_state__5_renewed
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__subscription_state__5_renewed
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__subscription_state__5_renewed
msgid "Renewed"
msgstr "تم التجديد "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Reopen"
msgstr "إعادة فتح"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Reopen your subscription"
msgstr "إعادة فتح الاشتراك "

#. module: sale_subscription
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_report
msgid "Reporting"
msgstr "إعداد التقارير "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid ""
"Request a online payment from the customer to confirm the order. For a "
"subscription, a payment will be required also before each renewal"
msgstr ""
"طلب من العميل الدفع عبر الإنترنت لتأكيد الطلب. بالنسبة للاشتراك، سيكون الدفع"
" مطلوباً أيضاً قبل كل تجديد "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__activity_user_id
msgid "Responsible"
msgstr "المسؤول "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Resume"
msgstr "المتابعة "

#. module: sale_subscription
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_report_cohort
msgid "Retention"
msgstr "الاحتفاظ "

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_report_cohort_action
msgid "Retention Analysis"
msgstr "تحليل الاستبقاء"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل النصية القصيرة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__sms_template_id
msgid "SMS Template"
msgstr "قالب الرسائل النصية القصيرة "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid "Sad face"
msgstr "وجه حزين"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_option
msgid "Sale Options"
msgstr "خيارات البيع"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__order_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__order_id
msgid "Sale Order"
msgstr "أمر البيع"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_alert
msgid "Sale Order Alert"
msgstr "تنبيه أمر البيع "

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.action_sale_order_lines
msgid "Sale Order Lines"
msgstr "بنود أمر البيع "

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_log
msgid "Sale Order Log"
msgstr "سجل أمر البيع "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_analysis_graph
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_graph
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_pivot
msgid "Sale Order Log Analysis"
msgstr "تحليل سجل أمر البيع "

#. module: sale_subscription
#: model:ir.actions.server,name:sale_subscription.ir_cron_sale_subscription_update_kpi_ir_actions_server
msgid "Sale Subscription: Update KPI"
msgstr "اشتراك البيع: تحديث المؤشر الرئيسي"

#. module: sale_subscription
#: model:ir.actions.server,name:sale_subscription.account_analytic_cron_for_invoice_ir_actions_server
msgid "Sale Subscription: generate recurring invoices and payments"
msgstr "اشتراك البيع: إنشاء فواتير ومدفوعات متكررة"

#. module: sale_subscription
#: model:ir.actions.server,name:sale_subscription.send_payment_reminder_ir_actions_server
msgid "Sale Subscription: send reminder for subscriptions with no token"
msgstr "اشتراك البيع: إرسال تذكير للاشتراكات التي لا تحتوي على رمز "

#. module: sale_subscription
#: model:ir.actions.server,name:sale_subscription.account_analytic_cron_ir_actions_server
msgid "Sale Subscription: subscriptions expiration"
msgstr "اشتراك البيع: انتهاء صلاحية الاشتراكات"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
msgid "Sale order"
msgstr "أمر البيع "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order_log.py:0
msgid "Sale order log: %s"
msgstr "سجل أوامر البيع: %s "

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "فاتورة الدفعة المقدمة للمبيعات"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_report
msgid "Sales Analysis Report"
msgstr "تقرير المبيعات التحليلي"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Sales History"
msgstr "سجل المبيعات "

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_log_report
msgid "Sales Log Analysis Report"
msgstr "تقرير تحليل سجل المبيعات "

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__order_state__sale
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__state__sale
msgid "Sales Order"
msgstr "أمر البيع"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_line
msgid "Sales Order Line"
msgstr "بند أمر المبيعات"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__team_ids
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__team_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__team_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__team_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Sales Team"
msgstr "فريق المبيعات"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__activity_user__channel_leader
msgid "Sales Team Leader"
msgstr "قائد فريق المبيعات "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__user_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__user_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__user_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Salesperson"
msgstr "مندوب المبيعات "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
msgid "Search Sales Order"
msgstr "البحث في أمر البيع "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_template_view_search
msgid "Search Template"
msgstr "البحث عن قالب "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_pricing__product_variant_ids
msgid ""
"Select Variants of the Product for which this rule applies. Leave empty if "
"this rule applies for any variant of this template."
msgstr ""
"قم باختيار متغيرات المنتج التي تنطبق عليها هذه القاعدة. اتركه فارغاً إذا "
"كانت هذه القاعدة تنطبق على أي متغير لهذا القالب. "

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid "Select a recurring product"
msgstr "قم بتحديد منتج متكرر "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_pricing__product_template_id
msgid "Select products on which this pricing will be applied."
msgstr "قم بتحديد المنتجات التي سيتم تطبيق هذا التسعير عليها. "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "Selectable in Portal"
msgstr "يمكن تحديده في البوابة "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_plan_view_form
msgid "Self-Service"
msgstr "خدمة ذاتية"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__payment_transaction__subscription_action__automatic_send_mail
msgid "Send Mail (automatic payment)"
msgstr "إرسال البريد (الدفع التلقائي) "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__payment_transaction__subscription_action__manual_send_mail
msgid "Send Mail (manual payment)"
msgstr "إرسال البريد (الدفع اليدوي) "

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_act_window_sms_composer_multi
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_act_window_sms_composer_single
msgid "Send an SMS Text Message"
msgstr "إرسال رسالة نصية قصيرة "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__action__sms
msgid "Send an SMS Text Message to the customer"
msgstr "إرسال رسالة نصية قصيرة إلى العميل "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__action__mail_post
msgid "Send an email to the customer"
msgstr "إرسال بريد إلكتروني إلى العميل "

#. module: sale_subscription
#: model:mail.template,description:sale_subscription.email_payment_close
msgid ""
"Sent to customer to indicate that subscription is automatically terminated"
msgstr "يتم إرساله إلى العملاء للإشارة إلى أن الاشتراك قد تم إنهاؤه تلقائياً "

#. module: sale_subscription
#: model:mail.template,description:sale_subscription.email_payment_reminder
msgid ""
"Sent to customer when payment failed but subscription is not yet cancelled"
msgstr ""
"يتم إرساله إلى العملاء عند فشل عملية الدفع ولكن لم يتم إلغاء الاشتراك بعد "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__sequence
msgid "Sequence"
msgstr "تسلسل "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__action_id
msgid "Server Action"
msgstr "إجراء الخادم "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__action__set_health_value
msgid "Set Contract Health value"
msgstr "تعيين قيمة صحة العقد "

#. module: sale_subscription
#: model:mail.template,description:sale_subscription.mail_template_subscription_rating
msgid ""
"Set on subscription's stage (e.g. Closed, Upsell) to ask a rating to "
"customers"
msgstr ""
"قم بإعداده في صفحة الاشتراك (مثال: مغلق، الارتقاء بالصفقة) لطلب التقييمات من"
" العملاء "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.payment_form_inherit
msgid "Set payment method for subscription"
msgstr "قم بإعداد طريقة دفع للاشتراك "

#. module: sale_subscription
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_settings
msgid "Settings"
msgstr "الإعدادات"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__starred
msgid "Show Subscription on dashboard"
msgstr "إظهار الاشتراك في لوحة البيانات "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Show all records which has next action date is before today"
msgstr ""
"عرض كافة السجلات التي يسبق تاريخ الإجراء التالي فيها تاريخ اليوم الجاري "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__health
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__health
msgid "Show the health status"
msgstr "عرض حالة الاشتراك "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__trg_selection_field_id
msgid ""
"Some triggers need a reference to a selection field. This field is used to "
"store it."
msgstr "تحتاج بعض المشغلات إلى مرجع لحقل اختيار. يُستخدَم هذا الحقل لتخزينه. "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__trg_field_ref
msgid ""
"Some triggers need a reference to another field. This field is used to store"
" it."
msgstr "تحتاج بعض المشغلات إلى مرجع لحقل آخر. يُستخدَم هذا الحقل لتخزينه. "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__source_id
msgid "Source"
msgstr "المصدر"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__product_ids
msgid "Specific Products"
msgstr "منتجات معينة"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__activity_user_ids
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__activity_user__users
msgid "Specific Users"
msgstr "مستخدمون معينون"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__subscription_state
msgid "Stage"
msgstr "المرحلة"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__subscription_state_from
msgid "Stage from"
msgstr "المرحلة من "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Stage goes from"
msgstr "تبدأ المرحلة من "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__start_date
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__subscription_start_date
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sales_order_line_filter
msgid "Start Date"
msgstr "تاريخ البدء "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_digest
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Start Date:"
msgstr "تاريخ البدء: "

#. module: sale_subscription
#: model:mail.message.subtype,name:sale_subscription.subtype_state_change
msgid "State Change"
msgstr "تغير المرحلة "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__order_state
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__state
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__state
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid "Status"
msgstr "الحالة"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_change_customer_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_wizard_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_so_portal_template
msgid "Submit"
msgstr "إرسال"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale_subscription.field_account_move_line__subscription_id
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_template_view_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_view_cohort
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_view_cohort
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sale_order_line_list
msgid "Subscription"
msgstr "الاشتراك"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Subscription %(link)s has been cancelled. The parent order %(parent_link)s has been reopened.\n"
"                                                You should close %(parent_link)s if the customer churned, or renew it if the customer continue the service.\n"
"                                                Note: if you already created a new subscription instead of renewing it, please cancel your newly\n"
"                                                created subscription and renew %(parent_link)s instead"
msgstr ""
"لقد تم إلغاء الاشتراك %(link)s وإعادة فتح الأمر الرئيسي %(parent_link)s.\n"
"                                                عليك إغلاق %(parent_link)s في حال توقف العميل عن استخدام الخدمة، أو التجديد في حال استمرار العميل في استخدام الخدمة.\n"
"                                                ملاحظة: إذا قمت بإنشاء اشتراك جديد عوضاً عن تجديد اشتراكك، يرجى إغلاق اشتراكك الذي أنشأته للتو،\n"
"                                                وقم بتجديد %(parent_link)s عوضاً عن ذلك "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Subscription %s has been reopened. The end date has been removed"
msgstr "لقد تمت إعادة فتح الاشتراك %s، وإزالة تاريخ الانتهاء "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_payment_transaction__subscription_action
msgid "Subscription Action"
msgstr "إجراء الاشتراك "

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_subscription_report
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_analysis_view_graph
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_analysis_view_pivot
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_analysis_view_tree
msgid "Subscription Analysis"
msgstr "تحليل الاشتراك"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_subscription_change_customer_wizard
msgid "Subscription Change Customer Wizard"
msgstr "معالج تغيير عميل الاشتراك "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__subscription_child_ids
msgid "Subscription Child"
msgstr "الاشتراك الفرعي "

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_order_close_reason
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "Subscription Close Reason"
msgstr "سبب إغلاق الاشتراك"

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_subscription_close_reason_wizard
msgid "Subscription Close Reason Wizard"
msgstr "معالج سبب إغلاق الاشتراك"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__subscription_count
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__subscription_line_count
msgid "Subscription Count"
msgstr "عدد الاشتراكات"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_sale_order_line_list
msgid "Subscription Items"
msgstr "عناصر الاشتراك "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.view_sale_order_log_growth_tree
msgid "Subscription Log Analysis"
msgstr "تحليل سجل الاشتراك "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__order_log_ids
msgid "Subscription Logs"
msgstr "سجلات الاشتراك "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "Subscription Manager:"
msgstr "مدير الاشتراك "

#. module: sale_subscription
#: model:ir.model,name:sale_subscription.model_sale_subscription_plan
msgid "Subscription Plan"
msgstr "خطة الاشتراك "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__subscription_plan_ids
msgid "Subscription Plans"
msgstr "خطط الاشتراك "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_product_product__recurring_invoice
#: model:ir.model.fields,field_description:sale_subscription.field_product_template__recurring_invoice
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__recurring_invoice
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_template_line__recurring_invoice
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_template_option__recurring_invoice
msgid "Subscription Product"
msgstr "منتج الاشتراك"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Subscription Quotation"
msgstr "عرض سعر الاشتراك "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__activity_user__contract
msgid "Subscription Salesperson"
msgstr "مندوب مبيعات الاشتراك "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__subscription_state
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__subscription_state
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__subscription_state
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
msgid "Subscription State"
msgstr "حالة الاشتراك "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__subscription_state
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Subscription Status"
msgstr "حالة الاشتراك "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__template_id
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__template_id
msgid "Subscription Template"
msgstr "قالب الاشتراك"

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_2
msgid "Subscription does not meet my requirements"
msgstr "لا يستوفي الاشتراك متطلباتي "

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_1
msgid "Subscription is too expensive"
msgstr "الاشتراك غالٍ للغاية "

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_3
msgid "Subscription reached its end date"
msgstr "وصل الاشتراك لتاريخ نهايته"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_log__subscription_state
msgid "Subscription stage when the change occurred"
msgstr "مرحلة الاشتراك عند حدوث التغيير "

#. module: sale_subscription
#: model:mail.message.subtype,description:sale_subscription.subtype_state_change
msgid "Subscription state has changed"
msgstr "تغيرت حالة الاشتراك "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_portal_content_inherit
msgid "Subscription:"
msgstr "الاشتراك: "

#. module: sale_subscription
#: model:mail.template,name:sale_subscription.mail_template_subscription_alert
msgid "Subscription: Default Email Alert"
msgstr "الاشتراك: تنبيه البريد الإلكتروني الافتراضي "

#. module: sale_subscription
#: model:sms.template,name:sale_subscription.sms_template_data_default_alert
msgid "Subscription: Default SMS Alert"
msgstr "الاشتراك: تنبيه الرسائل النصية القصيرة الافتراضي "

#. module: sale_subscription
#: model:mail.template,name:sale_subscription.email_payment_close
msgid "Subscription: Payment Failure"
msgstr "الاشتراك: فشلت عملية الدفع "

#. module: sale_subscription
#: model:mail.template,name:sale_subscription.email_payment_reminder
msgid "Subscription: Payment Reminder"
msgstr "الاشتراك: تذكير الدفع "

#. module: sale_subscription
#: model:sms.template,name:sale_subscription.sms_template_data_payment_failure
msgid "Subscription: Payment failure"
msgstr "الاشتراك: فشلت عملية الدفع "

#. module: sale_subscription
#: model:sms.template,name:sale_subscription.sms_template_data_payment_reminder
msgid "Subscription: Payment reminder"
msgstr "الاشتراك: تذكير الدفع "

#. module: sale_subscription
#: model:mail.template,name:sale_subscription.mail_template_subscription_rating
msgid "Subscription: Rating Request"
msgstr "الاشتراك: طلب تقييم "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order_alert.py:0
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_action
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_action_filtered
#: model:ir.model.fields,field_description:sale_subscription.field_res_partner__subscription_count
#: model:ir.model.fields,field_description:sale_subscription.field_res_users__subscription_count
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__active_subs_count
#: model:ir.module.category,name:sale_subscription.module_category_subscription_management
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_action
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_analysis
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_root
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_home_menu_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_home_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
#: model_terms:ir.ui.view,arch_db:sale_subscription.product_template_search_view_inherit_sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.product_template_view_form_recurring
#: model_terms:ir.ui.view,arch_db:sale_subscription.res_partner_view_inherit_sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_activity
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Subscriptions"
msgstr "الاشتراكات"

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_report_analysis_action
msgid "Subscriptions Analysis"
msgstr "تحليل الاشتراكات"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Subscriptions that are not assigned to an account manager."
msgstr "الاشتراكات غير المسندة لمدير حسابات."

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_analysis_view_tree
msgid "Sum of Monthly Recurring"
msgstr "مجموع الشهرية المتكررة "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_analysis_view_tree
msgid "Sum of Yearly Recurring Revenue"
msgstr "إجمالي الإيرادات السنوية المتكررة "

#. module: sale_subscription
#: model:base.automation,name:sale_subscription.subscription_alert_percent_revenue_base_automation
msgid "Take action on less satisfied clients"
msgstr "اتخاذ إجراءات للعملاء غير الراضين"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__team_user_id
msgid "Team Leader"
msgstr "قائد الفريق"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "Tell us, why are you leaving?"
msgstr "ما هو سبب مغادرتك؟ "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_log_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Template"
msgstr "القالب "

#. module: sale_subscription
#: model:mail.template,description:sale_subscription.mail_template_subscription_alert
msgid ""
"Template to be used on customized alerts for subscriptions requiring "
"attention"
msgstr ""
"قالب لاستخدامه في التنبيهات المخصصة للاشتراكات التي تحتاج إلى اتخاذ إجراء "

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_template_action
msgid ""
"Templates are used to prefigure subscription that\n"
"                can be selected by the salespeople to quickly configure the\n"
"                terms and conditions of the subscription."
msgstr ""
"تُستخدم القوالب لتهيئة الاشتراكات مسبقاً\n"
"                حتى يتمكن مندوبو المبيعات من اختيارها\n"
"                لتهيئة شروط وأحكام الاشتراك سريعاً. "

#. module: sale_subscription
#: model:mail.template,subject:sale_subscription.email_payment_close
msgid ""
"Termination of subscription {{ object.client_order_ref or object.name }}"
msgstr "إنهاء الاشتراك {{ object.client_order_ref or object.name }} "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_account_move_line__subscription_mrr
msgid ""
"The MRR is computed by dividing the signed amount (in company currency) by the amount of time between the start and end dates converted in months.\n"
"This allows comparison of invoice lines created by subscriptions with different temporalities.\n"
"The computation assumes that 1 month is comprised of exactly 30 days, regardless  of the actual length of the month."
msgstr ""
"يتم احتساب الإيرادات الشهرية المتكررة عن طريق تقسيم المبلغ الموقّع (بعملة الشركة) على الوقت بين تواريخ البدء والانتهاء محولة بالأشهر. \n"
"يتيح ذلك مقارنة بنود الفواتير التي تنشئها الاشتراكات مع الوتيرات المختلفة. \n"
"تفترض أداة الاحتساب أن الشهر الواحد يتألف من 30 يوماً، بغض النظر عن عدد الأيام الفعلي لكل شهر. "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__trigger_field_ids
msgid ""
"The automation rule will be triggered if and only if one of these fields is "
"updated.If empty, all fields are watched."
msgstr ""
"سوف يتم تشغيل قاعدة الأتمتة فقط إذا كانت إحدى تلك الحقول محدثة. إذا كانت "
"فارغة، فستتم مراقبة كافة الحقول. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_pricing.py:0
msgid "The company of the plan is different from the company of the pricelist"
msgstr "تختلف الشركة الموجودة في الخطة عن الشركة الموجودة في قائمة الأسعار "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"The deferred settings are not properly set. Please complete them to generate"
" subscription deferred revenues"
msgstr ""
"لم يتم ضبط الإعدادات التأجيل بشكل صحيح. يرجى إكمالها لإنشاء إيرادات الاشتراك"
" المؤجلة "

#. module: sale_subscription
#: model:ir.model.constraint,message:sale_subscription.constraint_sale_order_template_check_duration_value
msgid "The duration can't be negative or 0."
msgstr "لا يمكن أن تكون المدة سالبة أو 0. "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__first_contract_date
msgid ""
"The first contract date is the start date of the first contract of the "
"sequence. It is common across a subscription and its renewals."
msgstr ""
"تاريخ العقد الأول هو تاريخ بداية أول عقد من التسلسل. هذا الأمر شائع بين "
"الاشتراك وتجديداته. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"The following recurring orders have draft invoices. Please Confirm them or "
"cancel them before creating new invoices. %s."
msgstr ""
"الطلبات المتكررة التالية بها فواتير بحالة المسودة. يرجى تأكيدها أو إلغاؤها "
"قبل إنشاء فواتير جديدة. %s. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/account_move.py:0
msgid ""
"The following refund %s has been made on this contract. Please check the "
"next invoice date if necessary."
msgstr ""
"لقد تم إرجاع الأموال %s لهذا العقد. يرجى التحقق من تاريخ الفاتورة التالية، "
"إذا لزم الأمر. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"The last invoice (%s) of this subscription is unpaid after the due date."
msgstr "آخر فاتورة (%s) لهذا الاشتراك تكون غير مدفوعة بعد تاريخ الاستحقاق. "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__pending_transaction
msgid "The last transaction of the order is currently pending"
msgstr "آخر معاملة للطلب هي قيد الانتظار حالياً "

#. module: sale_subscription
#: model:ir.model.constraint,message:sale_subscription.constraint_sale_order_check_start_date_lower_next_invoice_date
msgid "The next invoice date of a sale order should be after its start date."
msgstr "يجب أن يكون تاريخ الفاتورة التالية لأمر البيع بعد تاريخ البداية. "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__next_invoice_date
#: model:ir.model.fields,help:sale_subscription.field_sale_order_line__next_invoice_date
msgid ""
"The next invoice will be created on this date then the period will be "
"extended."
msgstr "سيتم إنشاء الفاتورة القادمة في هذا التاريخ ثم سيتم مد الفترة."

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
msgid ""
"The payment method you selected can only pay amounts up to %s. Please create"
" or select another one."
msgstr ""
"طريقة الدفع التي حددتها يمكن أن تدفع المبالغ حتى %s. يرجى إنشاء أو تحديد "
"طريقة دفع أخرى. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order_close_reason.py:0
msgid ""
"The reason %s is required by the Subscription application and cannot be "
"deleted."
msgstr "السبب %s مطلوب من قِبَل تطبيق الاشتراكات ولا يمكن حذفه. "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "The reason for closing a subscription"
msgstr "سبب إغلاق الاشتراك "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "The redirect link of the call to action"
msgstr "رابط إعادة التوجيه لدعوة اتخاذ الإجراء "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "The renewal %s has been cancelled."
msgstr "لقد تم إلغاء عملية التجديد %s. "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid ""
"The scheduled action for alerts has been deleted. Update the Subscriptions "
"module to re-create it."
msgstr ""
"لقد تم حذف الإجراء المجدول للتنبيهات. قم بتحديث تطبيق الاشتراكات لإعادة "
"إنشائه. "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__start_date
#: model:ir.model.fields,help:sale_subscription.field_sale_order_line__subscription_start_date
msgid "The start date indicate when the subscription periods begin."
msgstr "يشير تاريخ البدء إلى وقت بدء فترة الاشتراك. "

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_renew
msgid "The subscription was renewed with a new plan"
msgstr "لقد تم تجديد الاشتراك مع خطة جديدة "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "The text to display on the call to action"
msgstr "النص لعرضه في الدعوة إلى اتخاذ الإجراء "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "The upsell %s has been cancelled."
msgstr "لقد تم إلغاء عملية الارتقاء بالصفقة %s. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"The upsell %s has been cancelled. Please recheck the quantities as they may "
"have been affected by this cancellation."
msgstr ""
"تم إلغاء البيع الإضافي %s. يرجى إعادة التحقق من الكميات لأنها ربما تأثرت "
"بهذا الإلغاء. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "The upsell %s has been confirmed."
msgstr "لقد تم تأكيد عملية الارتقاء بالصفقة %s. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_pricing.py:0
msgid "There are multiple pricings for an unique product, plan and pricelist."
msgstr "توجد عدة أسعار للمنتج والخطة وقائمة الأسعار الفريدة. "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "There is a"
msgstr "هناك "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_kanban
msgid ""
"This bar allows to filter the opportunities based on scheduled activities."
msgstr "يتيح لك هذا الشريط تصفية الفرص حسب الأنشطة المُجدولة. "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__record_getter
msgid ""
"This code will be run to find on which record the automation rule should be "
"run."
msgstr ""
"سيتم تشغيل هذا الكود للعثور على أي السجلات يجب أن يتم تشغيل قاعدة الأتمتة "
"عليها. "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid ""
"This message will be displayed to convince the customer to stay (e.g., We "
"don't want you to leave, can we offer to schedule a meeting with your "
"account manager?)"
msgstr ""
"سيتم عرض هذه الرسالة لإقناع العميل بالبقاء (مثال: لا نريد أن نخسرك، هلّا "
"عرضنا عليك اجتماعاً مع مدير الحساب المسند إليك؟) "

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/xml/payment_form_templates.xml:0
msgid ""
"This payment method cannot be deleted, because it is currently linked to the\n"
"                following active subscriptions:"
msgstr ""
"لا يمكن حذف طريقة الدفع هذه لأنها مرتبطة حالياً\n"
"                بالاشتراكات النشطة التالية: "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "This subscription is renewed in %s with a change of plan."
msgstr "سيتم تجديد هذا الاشتراك في %s مع تغيير في الخطة. "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_digest
msgid "This subscription is the renewal of subscription"
msgstr "هذا الاشتراك هو تجديد للاشتراك "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_digest
msgid "This upsell order has been created from the subscription"
msgstr "لقد تم إنشاء أمر الارتقاء بالصفقة هذا من الاشتراك "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid ""
"This will trigger the action on all linked subsccriptions, regardless of "
"possible timed conditions. Are you sure you want to proceed?"
msgstr ""
"سيقوم ذلك بتشغيل الإجراء في كافة الاشتراكات المرتبطة، بغض النظر عن الشروط "
"المحتملة. هل أنت متأكد من أنك ترغب في الاستمرار؟ "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__trigger_condition__on_time
msgid "Timed Condition"
msgstr "شرط زمني"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__activity_summary
msgid "Title"
msgstr "العنوان"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "To Invoice"
msgstr "بانتظار الفوترة"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/controllers/portal.py:0
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_action_pending
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_pending
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "To Renew"
msgstr "للتجديد"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_report_search
msgid "To renew"
msgstr "بانتظار التجديد "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Today Activities"
msgstr "أنشطة اليوم "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__price_total
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid "Total"
msgstr "الإجمالي"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__non_recurring_total
msgid "Total Non Recurring Revenue"
msgstr "إجمالي الإيرادات غير المتكررة "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_portal_content_inherit
msgid "Total Quantity"
msgstr "إجمالي الكمية"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__recurring_total
msgid "Total Recurring"
msgstr "إجمالي التكرار "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__event_type__3_transfer
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__event_type__3_transfer
msgid "Transfer"
msgstr "تحويل "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trigger
msgid "Trigger"
msgstr "المشغّل "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trg_date_id
msgid "Trigger Date"
msgstr "تاريخ التشغيل  "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trg_selection_field_id
msgid "Trigger Field"
msgstr "تشغيل الحقل "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trg_field_ref_model_name
msgid "Trigger Field Model"
msgstr "تشغيل نموذج الحقل "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trigger_field_ids
msgid "Trigger Fields"
msgstr "تشغيل الحقول "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "Trigger Now"
msgstr "التشغيل الآن "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trigger_condition
msgid "Trigger On"
msgstr "التشغيل عند "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trg_field_ref
msgid "Trigger Reference"
msgstr "تشغيل المرجع "

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_alert_action
msgid ""
"Trigger alerts for salespersons or customers: churn, invoice not paid, "
"upsell, etc."
msgstr ""
"قم بتشغيل التنبيهات لمندوبي المبيعات أو العملاء: التوقف عن استخدام الخدمات، "
"عدم دفع الفواتير، الارتقاء بالصفقة، وما إلى ذلك. "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_close_reason__retention_message
msgid ""
"Try to prevent customers from leaving and closing their subscriptions, "
"thanks to a catchy message and a call to action."
msgstr ""
"حاول منع العملاء من المغادرة وإغلاق اشتراكاتهم، باستخدام رسالة تشد انتباههم "
"ودعوة إلى اتخاذ إجراء. "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log__event_type
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_log_report__event_type
msgid "Type of event"
msgstr "نوع الفعالية "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_view_search
msgid "Unassigned"
msgstr "غير مسند "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_plan__billing_period_unit
msgid "Unit"
msgstr "الوحدة"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__price_unit
msgid "Unit Price"
msgstr "سعر الوحدة"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__product_uom
msgid "Unit of Measure"
msgstr "وحدة القياس"

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_unknown
msgid "Unknown"
msgstr "غير معروف"

#. module: sale_subscription
#: model:sale.order.close.reason,name:sale_subscription.close_reason_unpaid_subscription
msgid "Unpaid subscription"
msgstr "الاشتراكات غير المدفوعة "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_subscription_plan__auto_close_limit
msgid ""
"Unpaid subscription after the due date majored by this number of days will be automatically closed by the subscriptions expiration scheduled action. \n"
"If the chosen payment method has failed to renew the subscription after this time, the subscription is automatically closed."
msgstr ""
"سيتم إغلاق الاشتراك غير المدفوع تلقائياً بعد تاريخ الاستحقاق بهذا القدر من الأيام، بواسطو إجراء انتهاء صلاحية الاشتراك المجدول. \n"
"في حال فشل طريقة الدفع المحددة في تجديد الاشتراك بعد هذا الوقت، يتم إغلاق الاشتراك تلقائياً. "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__untaxed_amount_invoiced
msgid "Untaxed Amount Invoiced"
msgstr "المبلغ المفوتر غير الشامل للضريبة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__untaxed_amount_to_invoice
msgid "Untaxed Amount To Invoice"
msgstr "المبلغ المُراد فوترته غير الشامل للضريبة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__price_subtotal
msgid "Untaxed Total"
msgstr "الإجمالي دون الضريبة "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_order_digest
msgid "Untaxed Total:"
msgstr "الإجمالي غير شامل الضريبة: "

#. module: sale_subscription
#: model:base.automation,name:sale_subscription.subscription_alert_bad_health_base_automation
msgid "Update health value according to MRR"
msgstr "تحديث قيمة الصحة وفقاً للإيرادات الشهرية المتكررة "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order__subscription_state__7_upsell
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state__7_upsell
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__subscription_state_from__7_upsell
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log__subscription_state__7_upsell
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_log_report__subscription_state__7_upsell
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_report__subscription_state__7_upsell
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Upsell"
msgstr "الارتقاء بالصفقة "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"Upsell %(order)s for customer %(customer)s for the period %(date_start)s to "
"%(date_end)s %(nl)s%(lines)s"
msgstr ""
"البيع الإضافي في %(order)s للعميل %(customer)s للفترة من %(date_start)s إلى "
"%(date_end)s %(nl)s%(lines)s "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__upsell_count
msgid "Upsell Count"
msgstr "عدد مرات الارتقاء بالصفقة "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "Upsell Quotations"
msgstr "عروض أسعار الارتقاء بالصفقة "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Upsell Quote"
msgstr "عرض سعر الارتقاء بالصفقة "

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.sale_subscription_action_upsell
msgid ""
"Upsell orders allow you to add or remove products to ongoing subscriptions.\n"
"                It can be created by clicking on the Upsell button from an active subscription."
msgstr ""
"يتيح لك أمر الارتقاء بالصفقة إضافة أو إزالة منتجات من وإلى الاشتراكات الجارية.\n"
"                يمكن إنشاؤه عن طريق الضغط على زر \"الارتقاء بالصفقة\" من اشتراك نشط. "

#. module: sale_subscription
#: model:ir.actions.act_window,name:sale_subscription.sale_subscription_action_upsell
#: model:ir.ui.menu,name:sale_subscription.menu_sale_subscription_upsell
msgid "Upsells"
msgstr "عمليات الارتقاء بالصفقة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__url
msgid "Url"
msgstr "رابط URL "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trg_date_calendar_id
msgid "Use Calendar"
msgstr "استخدام التقويم"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__trg_date_resource_field_id
msgid "Use employee work schedule"
msgstr "استخدام جدول عمل الموظف"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__trg_date_resource_field_id
msgid "Use the user's working schedule."
msgstr "استخدام جدول عمل المستخدم."

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_line__product_template_variant_value_ids
msgid "Variant Values"
msgstr "قيم المتغير "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_close_reason__visible_in_portal
msgid "Visible In Portal"
msgstr "مرئي في البوابة "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__volume
msgid "Volume"
msgstr "الحجم "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_order_view_form
msgid "Waiting customer payment"
msgstr "بانتظار دفع العميل "

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/tours/sale_subscription.js:0
msgid ""
"Want a recurring billing through subscription management? Get started by "
"clicking here"
msgstr ""
"هل تبحث عن نظام لإدارة وإصدار الفواتير المتكررة من خلال إدارة الاشتراك؟ ابدأ"
" بالضغط هنا "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__warehouse_id
msgid "Warehouse"
msgstr "المستودع "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/product.py:0
msgid "Warning"
msgstr "تحذير"

#. module: sale_subscription
#. odoo-javascript
#: code:addons/sale_subscription/static/src/js/payment_form.js:0
msgid "Warning!"
msgstr "تحذير!"

#. module: sale_subscription
#: model_terms:sale.order.close.reason,retention_message:sale_subscription.close_reason_1
msgid "We are sorry to hear that."
msgstr "يحزننا معرفة ذلك. "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "We are sorry to see you go."
msgstr "تحزننا مغادرتك. "

#. module: sale_subscription
#: model_terms:sale.order.close.reason,retention_message:sale_subscription.close_reason_1
msgid "We don't want you to leave us like this."
msgstr "لا نريدك أن تغادر هكذا. "

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order_alert__webhook_uuid
msgid "Webhook UUID"
msgstr "Webhook UUID"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_order__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع الإلكتروني "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع الإلكتروني "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_plan__billing_period_unit__week
msgid "Weeks"
msgstr "أسابيع"

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__trg_date_calendar_id
msgid ""
"When calculating a day-based timed condition, it is possibleto use a "
"calendar to compute the date based on working days."
msgstr ""
"عند حساب شروط زمنية تعتمد على الأيام، من الممكن استخدام التقويم لحساب "
"التاريخ المطلوب وفقًا لأيام العمل."

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__trg_date_id
msgid ""
"When should the condition be triggered.\n"
"                If present, will be checked by the scheduler. If empty, will be checked at creation and update."
msgstr ""
"متى يتم تفعيل الشرط.\n"
"                إذا كان موجوداً، سيتم التحقق منه من قِبَل المجدوِل. إذا كان فارغاً، سيتم التحقق منه عند الإنشاء والتحديث. "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order_alert__active
msgid "When unchecked, the rule is hidden and will not be executed."
msgstr "عند إلغاء التحديد، سوف يتم إخفاء هذه القاعدة ولن يتم تنفيذها. "

#. module: sale_subscription
#: model:ir.model.fields,help:sale_subscription.field_sale_order__starred
msgid "Whether this subscription should be displayed on the dashboard or not"
msgstr "ما إذا كان ينبغي عرض هذا الاشتراك في لوحة المعلومات أم لا"

#. module: sale_subscription
#: model:sale.subscription.plan,name:sale_subscription.subscription_plan_year
msgid "Yearly"
msgstr "سنويًا"

#. module: sale_subscription
#: model:ir.model.fields,field_description:sale_subscription.field_sale_subscription_report__recurring_yearly
msgid "Yearly Recurring"
msgstr "السنوية المتكررة "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_template__duration_unit__year
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_subscription_plan__billing_period_unit__year
msgid "Years"
msgstr "سنوات"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_portal_templates_warnings
msgid ""
"You are about to permanently close a subscription that is valid until\n"
"                    &amp;nbsp;"
msgstr ""
"أنت على وشك إغلاق اشتراك بشكل دائم، على الرغم من أنه سارٍ\n"
"                    حتى "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/product.py:0
msgid ""
"You can not change the recurring property of this product because it has "
"been sold already."
msgstr "لا يمكنك تغيير الخاصية المتكررة لهذا المنتج لأنه قد تم بيعه بالفعل. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/wizard/sale_subscription_close_reason_wizard.py:0
msgid ""
"You can not churn a contract that has not been invoiced. Please cancel the "
"contract instead."
msgstr ""
"لا يمكنك وضع عقد قد تمت فوترته مع قائمة العقود التي توقفت عن استخدام "
"الخدمات. يُرجى إلغاء العقد عوضاً عن ذلك. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"You can not delete a confirmed subscription. You must first close and cancel"
" it before you can delete it."
msgstr ""
"لا يمكنك حذف اشتراك مؤكد. عليك أولاً إغلاقه وإلغاؤه قبل أن تقوم بحذفه. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"You can not upsell or renew a subscription that has not been invoiced yet. "
"Please, update directly the %s contract or invoice it first."
msgstr ""
"لا يمكنك الارتقاء بصفقة اشتراك أو تجديده إذا لم تتم فوترته بعد. يرجى تحديث "
"عقد %s مباشرة أو فوترته أولاً. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/res_partner.py:0
msgid ""
"You can't archive the partner as it is used in the following recurring "
"orders: %s"
msgstr ""
"لا يمكنك أرشفة الشريك، حيث إنه يتم استخدامه في الطلبات المكررة التالية: %s "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "You cannot cancel a churned renewed subscription."
msgstr "لا يمكنك إلغاء اشتراك متوقف عن استخدام الخدمات تم تجديده. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "You cannot cancel a subscription that has been invoiced."
msgstr "لا يمكنك إلغاء اشتراك قد تمت فوترته. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/wizard/sale_subscription_change_customer_wizard.py:0
msgid "You cannot change the customer of non recurring sale order."
msgstr "لا يمكنك تغيير العميل في أمر بيع غير متكرر. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"You cannot create an upsell for this subscription because it :\n"
" - Has not started yet.\n"
" - Has no invoiced period in the future."
msgstr ""
"لا يمكنك إنشاء عملية ارتقاء بالصفقة لهذا الاشتراك لأنه: \n"
"- لم يبدأ بعد. \n"
"- لا توجد بها فترة مفوترة في المستقبل. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/wizard/payment_link_wizard.py:0
msgid "You cannot generate a payment link for a renewed subscription"
msgstr "لا يمكنك إنشاء رابط دفع لاشتراك تم تجديده بالفعل "

#. module: sale_subscription
#: model:ir.model.constraint,message:sale_subscription.constraint_sale_order_sale_subscription_state_coherence_2
msgid "You cannot have a draft SO be a confirmed subscription."
msgstr "لا يمكن أن تكون مسودة أمر البيع اشتراكاً مؤكداً. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "You cannot renew a contract that already has an active subscription. "
msgstr "لا يمكنك تجديد عقد به اشتراك نشط بالفعل. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "You cannot renew a subscription that has been renewed. "
msgstr "لا يمكنك تجديد اشتراك قد تم تجديده بالفعل. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "You cannot reopen a subscription that isn't closed."
msgstr "لا يمكنك إعادة فتح اشتراك غير مغلق. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"You cannot set to draft a cancelled quotation linked to invoiced "
"subscriptions. Please create a new quotation."
msgstr ""
"لا يمكنك تعيين عرض سعر ملغي مرتبط بالاشتراكات المفوترة كمسودة. يرجى إنشاء "
"عرض سعر جديد. "

#. module: sale_subscription
#: model:ir.model.constraint,message:sale_subscription.constraint_sale_order_sale_subscription_state_coherence
msgid ""
"You cannot set to draft a confirmed subscription. Please create a new "
"quotation"
msgstr "لا يمكنك تعيين اشتراك مؤكد كمسودة. يرجى إنشاء عرض سعر جديد. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid "You cannot upsell a subscription using a different currency."
msgstr "لا يمكنك الارتقاء بصفقة اشتراك باستخدام عملة مختلفة. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"You cannot upsell a subscription whose next invoice date is in the past.\n"
"Please, invoice directly the %s contract."
msgstr ""
"لا يمكنك الارتقاء بصفقة اشتراك تاريخ انتهاء فاتورته في الماضي. \n"
"يرجى فوترة عقد %s مباشرة. "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_order.py:0
msgid ""
"You cannot validate a renewal quotation starting before the next invoice "
"date of the parent contract. Please update the start date after the %s."
msgstr ""
"لا يمكنك تصديق عرض سعر للتجديد يبدأ قبل تاريخ الفاتورة القادمة للعقد الأصلي."
" يرجى تحديث تاريخ البدء بعد %s. "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.portal_my_subscriptions
msgid "You don't have any subscriptions yet."
msgstr "ليس لديك أي اشتراكات حتى الآن."

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "You must be logged in to manage your payment methods."
msgstr "يجب أن تقوم بتسجيل دخولك لإدارة طرق الدفع الخاصة بك. "

#. module: sale_subscription
#: model_terms:ir.actions.act_window,help:sale_subscription.product_action_subscription
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service."
msgstr ""
"يجب عليك تحديد منتج لكل شيئ تقوم ببيعه أو شرائه،\n"
"                سواءً كان منتجاً قابلاً للتخزين أو استهلاكي أو خدمة. "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Your Subscription"
msgstr "اشتراكك "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.payment_method_form
msgid "Your payment details will be saved for automatic renewals."
msgstr "سيتم حفظ تفاصيل الدفع للتجديدات التلقائية. "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Your subscription is about to end on the"
msgstr "اشتراكك على وشك الانتهاء بتاريخ "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Your subscription is closed."
msgstr "اشتراكك مغلق."

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "Your subscription is expired, will be closed soon."
msgstr "انتهت صلاحية اشتراكك وسيتم إغلاقه قريباً. "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_portal_templates_warnings
msgid "Your subscription will be closed on the"
msgstr "سيتم إغلاق اشتراكك بتاريخ "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "and"
msgstr "و "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "button link"
msgstr "رابط الزر "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "button text"
msgstr "نص الزر "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "e.g. Closed Subscription"
msgstr "مثال: اشتراك مغلق "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_alert_view_form
msgid "e.g. Discuss proposal"
msgstr "مثال: مناقشة المقترح "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "existing for this subscription, please confirm it or reject it."
msgstr "موجود لهذا الاشتراك، يرجى إما تأكيده أو رفضه. "

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__rating_operator__>
msgid "greater than"
msgstr "أكبر من"

#. module: sale_subscription
#: model:ir.model.fields.selection,name:sale_subscription.selection__sale_order_alert__rating_operator__<
msgid "less than"
msgstr "أقل من"

#. module: sale_subscription
#: model:sale.order.close.reason,retention_button_link:sale_subscription.close_reason_1
msgid "mailto:<EMAIL>?subject=Close contract: too expensive"
msgstr "mailto:<EMAIL>?subject=Close contract: غالٍ جداً "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.sale_subscription_close_reason_view_form
msgid "message"
msgstr "رسالة "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "missing payments (from"
msgstr "المدفوعات المفقودة (من"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_sidebar
msgid "on"
msgstr "في"

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
msgid "per %d months"
msgstr "كل %d أشهر "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
msgid "per %d weeks"
msgstr "كل %d أسابيع "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
msgid "per %d years"
msgstr "كل %d سنوات "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
msgid "per month"
msgstr "في الشهر "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
msgid "per week"
msgstr "في الأسبوع "

#. module: sale_subscription
#. odoo-python
#: code:addons/sale_subscription/models/sale_subscription_plan.py:0
msgid "per year"
msgstr "في السنة "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "quotation"
msgstr "عرض سعر"

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid ""
"quotation existing for this subscription, please confirm it or reject it."
msgstr "عرض سعر موجود لهذا الاشتراك، يرجى إما تأكيده أو رفضه. "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid ""
"quotation existing for this subscription, please confirm them or reject "
"them."
msgstr "عرض سعر موجود لهذا الاشتراك، يرجى إما تأكيده أو رفضه. "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_template
msgid "renewal"
msgstr "تجديد "

#. module: sale_subscription
#: model_terms:ir.ui.view,arch_db:sale_subscription.subscription_portal_content
msgid "to this day) will be automatically processed."
msgstr "حتى هذا اليوم) ستتم معالجتها تلقائياً. "

#. module: sale_subscription
#: model:sms.template,body:sale_subscription.sms_template_data_payment_failure
msgid ""
"{{ object.company_id.name }}: Our final attempt to process a payment for "
"your subscription failed. As your payment should have been made on {{ "
"object.next_invoice_date }}, your subscription has been terminated."
msgstr ""
"{{ object.company_id.name }}: لقد فشلت محاولتنا الأخيرة في معالجة الدفع "
"لاشتراكك. لقد تم إنهاء اشتراكك، حيث أنه كان يجب المباشرة بعملية الدفع بتاريخ"
" {{ object.next_invoice_date }}. "

#. module: sale_subscription
#: model:mail.template,subject:sale_subscription.mail_template_subscription_alert
msgid ""
"{{ object.company_id.name }}: Please check the subscription {{ object.name "
"}}"
msgstr ""
"{{ object.company_id.name }}: يرجى التحقق من الاشتراك {{ object.name }} "

#. module: sale_subscription
#: model:mail.template,subject:sale_subscription.mail_template_subscription_rating
msgid "{{ object.company_id.name }}: Service Rating Request"
msgstr "{{ object.company_id.name }}: طلب تقييم الخدمة "

#. module: sale_subscription
#: model:sms.template,body:sale_subscription.sms_template_data_payment_reminder
msgid ""
"{{ object.company_id.name }}: We were unable to process a payment for your "
"subscription. Your subscription {{ object.name }} is still valid but will be"
" suspended on {{ object.next_invoice_date }} unless the payment succeeds in "
"the meantime."
msgstr ""
"{{ object.company_id.name }}: لم نتمكن من معالجة الدفع لاشتراكك. اشتراكك {{ "
"object.name }} لا يزال صالحاً، ولكن سيتم تعليقه في {{ "
"object.next_invoice_date }} إلا إذا تمت معالجة الدفع بنجاح في الوقت الراهن. "

#. module: sale_subscription
#: model:sms.template,body:sale_subscription.sms_template_data_default_alert
msgid ""
"{{ object.company_id.name }}: Your subscription {{ object.name }} needs your"
" attention. If you have some concerns about it, please contact {{ "
"object.user_id.name }}, your contact person."
msgstr ""
"{{ object.company_id.name }}: اشتراكك {{ object.name }} يحتاج إلى اهتمام "
"منك. إذا كانت لديك أي استفسارات حوله، يرجى التواصل مع {{ object.user_id.name"
" }}، الوظف المسند إليك. "
