<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="base.user_demo" model="res.users">
            <field name="groups_id" eval="[
                (3, ref('hr_expense.group_hr_expense_team_approver')),
                (3, ref('hr_expense.group_hr_expense_user')),
                (3, ref('hr_expense.group_hr_expense_manager')),
            ]"/>
        </record>

        <record id="hr.employee_mit" model="hr.employee">
            <field name="private_email"><EMAIL></field>
            <field name="private_phone">(*************</field>
        </record>

        <record id="hr_expense_account_journal" model="account.journal">
            <field name="name">Expense</field>
            <field name="code">EXP</field>
            <field name="type">purchase</field>
            <!-- avoid being selected as default journal -->
            <field name="sequence">99</field>
            <field name="alias_name">purchase_expense</field>
        </record>

        <record id="hr.employee_fme" model="hr.employee">
            <field name="private_street"><PERSON><PERSON><PERSON>, 40</field>
            <field name="private_zip">1367</field>
            <field name="private_city">Grand-Rosière-Hottomont</field>
            <field name="private_country_id" ref="base.be"/>
        </record>

        <record id="hr.employee_al" model="hr.employee">
            <field name="private_street">Chaussée de Namur, 40</field>
            <field name="private_zip">1367</field>
            <field name="private_city">Grand-Rosière-Hottomont</field>
            <field name="private_country_id" ref="base.be"/>
        </record>

        <!-- ++++++++++++++ Expense sheet for Admin ++++++++++++++-->
        <record id="screen_expense" model="hr.expense">
            <field name="name">Screen</field>
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="analytic_distribution" eval="{ref('analytic.analytic_our_super_product'): 100}"/>
            <field name="product_id" ref="product_product_no_cost"/>
            <field eval="289.0" name="total_amount_currency"/>
            <field name="date" eval="time.strftime('%Y')+'-04-03'"/>
        </record>

        <record id="laptop_expense" model="hr.expense">
            <field name="name">Laptop</field>
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="analytic_distribution" eval="{ref('analytic.analytic_our_super_product'): 100}"/>
            <field name="product_id" ref="product_product_no_cost"/>
            <field eval="889.0" name="total_amount_currency"/>
            <field name="date" eval="time.strftime('%Y')+'-04-03'"/>
        </record>

        <record id="travel_admin_by_car_expense" model="hr.expense">
            <field name="name">Travel by car</field>
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="product_id" ref="expense_product_mileage"/>
            <field name="product_uom_id" ref="uom.product_uom_km"/>
            <field eval="108.84" name="quantity"/>
        </record>

        <record id="breakfast_admin_expense" model="hr.expense">
            <field name="name">BreakFast</field>
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="product_id" ref="expense_product_meal"/>
            <field eval="20" name="total_amount_currency"/>
        </record>

        <record id="travel_ny_sheet" model="hr.expense.sheet">
            <field name="name">Commercial Travel at New York</field>
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="state">approve</field>
        </record>

        <record id="travel_by_air_expense" model="hr.expense">
            <field name="name">Travel by Air</field>
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="analytic_distribution" eval="{ref('analytic.analytic_our_super_product'): 100}"/>
            <field name="product_id" ref="expense_product_travel_accommodation"/>
            <field eval="700.0" name="total_amount_currency"/>
            <field name="product_uom_id" ref="uom.product_uom_unit"/>
            <field name="date" eval="time.strftime('%Y-%m')+'-12'"/>
            <field name="sheet_id" ref="travel_ny_sheet"/>
        </record>

        <record id="hotel_bill_expense" model="hr.expense">
            <field name="name">Hotel Expenses</field>
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="analytic_distribution" eval="{ref('analytic.analytic_nebula'): 100}"/>
            <field name="product_id" ref="expense_product_travel_accommodation"/>
            <field eval="2000.0" name="total_amount_currency"/>
            <field name="product_uom_id" ref="uom.product_uom_unit"/>
            <field name="date" eval="time.strftime('%Y-%m')+'-17'"/>
            <field name="sheet_id" ref="travel_ny_sheet"/>
        </record>

        <record id="lunch_customer_bill_expense" model="hr.expense">
            <field name="name">Lunch with Customer</field>
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="analytic_distribution" eval="{ref('analytic.analytic_nebula'): 100}"/>
            <field name="product_id" ref="expense_product_meal"/>
            <field eval="152.8" name="total_amount_currency"/>
            <field name="date" eval="time.strftime('%Y-%m')+'-13'"/>
            <field name="sheet_id" ref="travel_ny_sheet"/>
        </record>

        <record id="lunch_bill_expense" model="hr.expense">
            <field name="name">Lunch</field>
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="analytic_distribution" eval="{ref('analytic.analytic_nebula'): 100}"/>
            <field name="product_id" ref="expense_product_meal"/>
            <field name="date" eval="time.strftime('%Y-%m')+'-15'"/>
            <field eval="56.8" name="total_amount_currency"/>
            <field name="sheet_id" ref="travel_ny_sheet"/>
        </record>

        <!-- ++++++++++++++ Expense sheet for Demo ++++++++++++++-->
        <record id="customer_meeting_sheet" model="hr.expense.sheet">
            <field name="name">Customer meeting</field>
            <field name="employee_id" ref="hr.employee_qdp"/>
            <field name="state">submit</field>
        </record>

        <record id="travel_demo_by_car_expense" model="hr.expense">
            <field name="name">Travel by Car</field>
            <field name="employee_id" ref="hr.employee_qdp"/>
            <field name="analytic_distribution" eval="{ref('analytic.analytic_our_super_product'): 100}"/>
            <field name="product_id" ref="expense_product_mileage"/>
            <field name="product_uom_id" ref="uom.product_uom_km"/>
            <field name="date" eval="time.strftime('%Y')+'-01-15'"/>
            <field eval="120.85" name="quantity"/>
            <field name="sheet_id" ref="customer_meeting_sheet"/>
        </record>

        <record id="lunch_demo_customer_bill_expense" model="hr.expense">
            <field name="name">Lunch with Customer</field>
            <field name="employee_id" ref="hr.employee_qdp"/>
            <field name="analytic_distribution" eval="{ref('analytic.analytic_nebula'): 100}"/>
            <field name="product_id" ref="product_product_no_cost"/>
            <field name="date" eval="time.strftime('%Y')+'-01-15'"/>
            <field eval="152.8" name="total_amount_currency"/>
            <field name="sheet_id" ref="customer_meeting_sheet"/>
        </record>

        <!-- ++++++++++++++ Expense sheet for Keith Byrd ++++++++++++++-->
        <record id="team_building_sheet" model="hr.expense.sheet">
            <field name="name">Team Building</field>
            <field name="employee_id" ref="hr.employee_fme"/>
            <field name="state">submit</field>
        </record>

        <record id="pizzas_bill_expense" model="hr.expense">
            <field name="name">Pizzas</field>
            <field name="employee_id" ref="hr.employee_fme"/>
            <field name="analytic_distribution" eval="{ref('analytic.analytic_nebula'): 100}"/>
            <field name="product_id" ref="expense_product_meal"/>
            <field name="date" eval="time.strftime('%Y-%m')+'-05'"/>
            <field eval="154" name="total_amount_currency"/>
            <field name="sheet_id" ref="team_building_sheet"/>
        </record>

        <record id="drinks_bill_expense" model="hr.expense">
            <field name="name">Drinks</field>
            <field name="employee_id" ref="hr.employee_fme"/>
            <field name="analytic_distribution" eval="{ref('analytic.analytic_nebula'): 100}"/>
            <field name="product_id" ref="expense_product_meal"/>
            <field name="date" eval="time.strftime('%Y-%m')+'-05'"/>
            <field eval="42.5" name="total_amount_currency"/>
            <field name="sheet_id" ref="team_building_sheet"/>
        </record>

        <record id="paintball_bill_expense" model="hr.expense">
            <field name="name">Paintball</field>
            <field name="employee_id" ref="hr.employee_fme"/>
            <field name="analytic_distribution" eval="{ref('analytic.analytic_nebula'): 100}"/>
            <field name="product_id" ref="product_product_no_cost"/>
            <field name="date" eval="time.strftime('%Y-%m')+'-05'"/>
            <field eval="25" name="total_amount_currency"/>
            <field name="sheet_id" ref="team_building_sheet"/>
        </record>

        <!-- ++++++++++++++ Expense sheet for Ronnie Hart ++++++++++++++-->
        <record id="office_furniture_sheet" model="hr.expense.sheet">
            <field name="name">Office furniture</field>
            <field name="employee_id" ref="hr.employee_al"/>
            <field name="state">submit</field>
        </record>

        <record id="chair_bill_expense" model="hr.expense">
            <field name="name">Chairs</field>
            <field name="employee_id" ref="hr.employee_al"/>
            <field name="analytic_distribution" eval="{ref('analytic.analytic_nebula'): 100}"/>
            <field name="product_id" ref="product_product_no_cost"/>
            <field name="date" eval="time.strftime('%Y')+'-06-02'"/>
            <field eval="55.75" name="total_amount_currency"/>
            <field name="sheet_id" ref="office_furniture_sheet"/>
        </record>

        <record id="lamp_bill_expense" model="hr.expense">
            <field name="name">Lamp</field>
            <field name="employee_id" ref="hr.employee_al"/>
            <field name="analytic_distribution" eval="{ref('analytic.analytic_nebula'): 100}"/>
            <field name="product_id" ref="product_product_no_cost"/>
            <field name="date" eval="time.strftime('%Y')+'-06-02'"/>
            <field eval="28.99" name="total_amount_currency"/>
            <field name="sheet_id" ref="office_furniture_sheet"/>
        </record>

        <!-- ++++++++++++++ Expense for Randall Lewis ++++++++++++++-->
        <record id="afterwork_bill_expense" model="hr.expense">
            <field name="name">Car tyres</field>
            <field name="employee_id" ref="hr.employee_stw"/>
            <field name="analytic_distribution" eval="{ref('analytic.analytic_nebula'): 100}"/>
            <field name="product_id" ref="product_product_no_cost"/>
            <field name="date" eval="time.strftime('%Y')+'-03-15'"/>
            <field eval="450.58" name="total_amount_currency"/>
        </record>

    </data>
</odoo>
