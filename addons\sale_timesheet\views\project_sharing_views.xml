<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="project_sharing_inherit_project_task_view_form" model="ir.ui.view">
        <field name="name">project.task.form.inherit.timesheet</field>
        <field name="model">project.task</field>
        <field name="priority">600</field>
        <field name="inherit_id" ref="hr_timesheet.project_sharing_inherit_project_task_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='timesheet_ids']/list" position="attributes">
                <attribute name="decoration-muted">timesheet_invoice_id != False</attribute>
            </xpath>
            <xpath expr="//field[@name='timesheet_ids']/list/field[@name='unit_amount']" position="before">
                <field name="timesheet_invoice_id" column_invisible="True"/>
                <field name="so_line"
                    column_invisible="not parent.allow_billable"
                    context="{'with_remaining_hours': True, 'with_price_unit': True}" options="{'no_create': True, 'no_open': True}"
                    optional="hide"/>
            </xpath>
            <xpath expr="//field[@name='child_ids']/list/field[@name='remaining_hours']" position="after">
                <field name="remaining_hours_available" column_invisible="True"/>
                <field name="remaining_hours_so" optional="hide" widget="timesheet_uom" column_invisible="not parent.allow_timesheets"/>
            </xpath>
            <xpath expr="//field[@name='depend_on_ids']/list/field[@name='remaining_hours']" position="after">
                <field name="remaining_hours_available" column_invisible="True"/>
                <field name="remaining_hours_so" optional="hide" widget="timesheet_uom" column_invisible="not parent.allow_timesheets"/>
            </xpath>
            <xpath expr="//field[@name='remaining_hours']" position="after">
                <field name="allow_billable" invisible="1" />
                <field name="remaining_hours_available" invisible="1"/>
                <field name="sale_order_id" invisible="1"/>
                <span id="remaining_hours_so_label" invisible="not allow_billable or not sale_order_id or not partner_id or not sale_line_id or not remaining_hours_available" class="o_td_label float-start">
                    <label class="fw-bold" for="remaining_hours_so"
                            invisible="remaining_hours_so &lt; 0"/>
                    <label class="fw-bold text-danger" for="remaining_hours_so"
                            invisible="remaining_hours_so &gt;= 0"/>
                </span>
                <field name="remaining_hours_so" nolabel="1" widget="timesheet_uom" invisible="not allow_billable or not sale_order_id or not partner_id or not sale_line_id or not remaining_hours_available" decoration-danger="remaining_hours_so &lt; 0"></field>
            </xpath>
        </field>
    </record>

</odoo>
