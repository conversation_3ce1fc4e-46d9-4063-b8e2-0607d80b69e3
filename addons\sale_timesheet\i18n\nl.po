# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_timesheet
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# <PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:04+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/sale_order.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    No activities found. Let's start a new one!\n"
"                </p><p>\n"
"                    Track your working hours by projects every day and invoice this time to your customers.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Geen activiteiten gevonden! Laten we er een maken!\n"
"                </p><p>\n"
"                    Houd elke dag je gewerkte uren bij per project en factureer deze tijd aan je klanten.\n"
"                </p>\n"
"            "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/account_move.py:0
#: code:addons/sale_timesheet/models/project_project.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Record timesheets\n"
"                </p><p>\n"
"                    You can register and track your workings hours by project every\n"
"                    day. Every time spent on a project will become a cost and can be re-invoiced to\n"
"                    customers if required.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Vastleggen urenstaten\n"
"                </p><p>\n"
"                    Je kunt je uren registreren en iedere dag volgen per project.\n"
"Alle tijd die aan een project wordt besteed, wordt een kostenpost en kan opnieuw worden gefactureerd aan\n"
"klanten indien nodig.\n"
"                </p>\n"
"            "

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
msgid ""
"'%(missing_plan_names)s' analytic plan(s) required on the analytic "
"distribution of the sale order item '%(so_line_name)s' linked to the "
"timesheet."
msgstr ""
"'%(missing_plan_names)s' analytisch dimensie(s) vereist op de analytische "
"verdeling van de verkooporderregel '%(so_line_name)s' gekoppeld aan de "
"urenstaat."

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_sale_page
msgid "- Timesheet product"
msgstr "- Urenstaat product"

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "1 Onsite Interview"
msgstr "1 sollicitatiegesprek op locatie"

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "1 Phone Call"
msgstr "1 telefoongesprek"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "12 days / year, including <br>6 of your choice."
msgstr "12 dagen / jaar, waarvan <br>6 naar keuze."

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "2 open days"
msgstr "2 opendeurdagen"

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "4 Days after Interview"
msgstr "4 dagen na sollicitatiegesprek"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "<b>Daily Cost: </b>"
msgstr "<b>Kosten per dag: </b>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "<b>Unit Price: </b>"
msgstr "<b>Prijs: </b>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid ""
"<i class=\"fa fa-lightbulb-o\"/>\n"
"                        <span>\n"
"                            Define the rate at which an employee's time is billed based on their expertise, skills, or experience.\n"
"                            To bill the same service at a different rate, create separate sales order items.\n"
"                        </span>"
msgstr ""
"<i class=\"fa fa-lightbulb-o\"/>\n"
"                        <span>\n"
"Definieer het tarief waartegen de tijd van een werknemer wordt gefactureerd op basis van hun expertise, vaardigheden of ervaring.\n"
"                        Om dezelfde service tegen een ander tarief te factureren, maak je aparte verkooporderregels aan.\n"
"</span>"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "<small><b>READ</b></small>"
msgstr "<small><b>LEES</b></small>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_form_inherit
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning\" title=\"The sales "
"order associated with this timesheet entry has been cancelled.\" "
"invisible=\"sale_order_state != 'cancel'\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning\" title=\"De "
"verkooporder voor deze urenstaat is geannuleerd.\" "
"invisible=\"sale_order_state != 'cancel'\"/>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_form_inherit
msgid "<span class=\"o_stat_text\">Invoice</span>"
msgstr "<span class=\"o_stat_text\">Factuur</span>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.account_invoice_view_form_inherit_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
msgid "<span class=\"o_stat_text\">Recorded</span>"
msgstr "<span class=\"o_stat_text\">Geboekt</span>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_form_inherit
msgid "<span class=\"o_stat_text\">Sales Order</span>"
msgstr "<span class=\"o_stat_text\">Verkooporder</span>"

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "<span class=\"text-muted small\">Days to get an Offer</span>"
msgstr ""
"<span class=\"text-muted small\">Dagen om een aanbieding te krijgen</span>"

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "<span class=\"text-muted small\">Process</span>"
msgstr "<span class=\"text-muted small\">Proces</span>"

#. module: sale_timesheet
#: model_terms:hr.job,job_details:sale_timesheet.job_engineer
#: model_terms:hr.job,job_details:sale_timesheet.job_interior_designer
#: model_terms:hr.job,job_details:sale_timesheet.job_labour
msgid "<span class=\"text-muted small\">Time to Answer</span>"
msgstr "<span class=\"text-muted small\">Tijd om te antwoorden</span>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Amount Due:</strong>"
msgstr "<strong>Verschuldigd bedrag:</strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Invoiced:</strong>"
msgstr "<strong>Gefactureerd:</strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Invoices:</strong>"
msgstr "<strong>Facturen:</strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Sales Order:</strong>"
msgstr "<strong>Verkooporder:</strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_timesheet_table_inherit
msgid "<strong>Time Remaining on SO: </strong>"
msgstr "<strong>Resterende tijd op verkooporder: </strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "<u>Profitability</u>"
msgstr "<u>Winstgevendheid</u>"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "A full-time position <br>Attractive salary package."
msgstr "Een fulltime functie <br>Aantrekkelijk salarispakket."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"Afhankelijk van de productinstellingen, kan het geleverde aantal automatisch worden berekend met behulp van het mechanisme:\n"
"  - Handmatig: het aantal wordt handmatig op de regel ingesteld\n"
"  - Analytisch van declaraties: het aantal is de som van de geboekte declaraties\n"
"  - Urenstaat: het aantal is de som van de uren die zijn vastgelegd voor taken die aan deze verkoopregel zijn gekoppeld\n"
"  - Voorraadverplaatsingen: het aantal komt van de bevestigd pickings\n"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Achieve monthly sales objectives"
msgstr "Bereik maandelijke verkoopdoelstellingen"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Additional languages"
msgstr "Extra talen"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Administrative Work"
msgstr "Administratief werk"

#. module: sale_timesheet
#: model:account.analytic.account,name:sale_timesheet.account_analytic_account_project_support
#: model:project.project,name:sale_timesheet.project_support
msgid "After-Sales Services"
msgstr "Dienst na verkoop"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__allocated_hours
msgid "Allocated Time"
msgstr "Toegewezen tijd"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__amount_to_invoice
msgid "Amount to invoice"
msgstr "Bedrag te factureren"

#. module: sale_timesheet
#: model:ir.model.constraint,message:sale_timesheet.constraint_project_sale_line_employee_map_uniqueness_employee
msgid ""
"An employee cannot be selected more than once in the mapping. Please remove "
"duplicate(s) and try again."
msgstr ""
"Een werknemer kan niet meer dan één keer worden geselecteerd in de mapping. "
"Verwijderen dubbelen en probeer opnieuw."

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analytische boeking"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__analytic_line_ids
msgid "Analytic lines"
msgstr "Analytische boekingen"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid ""
"As an employee of our company, you will <b>collaborate with each department\n"
"                        to create and deploy disruptive products.</b> Come work at a growing company\n"
"                        that offers great benefits with opportunities to moving forward and learn\n"
"                        alongside accomplished leaders. We're seeking an experienced and outstanding\n"
"                        member of staff.\n"
"                        <br><br>\n"
"                        This position is both <b>creative and rigorous</b> by nature you need to think\n"
"                        outside the box. We expect the candidate to be proactive and have a \"get it done\"\n"
"                        spirit. To be successful, you will have solid solving problem skills."
msgstr ""
"Als werknemer van ons bedrijf <b>werk je samen met elke afdeling\n"
"om ontwrichtende producten te maken en in te zetten. </b>Kom werken bij een groeiend bedrijf\n"
"                     dat grote voordelen biedt met kansen om vooruit te komen en te leren\n"
"                     naast bekwame leiders. We zoeken een ervaren en uitstekende\n"
"                     werknemer.\n"
"                     <br><br>\n"
"                      Deze functie is zowel <b> creatief als rigoureus</b> van aard, je moet outside-the-box denken.\n"
"                      We verwachten dat de sollicitant proactief is en een 'get it done' mentaliteit heeft.\n"
"                      Om succesvol te zijn, moet je beschikken over grote probleemoplossende vaardigheden."

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Autonomy"
msgstr "Autonomie"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Bachelor Degree or Higher"
msgstr "Bacherlordiploma of hoger"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product_template.py:0
msgid "Based on Timesheets"
msgstr "Op basis van urenstaten"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__allow_billable
msgid "Billable"
msgstr "Factureerbaar"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__billable_time
msgid "Billable Time"
msgstr "Factureerbare tijd"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__timesheet_invoice_type
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__timesheet_invoice_type
msgid "Billable Type"
msgstr "Factureerbaar soort"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Billed"
msgstr "Gefactureerd"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_manual
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_manual
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed Manually"
msgstr "Handmatig gefactureerd"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed at a Fixed Price"
msgstr "Gefactureerd met een vaste prijs"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_fixed
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_fixed
msgid "Billed at a Fixed price"
msgstr "Gefactureerd met een vaste prijs"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_milestones
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_milestones
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed on Milestones"
msgstr "Gefactureerd op milstones"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_time
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__billable_time
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed on Timesheets"
msgstr "Gefactureerd op urenstaten"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Billing"
msgstr "Facturatie"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__billing_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billing Type"
msgstr "Facturatiesoort"

#. module: sale_timesheet
#: model:ir.ui.menu,name:sale_timesheet.menu_timesheet_billing_analysis
msgid "By Billing Type"
msgstr "Per facturatiesoort"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__sale_order_id
msgid "Choose the Sales Order to invoice"
msgstr "Kies de verkooporder om te factureren"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__commercial_partner_id
msgid "Commercial Partner"
msgstr "Commerciële partner"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie-instellingen"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Configure your services"
msgstr "Configureer je diensten"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__cost
msgid "Cost"
msgstr "Kostprijs"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Costs"
msgstr "Kosten"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.project_project_action_multi_create_invoice
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Create Invoice"
msgstr "Factuur maken"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_invoice
msgid "Create Invoice from project"
msgstr "Maak factuur vanuit project"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Create Sales Order from Project"
msgstr "Maak verkooporder vanuit project"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Create content that will help our users on a daily basis"
msgstr "Maak content die onze gebruikers dagelijks zal helpen"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__partner_id
msgid "Customer"
msgstr "Klant"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_order_timesheet_product_template
msgid "Customer Care (Prepaid Hours)"
msgstr "Klantenservice (pre paid)"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_kanban_inherit_sale_timesheet
msgid "Customer Ratings"
msgstr "Klantbeoordelingen"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Customer Relationship"
msgstr "Klantrelatie"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Days Ordered,"
msgstr "Dagen besteld,"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Days Remaining)"
msgstr "Resterende dagen)"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Discard"
msgstr "Negeren"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Discover our products."
msgstr "Ontdek onze producten."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "Draft Invoice"
msgstr "Conceptfactuur"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid ""
"Each employee has a chance to see the impact of his work.\n"
"                    You can make a real contribution to the success of the company.\n"
"                    <br>\n"
"                    Several activities are often organized all over the year, such as weekly\n"
"                    sports sessions, team building events, monthly drink, and much more"
msgstr ""
"Elke werknemer heeft de kans om de impact van zijn werk te zien.\n"
"                    Je kunt een echte bijdrage leveren aan het succes van het bedrijf.\n"
"                   <br>\n"
"                    Het hele jaar door worden er vaak verschillende activiteiten georganiseerd, zoals wekelijkse\n"
"                    sportsessies, teambuildingevenementen, maandelijkse borrels en nog veel meer"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Eat &amp; Drink"
msgstr "Eten & Drinken"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_product_elevator_installation_product_template
msgid "Elevator Installation"
msgstr "Liftinstallatie"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_hr_employee
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__employee_id
msgid "Employee"
msgstr "Werknemer"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__employee_rate
msgid "Employee rate"
msgstr "Werknemerstarief"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__date_end_invoice_timesheet
msgid "End Date"
msgstr "Einddatum"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Expand your knowledge of various business industries"
msgstr "Vergroot je kennis van verschillende bedrijfssectoren"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Expected"
msgstr "Verwacht"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Experience in writing online content"
msgstr "Ervaring in het schrijven van online content"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Fixed price services"
msgstr "Vaste prijs diensten"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Fruit, coffee and <br>snacks provided."
msgstr "Fruit, koffie en <br>snacks worden voorzien."

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_manual_product_template
msgid "Furniture Delivery (Manual)"
msgstr "Meubellevering (handmatig)"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Google Adwords experience"
msgstr "Google Adwords experience"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Great team of smart people, in a friendly and open culture"
msgstr "Geweldig team van slimme mensen, in een vriendelijke en open cultuur"

#. module: sale_timesheet
#: model:hr.job,name:sale_timesheet.job_labour
msgid "Handyman"
msgstr "Handyman"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__has_multi_sol
msgid "Has Multi Sol"
msgstr "Heeft Multi Sol"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Highly creative and autonomous"
msgstr "Zeer creatief en autonoom"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__display_cost
msgid "Hourly Cost"
msgstr "Uurkosten"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Hours Ordered,"
msgstr "Uren besteld,"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Hours Remaining)"
msgstr "Uren resterend)"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__id
msgid "ID"
msgstr "ID"

#. module: sale_timesheet
#: model:hr.job,name:sale_timesheet.job_interior_designer
msgid "Interior Designer"
msgstr "Interieurontwerper"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_product_interior_designing_product_template
msgid "Interior Designing"
msgstr "Interieurontwerp"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__timesheet_invoice_id
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__timesheet_invoice_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.report_timesheet_account_move
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Invoice"
msgstr "Factuur"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_res_config_settings__invoice_policy
msgid "Invoice Policy"
msgstr "Factuurbeleid"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product_template.py:0
msgid "Invoice based on timesheets (delivered quantity)."
msgstr "Facture gebaseerd op urenstaten (geleverde hoeveelheden)."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_analytic_line__timesheet_invoice_id
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__timesheet_invoice_id
msgid "Invoice created from the timesheet"
msgstr "Factuur aangemaakt van urenstaat"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Invoiced"
msgstr "Gefactureerd"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Invoices"
msgstr "Facturen"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Invoicing"
msgstr "Facturatie"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__is_project_map_empty
msgid "Is Project map empty"
msgstr "Projectmap is leeg"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__is_so_line_edited
msgid "Is Sales Order Item Manually Edited"
msgstr "Is het verkooporderregel handmatig bewerkt"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_move
msgid "Journal Entry"
msgstr "Boeking"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_move_line
msgid "Journal Item"
msgstr "Boekingsregel"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_timesheet_2_product_template
msgid "Junior Architect (Invoice on Timesheets)"
msgstr "Junior architect (factureer op urenstaten)"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_milestones_product_template
msgid "Kitchen Assembly (Milestones)"
msgstr "Keukenassemblage (Milestones)"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Lead the entire sales cycle"
msgstr "Leid de volledige verkoopcyclus"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__service_type
#: model:ir.model.fields,help:sale_timesheet.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"Bepaal handmatig het aantal voor de order: Factuur op basis van het handmatig ingevoerde aantal, zonder het aanmaken van een analytische rekening.\n"
"Urenstaten op project: Factuur op basis van de geschreven uren op gerelateerde urenstaten.\n"
"Maak een taak aan en volg de uren ervan op: Een taak maken bij e bevestiging van een verkooporder en de werkuren opvolgen."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__margin
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Margin"
msgstr "Marge"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Master demos of our software"
msgstr "Beheers demo's van onze software"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Materials"
msgstr "Materialen"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Methode om de geleverde hoeveelheid bij te werken"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Milestone services"
msgstr "Milestone diensten"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Must Have"
msgstr "Vereist"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Negotiate and contract"
msgstr "Onderhandelen en deals sluiten"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Nice to have"
msgstr "Leuk om te hebben"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "No Invoice"
msgstr "Geen factuur"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.action_timesheet_from_invoice
msgid "No activities found"
msgstr "Geen activiteiten gevonden"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_from_sales_order_item
msgid "No activities found. Let's start a new one!"
msgstr "Geen activiteiten gevonden. Laten we een nieuwe beginnen!"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_billing_report
msgid "No data yet!"
msgstr "Nog geen gegevens!"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "No dumb managers, no stupid tools to use, no rigid working hours"
msgstr "Geen domme managers, geen domme tools en geen vaste werktijden"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid ""
"No waste of time in enterprise processes, real responsibilities and autonomy"
msgstr ""
"Geen tijdverspilling in bedrijfsprocessen, echte verantwoordelijkheden en "
"autonomie"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__non_billable
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__non_billable
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Non-Billable"
msgstr "Niet factureerbaar"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_form_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_line_tree_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_task_view_form_inherit_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheete_analysis_report_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_list_inherited
msgid "Non-billable"
msgstr "Niet factureerbaar"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__non_billable_time
msgid "Non-billable Time"
msgstr "Niet-factureerbare tijd"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Not Billed"
msgstr "Niet gefactureerd"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__timesheet_revenues
msgid "Number of hours spent multiplied by the unit price per hour/day."
msgstr "Aantal bestede uren vermenigvuldigd met de prijs per uur/dag."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__billable_time
msgid "Number of hours/days linked to a SOL."
msgstr "Aantal uren/dagen gekoppeld aan een SOL."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__non_billable_time
msgid "Number of hours/days not linked to a SOL."
msgstr "Aantal uren/dagen niet gekoppeld aan een SOL."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_advance_payment_inv__date_end_invoice_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_advance_payment_inv__date_start_invoice_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_advance_payment_inv_timesheet_view_form
msgid ""
"Only timesheets not yet invoiced (and validated, if applicable) from this "
"period will be invoiced. If the period is not indicated, all timesheets not "
"yet invoiced (and validated, if applicable) will be invoiced without "
"distinction."
msgstr ""
"Alleen urenstaten die nog niet zijn gefactureerd (en indien van toepassing "
"goedgekeurd) uit deze periode worden gefactureerd. Als de periode niet is "
"aangegeven, worden alle nog niet gefactureerde (en goedgekeurde, indien van "
"toepassing) urenstaten zonder onderscheid gefactureerd."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Operation not supported"
msgstr "Bewerking niet ondersteund"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__order_id
msgid "Order Reference"
msgstr "Orderreferentie"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__other_costs
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__other_costs
msgid "Other costs"
msgstr "Andere kosten"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__other_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__other_revenues
msgid "Other revenues"
msgstr "Andere omzet"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Our Product"
msgstr "Ons product"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Passion for software products"
msgstr "Passie voor softwareproducten"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__service_upsell_threshold
#: model:ir.model.fields,help:sale_timesheet.field_product_template__service_upsell_threshold
msgid ""
"Percentage of time delivered compared to the prepaid amount that must be "
"reached for the upselling opportunity activity to be triggered."
msgstr ""
"Percentage van de geleverde tijd vergeleken met het vooruitbetaalde bedrag "
"dat moet worden bereikt om de upsellingactiviteit te activeren."

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Perfect written English"
msgstr "Perfect geschreven Engels"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Perks"
msgstr "Voordelen"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Personal Evolution"
msgstr "Persoonlijke ontwikkeling"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Play any sport with colleagues, <br>the bill is covered."
msgstr "Samen met collega's sporten, <br>de rekening is gedekt."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__pricing_type
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__pricing_type
msgid "Pricing"
msgstr "Prijzen"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_template
msgid "Product"
msgstr "Product"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_product
msgid "Product Variant"
msgstr "Productvariant"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_project
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__project_id
msgid "Project"
msgstr "Project"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_sale_line_employee_map
msgid "Project Sales line, employee mapping"
msgstr "Project verkoopregel/Werknemer mapping"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__project_template_id
msgid "Project Template"
msgstr "Projectsjabloon"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_update
msgid "Project Update"
msgstr "Projectupdate"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__fixed_rate
msgid "Project rate"
msgstr "Projecttarief"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_invoice__project_id
msgid "Project to make billable"
msgstr "Project om factureerbaar te maken"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Qualify the customer needs"
msgstr "Kwalificeer de behoeften van de klant"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Quotation"
msgstr "Offerte"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Real responsibilities and challenges in a fast evolving company"
msgstr ""
"Echte verantwoordelijkheden en uitdagingen in een snel groeiend bedrijf"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__remaining_hours_available
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__remaining_hours_available
msgid "Remaining Hours Available"
msgstr "Resterende uren beschikbaar"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Responsibilities"
msgstr "Verantwoordelijkheden"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Revenues"
msgstr "Omzet"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_billing_report
msgid ""
"Review your timesheets by billing type and make sure your time is billable."
msgstr ""
"Bekijk je urenstaten op facturatiesoort en zorg ervoor dat je tijd "
"factureerbaar is."

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_product_roofing_product_template
msgid "Roofing"
msgstr "Dakdekking"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_sale_page
msgid "S0001"
msgstr "S0001"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "Verkoop vooruibetaling factuur"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
#: model:ir.model,name:sale_timesheet.model_sale_order
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__sale_order_id
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__order_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_report_search_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Sales Order"
msgstr "Verkooporder"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__so_line
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__sale_line_id
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__so_line
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_timesheet_table_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.report_timesheet_sale_order
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Sales Order Item"
msgstr "Verkooporderregel"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_order_line
msgid "Sales Order Line"
msgstr "Verkooporderregel"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_kanban_inherit_sale_timesheet_so_button
msgid "Sales Orders"
msgstr "Verkooporders"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__sale_line_employee_ids
msgid ""
"Sales order item that will be selected by default on the timesheets of the corresponding employee. It bypasses the sales order item defined on the project and the task, and can be modified on each timesheet entry if necessary. In other words, it defines the rate at which an employee's time is billed based on their expertise, skills or experience, for instance.\n"
"If you would like to bill the same service at a different rate, you need to create two separate sales order items as each sales order item can only have a single unit price at a time.\n"
"You can also define the hourly company cost of your employees for their timesheets on this project specifically. It will bypass the timesheet cost set on the employee."
msgstr ""
"Verkooporderregel dat standaard wordt geselecteerd op de urenstaten van de bijbehorende werknemer. Het omzeilt het verkooporderregel dat is gedefinieerd voor het project en de taak en kan indien nodig worden gewijzigd bij elke invoer in de urenstaat. Met andere woorden, het definieert het tarief waartegen de tijd van een werknemer wordt gefactureerd op basis van bijvoorbeeld zijn expertise, vaardigheden of ervaring.\n"
"Als je dezelfde service tegen een ander tarief wilt factureren, moet je twee afzonderlijke verkooporderregels maken, aangezien elk verkooporderregel slechts één prijs tegelijk kan hebben.\n"
"Je kunt ook specifiek de uurloonkosten van je werknemers voor hun urenstaten voor dit project definiëren. Het omzeilt de urenstaatkosten die zijn ingesteld op de werknemer."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_analytic_line__so_line
msgid ""
"Sales order item to which the time spent will be added in order to be "
"invoiced to your customer. Remove the sales order item for the timesheet "
"entry to be non-billable."
msgstr ""
"Verkooporderregel waaraan de bestede tijd wordt toegevoegd om aan je klant "
"te worden gefactureerd. Verwijder het verkooporderregel om de urenstaatpost "
"niet-factureerbaar te maken."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr "Verkooporder waaraan de taak is gerelateerd."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Search in Invoice"
msgstr "Zoek in factuur"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/controllers/portal.py:0
msgid "Search in Sales Order"
msgstr "Zoek in verkooporder"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Sell services and invoice time spent"
msgstr "Verkoop diensten en factureer gespendeerde tijd"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_service_deliver_timesheet_1_product_template
msgid "Senior Architect (Invoice on Timesheets)"
msgstr "Senior architect (Factureer op urenstaten)"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__service_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__service_revenues
msgid "Service Revenues"
msgstr "Omzet op diensten"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.time_product_product_template
msgid "Service on Timesheets"
msgstr "Diensten op basis van urenstaten"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__timesheet_product_id
#: model:ir.model.fields,help:sale_timesheet.field_project_task__timesheet_product_id
msgid ""
"Service that will be used by default when invoicing the time spent on a "
"task. It can be modified on each task individually by selecting a specific "
"sales order item."
msgstr ""
"Service die standaard wordt gebruikt bij het factureren van de tijd besteed "
"aan een taak. Het kan voor elke taak afzonderlijk worden gewijzigd door een "
"specifiek verkooporderregel te selecteren."

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.product_template_action_default_services
#: model:project.project,label_tasks:sale_timesheet.project_support
msgid "Services"
msgstr "Diensten"

#. module: sale_timesheet
#: model:hr.job,name:sale_timesheet.job_engineer
msgid "Site Manager"
msgstr "Site manager"

#. module: sale_timesheet
#: model:product.template,name:sale_timesheet.product_product_solar_installation_product_template
msgid "Solar Panel Installation"
msgstr "Installatie van zonnepanelen"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Sport Activity"
msgstr "Sportactiviteit"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__date_start_invoice_timesheet
msgid "Start Date"
msgstr "Begindatum"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__sale_order_state
msgid "Status"
msgstr "Status"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Strong analytical skills"
msgstr "Sterke analytische vaardighede"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_task
msgid "Task"
msgstr "Taak"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__task_rate
msgid "Task rate"
msgstr "Taaksnelheid"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "Takenanalyse"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Technical Expertise"
msgstr "Technische expertise"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product_product.py:0
#: code:addons/sale_timesheet/models/product_template.py:0
msgid ""
"The %s product is required by the Timesheets app and cannot be archived nor "
"deleted."
msgstr ""
"Het %s-product is vereist voor de Urenstaten-app en kan niet worden "
"gearchiveerd of verwijderd."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/product_template.py:0
msgid ""
"The %s product is required by the Timesheets app and cannot be linked to a "
"company."
msgstr ""
"Het %s product is vereist voor de urenstaten-app en kan niet aan een bedrijf"
" worden gekoppeld."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_invoice.py:0
msgid "The selected Sales Order should contain something to invoice."
msgstr ""
"De geselecteerde verkooporder hoort iets te bevatten om te factureren."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__pricing_type
#: model:ir.model.fields,help:sale_timesheet.field_project_task__pricing_type
msgid ""
"The task rate is perfect if you would like to bill different services to "
"different customers at different rates. The fixed rate is perfect if you "
"bill a service at a fixed rate per hour or day worked regardless of the "
"employee who performed it. The employee rate is preferable if your employees"
" deliver the same service at a different rate. For instance, junior and "
"senior consultants would deliver the same service (= consultancy), but at a "
"different rate because of their level of seniority."
msgstr ""
"Het tarief per taak is perfect als je verschillende diensten tegen "
"verschillende tarieven aan verschillende klanten wilt factureren. Het vaste "
"tarief is perfect als je een dienst factureert tegen een vast tarief per "
"gewerkt uur of dag ongeacht de werknemer die het heeft uitgevoerd. Het "
"werknemerstarief heeft de voorkeur als je werknemers dezelfde dienst leveren"
" tegen een ander tarief. Junior en senior consultants zouden bijvoorbeeld "
"dezelfde dienst (= consultancy) leveren, maar in een verschillend tempo "
"vanwege hun senioriteit."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_sale_line_employee_map__cost
msgid ""
"This cost overrides the employee's default employee hourly wage in "
"employee's HR Settings"
msgstr ""
"Deze kostenoverschrijven het standaard uurloon van de werknemer in de HR-"
"instellingen van de werknemer"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_upsell_threshold
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_upsell_threshold
msgid "Threshold"
msgstr "Drempel"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Time Billing"
msgstr "Tijdfacturatie"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__remaining_hours_so
#: model:ir.model.fields,field_description:sale_timesheet.field_report_project_task_user__remaining_hours_so
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__remaining_hours
msgid "Time Remaining on SO"
msgstr "Resterende tijd op verkooporder"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_graph_employee_per_date
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
msgid "Time Spent"
msgstr "Bestede tijd"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Time-based services"
msgstr "Tijdgebaseerde diensten"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_plan
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_plan_pivot
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_graph_employee_per_date
msgid "Timesheet"
msgstr "Urenstaat"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Timesheet Activities"
msgstr "Urenstaat activiteiten"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_graph_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_pivot_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_graph_employee_per_date
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
msgid "Timesheet Costs"
msgstr "Urenstaat kosten"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__timesheet_product_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__timesheet_product_id
msgid "Timesheet Product"
msgstr "Urenstaat product"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_report_search_sale_timesheet
msgid "Timesheet Report"
msgstr "Urenstaatrapport"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_timesheets_analysis_report__timesheet_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__timesheet_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__timesheets_analysis_report__timesheet_invoice_type__timesheet_revenues
msgid "Timesheet Revenues"
msgstr "Urenstaat omzet"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_total_duration
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_total_duration
msgid "Timesheet Total Duration"
msgstr "Totale duur urenstaat"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/account_move.py:0
#: model:ir.actions.act_window,name:sale_timesheet.action_timesheet_from_invoice
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_sales_order
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_sales_order_item
#: model:ir.actions.report,name:sale_timesheet.timesheet_report_account_move
#: model:ir.actions.report,name:sale_timesheet.timesheet_report_sale_order
#: model:ir.model.fields.selection,name:sale_timesheet.selection__sale_order_line__qty_delivered_method__timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_graph_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
msgid "Timesheets"
msgstr "Urenstaten"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Billed Manually)"
msgstr "Urenstaten (handmatig gefactureerd)"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Billed on Milestones)"
msgstr "Urenstaten (gefactureerd op milestones)"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Billed on Timesheets)"
msgstr "Urenstaten (gefactureerd op urenstaten)"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Fixed Price)"
msgstr "Urenstaten (vaste prijs)"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets (Non-Billable)"
msgstr "Urenstaten (niet factureerbaar)"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_pivot_invoice_type
msgid "Timesheets Analysis"
msgstr "Analyse urenstaten"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_timesheets_analysis_report
msgid "Timesheets Analysis Report"
msgstr "Analyserapport urenstaten"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_advance_payment_inv_timesheet_view_form
msgid "Timesheets Period"
msgstr "Urenstatenperiode"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_billing_report
msgid "Timesheets by Billing Type"
msgstr "Urenstaten per facturatiesoort"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_sale_page
msgid "Timesheets for the"
msgstr "Urenstaten voor"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets of %s"
msgstr "Urenstaten van %s"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_type__timesheet
msgid "Timesheets on project (one fare per SO/Project)"
msgstr "Urenstaten op project (één prijs per Verkooporder/Project)"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Timesheets revenues"
msgstr "Omzet urenstaten"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_timesheets_analysis_report__margin
msgid "Timesheets revenues minus the costs"
msgstr "Omzet uit urenstaten minus de kosten"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Timesheets taken into account when invoicing your time"
msgstr ""
"Urenstaten waarmee rekening wordt gehouden bij het factureren van je tijd"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_res_config_settings__invoice_policy
msgid "Timesheets taken when invoicing time spent"
msgstr "Urenstaten genomen bij het factureren van bestede tijd"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Timesheets without a sales order item are reported as"
msgstr "Urenstaten zonder verkooporderitem worden gerapporteerd als"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "To Bill"
msgstr "Te factureren"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "To Invoice"
msgstr "Te factureren"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheets_analysis_report_list_inherited
msgid "Total"
msgstr "Totaal"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_invoice__amount_to_invoice
msgid ""
"Total amount to invoice on the sales order, including all items (services, "
"storables, expenses, ...)"
msgstr ""
"Totaal te factureren bedrag op de verkooporder, inclusief alle items "
"(diensten, voorraadproducten, declaraties, ...)"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_bank_statement_line__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_account_move__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_sale_order__timesheet_total_duration
msgid ""
"Total recorded duration, expressed in the encoding UoM, and rounded to the "
"unit"
msgstr ""
"Totale geregistreerde duur, uitgedrukt in de coderingshoeveelheidseenheid, "
"en afgerond op de eenheid"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_type
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_type
msgid "Track Service"
msgstr "Dienst opvolgen"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.action_timesheet_from_invoice
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_from_sales_order_item
msgid ""
"Track your working hours by projects every day and invoice this time to your"
" customers."
msgstr ""
"Houd elke dag je werkuren per project bij en factureer deze tijd aan je "
"klanten."

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Trainings"
msgstr "Trainings"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__price_unit
msgid "Unit Price"
msgstr "Prijs"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "Valid work permit for Belgium"
msgstr "Geldig werkvisum voor België"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid "Value does not exist in the pricing type"
msgstr "Waarde bestaat niet in het prijstype"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_invoice_page_inherit
msgid "View Timesheet"
msgstr "Urenstaten bekijken"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_invoice_page_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_order_portal_content_inherit
msgid "View Timesheets"
msgstr "Urenstaten bekijken"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_product_timesheet_form
msgid "Warn the salesperson for an upsell when work done exceeds"
msgstr ""
"Waarschuw de verkoper voor een upsell wanneer het uitgevoerde werk meer is "
"dan"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "What We Offer"
msgstr "Wat wij aanbieden"

#. module: sale_timesheet
#: model_terms:hr.job,website_description:sale_timesheet.job_engineer
#: model_terms:hr.job,website_description:sale_timesheet.job_interior_designer
#: model_terms:hr.job,website_description:sale_timesheet.job_labour
msgid "What's great in the job?"
msgstr "Wat is geweldig aan de baan?"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/wizard/project_create_invoice.py:0
msgid "You can only apply this action from a project."
msgstr "Je kunt deze actie alleen toepassen vanuit een project."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid ""
"You cannot link a billable project to a sales order item that comes from an "
"expense or a vendor bill."
msgstr ""
"Je kunt een factureerbaar project niet koppelen aan een verkooporderregel "
"dat afkomstig is van een declaratie- of leveranciersfactuur."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/project_project.py:0
msgid ""
"You cannot link a billable project to a sales order item that is not a "
"service."
msgstr ""
"Je kunt een factureerbaar project niet koppelen aan een verkooporderregel "
"dat geen dienst is."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
msgid "You cannot modify timesheets that are already invoiced."
msgstr "Je kunt geen urenstaten wijzigen die al zijn gefactureerd."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/hr_timesheet.py:0
msgid "You cannot remove a timesheet that has already been invoiced."
msgstr "Je kunt een urenstaat die al is gefactureerd, niet verwijderen."

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__billing_type__manually
msgid "billed manually"
msgstr "handmatig gefactureerd"

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/sale_order_line.py:0
msgid "days remaining"
msgstr "resterende dagen"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__billing_type__not_billable
msgid "not billable"
msgstr "niet factureerbaar"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_product_timesheet_form
msgid "of hours sold."
msgstr "aantal uren verkocht."

#. module: sale_timesheet
#. odoo-python
#: code:addons/sale_timesheet/models/sale_order_line.py:0
msgid "remaining"
msgstr "resterend"
