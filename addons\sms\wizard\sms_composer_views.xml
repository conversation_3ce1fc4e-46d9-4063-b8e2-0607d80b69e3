<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="sms_composer_view_form" model="ir.ui.view">
        <field name="name">sms.composer.view.form</field>
        <field name="model">sms.composer</field>
        <field name="arch" type="xml">
            <form string="Send an SMS">
                <!-- Single mode information (invalid number) -->
                <div class="alert alert-danger text-center" role="alert"
                    invisible="not res_model_description or not comment_single_recipient or recipient_single_valid">
                    <p class="my-0">
                        <strong>Invalid number:</strong>
                        <span> make sure to set a country on the </span>
                        <span><field name="res_model_description"/></span>
                        <span> or to specify the country code.</span>
                    </p>
                </div>

                <!-- Mass mode information (res_ids versus active domain) -->
                <div class="alert alert-info text-center" role="alert"
                        invisible="comment_single_recipient or recipient_invalid_count == 0">
                    <p class="my-0">
                        <field class="oe_inline fw-bold" name="recipient_invalid_count"/> out of
                        <field class="oe_inline fw-bold" name="res_ids_count"/> recipients have an invalid phone number and will not receive this text message.
                    </p>
                </div>
                <sheet>
                    <group>
                        <field name="composition_mode" invisible="1"/>
                        <field name="comment_single_recipient" invisible="1"/>
                        <field name="res_id" invisible="1"/>
                        <field name="res_ids" invisible="1"/>
                        <field name="res_model" invisible="1"/>
                        <field name="mass_force_send" invisible="1"/>
                        <field name="recipient_single_valid" invisible="1"/>
                        <field name="recipient_single_number" invisible="1"/>
                        <field name="number_field_name" invisible="1"/>
                        <field name="numbers" invisible="1"/>
                        <field name="sanitized_numbers" invisible="1"/>
                        <field name="template_id" invisible="1"/>

                        <label for="recipient_single_description" string="Recipient"
                            class="fw-bold"
                            invisible="not comment_single_recipient"/>
                        <div invisible="not comment_single_recipient">
                            <field name="recipient_single_description" class="oe_inline" invisible="not recipient_single_description"/>
                            <field name="recipient_single_number_itf" class="oe_inline" nolabel="1" onchange_on_keydown="True" placeholder="e.g. +1 415 555 0100"/>
                        </div>
                        <field name="body" widget="sms_widget" invisible="comment_single_recipient or recipient_single_valid"
                            options="{'dynamic_placeholder': true, 'dynamic_placeholder_model_reference_field': 'res_model'}"/>
                        <field name="body" widget="sms_widget" invisible="comment_single_recipient or not recipient_single_valid" default_focus="1"
                            options="{'dynamic_placeholder': true, 'dynamic_placeholder_model_reference_field': 'res_model'}"/>
                        <field name="body" widget="sms_widget" invisible="not comment_single_recipient"/>
                        <field name="mass_keep_log" invisible="1"/>
                    </group>
                </sheet>
                <footer>
                    <!-- attrs doesn't work for 'disabled'-->
                    <button string="Send SMS" type="object" class="oe_highlight" name="action_send_sms" data-hotkey="q"
                            invisible="composition_mode not in ('comment', 'numbers') or not recipient_single_valid"/>
                    <button string="Send SMS" type="object" class="oe_highlight" name="action_send_sms" data-hotkey="q"
                            invisible="composition_mode not in ('comment', 'numbers') or recipient_single_valid" disabled='1'/>
                    <button string="Put in queue" type="object" class="oe_highlight" name="action_send_sms" data-hotkey="q"
                        invisible="composition_mode != 'mass'"/>
                    <button string="Send Now" type="object" name="action_send_sms_mass_now" data-hotkey="w"
                        invisible="composition_mode != 'mass'"/>
                    <button string="Close" class="btn btn-secondary" special="cancel" data-hotkey="x"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="sms_composer_action_form" model="ir.actions.act_window">
        <field name="name">Send SMS</field>
        <field name="res_model">sms.composer</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

</odoo>
