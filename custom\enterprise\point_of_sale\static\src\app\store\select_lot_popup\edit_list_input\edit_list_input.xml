<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.EditListInput">
        <div class="input-group mb-3">
            <input
                type="text"
                placeholder="Serial/Lot Number"
                t-att-readOnly="!props.customInput"
                t-att-value="props.item.text"
                class="popup-input list-line-input form-control form-control-lg w-75 mx-auto"
                t-att-class="{'is-invalid': props.hasInvalidValue}"
                t-on-input="onInput"
                t-on-keyup="onKeyup"
                t-on-keydown="onKeydown"
                t-on-focus="onFocus"
                t-on-blur="onBlur"
                t-on-click="onClick" />
            <button t-if="props.deletable" class="btn btn-danger btn-lg" t-on-click="props.removeItem">
                <i class="oe_link_icon fa fa-trash-o" role="img" aria-label="Remove" title="Remove" />
            </button>
            <div class="options-dropdown" t-ref="options-dropdown" t-if="props.shouldShowOptions and !state.hideOptions">
                <span t-if="displayedOptions.length === 0" class="no-match">No existing serial/lot number matching...</span>
                <t t-foreach="displayedOptions" t-as="option" t-key="option">
                    <div class="option" t-att-class="{'selected': option === state.selectedOptionValue}" t-on-click="() => this.onSelectOption(option)">
                        <span t-esc="option" />
                    </div>
                </t>
            </div>
        </div>
    </t>

</templates>
