# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* snailmail
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__attachment_error
msgid "ATTACHMENT_ERROR"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_cover
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_cover
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__snailmail_cover
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Add a Cover Page"
msgstr ""

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Address"
msgstr "Adresa"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core/failure_model_patch.js:0
msgid ""
"An error occurred when sending a letter with Snailmail on “%(record_name)s”"
msgstr ""

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core/failure_model_patch.js:0
msgid "An error occurred when sending a letter with Snailmail."
msgstr ""

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "An error occurred when sending the document by post.<br>Error: %s"
msgstr ""

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "An unknown error happened. Please contact the support."
msgstr "Došlo k neznámej chybe. Kontaktujte prosím podporu."

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid "An unknown error occurred. Please contact our"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_id
msgid "Attachment"
msgstr "Príloha"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_fname
msgid "Attachment Filename"
msgstr ""

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core/notification_model_patch.js:0
msgid "Awaiting Dispatch"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__duplex
msgid "Both side"
msgstr "Obe strany"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_duplex
msgid "Both sides"
msgstr "Obe strany"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid "Buy credits"
msgstr "Kúpiť kredity"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__credit_error
msgid "CREDIT_ERROR"
msgstr ""

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_form
msgid "Cancel"
msgstr "Zrušené"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Cancel Letter"
msgstr ""

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Cancel letter"
msgstr ""

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Cancel notification in failure"
msgstr "Zrušiť oznámenie pri zlyhaní"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core/notification_model_patch.js:0
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__canceled
msgid "Cancelled"
msgstr "Zrušené"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__city
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__city
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "City"
msgstr "Mesto"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Close"
msgstr "Zatvoriť"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__color
msgid "Color"
msgstr "Farba"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_company
msgid "Companies"
msgstr "Spoločnosti"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__company_id
msgid "Company"
msgstr "Spoločnost"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavenia konfigurácie"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__country_id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__country_id
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Country"
msgstr "Štát"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__cover
msgid "Cover Page"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__create_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__create_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__create_uid
msgid "Created by"
msgstr "Vytvoril"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__create_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__create_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__create_date
msgid "Created on"
msgstr "Vytvorené"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__display_name
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__display_name
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__display_name
msgid "Display Name"
msgstr "Zobrazovaný Názov"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_datas
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_list
msgid "Document"
msgstr "Dokument"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__res_id
msgid "Document ID"
msgstr "ID dokumentu"

#. module: snailmail
#: model:ir.model,name:snailmail.model_mail_thread
msgid "Email Thread"
msgstr "Emailové vlákno"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core/notification_model_patch.js:0
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__error_code
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__error
msgid "Error"
msgstr "Chyba"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__format_error
msgid "FORMAT_ERROR"
msgstr ""

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
#: model:ir.actions.act_window,name:snailmail.snailmail_letter_missing_required_fields_action
msgid "Failed letter"
msgstr "Zlyhanie listu"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_notification__failure_type
msgid "Failure type"
msgstr "Typ zlyhania"

#. module: snailmail
#: model:ir.actions.act_window,name:snailmail.snailmail_letter_format_error_action
msgid "Format Error"
msgstr ""

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter_format_error
msgid "Format Error Sending a Snailmail Letter"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__id
msgid "ID"
msgstr "ID"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__pending
msgid "In Queue"
msgstr "V zásobníku"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__info_msg
msgid "Information"
msgstr "Informácia"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "Invalid recipient name."
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__write_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__write_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__write_uid
msgid "Last Updated by"
msgstr "Naposledy upravoval"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__write_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__write_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__write_date
msgid "Last Updated on"
msgstr "Naposledy upravované"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_mail__letter_ids
#: model:ir.model.fields,field_description:snailmail.field_mail_message__letter_ids
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__letter_id
msgid "Letter"
msgstr "Dopis"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "Letter sent by post with Snailmail"
msgstr ""

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_list
msgid "Letters"
msgstr "Listy"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__missing_required_fields
msgid "MISSING_REQUIRED_FIELDS"
msgstr ""

#. module: snailmail
#: model:ir.model,name:snailmail.model_mail_message
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__message_id
msgid "Message"
msgstr "Správa"

#. module: snailmail
#: model:ir.model,name:snailmail.model_mail_notification
msgid "Message Notifications"
msgstr "Upozornenie na správu"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__model
msgid "Model"
msgstr "Model"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__no_price_available
msgid "NO_PRICE_AVAILABLE"
msgstr ""

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "Not enough credits for Snail Mail"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "Typ Notifikácie"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__notification_ids
msgid "Notifications"
msgstr "Notifikácie"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "One or more required fields are empty."
msgstr "Jedno alebo viac vyžadovaných polí je prázdne."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__report_template
msgid "Optional report to print and attach"
msgstr "Voliteľný report na tlač všetkých príloh"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid ""
"Our service cannot read your letter due to its format.<br/>\n"
"                Please modify the format of the template or update your settings\n"
"                to automatically add a blank cover page to all letters."
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__partner_id
msgid "Partner"
msgstr "Partner"

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "Please use an A4 Paper format."
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_duplex
msgid "Print Both sides"
msgstr "Obojstranná tlač"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_color
msgid "Print In Color"
msgstr "Tlačiť farebne"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid "Re-send letter"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__partner_id
msgid "Recipient"
msgstr "Príjemca"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__reference
msgid "Related Record"
msgstr ""

#. module: snailmail
#: model:ir.model,name:snailmail.model_ir_actions_report
msgid "Report Action"
msgstr "Akcia výkazu"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_form
msgid "Send Now"
msgstr "Odoslať teraz"

#. module: snailmail
#: model:iap.service,description:snailmail.iap_service_snailmail
msgid "Send your customer invoices and follow-up reports by post, worldwide."
msgstr ""

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core/notification_model_patch.js:0
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__sent
msgid "Sent"
msgstr "Poslané"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__user_id
msgid "Sent by"
msgstr "Poslané "

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "Snail Mails are successfully sent"
msgstr ""

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_message__message_type__snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__notification_type__snail
msgid "Snailmail"
msgstr "Pomalý mail"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_color
msgid "Snailmail Color"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_cover_readonly
msgid "Snailmail Cover Readonly"
msgstr ""

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_credit
msgid "Snailmail Credit Error"
msgstr ""

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/messaging_menu/messaging_menu_patch.js:0
msgid "Snailmail Failure: %(modelName)s"
msgstr ""

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/messaging_menu/messaging_menu_patch.js:0
msgid "Snailmail Failures"
msgstr ""

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_format
msgid "Snailmail Format Error"
msgstr ""

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter
#: model:ir.model.fields,field_description:snailmail.field_mail_notification__letter_id
msgid "Snailmail Letter"
msgstr ""

#. module: snailmail
#: model:ir.actions.act_window,name:snailmail.action_mail_letters
#: model:ir.ui.menu,name:snailmail.menu_snailmail_letters
msgid "Snailmail Letters"
msgstr ""

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_fields
msgid "Snailmail Missing Required Fields"
msgstr ""

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_price
msgid "Snailmail No Price Available"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__message_id
msgid "Snailmail Status Message"
msgstr ""

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_trial
msgid "Snailmail Trial Error"
msgstr ""

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_error
msgid "Snailmail Unknown Error"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_mail__snailmail_error
#: model:ir.model.fields,field_description:snailmail.field_mail_message__snailmail_error
msgid "Snailmail message in error"
msgstr ""

#. module: snailmail
#: model:ir.actions.server,name:snailmail.snailmail_print_ir_actions_server
msgid "Snailmail: process letters queue"
msgstr ""

#. module: snailmail
#: model:iap.service,unit_name:snailmail.iap_service_snailmail
msgid "Stamps"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__state_id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__state_id
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "State"
msgstr "Štát"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__state
msgid "Status"
msgstr "Stav"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__street
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__street
msgid "Street"
msgstr "Ulica"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Street 2..."
msgstr "Ulica 2..."

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Street..."
msgstr "Ulica..."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__street2
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__street2
msgid "Street2"
msgstr "Ulica2"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__trial_error
msgid "TRIAL_ERROR"
msgstr ""

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "The address of the recipient is not complete"
msgstr ""

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid ""
"The attachment of the letter could not be sent. Please check its content and"
" contact the support if the problem persists."
msgstr ""

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "The country of the partner is not covered by Snailmail."
msgstr "SnailMail nepokrýva krajinu partnera"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid ""
"The country to which you want to send the letter is not supported by our "
"service."
msgstr ""

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid ""
"The customer address is not complete. Update the address here and re-send "
"the letter."
msgstr ""

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid "The document was correctly sent by post.<br>The tracking id is %s"
msgstr ""

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid ""
"The letter could not be sent due to insufficient credits on your IAP "
"account."
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:snailmail.field_mail_message__message_type
msgid "Type"
msgstr "Typ"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__unknown_error
msgid "UNKNOWN_ERROR"
msgstr ""

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Update Config and Re-send"
msgstr ""

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Update address and re-send"
msgstr ""

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter_missing_required_fields
msgid "Update address of partner"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,help:snailmail.field_mail_mail__message_type
#: model:ir.model.fields,help:snailmail.field_mail_message__message_type
msgid ""
"Used to categorize message generator\n"
"'email': generated by an incoming email e.g. mailgateway\n"
"'comment': generated by user input e.g. through discuss or composer\n"
"'email_outgoing': generated by a mailing\n"
"'notification': generated by system e.g. tracking messages\n"
"'auto_comment': generated by automated notification mechanism e.g. acknowledgment\n"
"'user_notification': generated for a specific recipient"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,help:snailmail.field_snailmail_letter__state
msgid ""
"When a letter is created, the status is 'Pending'.\n"
"If the letter is correctly sent, the status goes in 'Sent',\n"
"If not, it will got in state 'Error' and the error message will be displayed in the field 'Error Message'."
msgstr ""

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid ""
"You don't have an IAP account registered for this service.<br>Please go to "
"<a href=%s target=\"new\">iap.odoo.com</a> to claim your free credits."
msgstr ""

#. module: snailmail
#. odoo-python
#: code:addons/snailmail/models/snailmail_letter.py:0
msgid ""
"You don't have enough credits to perform this operation.<br>Please go to "
"your <a href=%s target=\"new\">iap account</a>."
msgstr ""

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid "You need credits on your IAP account to send a letter."
msgstr ""

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "ZIP"
msgstr "PSČ"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__zip
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__zip
msgid "Zip"
msgstr "PSČ"

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid "for further assistance."
msgstr ""

#. module: snailmail
#. odoo-javascript
#: code:addons/snailmail/static/src/core_ui/snailmail_error.xml:0
msgid "support"
msgstr ""
