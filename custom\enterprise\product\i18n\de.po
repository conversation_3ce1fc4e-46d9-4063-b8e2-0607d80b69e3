# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product
# 
# Translators:
# <PERSON>, 2024
# <PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:04+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
msgid ""
"\n"
"\n"
"Note: products that you don't have access to will not be shown above."
msgstr ""
"\n"
"\n"
"Hinweis: Produkte, auf die Sie keinen Zugriff haben, werden oben nicht angezeigt."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__product_variant_count
#: model:ir.model.fields,field_description:product.field_product_product__product_variant_count
#: model:ir.model.fields,field_description:product.field_product_template__product_variant_count
msgid "# Product Variants"
msgstr "# Produktvarianten"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__product_count
msgid "# Products"
msgstr "# Produkte"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "$14.00"
msgstr "14,00 €"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "$15.00"
msgstr "15,00 €"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid ""
"%(base)s with a %(discount)s %% %(discount_type)s and %(surcharge)s extra fee\n"
"Example: %(amount)s * %(discount_charge)s + %(price_surcharge)s → %(total_amount)s"
msgstr ""
"%(base)s mit einem Rabatt von %(discount)s %% %(discount_type)s und einer Zusatzgebühr von %(surcharge)s \n"
"Beispiel: %(amount)s * %(discount_charge)s + %(price_surcharge)s → %(total_amount)s"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid ""
"%(item_name)s: end date (%(end_date)s) should be after start date "
"(%(start_date)s)"
msgstr ""
"%(item_name)s: Enddatum (%(end_date)s) sollte größer sein als Startdatum "
"(%(start_date)s)"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "%(percentage)s %% %(discount_type)s on %(base)s %(extra)s"
msgstr "%(percentage)s %% %(discount_type)s auf %(base)s %(extra)s"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "%(percentage)s %% discount on %(pricelist)s"
msgstr "%(percentage)s %% Rabatt auf %(pricelist)s"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "%(percentage)s %% discount on sales price"
msgstr "%(percentage)s %% auf den Verkaufspreis"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist.py:0
#: code:addons/product/models/product_tag.py:0
#: code:addons/product/models/product_template.py:0
msgid "%s (copy)"
msgstr "%s (Kopie)"

#. module: product
#: model:ir.actions.report,print_report_name:product.report_product_template_label_2x7
#: model:ir.actions.report,print_report_name:product.report_product_template_label_4x12
#: model:ir.actions.report,print_report_name:product.report_product_template_label_4x12_noprice
#: model:ir.actions.report,print_report_name:product.report_product_template_label_4x7
#: model:ir.actions.report,print_report_name:product.report_product_template_label_dymo
msgid "'Products Labels - %s' % (object.name)"
msgstr "'Produktetikett - %s' % (object.name)"

#. module: product
#: model:ir.actions.report,print_report_name:product.report_product_packaging
msgid "'Products packaging - %s' % (object.name)"
msgstr "'Produktverpackung - %s' % (object.name)"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid "(e.g: product description, ebook, legal notice, ...)."
msgstr "(z. B.: Produktbeschreibung, E-Book, Impressum ...)"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "+ %(amount)s extra fee"
msgstr "+ %(amount)s Zusatzgebühr"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "- %(amount)s rebate"
msgstr "- %(amount)s Preisnachlass"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
msgid "- Barcode \"%(barcode)s\" already assigned to product(s): %(product_list)s"
msgstr ""
"- Barcode „%(barcode)s“ bereits dem/den Produkt(en)  zugeordnet: "
"%(product_list)s"

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute__create_variant
msgid ""
"- Instantly: All possible variants are created as soon as the attribute and its values are added to a product.\n"
"        - Dynamically: Each variant is created only when its corresponding attributes and values are added to a sales order.\n"
"        - Never: Variants are never created for the attribute.\n"
"        Note: this cannot be changed once the attribute is used on a product."
msgstr ""
"- Sofort: Alle möglichen Varianten werden erstellt, sobald die Attribute und deren Werte zum Produkt hinzugefügt werden.\n"
"        - Dynamisch: Eine Variante wird nur dann erstellt, wenn deren Attribute und Werte zu einem Verkaufsauftrag hinzugefügt werden.\n"
"        - Niemals: Es werden keine Varianten für dieses Attribut erstellt.\n"
"        Hinweis: Dies kann nicht mehr geändert werden, sobald das Attribut bei einem Produkt verwendet wird."

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_5
msgid "1 year"
msgstr "1 Jahr"

#. module: product
#: model:product.attribute.value,name:product.pav_warranty
msgid "1 year warranty extension"
msgstr "1 Jahr Garantieverlängerung"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "10"
msgstr "10"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "10 Units"
msgstr "10 Einheiten"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "123456789012"
msgstr "123456789012"

#. module: product
#: model:product.template,description_sale:product.product_product_4_product_template
msgid "160x80cm, with large legs."
msgstr "160x80cm, mit langen Beinen."

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__2x7xprice
msgid "2 x 7 with price"
msgstr "2 x 7 mit Preis"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_6
msgid "2 year"
msgstr "2 Jahre"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__4x12
msgid "4 x 12"
msgstr "4 x 12"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__4x12xprice
msgid "4 x 12 with price"
msgstr "4 x 12 mit Preis"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__4x7xprice
msgid "4 x 7 with price"
msgstr "4 x 7 mit Preis"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "<b>Tip: want to round at 9.99?</b>"
msgstr "<b>Tipp: Möchten Sie auf 9,99 runden?</b>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow "
"icon\" title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow "
"icon\" title=\"Arrow\"/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_kanban
msgid "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Currency\" title=\"Currency\"/>"
msgstr "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Currency\" title=\"Currency\"/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid ""
"<span class=\"o_stat_text\" invisible=\"pricelist_item_count == 1\">\n"
"                                            Rules\n"
"                                        </span>\n"
"                                        <span class=\"o_stat_text\" invisible=\"pricelist_item_count != 1\">\n"
"                                            Rule\n"
"                                        </span>"
msgstr ""
"<span class=\"o_stat_text\" invisible=\"pricelist_item_count == 1\">\n"
"                                            Regeln\n"
"                                        </span>\n"
"                                        <span class=\"o_stat_text\" invisible=\"pricelist_item_count != 1\">\n"
"                                            Regel\n"
"                                        </span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid ""
"<span class=\"o_stat_text\" invisible=\"pricelist_item_count == 1\">\n"
"                                        Pricelists\n"
"                                    </span>\n"
"                                    <span class=\"o_stat_text\" invisible=\"pricelist_item_count != 1\">\n"
"                                        Pricelist\n"
"                                    </span>"
msgstr ""
"<span class=\"o_stat_text\" invisible=\"pricelist_item_count == 1\">\n"
"                                        Preislisten\n"
"                                    </span>\n"
"                                    <span class=\"o_stat_text\" invisible=\"pricelist_item_count != 1\">\n"
"                                        Preisliste\n"
"                                    </span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "<span class=\"o_stat_text\"> Products</span>"
msgstr "<span class=\"o_stat_text\"> Produkte</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
msgid "<span class=\"o_stat_text\">Products</span>"
msgstr "<span class=\"o_stat_text\">Produkte</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "<span>%</span>"
msgstr "<span>%</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid ""
"<span>%</span>\n"
"                                <span>on</span>"
msgstr ""
"<span>%</span>\n"
"                                <span>auf</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "<span>All general settings about this product are managed on</span>"
msgstr ""
"<span>Um allgemeine Einstellungen zu diesem Produkt zu verwalten, verwenden "
"Sie </span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "<strong>Qty: </strong>"
msgstr "<strong>Menge</strong>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back\n"
"                                here to set up the feature."
msgstr ""
"<strong>Speichern</strong> Sie diese Seite und kehren Sie\n"
"                                hierher zurück, um die Funktion einzurichten."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid ""
"<strong>Warning</strong>: adding or deleting attributes\n"
"                        will delete and recreate existing variants and lead\n"
"                        to the loss of their possible customizations."
msgstr ""
"<strong>Achtung</strong>: Löschen oder Hinzufügen von Attributen\n"
"wird bestehende Varianten löschen und neu anlegen\n"
"und führt zum Verlust vorgenommener Anpassungen."

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_packaging_barcode_uniq
msgid "A barcode can only be assigned to one packaging."
msgstr "Ein Barcode darf nur genau einer Verpackung zugeordnet sein!"

#. module: product
#. odoo-python
#: code:addons/product/models/product_combo.py:0
msgid "A combo choice can't contain duplicate products."
msgstr "Eine Kombi-Auswahl kann keine doppelten Produkte enthalten."

#. module: product
#. odoo-python
#: code:addons/product/models/product_combo_item.py:0
msgid "A combo choice can't contain products of type \"combo\"."
msgstr "Eine Kombi-Auswahl kann keine Produkte vom Typ „Kombi“ enthalten."

#. module: product
#. odoo-python
#: code:addons/product/models/product_combo.py:0
msgid "A combo choice must contain at least 1 product."
msgstr "Eine Kombi-Auswahl muss mindestens ein Produkt enthalten."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid "A combo product must contain at least 1 combo choice."
msgstr ""
"Eine Kombi-Auswahl muss mindestens eine Kombinationsauswahl enthalten."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__description_sale
#: model:ir.model.fields,help:product.field_product_template__description_sale
msgid ""
"A description of the Product that you want to communicate to your customers."
" This description will be copied to every Sales Order, Delivery Order and "
"Customer Invoice/Credit Note"
msgstr ""
"Eine Beschreibung des Produkts, die Sie Ihren Kunden mitteilen möchten. "
"Diese Beschreibung wird in jeden Verkaufsauftrag, Lieferauftrag und jede "
"Kundenrechnung/Gutschrift kopiert."

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
msgid "A packaging already uses the barcode"
msgstr "Eine Verpackung benutzt bereits den Barcode"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid ""
"A price is a set of sales prices or rules to compute the price of sales order lines based on products, product categories, dates and ordered quantities.\n"
"            This is the perfect tool to handle several pricings, seasonal discounts, etc."
msgstr ""
"Ein Preis ist ein Satz von Verkaufspreisen oder Regeln, um den Preis von Verkaufsauftragszeilen basierend auf Produkten, Produktkategorien, Daten und bestellten Mengen zu berechnen.\n"
"            Dies ist das perfekte Werkzeug, um mehrere Preise, Saisonrabatte usw. zu verwalten."

#. module: product
#. odoo-python
#: code:addons/product/models/product_packaging.py:0
msgid "A product already uses the barcode"
msgstr "Ein Produkt benutzt bereits den Barcode"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid "A sellable combo product can only contain sellable products."
msgstr ""
"Ein verkäufliches Kombi-Produkt kann nur verkäufliche Produkte enthalten."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__access_token
msgid "Access Token"
msgstr "Zugriffstoken"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Acme Widget"
msgstr "ACME-Widget"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Acme Widget - Blue"
msgstr "ACME-Widget - Blau"

#. module: product
#: model:product.template,name:product.product_template_acoustic_bloc_screens
msgid "Acoustic Bloc Screens"
msgstr "Akustik-Trennwände"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__message_needaction
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_needaction
#: model:ir.model.fields,field_description:product.field_product_product__message_needaction
#: model:ir.model.fields,field_description:product.field_product_template__message_needaction
msgid "Action Needed"
msgstr "Aktion notwendig"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__active
#: model:ir.model.fields,field_description:product.field_product_attribute_value__active
#: model:ir.model.fields,field_description:product.field_product_document__active
#: model:ir.model.fields,field_description:product.field_product_pricelist__active
#: model:ir.model.fields,field_description:product.field_product_product__active
#: model:ir.model.fields,field_description:product.field_product_template__active
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__active
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__ptav_active
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_search
msgid "Active"
msgstr "Aktiv"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
msgid "Active Products"
msgstr "Aktive Produkte"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_ids
#: model:ir.model.fields,field_description:product.field_product_product__activity_ids
#: model:ir.model.fields,field_description:product.field_product_template__activity_ids
msgid "Activities"
msgstr "Aktivitäten"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_exception_decoration
#: model:ir.model.fields,field_description:product.field_product_product__activity_exception_decoration
#: model:ir.model.fields,field_description:product.field_product_template__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Aktivitätsausnahme-Dekoration"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_state
#: model:ir.model.fields,field_description:product.field_product_product__activity_state
#: model:ir.model.fields,field_description:product.field_product_template__activity_state
msgid "Activity State"
msgstr "Status der Aktivität"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_type_icon
#: model:ir.model.fields,field_description:product.field_product_product__activity_type_icon
#: model:ir.model.fields,field_description:product.field_product_template__activity_type_icon
msgid "Activity Type Icon"
msgstr "Symbol des Aktivitätstyps"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/order_line/order_line.xml:0
msgid "Add"
msgstr "Hinzufügen"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
msgid "Add Products"
msgstr "Produkte hinzufügen"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
msgid "Add Products to pricelist report"
msgstr "Produkte zum Preislistenbericht hinzufügen"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
msgid "Add a quantity"
msgstr "Menge hinzufügen"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
msgid ""
"Add products using the \"Add Products\" button at the top right to\n"
"                                include them in the report."
msgstr ""
"Fügen Sie Produkte hinzu, indem Sie oben rechts auf die Schaltfläche „Produkte hinzufügen“\n"
"                                klicken, um sie dem Bericht hinzuzufügen."

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute_value.py:0
msgid "Add to all products"
msgstr "Zu allen Produkten hinzufügen"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__update_product_attribute_value__mode__add
msgid "Add to existing products"
msgstr "Zu bestehenden Produkten hinzufügen"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
msgid "Add to products"
msgstr "Zu Produkten hinzufügen"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_search
msgid "All"
msgstr "Alle"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "All Categories"
msgstr "Alle Kategorien"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__all_product_tag_ids
msgid "All Product Tag"
msgstr "Alle Produkt-Stichwörter"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_tag__product_ids
msgid "All Product Variants using this Tag"
msgstr "Alle Produktvarianten, die dieses Stichwort verwenden"

#. module: product
#. odoo-javascript
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__applied_on__3_global
#: model_terms:ir.ui.view,arch_db:product.product_tag_form_view
msgid "All Products"
msgstr "Alle Produkte"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "All categories"
msgstr "Alle Kategorien"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_tree
msgid "All countries"
msgstr "Alle Länder"

#. module: product
#. odoo-python
#: code:addons/product/controllers/product_document.py:0
msgid "All files uploaded"
msgstr "Alle Dateien hochgeladen"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "All products"
msgstr "Alle Produkte"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view_from_product
msgid "All variants"
msgstr "Alle Varianten"

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_value__is_custom
#: model:ir.model.fields,help:product.field_product_template_attribute_value__is_custom
msgid "Allow customers to set their own value"
msgstr "Kunden erlauben, ihren eigenen Wert festzulegen"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_2
msgid "Aluminium"
msgstr "Aluminium"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view
msgid "Applied On"
msgstr "Angewendet auf"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__applied_on
msgid "Apply On"
msgstr "Anwenden auf"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Apply To"
msgstr "Anwenden auf"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Apply on"
msgstr "Anwenden auf"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
#: model_terms:ir.ui.view,arch_db:product.product_document_search
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Archived"
msgstr "Archiviert"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__sequence
msgid "Assigns the priority to the list of product vendor."
msgstr "Weist der Produkt-Lieferantenliste Prioritäten zu."

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
msgid ""
"At most %s quantities can be displayed simultaneously. Remove a selected "
"quantity to add others."
msgstr ""
"Es können maximal %s Mengen gleichzeitig angezeigt werden. Entfernen Sie "
"eine, um andere hinzuzufügen."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_form
msgid "Attached To"
msgstr "Angehängt an"

#. module: product
#: model:ir.model,name:product.model_ir_attachment
msgid "Attachment"
msgstr "Dateianhang"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__message_attachment_count
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_attachment_count
#: model:ir.model.fields,field_description:product.field_product_product__message_attachment_count
#: model:ir.model.fields,field_description:product.field_product_template__message_attachment_count
msgid "Attachment Count"
msgstr "Anzahl Anhänge"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__local_url
msgid "Attachment URL"
msgstr "URL des Dateianhangs"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__name
#: model:ir.model.fields,field_description:product.field_product_attribute_value__attribute_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__attribute_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__attribute_id
msgid "Attribute"
msgstr "Attribut"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__attribute_line_id
msgid "Attribute Line"
msgstr "Attributzeile"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_line_form
msgid "Attribute Name"
msgstr "Attributname"

#. module: product
#: model:ir.model,name:product.model_product_attribute_value
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__custom_product_template_attribute_value_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__product_template_attribute_value_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__product_attribute_value_id
#: model:ir.model.fields,field_description:product.field_update_product_attribute_value__attribute_value_id
msgid "Attribute Value"
msgstr "Attributwert"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_template_attribute_value_ids
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__value_ids
#: model_terms:ir.ui.view,arch_db:product.product_attribute_value_list
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
msgid "Attribute Values"
msgstr "Attributwerte"

#. module: product
#: model:ir.actions.act_window,name:product.attribute_action
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_tree
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Attributes"
msgstr "Attribute"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid "Attributes & Variants"
msgstr "Attribute & Varianten"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/kanban_controller.js:0
msgid "Back to Order"
msgstr "Zurück zum Auftrag"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/kanban_controller.js:0
msgid "Back to Quotation"
msgstr "Zurück zum Angebot"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__barcode
#: model:ir.model.fields,field_description:product.field_product_product__barcode
#: model:ir.model.fields,field_description:product.field_product_template__barcode
msgid "Barcode"
msgstr "Barcode"

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__barcode
msgid ""
"Barcode used for packaging identification. Scan this packaging barcode from "
"a transfer in the Barcode app to move all the contained units"
msgstr ""
"Barcode für die Identifizierung von Verpackungen. Scannen Sie diesen "
"Verpackungsbarcode von einem Transfer in der Barcode-App, um alle "
"enthaltenen Einheiten zu verschieben."

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
msgid ""
"Barcode(s) already assigned:\n"
"\n"
"%s"
msgstr ""
"Barcode(s) bereits zugeordnet:\n"
"\n"
"%s"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__base
msgid ""
"Base price for computation.\n"
"Sales Price: The base price will be the Sales Price.\n"
"Cost Price: The base price will be the cost price.\n"
"Other Pricelist: Computation of the base price based on another Pricelist."
msgstr ""
"Grundpreis für die Berechnung.\n"
"Verkaufspreis: Der Grundpreis wird der Verkaufspreis.\n"
"Einstandspreis: Der Grundpreis wird der Einstandspreis.\n"
"Andere Preisliste: Berechnung des Grundpreises auf der Grundlage einer anderen Preisliste."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__base
msgid "Based on"
msgstr "Basiert auf"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Based price"
msgstr "Grundpreis"

#. module: product
#: model:res.groups,name:product.group_product_pricelist
msgid "Basic Pricelists"
msgstr "Basispreisliste"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_4
msgid "Black"
msgstr "Schwarz"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/product_attribute_value_list.js:0
msgid "Bye-bye, record!"
msgstr "Tschau, Datensatz!"

#. module: product
#: model:product.template,name:product.product_product_10_product_template
msgid "Cabinet with Doors"
msgstr "Schrank mit Türen"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__can_image_1024_be_zoomed
#: model:ir.model.fields,field_description:product.field_product_template__can_image_1024_be_zoomed
msgid "Can Image 1024 be zoomed"
msgstr "Kann Bild 1024 gezoomt werden"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__can_image_variant_1024_be_zoomed
msgid "Can Variant Image 1024 be zoomed"
msgstr "Kann Varianten-Bild 1024 gezoomt werden"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.update_product_attribute_value_form
msgid "Cancel"
msgstr "Abbrechen"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__categ_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__display_applied_on__2_product_category
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Category"
msgstr "Kategorie"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "Category: %s"
msgstr "Kategorie: %s"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__checksum
msgid "Checksum/SHA1"
msgstr "Prüfsumme/SHA1"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__child_id
msgid "Child Categories"
msgstr "Unterkategorien"

#. module: product
#: model:ir.actions.act_window,name:product.action_open_label_layout
msgid "Choose Labels Layout"
msgstr "Etikettenlayout auswählen"

#. module: product
#: model:ir.model,name:product.model_product_label_layout
msgid "Choose the sheet layout to print the labels"
msgstr "Wählen Sie das Seitenlayout für den Druck der Etiketten"

#. module: product
#: model:product.attribute.value,name:product.pav_cleaning_kit
msgid "Cleaning kit"
msgstr "Reinigungskit"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Codes"
msgstr "Codes"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__html_color
#: model:ir.model.fields,field_description:product.field_product_tag__color
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__color
#: model:ir.model.fields.selection,name:product.selection__product_attribute__display_type__color
#: model:product.attribute,name:product.product_attribute_2
msgid "Color"
msgstr "Farbe"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__color
#: model:ir.model.fields,field_description:product.field_product_product__color
#: model:ir.model.fields,field_description:product.field_product_template__color
msgid "Color Index"
msgstr "Farbkennzeichnung"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__columns
msgid "Columns"
msgstr "Spalten"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__combination_indices
msgid "Combination Indices"
msgstr "Kombinationshinweise"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_combo_item__combo_id
#: model:ir.model.fields.selection,name:product.selection__product_template__type__combo
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Combo"
msgstr "Kombi"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_combo_view_form
msgid "Combo Choice"
msgstr "Kombi-Auswahl"

#. module: product
#: model:ir.actions.act_window,name:product.product_combo_action
#: model:ir.model.fields,field_description:product.field_product_product__combo_ids
#: model:ir.model.fields,field_description:product.field_product_template__combo_ids
#: model_terms:ir.ui.view,arch_db:product.product_combo_view_tree
msgid "Combo Choices"
msgstr "Kombi-Möglichkeiten"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_combo__combo_item_ids
msgid "Combo Item"
msgstr "Kombi-Artikel"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_combo__base_price
msgid "Combo Price"
msgstr "Kombi-Preis"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid "Combo products can't have attributes."
msgstr "Kombi-Produkte können keine Attribute enthalten."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid ""
"Combos allow to choose one product amongst a selection of choices per "
"category."
msgstr ""
"Kombis ermöglichen die Auswahl eines Produkts aus einer Auswahl pro "
"Kategorie."

#. module: product
#: model:ir.model,name:product.model_res_company
msgid "Companies"
msgstr "Unternehmen"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_combo__company_id
#: model:ir.model.fields,field_description:product.field_product_combo_item__company_id
#: model:ir.model.fields,field_description:product.field_product_document__company_id
#: model:ir.model.fields,field_description:product.field_product_packaging__company_id
#: model:ir.model.fields,field_description:product.field_product_pricelist__company_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__company_id
#: model:ir.model.fields,field_description:product.field_product_product__company_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__company_id
#: model:ir.model.fields,field_description:product.field_product_template__company_id
msgid "Company"
msgstr "Unternehmen"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Company Settings"
msgstr "Unternehmenseinstellungen"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__complete_name
msgid "Complete Name"
msgstr "Vollständiger Name"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__compute_price
msgid "Compute Price"
msgstr "Preis berechnen"

#. module: product
#: model:product.template,name:product.product_product_11_product_template
msgid "Conference Chair"
msgstr "Konferenzstuhl"

#. module: product
#: model:product.template,description_sale:product.consu_delivery_02_product_template
msgid "Conference room table"
msgstr "Konferenzraumtisch"

#. module: product
#: model:ir.model,name:product.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid "Configure"
msgstr "Konfigurieren"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_label_layout_form
#: model_terms:ir.ui.view,arch_db:product.update_product_attribute_value_form
msgid "Confirm"
msgstr "Bestätigen"

#. module: product
#: model:ir.model,name:product.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid "Contact Us"
msgstr "Kontaktieren Sie uns"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__qty
msgid "Contained Quantity"
msgstr "Enthaltene Menge"

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_packaging_positive_qty
msgid "Contained Quantity should be positive."
msgstr "Die enthaltene Menge sollte positiv sein."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
msgid "Contained quantity"
msgstr "Enthaltene Menge"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__uom_category_id
#: model:ir.model.fields,help:product.field_product_template__uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Die Umrechnung zwischen Maßeinheiten kann nur erfolgen, wenn sie derselben "
"Kategorie angehören. Die Umrechnung erfolgt auf Basis der Kennzahlen."

#. module: product
#: model:product.template,name:product.product_product_13_product_template
msgid "Corner Desk Left Sit"
msgstr "Eckschreibtisch mit Sitz auf der linken Seite"

#. module: product
#: model:product.template,name:product.product_product_5_product_template
msgid "Corner Desk Right Sit"
msgstr "Eckschreibtisch mit Sitz auf der rechten Seite"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__standard_price
#: model:ir.model.fields,field_description:product.field_product_template__standard_price
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__base__standard_price
msgid "Cost"
msgstr "Kosten"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__cost_currency_id
#: model:ir.model.fields,field_description:product.field_product_template__cost_currency_id
msgid "Cost Currency"
msgstr "Kostenwährung"

#. module: product
#: model:ir.model,name:product.model_res_country_group
msgid "Country Group"
msgstr "Ländergruppe"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__country_group_ids
msgid "Country Groups"
msgstr "Ländergruppen"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid "Create a new pricelist"
msgstr "Eine neue Preisliste erstellen"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_template_action
#: model_terms:ir.actions.act_window,help:product.product_template_action_all
msgid "Create a new product"
msgstr "Ein neues Produkt erstellen"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid "Create a new product variant"
msgstr "Eine neue Produktvariante erstellen"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/kanban_renderer.xml:0
msgid "Create a product"
msgstr "Ein Produkt erstellen"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__service_tracking
#: model:ir.model.fields,field_description:product.field_product_template__service_tracking
msgid "Create on Order"
msgstr "Bei Auftrag erstellen"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__create_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__create_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_value__create_uid
#: model:ir.model.fields,field_description:product.field_product_category__create_uid
#: model:ir.model.fields,field_description:product.field_product_combo__create_uid
#: model:ir.model.fields,field_description:product.field_product_combo_item__create_uid
#: model:ir.model.fields,field_description:product.field_product_document__create_uid
#: model:ir.model.fields,field_description:product.field_product_label_layout__create_uid
#: model:ir.model.fields,field_description:product.field_product_packaging__create_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist__create_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__create_uid
#: model:ir.model.fields,field_description:product.field_product_product__create_uid
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__create_uid
#: model:ir.model.fields,field_description:product.field_product_tag__create_uid
#: model:ir.model.fields,field_description:product.field_product_template__create_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__create_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__create_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__create_uid
#: model:ir.model.fields,field_description:product.field_update_product_attribute_value__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__create_date
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__create_date
#: model:ir.model.fields,field_description:product.field_product_attribute_value__create_date
#: model:ir.model.fields,field_description:product.field_product_category__create_date
#: model:ir.model.fields,field_description:product.field_product_combo__create_date
#: model:ir.model.fields,field_description:product.field_product_combo_item__create_date
#: model:ir.model.fields,field_description:product.field_product_document__create_date
#: model:ir.model.fields,field_description:product.field_product_label_layout__create_date
#: model:ir.model.fields,field_description:product.field_product_packaging__create_date
#: model:ir.model.fields,field_description:product.field_product_pricelist__create_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__create_date
#: model:ir.model.fields,field_description:product.field_product_product__create_date
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__create_date
#: model:ir.model.fields,field_description:product.field_product_tag__create_date
#: model:ir.model.fields,field_description:product.field_product_template__create_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__create_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__create_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__create_date
#: model:ir.model.fields,field_description:product.field_update_product_attribute_value__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_form
msgid "Creation"
msgstr "Erstellung"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_volume_volume_in_cubic_feet__1
msgid "Cubic Feet"
msgstr "Kubikfuß"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_volume_volume_in_cubic_feet__0
msgid "Cubic Meters"
msgstr "Kubikmeter"

#. module: product
#: model:ir.model,name:product.model_res_currency
#: model:ir.model.fields,field_description:product.field_product_combo__currency_id
#: model:ir.model.fields,field_description:product.field_product_combo_item__currency_id
#: model:ir.model.fields,field_description:product.field_product_pricelist__currency_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__currency_id
#: model:ir.model.fields,field_description:product.field_product_product__currency_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__currency_id
#: model:ir.model.fields,field_description:product.field_product_template__currency_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__currency_id
msgid "Currency"
msgstr "Währung"

#. module: product
#: model:product.attribute.value,name:product.fabric_attribute_custom
msgid "Custom"
msgstr "Benutzerdefiniert"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__custom_value
msgid "Custom Value"
msgstr "Benutzerdefinierter Wert"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__partner_ref
msgid "Customer Ref"
msgstr "Kundenref."

#. module: product
#: model:product.template,name:product.product_product_4_product_template
msgid "Customizable Desk"
msgstr "Anpassbarer Schreibtisch"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__db_datas
msgid "Database Data"
msgstr "Datenbankdaten"

#. module: product
#: model:ir.model,name:product.model_decimal_precision
msgid "Decimal Precision"
msgstr "Dezimalstellen"

#. module: product
#. odoo-python
#: code:addons/product/models/res_company.py:0
msgid "Default"
msgstr "Standard"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__default_extra_price
msgid "Default Extra Price"
msgstr "Standardsonderpreis"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__default_extra_price_changed
msgid "Default Extra Price Changed"
msgstr "Standardsonderpreis geändert"

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__product_uom_id
#: model:ir.model.fields,help:product.field_product_product__uom_id
#: model:ir.model.fields,help:product.field_product_template__uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "Standardmaßeinheit, die für alle Lagervorgänge verwendet wird."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__uom_po_id
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_uom
#: model:ir.model.fields,help:product.field_product_template__uom_po_id
msgid ""
"Default unit of measure used for purchase orders. It must be in the same "
"category as the default unit of measure."
msgstr ""
"Standardmaßeinheit, die für Bestellungen verwendet wird. Sie muss der "
"gleichen Kategorie angehören wie die Standardmaßeinheit."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_tag_action
msgid "Define a new tag"
msgstr "Ein neues Stichwort erstellen"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Define your volume unit of measure"
msgstr "Definieren Sie Ihre Volumeneinheit"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Define your weight unit of measure"
msgstr "Definieren Sie Ihre Gewichtsmaßeinheit"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/product_attribute_value_list.js:0
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
msgid "Delete"
msgstr "Löschen"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__delay
msgid "Delivery Lead Time"
msgstr "Liefervorlaufzeit"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__description
#: model:ir.model.fields,field_description:product.field_product_product__description
#: model:ir.model.fields,field_description:product.field_product_template__description
msgid "Description"
msgstr "Beschreibung"

#. module: product
#: model:product.template,name:product.product_product_3_product_template
msgid "Desk Combination"
msgstr "Schreibtischkombination"

#. module: product
#: model:product.template,name:product.desk_organizer_product_template
msgid "Desk Organizer"
msgstr "Schreibtisch-Organizer"

#. module: product
#: model:product.template,name:product.desk_pad_product_template
msgid "Desk Pad"
msgstr "Schreibtischunterlage"

#. module: product
#: model:product.template,name:product.product_product_22_product_template
msgid "Desk Stand with Screen"
msgstr "Schreibtischstand mit Leinwand"

#. module: product
#: model:product.template,description_sale:product.product_product_3_product_template
msgid "Desk combination, black-brown: chair + desk + drawer."
msgstr ""
"Schreibtischkombination, schwarzbraun: Stuhl + Schreibtisch + Schublade."

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute__sequence
#: model:ir.model.fields,help:product.field_product_attribute_value__sequence
msgid "Determine the display order"
msgstr "Anzeigereihenfolge festlegen"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_label_layout_form
msgid "Discard"
msgstr "Verwerfen"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__compute_price__percentage
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Discount"
msgstr "Rabatt"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__discount
msgid "Discount (%)"
msgstr "Rabatt (%)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__price_discounted
msgid "Discounted Price"
msgstr "Rabattierter Preis"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__display_applied_on
msgid "Display Applied On"
msgstr "„Angewendet auf“ anzeigen"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__display_name
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__display_name
#: model:ir.model.fields,field_description:product.field_product_attribute_value__display_name
#: model:ir.model.fields,field_description:product.field_product_category__display_name
#: model:ir.model.fields,field_description:product.field_product_combo__display_name
#: model:ir.model.fields,field_description:product.field_product_combo_item__display_name
#: model:ir.model.fields,field_description:product.field_product_document__display_name
#: model:ir.model.fields,field_description:product.field_product_label_layout__display_name
#: model:ir.model.fields,field_description:product.field_product_packaging__display_name
#: model:ir.model.fields,field_description:product.field_product_pricelist__display_name
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__display_name
#: model:ir.model.fields,field_description:product.field_product_product__display_name
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__display_name
#: model:ir.model.fields,field_description:product.field_product_tag__display_name
#: model:ir.model.fields,field_description:product.field_product_template__display_name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__display_name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__display_name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__display_name
#: model:ir.model.fields,field_description:product.field_update_product_attribute_value__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__display_type
#: model:ir.model.fields,field_description:product.field_product_attribute_value__display_type
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__display_type
msgid "Display Type"
msgstr "Anzeigetyp"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
msgid "Document"
msgstr "Dokument"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#: model:ir.model.fields,field_description:product.field_product_product__product_document_ids
#: model:ir.model.fields,field_description:product.field_product_template__product_document_ids
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Documents"
msgstr "Dokumente"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_document_count
#: model:ir.model.fields,field_description:product.field_product_template__product_document_count
msgid "Documents Count"
msgstr "Anzahl Dokumente"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_search
msgid "Documents of this variant"
msgstr "Dokumente dieser Variante"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
msgid "Download"
msgstr "Herunterladen"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid "Download examples"
msgstr "Beispiele herunterladen"

#. module: product
#: model:product.template,name:product.product_product_27_product_template
msgid "Drawer"
msgstr "Schublade"

#. module: product
#: model:product.template,name:product.product_product_16_product_template
msgid "Drawer Black"
msgstr "Schublade schwarz"

#. module: product
#: model_terms:product.template,description:product.product_product_27_product_template
msgid "Drawer with two routing possiblities."
msgstr "Schublade mit zwei Fräsmöglichkeiten."

#. module: product
#: model:product.attribute,name:product.product_attribute_3
msgid "Duration"
msgstr "Dauer"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__dymo
msgid "Dymo"
msgstr "Dymo"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__create_variant__dynamic
msgid "Dynamically"
msgstr "Dynamisch"

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_template_attribute_value_attribute_value_unique
msgid "Each value should be defined only once per attribute per product."
msgstr ""
"Jeder Wert sollte nur einmal pro Attribut und Produkt definiert werden."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "Eco-friendly Wooden Chair"
msgstr "Umweltfreundlicher Holzstuhl"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
#: model_terms:ir.ui.view,arch_db:product.product_view_kanban_catalog
msgid "Edit"
msgstr "Bearbeiten"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__date_end
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__date_end
msgid "End Date"
msgstr "Enddatum"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__date_end
msgid "End date for this vendor price"
msgstr "Enddatum dieses Lieferantenpreises"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__date_end
msgid ""
"Ending datetime for the pricelist item validation\n"
"The displayed value depends on the timezone set in your preferences."
msgstr ""
"Endzeitpunkt für die Validierung der Preislistenposition\n"
"Der angezeigte Wert hängt von der in Ihren Einstellungen festgelegten Zeitzone ab."

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid "Ergonomic"
msgstr "Ergonomisch"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
msgid "Error exporting file. Please try again."
msgstr "Fehler beim Datenexport. Bitte versuchen Sie es später erneut."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__exclude_for
msgid "Exclude for"
msgstr "Ausschließen für"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__name
#: model:ir.model.fields,help:product.field_product_pricelist_item__price
msgid "Explicit rule name for this pricelist line."
msgstr "Berechnungsregel für diese Position der Preisliste"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__extra_html
msgid "Extra Content"
msgstr "Zusätzlicher Inhalt"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_surcharge
msgid "Extra Fee"
msgstr "Zusatzgebühr"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Extra Info"
msgstr "Weitere Informationen"

#. module: product
#: model:product.attribute,name:product.pa_extra_options
msgid "Extra Options"
msgstr "Zusatzoptionen"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_combo_item__extra_price
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__price_extra
msgid "Extra Price"
msgstr "Sonderpreis"

#. module: product
#: model:ir.model.fields,help:product.field_product_template_attribute_value__price_extra
msgid ""
"Extra price for the variant with this attribute value on sale price. eg. 200"
" price extra, 1000 + 200 = 1200."
msgstr ""
"Sonderpreis für die Variante mit diesem Attributwert zum Verkaufspreis. z. "
"B. 200 Preis extra, 1000 + 200 = 1200."

#. module: product
#: model:product.attribute,name:product.fabric_attribute
msgid "Fabric"
msgstr "Stoff"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__is_favorite
#: model:ir.model.fields,field_description:product.field_product_template__is_favorite
msgid "Favorite"
msgstr "Favorit"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Favorites"
msgstr "Favoriten"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__datas
msgid "File Content (base64)"
msgstr "Datei-Inhalt (base64)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__raw
msgid "File Content (raw)"
msgstr "Datei-Inhalt (roh)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__file_size
msgid "File Size"
msgstr "Dateigröße"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__fixed_price
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__compute_price__fixed
msgid "Fixed Price"
msgstr "Festpreis"

#. module: product
#: model:product.template,name:product.product_product_20_product_template
msgid "Flipover"
msgstr "Flipover"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__message_follower_ids
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_follower_ids
#: model:ir.model.fields,field_description:product.field_product_product__message_follower_ids
#: model:ir.model.fields,field_description:product.field_product_template__message_follower_ids
msgid "Followers"
msgstr "Follower"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__message_partner_ids
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_partner_ids
#: model:ir.model.fields,field_description:product.field_product_product__message_partner_ids
#: model:ir.model.fields,field_description:product.field_product_template__message_partner_ids
msgid "Followers (Partners)"
msgstr "Follower (Partner)"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__activity_type_icon
#: model:ir.model.fields,help:product.field_product_product__activity_type_icon
#: model:ir.model.fields,help:product.field_product_template__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "FontAwesome-Icon, z. B. fa-tasks"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__min_quantity
msgid ""
"For the rule to apply, bought/sold quantity must be greater than or equal to the minimum quantity specified in this field.\n"
"Expressed in the default unit of measure of the product."
msgstr ""
"Damit diese Regel zutrifft, muss die gekaufte/verkaufte Menge größer oder gleich der hier angegebenen Menge sein.\n"
"Angegeben in der Standardmaßeinheit des Produkts."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__print_format
msgid "Format"
msgstr "Format"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__compute_price__formula
msgid "Formula"
msgstr "Formel"

#. module: product
#: model:product.template,name:product.consu_delivery_03_product_template
msgid "Four Person Desk"
msgstr "Schreibtisch für vier Personen"

#. module: product
#: model:product.template,description_sale:product.consu_delivery_03_product_template
msgid "Four person modern office workstation"
msgstr "Moderner Büroarbeitsplatz für vier Personen"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__is_custom
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__is_custom
msgid "Free text"
msgstr "Freitext"

#. module: product
#: model:product.template,name:product.product_product_furniture_product_template
msgid "Furniture Assembly"
msgstr "Möbelaufbau"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Future Activities"
msgstr "Anstehende Aktivitäten"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "General Information"
msgstr "Allgemeine Informationen"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Get product pictures using Barcode"
msgstr "Produktbilder mit Barcode abrufen"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__packaging_ids
#: model:ir.model.fields,help:product.field_product_template__packaging_ids
msgid "Gives the different ways to package the same product."
msgstr ""
"Stellt verschiedene Möglichkeiten bereit, dasselbe Produkt zu verpacken."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__sequence
#: model:ir.model.fields,help:product.field_product_template__sequence
msgid "Gives the sequence order when displaying a product list"
msgstr "Reihenfolge bei Anzeige einer Produktliste"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Gold Member Pricelist"
msgstr "Preisliste für Gold-Mitglieder"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_template__type__consu
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Goods"
msgstr "Güter"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__type
#: model:ir.model.fields,help:product.field_product_template__type
msgid ""
"Goods are tangible materials and merchandise you provide.\n"
"A service is a non-material product you provide."
msgstr ""
"Waren sind materielle Güter und Handelswaren, die Sie bereitstellen.\n"
"Eine Dienstleistung ist ein immaterielles Produkt, das Sie bereitstellen."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Google Images"
msgstr "Google Bilder"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Group By"
msgstr "Gruppieren nach"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__html_color
msgid "HTML Color Index"
msgstr "HTML-Farbkennzeichnung"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__has_message
#: model:ir.model.fields,field_description:product.field_product_pricelist__has_message
#: model:ir.model.fields,field_description:product.field_product_product__has_message
#: model:ir.model.fields,field_description:product.field_product_template__has_message
msgid "Has Message"
msgstr "Hat eine Nachricht"

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_value__html_color
#: model:ir.model.fields,help:product.field_product_template_attribute_value__html_color
msgid ""
"Here you can set a specific HTML color index (e.g. #ff0000) to display the "
"color if the attribute type is 'Color'."
msgstr ""
"Hier können Sie eine bestimmte HTML-Farbkennzeichnung festlegen (z. B. "
"#ff0000), um die Farbe anzuzeigen, wenn das Attribut vom Typ „Farbe“ ist."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_form
msgid "History"
msgstr "Historie"

#. module: product
#: model:product.template,name:product.expense_hotel_product_template
msgid "Hotel Accommodation"
msgstr "Übernachtungskosten"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__id
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__id
#: model:ir.model.fields,field_description:product.field_product_attribute_value__id
#: model:ir.model.fields,field_description:product.field_product_category__id
#: model:ir.model.fields,field_description:product.field_product_combo__id
#: model:ir.model.fields,field_description:product.field_product_combo_item__id
#: model:ir.model.fields,field_description:product.field_product_document__id
#: model:ir.model.fields,field_description:product.field_product_label_layout__id
#: model:ir.model.fields,field_description:product.field_product_packaging__id
#: model:ir.model.fields,field_description:product.field_product_pricelist__id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__id
#: model:ir.model.fields,field_description:product.field_product_product__id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__id
#: model:ir.model.fields,field_description:product.field_product_tag__id
#: model:ir.model.fields,field_description:product.field_product_template__id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__id
#: model:ir.model.fields,field_description:product.field_update_product_attribute_value__id
msgid "ID"
msgstr "ID"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_exception_icon
#: model:ir.model.fields,field_description:product.field_product_product__activity_exception_icon
#: model:ir.model.fields,field_description:product.field_product_template__activity_exception_icon
msgid "Icon"
msgstr "Icon"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__activity_exception_icon
#: model:ir.model.fields,help:product.field_product_product__activity_exception_icon
#: model:ir.model.fields,help:product.field_product_template__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icon, um eine Ausnahmeaktivität anzuzeigen."

#. module: product
#: model:ir.model.fields,help:product.field_product_category__message_needaction
#: model:ir.model.fields,help:product.field_product_pricelist__message_needaction
#: model:ir.model.fields,help:product.field_product_product__message_needaction
#: model:ir.model.fields,help:product.field_product_template__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Falls markiert, erfordern neue Nachrichten Ihre Aufmerksamkeit."

#. module: product
#: model:ir.model.fields,help:product.field_product_category__message_has_error
#: model:ir.model.fields,help:product.field_product_pricelist__message_has_error
#: model:ir.model.fields,help:product.field_product_product__message_has_error
#: model:ir.model.fields,help:product.field_product_template__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Falls markiert, weisen einige Nachrichten einen Zustellungsfehler auf."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_id
msgid ""
"If not set, the vendor price will apply to all variants of this product."
msgstr ""
"Wenn nicht eingestellt, gilt der Lieferantenpreis für alle Varianten dieses "
"Produkts."

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute__active
msgid ""
"If unchecked, it will allow you to hide the attribute without removing it."
msgstr ""
"Wenn diese Option nicht markiert ist, können Sie das Attribut ausblenden, "
"ohne es zu entfernen."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__active
msgid ""
"If unchecked, it will allow you to hide the pricelist without removing it."
msgstr ""
"Bei Deaktivierung können Sie Preislisten verbergen, ohne diese zu entfernen."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__active
#: model:ir.model.fields,help:product.field_product_template__active
msgid ""
"If unchecked, it will allow you to hide the product without removing it."
msgstr ""
"Wenn diese Option nicht markiert ist, können Sie das Produkt ausblenden, "
"ohne es zu entfernen."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__image
#: model:ir.model.fields,field_description:product.field_product_product__image_1920
#: model:ir.model.fields,field_description:product.field_product_template__image_1920
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__image
msgid "Image"
msgstr "Bild"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_1024
#: model:ir.model.fields,field_description:product.field_product_template__image_1024
msgid "Image 1024"
msgstr "Bild 1024"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_128
#: model:ir.model.fields,field_description:product.field_product_template__image_128
msgid "Image 128"
msgstr "Bild 128"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_256
#: model:ir.model.fields,field_description:product.field_product_template__image_256
msgid "Image 256"
msgstr "Bild 256"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_512
#: model:ir.model.fields,field_description:product.field_product_template__image_512
msgid "Image 512"
msgstr "Bild 512"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__image_height
msgid "Image Height"
msgstr "Bildhöhe"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__image_src
msgid "Image Src"
msgstr "Bildquelle"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__image_width
msgid "Image Width"
msgstr "Bildbreite"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
msgid "Image is a link"
msgstr "Bild ist ein Link"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist.py:0
msgid "Import Template for Pricelists"
msgstr "Importvorlage für Preislisten"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid "Import Template for Products"
msgstr "Importvorlage für Produkte"

#. module: product
#. odoo-python
#: code:addons/product/models/product_supplierinfo.py:0
msgid "Import Template for Vendor Pricelists"
msgstr "Importvorlage für Lieferantenpreislisten"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_attribute_search
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_search
msgid "Inactive"
msgstr "Inaktiv"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__index_content
msgid "Indexed Content"
msgstr "Inhaltsindizierung"

#. module: product
#: model:product.template,name:product.product_product_24_product_template
msgid "Individual Workplace"
msgstr "Individueller Arbeitsplatz"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__create_variant__always
msgid "Instantly"
msgstr "Sofort"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Internal Notes"
msgstr "Interne Notizen"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__default_code
#: model:ir.model.fields,field_description:product.field_product_template__default_code
msgid "Internal Reference"
msgstr "Interne Referenz"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__barcode
msgid "International Article Number used for product identification."
msgstr "Internationale Artikelnummer zur Produktidentifikation"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/product_attribute_value_list.js:0
msgid "Invalid Operation"
msgstr "Ungültiger Vorgang"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Inventory"
msgstr "Lager"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__message_is_follower
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_is_follower
#: model:ir.model.fields,field_description:product.field_product_product__message_is_follower
#: model:ir.model.fields,field_description:product.field_product_template__message_is_follower
msgid "Is Follower"
msgstr "Ist Follower"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__is_product_variant
msgid "Is Product Variant"
msgstr "Ist Produktvariante"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__has_configurable_attributes
#: model:ir.model.fields,field_description:product.field_product_template__has_configurable_attributes
msgid "Is a configurable product"
msgstr "Ist ein konfigurierbares Produkt"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template__is_product_variant
msgid "Is a product variant"
msgstr "Ist eine Produktvariante"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__public
msgid "Is public document"
msgstr "Ist öffentliches Dokument"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_weight_in_lbs__0
msgid "Kilograms"
msgstr "Kilogramm"

#. module: product
#: model:product.template,name:product.product_product_6_product_template
msgid "Large Cabinet"
msgstr "Großer Schrank"

#. module: product
#: model:product.template,name:product.product_product_8_product_template
msgid "Large Desk"
msgstr "Großer Schreibtisch"

#. module: product
#: model:product.template,name:product.consu_delivery_02_product_template
msgid "Large Meeting Table"
msgstr "Großer Besprechungstisch"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__write_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__write_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_value__write_uid
#: model:ir.model.fields,field_description:product.field_product_category__write_uid
#: model:ir.model.fields,field_description:product.field_product_combo__write_uid
#: model:ir.model.fields,field_description:product.field_product_combo_item__write_uid
#: model:ir.model.fields,field_description:product.field_product_document__write_uid
#: model:ir.model.fields,field_description:product.field_product_label_layout__write_uid
#: model:ir.model.fields,field_description:product.field_product_packaging__write_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist__write_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__write_uid
#: model:ir.model.fields,field_description:product.field_product_product__write_uid
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__write_uid
#: model:ir.model.fields,field_description:product.field_product_tag__write_uid
#: model:ir.model.fields,field_description:product.field_product_template__write_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__write_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__write_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__write_uid
#: model:ir.model.fields,field_description:product.field_update_product_attribute_value__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__write_date
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__write_date
#: model:ir.model.fields,field_description:product.field_product_attribute_value__write_date
#: model:ir.model.fields,field_description:product.field_product_category__write_date
#: model:ir.model.fields,field_description:product.field_product_combo__write_date
#: model:ir.model.fields,field_description:product.field_product_combo_item__write_date
#: model:ir.model.fields,field_description:product.field_product_document__write_date
#: model:ir.model.fields,field_description:product.field_product_label_layout__write_date
#: model:ir.model.fields,field_description:product.field_product_packaging__write_date
#: model:ir.model.fields,field_description:product.field_product_pricelist__write_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__write_date
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__write_date
#: model:ir.model.fields,field_description:product.field_product_tag__write_date
#: model:ir.model.fields,field_description:product.field_product_template__write_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__write_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__write_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__write_date
#: model:ir.model.fields,field_description:product.field_update_product_attribute_value__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Late Activities"
msgstr "Verspätete Aktivitäten"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__delay
msgid ""
"Lead time in days between the confirmation of the purchase order and the "
"receipt of the products in your warehouse. Used by the scheduler for "
"automatic computation of the purchase order planning."
msgstr ""
"Vorlaufzeit in Tagen zwischen der Bestellbestätigung und dem Eingang der "
"Produkte in Ihrem Lager. Wird vom Zeitplaner für die automatische Berechnung"
" der Planung von Bestellungen verwendet."

#. module: product
#: model:product.attribute.value,name:product.fabric_attribute_leather
msgid "Leather"
msgstr "Leder"

#. module: product
#: model:product.attribute,name:product.product_attribute_1
msgid "Legs"
msgstr "Tischbeine"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__attribute_line_ids
#: model:ir.model.fields,field_description:product.field_product_attribute_value__pav_attribute_line_ids
msgid "Lines"
msgstr "Zeilen"

#. module: product
#: model:product.template,name:product.product_product_local_delivery_product_template
msgid "Local Delivery"
msgstr "Lokale Lieferung"

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid "Locally handmade"
msgstr "Handgefertigt in der Region"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Logistics"
msgstr "Logistik"

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid ""
"Looking for a custom bamboo stain to match existing furniture? Contact us "
"for a quote."
msgstr ""
"Suchen Sie eine individuelle Bambuslasur, die zu Ihren Möbeln passt? "
"Kontaktieren Sie uns für ein Angebot."

#. module: product
#: model:ir.model.fields,help:product.field_product_template_attribute_value__exclude_for
msgid ""
"Make this attribute value not compatible with other values of the product or"
" some attribute values of optional and accessory products."
msgstr ""
"Machen Sie diesen Attributwert nicht kompatibel mit anderen Werten des "
"Produkts oder einigen Attributwerten von optionalen und zusätzlichen "
"Produkten."

#. module: product
#: model:res.groups,name:product.group_stock_packaging
msgid "Manage Product Packaging"
msgstr "Produktverpackungen verwalten"

#. module: product
#: model:res.groups,name:product.group_product_variant
msgid "Manage Product Variants"
msgstr "Produktvarianten verwalten"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Margins"
msgstr "Margen"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_markup
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Markup"
msgstr "Preisaufschlag"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Max. Margin"
msgstr "Max. Marge"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_max_margin
msgid "Max. Price Margin"
msgstr "Max. Preismarge"

#. module: product
#: model:ir.model.fields,field_description:product.field_update_product_attribute_value__message
msgid "Message"
msgstr "Nachricht"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__message_has_error
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_has_error
#: model:ir.model.fields,field_description:product.field_product_product__message_has_error
#: model:ir.model.fields,field_description:product.field_product_template__message_has_error
msgid "Message Delivery error"
msgstr "Nachricht mit Zustellungsfehler"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__message_ids
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_ids
#: model:ir.model.fields,field_description:product.field_product_product__message_ids
#: model:ir.model.fields,field_description:product.field_product_template__message_ids
msgid "Messages"
msgstr "Nachrichten"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__mimetype
msgid "Mime Type"
msgstr "Mime-Typ"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Min Qty"
msgstr "Min. Menge"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Min. Margin"
msgstr "Min. Marge"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_min_margin
msgid "Min. Price Margin"
msgstr "Min. Preismarge"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__min_quantity
msgid "Min. Quantity"
msgstr "Min. Menge"

#. module: product
#: model:ir.model.fields,field_description:product.field_update_product_attribute_value__mode
msgid "Mode"
msgstr "Modus"

#. module: product
#: model:product.template,name:product.monitor_stand_product_template
msgid "Monitor Stand"
msgstr "Monitorständer"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__display_type__multi
msgid "Multi-checkbox"
msgstr "Mehrere Kontrollkästchen"

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_attribute_check_multi_checkbox_no_variant
msgid ""
"Multi-checkbox display type is not compatible with the creation of variants"
msgstr ""
"Der Anzeigetyp des Mehrfachkontrollkästchens ist nicht mit der Erstellung "
"von Varianten kompatibel."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__my_activity_date_deadline
#: model:ir.model.fields,field_description:product.field_product_product__my_activity_date_deadline
#: model:ir.model.fields,field_description:product.field_product_template__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Frist für meine Aktivitäten"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__name
#: model:ir.model.fields,field_description:product.field_product_category__name
#: model:ir.model.fields,field_description:product.field_product_combo__name
#: model:ir.model.fields,field_description:product.field_product_document__name
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__name
#: model:ir.model.fields,field_description:product.field_product_product__name
#: model:ir.model.fields,field_description:product.field_product_tag__name
#: model:ir.model.fields,field_description:product.field_product_template__name
msgid "Name"
msgstr "Name"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__create_variant__no_variant
msgid "Never"
msgstr "Niemals"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist.py:0
msgid "New"
msgstr "Neu"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_calendar_event_id
#: model:ir.model.fields,field_description:product.field_product_product__activity_calendar_event_id
#: model:ir.model.fields,field_description:product.field_product_template__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Nächstes Aktivitätskalenderereignis"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_date_deadline
#: model:ir.model.fields,field_description:product.field_product_product__activity_date_deadline
#: model:ir.model.fields,field_description:product.field_product_template__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Nächste Aktivitätsfrist"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_summary
#: model:ir.model.fields,field_description:product.field_product_product__activity_summary
#: model:ir.model.fields,field_description:product.field_product_template__activity_summary
msgid "Next Activity Summary"
msgstr "Zusammenfassung der nächsten Aktivität"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_type_id
#: model:ir.model.fields,field_description:product.field_product_product__activity_type_id
#: model:ir.model.fields,field_description:product.field_product_template__activity_type_id
msgid "Next Activity Type"
msgstr "Nächster Aktivitätstyp"

#. module: product
#. odoo-python
#: code:addons/product/wizard/product_label_layout.py:0
msgid ""
"No product to print, if the product is archived please unarchive it before "
"printing its label."
msgstr ""
"Kein zu druckendes Produkt, wenn das Produkt archiviert ist, deaktivieren "
"Sie es bitte, bevor Sie sein Etikett drucken."

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/kanban_renderer.xml:0
msgid "No products could be found."
msgstr "Es konnten keine Produkte gefunden werden."

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
msgid "No products found in the report"
msgstr "Kein Produkte im Bericht gefunden"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_supplierinfo_type_action
msgid "No vendor pricelist found"
msgstr "Keine Lieferantenpreisliste gefunden"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/product_attribute_value_list.js:0
msgid "No, keep it"
msgstr "Nein, behalten!"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
#: code:addons/product/models/product_template.py:0
msgid "Note:"
msgstr "Notiz:"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_template__service_tracking__no
msgid "Nothing"
msgstr "Nichts"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__number_related_products
msgid "Number Related Products"
msgstr "Anzahl verknüpfter Produkte"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__message_needaction_counter
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_needaction_counter
#: model:ir.model.fields,field_description:product.field_product_product__message_needaction_counter
#: model:ir.model.fields,field_description:product.field_product_template__message_needaction_counter
msgid "Number of Actions"
msgstr "Anzahl der Aktionen"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__message_has_error_counter
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_has_error_counter
#: model:ir.model.fields,field_description:product.field_product_product__message_has_error_counter
#: model:ir.model.fields,field_description:product.field_product_template__message_has_error_counter
msgid "Number of errors"
msgstr "Anzahl der Fehler"

#. module: product
#: model:ir.model.fields,help:product.field_product_category__message_needaction_counter
#: model:ir.model.fields,help:product.field_product_pricelist__message_needaction_counter
#: model:ir.model.fields,help:product.field_product_product__message_needaction_counter
#: model:ir.model.fields,help:product.field_product_template__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Anzahl der Nachrichten, die eine Aktion erfordern"

#. module: product
#: model:ir.model.fields,help:product.field_product_category__message_has_error_counter
#: model:ir.model.fields,help:product.field_product_pricelist__message_has_error_counter
#: model:ir.model.fields,help:product.field_product_product__message_has_error_counter
#: model:ir.model.fields,help:product.field_product_template__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Anzahl der Nachrichten mit Zustellungsfehler."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__pricelist_item_count
#: model:ir.model.fields,field_description:product.field_product_template__pricelist_item_count
msgid "Number of price rules"
msgstr "Anzahl der Preisregeln"

#. module: product
#: model:product.template,name:product.product_delivery_01_product_template
msgid "Office Chair"
msgstr "Bürostuhl"

#. module: product
#: model:product.template,name:product.product_product_12_product_template
msgid "Office Chair Black"
msgstr "Schwarzer Bürostuhl"

#. module: product
#: model:product.template,name:product.office_combo_product_template
msgid "Office Combo"
msgstr "Büro-Kombi"

#. module: product
#: model:product.template,name:product.product_order_01_product_template
msgid "Office Design Software"
msgstr "Büro-Design-Software"

#. module: product
#: model:product.template,name:product.product_delivery_02_product_template
msgid "Office Lamp"
msgstr "Bürolampe"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_line.py:0
msgid ""
"On the product %(product)s you cannot associate the value %(value)s with the"
" attribute %(attribute)s because they do not match."
msgstr ""
"Auf dem Produkt %(product)s können Sie den Wert %(value)s nicht mit dem "
"Attribut %(attribute)s verknüpfen, da sie nicht kompatibel sind."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_line.py:0
msgid ""
"On the product %(product)s you cannot transform the attribute "
"%(attribute_src)s into the attribute %(attribute_dest)s."
msgstr ""
"Für das Produkt %(product)s können Sie das Attribut %(attribute_src)s nicht "
"in das Attribut %(attribute_dest)s umwandeln."

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/product_document_kanban/upload_button/upload_button.js:0
msgid "Oops! '%(fileName)s' didn’t upload since its format isn’t allowed."
msgstr ""
"Ups! „%(fileName)s“ konnte nicht hochgeladen werden, da ihr Format nicht "
"erlaubt ist."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__original_id
msgid "Original (unoptimized, unresized) attachment"
msgstr "Originaler (nicht optimierter, nicht verkleinerter) Anhang"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_combo_item__lst_price
msgid "Original Price"
msgstr "Ursprünglicher Preis"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__base_pricelist_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__base__pricelist
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Other Pricelist"
msgstr "Andere Preisliste"

#. module: product
#: model:product.template,name:product.product_template_dining_table
msgid "Outdoor dining table"
msgstr "Esstisch für draußen"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "Package Type A"
msgstr "Pakettyp A"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
#: model_terms:ir.ui.view,arch_db:product.product_packaging_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Packaging"
msgstr "Verpackung"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__parent_id
msgid "Parent Category"
msgstr "Oberkategorie"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__parent_path
msgid "Parent Path"
msgstr "Übergeordneter Pfad"

#. module: product
#: model:product.template,name:product.product_product_9_product_template
msgid "Pedal Bin"
msgstr "Treteimer"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__percent_price
msgid "Percentage Price"
msgstr "Prozentualer Preis"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__display_type__pills
msgid "Pills"
msgstr "Viereck"

#. module: product
#: model:product.attribute.value,name:product.fabric_attribute_plastic
msgid "Plastic"
msgstr "Plastik"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
msgid "Please enter a positive whole number."
msgstr "Bitte geben Sie eine positive ganze Zahl ein"

#. module: product
#. odoo-python
#: code:addons/product/models/product_document.py:0
msgid ""
"Please enter a valid URL.\n"
"Example: https://www.odoo.com\n"
"\n"
"Invalid URL: %s"
msgstr ""
"Bitte geben Sie eine gültige URL ein.\n"
"Beispiel: https://www.odoo.com\n"
"\n"
"Ungültige URL: %s"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
msgid "Please select some products first."
msgstr "Bitte wählen Sie zuerst einige Produkte aus."

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "Please specify the category for which this rule should be applied"
msgstr ""
"Bitte geben Sie die Kategorie an, auf die diese Regel angewendet werden "
"soll."

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "Please specify the product for which this rule should be applied"
msgstr ""
"Bitte geben Sie das Produkt an, auf das diese Regel angewendet werden soll."

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid ""
"Please specify the product variant for which this rule should be applied"
msgstr ""
"Bitte geben Sie die Produktvariante an, für die diese Regel angewendet "
"werden soll."

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_weight_in_lbs__1
msgid "Pounds"
msgstr "Pfund"

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid ""
"Press a button and watch your desk glide effortlessly from sitting to "
"standing height in seconds."
msgstr ""
"Drücken Sie einen Knopf und beobachten Sie, wie Ihr Schreibtisch in "
"Sekundenschnelle mühelos von Sitz- auf Stehhöhe gleitet."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__price
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view_from_product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
msgid "Price"
msgstr "Preis"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_discount
msgid "Price Discount"
msgstr "Preisrabatt"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_round
msgid "Price Rounding"
msgstr "Preisrundung"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
#: code:addons/product/models/product_template.py:0
#: model:ir.actions.act_window,name:product.product_pricelist_item_action
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Price Rules"
msgstr "Preisregeln"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Price Type"
msgstr "Preistyp"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__list_price
#: model:ir.model.fields,help:product.field_product_template__list_price
msgid "Price at which the product is sold to customers."
msgstr "Preis, zu dem das Produkt an den Kunden verkauft wird."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
msgid "Price:"
msgstr "Preis:"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
#: model:ir.actions.report,name:product.action_report_pricelist
#: model:ir.model,name:product.model_product_pricelist
#: model:ir.model.fields,field_description:product.field_product_label_layout__pricelist_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__pricelist_id
#: model:ir.model.fields,field_description:product.field_res_partner__property_product_pricelist
#: model:ir.model.fields,field_description:product.field_res_users__property_product_pricelist
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view_from_product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Pricelist"
msgstr "Preisliste"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__applied_on
#: model:ir.model.fields,help:product.field_product_pricelist_item__display_applied_on
msgid "Pricelist Item applicable on selected option"
msgstr "Preislistenposition ist für ausgewählte Option anwendbar"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__name
msgid "Pricelist Name"
msgstr "Preislistenname"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
#: model:ir.actions.server,name:product.action_product_price_list_report
#: model:ir.actions.server,name:product.action_product_template_price_list_report
#: model:ir.model,name:product.model_report_product_report_pricelist
msgid "Pricelist Report"
msgstr "Preislistenbericht"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist.py:0
msgid "Pricelist Report Preview"
msgstr "Übersicht des Preislistenberichts"

#. module: product
#: model:ir.model,name:product.model_product_pricelist_item
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Pricelist Rule"
msgstr "Preislistenregel"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__item_ids
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view_from_product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Pricelist Rules"
msgstr "Preislistenregeln"

#. module: product
#: model:ir.actions.act_window,name:product.product_pricelist_action2
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_product_pricelist
#: model:ir.model.fields,field_description:product.field_res_country_group__pricelist_ids
msgid "Pricelists"
msgstr "Preislisten"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_partner_property_form
msgid "Pricelists are managed on"
msgstr "Preislisten werden verwaltet vom"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Pricing"
msgstr "Preiskalkulation"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Print"
msgstr "Drucken"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Print Labels"
msgstr "Etiketten drucken"

#. module: product
#. odoo-python
#: code:addons/product/controllers/pricelist_report.py:0
#: model:ir.model,name:product.model_product_template
#: model:ir.model.fields,field_description:product.field_product_combo_item__product_id
#: model:ir.model.fields,field_description:product.field_product_label_layout__product_ids
#: model:ir.model.fields,field_description:product.field_product_packaging__product_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_product__product_variant_id
#: model:ir.model.fields,field_description:product.field_product_template__product_variant_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__applied_on__1_product
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__display_applied_on__1_product
#: model_terms:ir.ui.view,arch_db:product.product_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_packaging_search_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
#: model_terms:ir.ui.view,arch_db:product.product_search_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_tree
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_view_kanban_catalog
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Product"
msgstr "Produkt"

#. module: product
#: model:ir.model,name:product.model_product_attribute
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_form
msgid "Product Attribute"
msgstr "Produktattribut"

#. module: product
#: model:ir.model,name:product.model_product_attribute_custom_value
msgid "Product Attribute Custom Value"
msgstr "Produktattribut Benutzerdefinierter Wert"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__product_template_value_ids
msgid "Product Attribute Values"
msgstr "Attributwerte des Produkts"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_line_form
msgid "Product Attribute and Values"
msgstr "Produktattribut und -werte"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__attribute_line_ids
#: model:ir.model.fields,field_description:product.field_product_template__attribute_line_ids
msgid "Product Attributes"
msgstr "Produktattribute"

#. module: product
#: model:ir.model,name:product.model_product_catalog_mixin
msgid "Product Catalog Mixin"
msgstr "Mixin des Produktkatalogs"

#. module: product
#: model:ir.actions.act_window,name:product.product_category_action_form
#: model_terms:ir.ui.view,arch_db:product.product_category_list_view
#: model_terms:ir.ui.view,arch_db:product.product_category_search_view
msgid "Product Categories"
msgstr "Produktkategorien"

#. module: product
#: model:ir.model,name:product.model_product_category
#: model:ir.model.fields,field_description:product.field_product_product__categ_id
#: model:ir.model.fields,field_description:product.field_product_template__categ_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__applied_on__2_product_category
#: model_terms:ir.ui.view,arch_db:product.product_category_list_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Product Category"
msgstr "Produktkategorie"

#. module: product
#: model:ir.model,name:product.model_product_combo
msgid "Product Combo"
msgstr "Produktkombi"

#. module: product
#: model:ir.model,name:product.model_product_combo_item
msgid "Product Combo Item"
msgstr "Produkt-Kombi-Artikel"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_combo__combo_item_count
#: model:ir.model.fields,field_description:product.field_update_product_attribute_value__product_count
msgid "Product Count"
msgstr "Produktanzahl"

#. module: product
#: model:res.groups,name:product.group_product_manager
msgid "Product Creation"
msgstr "Produkterstellung"

#. module: product
#: model:ir.model,name:product.model_product_document
msgid "Product Document"
msgstr "Produktdokument"

#. module: product
#: model:ir.actions.report,name:product.report_product_template_label_dymo
msgid "Product Label (PDF)"
msgstr "Produktetikett (PDF)"

#. module: product
#: model:ir.actions.report,name:product.report_product_template_label_2x7
msgid "Product Label 2x7 (PDF)"
msgstr "Produktetikett 2x7 (PDF)"

#. module: product
#: model:ir.actions.report,name:product.report_product_template_label_4x12
msgid "Product Label 4x12 (PDF)"
msgstr "Produktetikett 4x12 (PDF)"

#. module: product
#: model:ir.actions.report,name:product.report_product_template_label_4x12_noprice
msgid "Product Label 4x12 No Price (PDF)"
msgstr "Produktetikett 4x12 Kein Preis (PDF)"

#. module: product
#: model:ir.actions.report,name:product.report_product_template_label_4x7
msgid "Product Label 4x7 (PDF)"
msgstr "Produktetikett 4x7 (PDF)"

#. module: product
#: model:ir.model,name:product.model_report_product_report_producttemplatelabel_dymo
msgid "Product Label Report"
msgstr "Produktetikettenbericht"

#. module: product
#: model:ir.model,name:product.model_report_product_report_producttemplatelabel2x7
msgid "Product Label Report 2x7"
msgstr "Produktetikettenbericht 2x7"

#. module: product
#: model:ir.model,name:product.model_report_product_report_producttemplatelabel4x12
msgid "Product Label Report 4x12"
msgstr "Produktetikettenbericht 4x12"

#. module: product
#: model:ir.model,name:product.model_report_product_report_producttemplatelabel4x12noprice
msgid "Product Label Report 4x12 No Price"
msgstr "Produktetikettenbericht 4x12 Kein Preis"

#. module: product
#: model:ir.model,name:product.model_report_product_report_producttemplatelabel4x7
msgid "Product Label Report 4x7"
msgstr "Produktetikettenbericht 4x7"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_product_view_form_normalized
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Product Name"
msgstr "Produktname"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__packaging_ids
#: model:ir.model.fields,field_description:product.field_product_template__packaging_ids
msgid "Product Packages"
msgstr "Produktpakete"

#. module: product
#: model:ir.model,name:product.model_product_packaging
#: model:ir.model.fields,field_description:product.field_product_packaging__name
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
msgid "Product Packaging"
msgstr "Produktverpackung"

#. module: product
#: model:ir.actions.report,name:product.report_product_packaging
msgid "Product Packaging (PDF)"
msgstr "Produktverpackung (PDF)"

#. module: product
#: model:ir.actions.act_window,name:product.action_packaging_view
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_stock_packaging
#: model_terms:ir.ui.view,arch_db:product.product_packaging_search_view
#: model_terms:ir.ui.view,arch_db:product.product_packaging_tree_view
msgid "Product Packagings"
msgstr "Produktverpackungen"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__product_properties_definition
msgid "Product Properties"
msgstr "Produkteigenschaften"

#. module: product
#: model:ir.model,name:product.model_product_tag
#: model_terms:ir.ui.view,arch_db:product.product_tag_form_view
msgid "Product Tag"
msgstr "Produkt-Stichwort"

#. module: product
#: model:ir.actions.act_window,name:product.product_tag_action
#: model_terms:ir.ui.view,arch_db:product.product_tag_tree_view
msgid "Product Tags"
msgstr "Produkt-Stichwörter"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__product_tmpl_id
#: model_terms:ir.ui.view,arch_db:product.product_search_form_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Product Template"
msgstr "Produktvorlage"

#. module: product
#: model:ir.model,name:product.model_product_template_attribute_exclusion
msgid "Product Template Attribute Exclusion"
msgstr "Ausschluss des Attributs der Produktvorlage"

#. module: product
#: model:ir.model,name:product.model_product_template_attribute_line
msgid "Product Template Attribute Line"
msgstr "Attributzeile der Produktvorlage"

#. module: product
#: model:ir.model,name:product.model_product_template_attribute_value
msgid "Product Template Attribute Value"
msgstr "Attributwert der Produktvorlage"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_tag__product_template_ids
#: model_terms:ir.ui.view,arch_db:product.product_tag_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_view_tree_tag
msgid "Product Templates"
msgstr "Produktvorlagen"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__product_tmpl_ids
msgid "Product Tmpl"
msgstr "Produktvorlage"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_tooltip
#: model:ir.model.fields,field_description:product.field_product_template__product_tooltip
msgid "Product Tooltip"
msgstr "Produkt-Tooltipp"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__type
#: model:ir.model.fields,field_description:product.field_product_template__type
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Product Type"
msgstr "Produktart"

#. module: product
#: model:ir.model,name:product.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Maßeinheit des Produkts"

#. module: product
#: model:ir.model,name:product.model_product_product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__applied_on__0_product_variant
#: model_terms:ir.ui.view,arch_db:product.product_document_form
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
#: model_terms:ir.ui.view,arch_db:product.product_tag_tree_view
msgid "Product Variant"
msgstr "Produktvariante"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_line.py:0
msgid "Product Variant Values"
msgstr "Werte der Produktvarianten"

#. module: product
#: model:ir.actions.act_window,name:product.product_normal_action
#: model:ir.actions.act_window,name:product.product_normal_action_sell
#: model:ir.actions.act_window,name:product.product_variant_action
#: model:ir.model.fields,field_description:product.field_product_tag__product_product_ids
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_product_view_activity
#: model_terms:ir.ui.view,arch_db:product.product_product_view_tree_tag
#: model_terms:ir.ui.view,arch_db:product.product_tag_form_view
msgid "Product Variants"
msgstr "Produktvarianten"

#. module: product
#. odoo-python
#: code:addons/product/report/product_label_report.py:0
msgid "Product model not defined, Please contact your administrator."
msgstr ""
"Produktmodell nicht definiert, bitte wenden Sie sich an Ihren Administrator."

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute.py:0
#: code:addons/product/models/product_catalog_mixin.py:0
#: model:ir.actions.act_window,name:product.product_template_action
#: model:ir.actions.act_window,name:product.product_template_action_all
#: model:ir.model.fields,field_description:product.field_product_product__product_variant_ids
#: model:ir.model.fields,field_description:product.field_product_template__product_variant_ids
#: model_terms:ir.ui.view,arch_db:product.product_template_view_activity
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Products"
msgstr "Produkte"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
msgid "Products Price"
msgstr "Produktpreis"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_tree
msgid "Products Price List"
msgstr "Produktpreisliste"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
msgid "Products Price Rules Search"
msgstr "Suche nach Produktpreisregeln"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
msgid "Products Price Search"
msgstr "Produktpreissuche"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
msgid "Products: %(category)s"
msgstr "Produkte: %(category)s"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__module_loyalty
msgid "Promotions, Coupons, Gift Card & Loyalty Program"
msgstr "Werbeaktionen, Gutscheine, Geschenkkarte & Treueprogramme"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_properties
#: model:ir.model.fields,field_description:product.field_product_template__product_properties
msgid "Properties"
msgstr "Eigenschaften"

#. module: product
#: model:product.attribute.value,name:product.pav_protection_kit
msgid "Protection kit"
msgstr "Schutzkit"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__purchase_ok
#: model:ir.model.fields,field_description:product.field_product_template__purchase_ok
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Purchase"
msgstr "Einkauf"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__description_purchase
#: model:ir.model.fields,field_description:product.field_product_template__description_purchase
msgid "Purchase Description"
msgstr "Einkaufsbeschreibung"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__uom_po_id
#: model:ir.model.fields,field_description:product.field_product_template__uom_po_id
msgid "Purchase Unit"
msgstr "Einkaufseinheit"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
msgid "Quantities"
msgstr "Mengen"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Quantities (Price)"
msgstr "Mengen (Preis)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__custom_quantity
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__min_qty
msgid "Quantity"
msgstr "Menge"

#. module: product
#. odoo-python
#: code:addons/product/controllers/pricelist_report.py:0
msgid "Quantity (%s UoM)"
msgstr "Menge (%s Maßeinheit)"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
msgid "Quantity already present (%s)."
msgstr "Menge bereits vorhanden (%s)"

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__qty
msgid "Quantity of products contained in the packaging."
msgstr "Menge der in der Verpackung enthaltenen Produkte."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Quotation Description"
msgstr "Angebotsbeschreibung"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__display_type__radio
msgid "Radio"
msgstr "Radio"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__code
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid "Reference"
msgstr "Referenz"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_supplierinfo_type_action
msgid ""
"Register the prices requested by your vendors for each product, based on the"
" quantity and the period."
msgstr ""
"Erfassen Sie die von Ihren Lieferanten geforderten Preise für jedes Produkt,"
" basierend auf der Menge und dem Zeitraum."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__product_tmpl_ids
msgid "Related Products"
msgstr "Zugehörige Produkte"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__ptav_product_variant_ids
msgid "Related Variants"
msgstr "Zugehörige Varianten"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__ir_attachment_id
msgid "Related attachment"
msgstr "Zugehöriger Anhang"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/order_line/order_line.xml:0
msgid "Remove"
msgstr "Entfernen"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
msgid "Remove quantity"
msgstr "Menge entfernen"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__res_field
msgid "Resource Field"
msgstr "Ressourcenfeld"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__res_id
msgid "Resource ID"
msgstr "Ressourcen-ID"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__res_model
msgid "Resource Model"
msgstr "Ressourcenmodell"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__res_name
msgid "Resource Name"
msgstr "Ressourcenname"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_user_id
#: model:ir.model.fields,field_description:product.field_product_product__activity_user_id
#: model:ir.model.fields,field_description:product.field_product_template__activity_user_id
msgid "Responsible User"
msgstr "Verantwortlicher Benutzer"

#. module: product
#: model:product.template,name:product.expense_product_product_template
msgid "Restaurant Expenses"
msgstr "Restaurantausgaben"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Round off to"
msgstr "Abrunden auf"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__rows
msgid "Rows"
msgstr "Zeilen"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__rule_tip
msgid "Rule Tip"
msgstr "Regeltipp"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__sale_ok
#: model:ir.model.fields,field_description:product.field_product_template__sale_ok
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Sales"
msgstr "Verkauf"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__description_sale
#: model:ir.model.fields,field_description:product.field_product_template__description_sale
msgid "Sales Description"
msgstr "Verkaufsbeschreibung"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__list_price
#: model:ir.model.fields,field_description:product.field_product_template__list_price
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__base__list_price
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Sales Price"
msgstr "Verkaufspreis"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__lst_price
msgid "Sales Price"
msgstr "Verkaufspreis"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__display_type__select
msgid "Select"
msgstr "Auswählen"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__sequence
#: model:ir.model.fields,field_description:product.field_product_attribute_value__sequence
#: model:ir.model.fields,field_description:product.field_product_combo__sequence
#: model:ir.model.fields,field_description:product.field_product_document__sequence
#: model:ir.model.fields,field_description:product.field_product_packaging__sequence
#: model:ir.model.fields,field_description:product.field_product_pricelist__sequence
#: model:ir.model.fields,field_description:product.field_product_product__sequence
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__sequence
#: model:ir.model.fields,field_description:product.field_product_tag__sequence
#: model:ir.model.fields,field_description:product.field_product_template__sequence
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__sequence
msgid "Sequence"
msgstr "Sequenz"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_template__type__service
msgid "Service"
msgstr "Dienstleistung"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Services"
msgstr "Dienstleistungen"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_round
msgid ""
"Sets the price so that it is a multiple of this value.\n"
"Rounding is applied after the discount and before the surcharge.\n"
"To have prices that end in 9.99, round off to 10.00 and set an extra at -0.01"
msgstr ""
"Legt den Preis so fest, dass er ein Vielfaches dieses Wertes ist.\n"
"Die Rundung wird nach dem Rabatt und vor dem Aufschlag angewendet.\n"
"Um Preise zu haben, die auf 9,99 enden, runden Sie auf 10,00 ab und legen Sie einen Zuschlag von -0,01 fest"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
msgid "Show Name"
msgstr "Name anzeigen"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Show all records which has next action date is before today"
msgstr "Alle Datensätze mit vor heute geplanten Aktionen anzeigen"

#. module: product
#: model:product.attribute,name:product.size_attribute
msgid "Size"
msgstr "Größe"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_partner__specific_property_product_pricelist
#: model:ir.model.fields,field_description:product.field_res_users__specific_property_product_pricelist
msgid "Specific Property Product Pricelist"
msgstr "Preislist für Produkt mit bestimmter Eigenschaft"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__categ_id
msgid ""
"Specify a product category if this rule only applies to products belonging "
"to this category or its children categories. Keep empty otherwise."
msgstr ""
"Hinterlegen Sie eine Produktkategorie, um die Preisregel nur auf die "
"zugeordneten Produkte sowie die Produkte sämtlicher Untergruppen anzuwenden."
" Anderenfalls leer lassen."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__product_id
msgid ""
"Specify a product if this rule only applies to one product. Keep empty "
"otherwise."
msgstr ""
"Wählen Sie hier ein Produkt aus, wenn diese Regel nur für dieses eine "
"Produkt gelten soll. Ansonsten leer lassen."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__product_tmpl_id
msgid ""
"Specify a template if this rule only applies to one product template. Keep "
"empty otherwise."
msgstr ""
"Wählen Sie eine Vorlage aus, wenn diese Regel nur auf eine Produktvorlage "
"angewendet werden soll. Ansonsten leer lassen."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_surcharge
msgid ""
"Specify the fixed amount to add or subtract (if negative) to the amount "
"calculated with the discount."
msgstr ""
"Hinterlegen Sie den festen Betrag, den Sie zum errechneten reduzierten "
"Betrag addieren oder subtrahieren (wenn negativer Wert) möchten."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_max_margin
msgid "Specify the maximum amount of margin over the base price."
msgstr "Definieren Sie die Höchstmarge im Vergleich zum Grundpreis"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_min_margin
msgid "Specify the minimum amount of margin over the base price."
msgstr "Definieren Sie die Mindestmarge im Vergleich zum Grundpreis"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__date_start
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__date_start
msgid "Start Date"
msgstr "Startdatum"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__date_start
msgid "Start date for this vendor price"
msgstr "Startdatum dieses Lieferantenpreises"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__date_start
msgid ""
"Starting datetime for the pricelist item validation\n"
"The displayed value depends on the timezone set in your preferences."
msgstr ""
"Startzeitpunkt für die Validierung der Preislistenposition\n"
"Der angezeigte Wert hängt von der in Ihren Einstellungen festgelegten Zeitzone ab."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__activity_state
#: model:ir.model.fields,help:product.field_product_product__activity_state
#: model:ir.model.fields,help:product.field_product_template__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status basierend auf Aktivitäten\n"
"Überfällig: Fälligkeitsdatum bereits überschritten\n"
"Heute: Aktivitätsdatum ist heute\n"
"Geplant: anstehende Aktivitäten."

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_1
msgid "Steel"
msgstr "Stahl"

#. module: product
#: model:product.template,name:product.product_product_7_product_template
msgid "Storage Box"
msgstr "Aufbewahrungsbox"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__store_fname
msgid "Stored Filename"
msgstr "Gespeicherter Dateiname"

#. module: product
#: model:ir.model,name:product.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Lieferantenpreisliste"

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_tag_name_uniq
msgid "Tag name already exists!"
msgstr "Stichwortbezeichnung existiert bereits!"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_tag_ids
#: model:ir.model.fields,field_description:product.field_product_template__product_tag_ids
#: model_terms:ir.ui.view,arch_db:product.product_search_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Tags"
msgstr "Stichwörter"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_tag_action
msgid "Tags are used to search product for a given theme."
msgstr ""
"Stichwörter werden verwendet, um Produkte zu einem bestimmten Thema zu "
"suchen."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__template_value_ids
msgid "Template Values"
msgstr "Vorlagenwerte"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid "The Internal Reference '%s' already exists."
msgstr "Die interne Referenz „%s“ existiert bereits."

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
msgid "The Reference '%s' already exists."
msgstr "Die Referenz „%s“ existiert bereits."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_line.py:0
msgid ""
"The attribute %(attribute)s must have at least one value for the product "
"%(product)s."
msgstr ""
"Das Attribut %(attribute)s muss mindestens einen Wert für das Produkt "
"%(product)s haben."

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_value__attribute_id
msgid ""
"The attribute cannot be changed once the value is used on at least one "
"product."
msgstr ""
"Das Attribut kann nicht mehr geändert werden, sobald der Wert für mindestens"
" ein Produkt verwendet wird."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid ""
"The default Unit of Measure and the purchase Unit of Measure must be in the "
"same category."
msgstr ""
"Die Standardmaßeinheit und die Einkaufsmaßeinheit müssen sich in derselben "
"Kategorie befinden."

#. module: product
#: model:product.template,description_sale:product.desk_organizer_product_template
msgid ""
"The desk organiser is perfect for storing all kinds of small things and "
"since the 5 boxes are loose, you can move and place them in the way that "
"suits you and your things best."
msgstr ""
"Der Schreibtisch-Organizer ist perfekt für die Aufbewahrung aller Arten von "
"Kleinigkeiten geeignet. Da die 5 Boxen lose sind, können Sie sie so "
"verschieben und platzieren, wie es Ihnen und Ihren Sachen am besten passt."

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute__display_type
#: model:ir.model.fields,help:product.field_product_attribute_value__display_type
#: model:ir.model.fields,help:product.field_product_template_attribute_value__display_type
msgid "The display type used in the Product Configurator."
msgstr "Der Anzeigetyp, der im Produktkonfigurator verwendet wird."

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__sequence
msgid "The first in the sequence is the default one."
msgstr "Der Erste der Reihenfolge gilt als Standard."

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid ""
"The minimum height is 65 cm, and for standing work the maximum height "
"position is 125 cm."
msgstr ""
"Die Mindesthöhe beträgt 65 cm, und für die Arbeit im Stehen ist die maximale"
" Höhe 125 cm."

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "The minimum margin should be lower than the maximum margin."
msgstr "Die minimale Marge sollte niedriger sein als die maximale Marge."

#. module: product
#: model:ir.model.fields,help:product.field_product_combo__base_price
msgid ""
"The minimum price among the products in this combo. This value will be used "
"to prorate the price of this combo with respect to the other combos in a "
"combo product. This heuristic ensures that whatever product the user chooses"
" in a combo, it will always be the same price."
msgstr ""
"Der Mindestpreis unter den Produkten in dieser Kombi. Dieser Wert wird "
"verwendet, um den Preis dieser Kombi im Verhältnis zu den anderen Kombis in "
"einem Kombi-Produkt zu berechnen. Diese Heuristik stellt sicher, dass der "
"Preis für jedes Produkt, das der Benutzer in einer Kombi auswählt, immer "
"gleich bleibt."

#. module: product
#: model:ir.model.fields,help:product.field_product_category__product_count
msgid ""
"The number of products under this category (Does not consider the children "
"categories)"
msgstr ""
"Die Anzahl der Produkte in dieser Kategorie (untergeordnete Kategorien "
"werden nicht berücksichtigt)"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid ""
"The number of variants to generate is above allowed limit. You should either"
" not generate variants for each combination or generate them on demand from "
"the sales order. To do so, open the form view of attributes and change the "
"mode of *Create Variants*."
msgstr ""
"Die Anzahl der zu erstellenden Varianten liegt über dem zulässigen Limit. "
"Sie sollten entweder nicht für jede Kombination Varianten erstellen oder sie"
" bei Bedarf aus dem Verkaufsauftrag generieren. Öffnen Sie dazu die "
"Formularansicht der Attribute und ändern Sie den Modus auf *Varianten "
"erstellen*."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__price
msgid "The price to purchase a product"
msgstr "Der Preis zum Erwerb eines Produkts"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid "The product template is archived so no combination is possible."
msgstr ""
"Die Produktvorlage wird archiviert, daher ist keine Kombination möglich."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__min_qty
msgid ""
"The quantity to purchase from this vendor to benefit from the price, "
"expressed in the vendor Product Unit of Measure if not any, in the default "
"unit of measure of the product otherwise."
msgstr ""
"Die Menge, die von diesem Lieferanten gekauft werden soll, um von dem Preis "
"zu profitieren, ausgedrückt in der Produktmaßeinheit des Lieferanten, falls "
"keine vorhanden ist, ansonsten in der Standardmaßeinheit des Produkts."

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "The rounding method must be strictly positive."
msgstr "Die Rundungsmethode muss unbedingt positiv sein."

#. module: product
#: model:ir.model.fields,help:product.field_product_combo_item__lst_price
#: model:ir.model.fields,help:product.field_product_product__lst_price
msgid ""
"The sale price is managed from the product template. Click on the 'Configure"
" Variants' button to set the extra attribute prices."
msgstr ""
"Der Verkaufspreis wird aus der Produktvorlage verwaltet. Klicken Sie auf die"
" Schaltfläche „Varianten konfigurieren“, um die zusätzlichen Attributpreise "
"festzulegen."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_value.py:0
msgid ""
"The value %(value)s is not defined for the attribute %(attribute)s on the "
"product %(product)s."
msgstr ""
"Der Wert %(value)s ist für das Attribut %(attribute)s des Produkts "
"%(product)s nicht definiert."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid "There are no possible combination."
msgstr "Es gibt keine mögliche Kombination."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid "There are no remaining closest combination."
msgstr "Es gibt keine verbleibende engste Kombination."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid "There are no remaining possible combination."
msgstr "Es sind keine weiteren Kombinationen möglich."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid ""
"This configuration of product attributes, values, and exclusions would lead "
"to no possible variant. Please archive or delete your product directly if "
"intended."
msgstr ""
"Diese Konfiguration von Produktattributen, Werten und Ausschlüssen würde zu "
"keiner möglichen Variante führen. Bitte archivieren oder löschen Sie Ihr "
"Produkt direkt, wenn dies beabsichtigt ist."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__price_extra
msgid "This is the sum of the extra price of all attributes"
msgstr "Dies ist die Summe der Preisaufschläge aller Attribute"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "This note is added to sales orders and invoices."
msgstr "Die Notiz wird Verkaufsaufträgen und Rechnungen hinzugefügt."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "This note is only for internal purposes."
msgstr "Dieser Hinweis dient nur internen Zwecken."

#. module: product
#: model:ir.model.fields,help:product.field_res_partner__property_product_pricelist
#: model:ir.model.fields,help:product.field_res_users__property_product_pricelist
msgid ""
"This pricelist will be used, instead of the default one, for sales to the "
"current partner"
msgstr ""
"Diese Preisliste wird anstelle der Standardpreisliste beim Verkauf an den "
"aktuellen Partner genutzt."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid ""
"This product is part of a combo, so its type can't be changed to \"combo\"."
msgstr ""
"Dieses Produkt ist Teil einer Kombi, daher kann sein Typ nicht in „Kombi“ "
"geändert werden."

#. module: product
#. odoo-python
#: code:addons/product/models/uom_uom.py:0
msgid ""
"This rounding precision is higher than the Decimal Accuracy (%(digits)s digits).\n"
"This may cause inconsistencies in computations.\n"
"Please set a precision between %(min_precision)s and 1."
msgstr ""
"Diese Rundungsgenauigkeit ist höher als die Dezimalgenauigkeit (%(digits)s Stellen).\n"
"Dies kann zu Inkonsistenzen bei Berechnungen führen.\n"
"Bitte stellen Sie eine Genauigkeit zwischen %(min_precision)s und 1 ein."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_code
msgid ""
"This vendor's product code will be used when printing a request for "
"quotation. Keep empty to use the internal one."
msgstr ""
"Der Produktcode des Lieferanten wird z. B. bei einer Angebotsanfrage "
"verwendet. Lassen Sie dieses Feld leer, um den internen zu verwenden."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_name
msgid ""
"This vendor's product name will be used when printing a request for "
"quotation. Keep empty to use the internal one."
msgstr ""
"Die Produktbezeichnung des Lieferanten wird z. B. bei einer Angebotsanfrage "
"verwendet. Lassen Sie dieses Feld leer, um die interne zu verwenden."

#. module: product
#: model:product.template,description_sale:product.consu_delivery_01_product_template
msgid "Three Seater Sofa with Lounger in Steel Grey Colour"
msgstr "Dreisitzer-Sofa mit Liege in stahlgrauer Farbe"

#. module: product
#: model:product.template,name:product.consu_delivery_01_product_template
msgid "Three-Seat Sofa"
msgstr "Dreisitzer-Sofa"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Today Activities"
msgstr "Heutige Aktivitäten"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__type
msgid "Type"
msgstr "Typ"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__activity_exception_decoration
#: model:ir.model.fields,help:product.field_product_product__activity_exception_decoration
#: model:ir.model.fields,help:product.field_product_template__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ der Ausnahmeaktivität im Datensatz."

#. module: product
#. odoo-python
#: code:addons/product/controllers/pricelist_report.py:0
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "UOM"
msgstr "ME"

#. module: product
#. odoo-python
#: code:addons/product/wizard/product_label_layout.py:0
msgid "Unable to find report template for %s format"
msgstr "Berichtsvorlage für Format %s kann nicht gefunden werden"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
msgid "Unit"
msgstr "Einheit"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Unit Price"
msgstr "Einzelpreis"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__product_uom_id
#: model:ir.model.fields,field_description:product.field_product_product__uom_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_uom
#: model:ir.model.fields,field_description:product.field_product_template__uom_id
msgid "Unit of Measure"
msgstr "Maßeinheit"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__product_uom
#: model:ir.model.fields,field_description:product.field_product_product__uom_name
#: model:ir.model.fields,field_description:product.field_product_template__uom_name
msgid "Unit of Measure Name"
msgstr "Name der Maßeinheit"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/order_line/order_line.xml:0
msgid "Unit price:"
msgstr "Einzelpreis:"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "Units"
msgstr "Einheiten"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_uom
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Units of Measure"
msgstr "Maßeinheiten"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__uom_category_id
#: model:ir.model.fields,field_description:product.field_product_template__uom_category_id
msgid "UoM Category"
msgstr "Maßeinheitskategorie"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
msgid "Update extra prices"
msgstr "Sonderpreise aktualisieren"

#. module: product
#: model:ir.model,name:product.model_update_product_attribute_value
msgid "Update product attribute value"
msgstr "Attributwert für Produkt aktualisieren"

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute_value.py:0
msgid "Update product extra prices"
msgstr "Sonderpreise für Produkt aktualisieren"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__update_product_attribute_value__mode__update_extra_price
msgid "Update the extra price on existing products"
msgstr "Den Sonderpreis für bestehende Produkte aktualisieren"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/product_document_kanban/upload_button/upload_button.xml:0
msgid "Upload"
msgstr "Hochladen"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid "Upload files to your product"
msgstr "Dateien in Ihrem Produkt hochladen"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Upsell & Cross-Sell"
msgstr "Upselling & Cross-Selling"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__url
msgid "Url"
msgstr "URL"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__compute_price
msgid ""
"Use the discount rules and activate the discount settings in order to show "
"discount to customer."
msgstr ""
"Verwenden Sie Rabattregeln und aktivieren Sie die Rabatteinstellungen, um "
"dem Kunden den Rabatt anzuzeigen."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
msgid ""
"Use this feature to store any files you would like to share with your "
"customers"
msgstr ""
"Verwenden Sie diese Funktion, um Dateien zu speichern, die Sie Ihren Kunden "
"zur Verfügung stellen möchten."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__is_used_on_products
msgid "Used on Products"
msgstr "Auf Produkte angewendet"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
msgid "Username"
msgstr "Benutzername"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__valid_product_template_attribute_line_ids
#: model:ir.model.fields,field_description:product.field_product_template__valid_product_template_attribute_line_ids
msgid "Valid Product Attribute Lines"
msgstr "Gültige Produktattributzeilen"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Validity"
msgstr "Gültigkeit"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Validity Period"
msgstr "Gültigkeitsdauer"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__name
msgid "Value"
msgstr "Wert"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__value_count
msgid "Value Count"
msgstr "Wertanzahl"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__standard_price
#: model:ir.model.fields,help:product.field_product_template__standard_price
msgid ""
"Value of the product (automatically computed in AVCO).\n"
"        Used to value the product when the purchase cost is not known (e.g. inventory adjustment).\n"
"        Used to compute margins on sale orders."
msgstr ""
"Produktwert (automatisch als Durchschnittskosten berechnet).\n"
"        Wird verwendet, um das Produkt zu bewerten, wenn die Einkaufskosten nicht bekannt sind (z. B. Bestandsaufnahme).\n"
"        Wird zur Berechnung der Margen bei Verkaufsaufträgen verwendet."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__value_ids
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__value_ids
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_line_form
msgid "Values"
msgstr "Werte"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__product_id
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
msgid "Variant"
msgstr "Variante"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_variant_count
msgid "Variant Count"
msgstr "Variantenanzahl"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__create_variant
msgid "Variant Creation"
msgstr "Variantenerstellung"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_1920
msgid "Variant Image"
msgstr "Variantenbild"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_1024
msgid "Variant Image 1024"
msgstr "Variantenbild 1024"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_128
msgid "Variant Image 128"
msgstr "Variantenbild 128"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_256
msgid "Variant Image 256"
msgstr "Variantenbild 256"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_512
msgid "Variant Image 512"
msgstr "Variantenbild 512"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Variant Information"
msgstr "Varianteninformation"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__price_extra
msgid "Variant Price Extra"
msgstr "Preisaufschlag für Variante"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__variant_seller_ids
#: model:ir.model.fields,field_description:product.field_product_template__variant_seller_ids
msgid "Variant Seller"
msgstr "Verkäufer von Variante"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__additional_product_tag_ids
msgid "Variant Tags"
msgstr "Variantenstichwörter"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_template_variant_value_ids
#: model_terms:ir.ui.view,arch_db:product.attribute_tree_view
msgid "Variant Values"
msgstr "Variantenwerte"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "Variant: %s"
msgstr "Variante: %s"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_product_variant
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid "Variants"
msgstr "Varianten"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__partner_id
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
msgid "Vendor"
msgstr "Lieferant"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Vendor Bills"
msgstr "Lieferantenrechnungen"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
msgid "Vendor Information"
msgstr "Lieferanteninformation"

#. module: product
#: model:ir.actions.act_window,name:product.product_supplierinfo_type_action
msgid "Vendor Pricelists"
msgstr "Lieferantenpreislisten"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_code
msgid "Vendor Product Code"
msgstr "Produktcode des Lieferanten"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_name
msgid "Vendor Product Name"
msgstr "Produktname des Lieferanten"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__seller_ids
#: model:ir.model.fields,field_description:product.field_product_template__seller_ids
msgid "Vendors"
msgstr "Lieferanten"

#. module: product
#: model:product.template,name:product.product_product_2_product_template
msgid "Virtual Home Staging"
msgstr "Virtuelle Heiminszenierung"

#. module: product
#: model:product.template,name:product.product_product_1_product_template
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Virtual Interior Design"
msgstr "Virtuelle Innenarchitektur"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_combo_view_form
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Visible to all"
msgstr "Für alle sichtbar"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__voice_ids
msgid "Voice"
msgstr "Stimme"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__volume
#: model:ir.model.fields,field_description:product.field_product_template__volume
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Volume"
msgstr "Volumen"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__product_volume_volume_in_cubic_feet
msgid "Volume unit of measure"
msgstr "Volumenmaßeinheit"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__volume_uom_name
#: model:ir.model.fields,field_description:product.field_product_template__volume_uom_name
msgid "Volume unit of measure label"
msgstr "Bezeichnung der Volumenmaßeinheit"

#. module: product
#. odoo-python
#: code:addons/product/models/decimal_precision.py:0
#: code:addons/product/models/uom_uom.py:0
msgid "Warning!"
msgstr "Warnung!"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Warnings"
msgstr "Warnungen"

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid ""
"We pay special attention to detail, which is why our desks are of a superior"
" quality."
msgstr ""
"Wir achten ganz besonders auf die Details, darum sind unsere Schreibtische "
"so hochwertig."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__weight
#: model:ir.model.fields,field_description:product.field_product_template__weight
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Weight"
msgstr "Gewicht"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__product_weight_in_lbs
msgid "Weight unit of measure"
msgstr "Gewichtsmaßeinheit"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__weight_uom_name
#: model:ir.model.fields,field_description:product.field_product_template__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Bezeichnung der Gewichtsmaßeinheit"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_3
msgid "White"
msgstr "Weiß"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_color_wood
msgid "Wood"
msgstr "Holz"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__write_date
msgid "Write Date"
msgstr "Änderungsdatum"

#. module: product
#. odoo-python
#: code:addons/product/wizard/update_product_attribute_value.py:0
msgid ""
"You are about to add the value \"%(attribute_value)s\" to %(product_count)s "
"products."
msgstr ""
"Sie sind im Begriff, den Wert „%(attribute_value)s“ zu %(product_count)s "
"Produkten hinzuzufügen."

#. module: product
#. odoo-python
#: code:addons/product/wizard/update_product_attribute_value.py:0
msgid "You are about to update the extra price of %s products."
msgstr ""
"Sie sind im Begriff, den Sonderpreis von %s Produkten zu aktualisieren."

#. module: product
#. odoo-python
#: code:addons/product/models/res_config_settings.py:0
msgid ""
"You are deactivating the pricelist feature. Every active pricelist will be "
"archived."
msgstr ""
"Sie sind im Begriff, die Preislistenfunktion zu deaktivieren. Jede aktive "
"Preisliste wird archiviert."

#. module: product
#. odoo-python
#: code:addons/product/models/decimal_precision.py:0
msgid ""
"You are setting a Decimal Accuracy less precise than the UOMs:\n"
"%s\n"
"This may cause inconsistencies in computations.\n"
"Please increase the rounding of those units of measure, or the digits of this Decimal Accuracy."
msgstr ""
"Sie stellen eine Dezimalgenauigkeit ein, die weniger genau ist als die Maßeinheit:\n"
"%s\n"
"Dies kann zu Inkonsistenzen bei Berechnungen führen.\n"
"Bitte erhöhen Sie die Rundung dieser Maßeinheiten oder die Stellen dieser Dezimalgenauigkeit."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__percent_price
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_discount
msgid "You can apply a mark-up by setting a negative discount."
msgstr ""
"Sie können einen Aufschlag vornehmen, indem Sie einen negativen Rabatt "
"festlegen."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_markup
msgid "You can apply a mark-up on the cost"
msgstr "Sie können einen Aufschlag auf die Kosten anwenden"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid ""
"You can assign pricelists to your customers or select one when creating a "
"new sales quotation."
msgstr ""
"Sie können Ihren Kunden Preislisten zuordnen oder eine beim Anlegen eines "
"neuen Verkaufsangebots auswählen."

#. module: product
#: model:ir.model.fields,help:product.field_product_document__type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr ""
"Sie können entweder eine Datei von Ihrem Rechner hochladen oder einen "
"Internetlink kopieren und einfügen."

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_value__image
#: model:ir.model.fields,help:product.field_product_template_attribute_value__image
msgid ""
"You can upload an image that will be used as the color of the attribute "
"value."
msgstr ""
"Sie können ein Bild hochladen, das als Farbe für den Attributwert verwendet "
"wird."

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/order_line/order_line.xml:0
msgid "You can't edit this product in the catalog."
msgstr "Sie können dieses Produkt im Katalog nicht bearbeiten."

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute.py:0
msgid ""
"You cannot archive this attribute as there are still products linked to it"
msgstr ""
"Sie können dieses Attribut nicht archivieren, da mpcjj Produkte mit diesem "
"verknüpft sind"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid ""
"You cannot assign the Main Pricelist as Other Pricelist in PriceList Item"
msgstr ""
"Sie können die Hauptpreisliste nicht als andere Preisliste in der "
"Preislistenposition zuordnen."

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute.py:0
msgid ""
"You cannot change the Variants Creation Mode of the attribute %(attribute)s because it is used on the following products:\n"
"%(products)s"
msgstr ""
"Sie können den Variantenerstellmodus des Attributs %(attribute)s nicht ändern, da es für die folgenden Produkte verwendet wird:\n"
"%(products)s"

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute_value.py:0
msgid ""
"You cannot change the attribute of the value %(value)s because it is used on"
" the following products: %(products)s"
msgstr ""
"Sie können das Attribut des Wertes %(value)s nicht ändern, da es für die "
"folgenden Produkte verwendet wird:%(products)s"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_value.py:0
msgid ""
"You cannot change the product of the value %(value)s set on product "
"%(product)s."
msgstr ""
"Sie können das Produkt des auf Produkt %(product)s eingestellten Wertes "
"%(value)s nicht ändern."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_value.py:0
msgid ""
"You cannot change the value of the value %(value)s set on product "
"%(product)s."
msgstr ""
"Sie können den Wert des auf Produkt %(product)s eingestellten Wertes "
"%(value)s nicht ändern."

#. module: product
#. odoo-python
#: code:addons/product/models/product_category.py:0
msgid "You cannot create recursive categories."
msgstr "Sie können keine rekursiven Kategorien erstellen."

#. module: product
#. odoo-python
#: code:addons/product/models/decimal_precision.py:0
msgid ""
"You cannot define the decimal precision of 'Account' as greater than the "
"rounding factor of the company's main currency"
msgstr ""
"Die Nachkommastellen des „Kontos“ dürfen nicht größer sein als der "
"Rundungsfaktor für die Hauptwährung des Unternehmens."

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist.py:0
msgid ""
"You cannot delete pricelist(s):\n"
"(%(pricelists)s)\n"
"They are used within pricelist(s):\n"
"%(other_pricelists)s"
msgstr ""
"Sie können diese Preisliste(n) nicht löschen:\n"
"(%(pricelists)s)\n"
"Sie werden in der/den Preisliste(n) verwendet:\n"
"%(other_pricelists)s"

#. module: product
#. odoo-python
#: code:addons/product/models/product_category.py:0
msgid "You cannot delete the %s product category."
msgstr "Sie können die Produktkategorie %s nicht löschen."

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute.py:0
msgid ""
"You cannot delete the attribute %(attribute)s because it is used on the following products:\n"
"%(products)s"
msgstr ""
"Sie können das Attribut %(attribute)s nicht löschen, weil es für die folgenden Produkte verwendet wird:\n"
"%(products)s"

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute_value.py:0
msgid ""
"You cannot delete the value %(value)s because it is used on the following products:\n"
"%(products)s\n"
msgstr ""
"Sie können den Wert %(value)s nicht löschen, weil er für die folgenden Produkte verwendet wird:\n"
"%(products)s\n"

#. module: product
#. odoo-python
#: code:addons/product/models/product_category.py:0
msgid ""
"You cannot delete this product category, it is the default generic category."
msgstr ""
"Sie können diese Produktkategorie nicht löschen, sie ist die generische "
"Standardkategorie."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_line.py:0
msgid ""
"You cannot move the attribute %(attribute)s from the product %(product_src)s"
" to the product %(product_dest)s."
msgstr ""
"Sie können das Attribut %(attribute)s nicht vom Produkt %(product_src)s zum "
"Produkt %(product_dest)s verschieben."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_value.py:0
msgid ""
"You cannot update related variants from the values. Please update related "
"values from the variants."
msgstr ""
"Sie können verwandte Varianten nicht aus den Werten heraus aktualisieren. "
"Bitte aktualisieren Sie zugehörige Werte aus den Varianten heraus."

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/kanban_renderer.xml:0
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                            whether it's a storable product, a consumable or a service."
msgstr ""
"Sie müssen ein Produkt definieren für alles, was Sie verkaufen oder kaufen,\n"
"                             ob es sich nun um ein lagerfähiges Produkt, ein Verbrauchsartikel oder eine Dienstleistung handelt."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_template_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service."
msgstr ""
"Sie müssen ein Produkt definieren für alles, was Sie verkaufen oder kaufen,\n"
"                ob es sich nun um ein lagerfähiges Produkt, ein Verbrauchsartikel oder eine Dienstleistung handelt."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service.\n"
"                The product form contains information to simplify the sale process:\n"
"                price, notes in the quotation, accounting data, procurement methods, etc."
msgstr ""
"Sie müssen ein Produkt definieren für alles, was Sie verkaufen oder kaufen,\n"
"                ob es sich nun um ein lagerfähiges Produkt, ein Verbrauchsartikel oder eine Dienstleistung handelt.\n"
"                Das Produktformular enthält Informationen, um den Verkaufsvorgang zu vereinfachen:\n"
"                Preis, Hinweise im Angebot, Buchhaltungsdaten, Beschaffungsmethoden etc."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
msgid ""
"You must define a product for everything you sell, whether it's a physical product,\n"
"                a consumable or a service you offer to customers.\n"
"                The product form contains information to simplify the sale process:\n"
"                price, notes in the quotation, accounting data, procurement methods, etc."
msgstr ""
"Sie müssen ein Produkt definieren für alles, was Sie verkaufen, ob es sich nun um ein physisches Produkt,\n"
"                einen Verbrauchsartikel oder eine Dienstleistung, die Sie Ihren Kunden anbieten, handelt.\n"
"                Das Produktformular enthält Informationen, um den Verkaufsvorgang zu vereinfachen:\n"
"                Preis, Hinweise im Angebot, Buchhaltungsdaten, Beschaffungsmethoden etc."

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
msgid "You must leave at least one quantity."
msgstr "Sie müssen mindestens eine Menge eingeben."

#. module: product
#. odoo-python
#: code:addons/product/wizard/product_label_layout.py:0
msgid "You need to set a positive quantity."
msgstr "Sie müssen eine positive Menge einstellen."

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
msgid "csv"
msgstr "csv"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_view_kanban
msgid "days"
msgstr "Tage"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "discount"
msgstr "Rabatt"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_product_view_form_normalized
msgid "e.g. 1234567890"
msgstr "z. B. 1234567890"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_combo_view_form
msgid "e.g. Burger Choice"
msgstr "z. B. Burgerkombination"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_product_view_form_normalized
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "e.g. Cheese Burger"
msgstr "z. B. Cheeseburger"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "e.g. Lamps"
msgstr "z. B. Lampen"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "e.g. Odoo Enterprise Subscription"
msgstr "z. B. Odoo-Enterprise-Abonnement"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "e.g. Starter - Meal - Desert"
msgstr "z. B. Vorspeise – Hauptgang – Dessert"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "e.g. USD Retailers"
msgstr "z. B. USD-Händler"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "markup"
msgstr "Preisaufschlag"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_form
msgid "on"
msgstr "am"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
msgid "pdf"
msgstr "PDF"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "per"
msgstr "pro"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
#: code:addons/product/models/product_template.py:0
msgid "product"
msgstr "Produkt"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
msgid "product cost"
msgstr "Produktkosten"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "round off to 10.00 and set an extra at -0.01"
msgstr "auf 10,00 abrunden und einen Aufschlag von -0,01 festlegen"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "sales price"
msgstr "Verkaufspreis"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_partner_property_form
msgid "the parent company"
msgstr "Mutterunternehmen."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "the product template."
msgstr "die Produktvorlage."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "to"
msgstr "bis"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
msgid "xlsx"
msgstr "xlsx"
