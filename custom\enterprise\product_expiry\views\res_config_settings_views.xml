<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form_stock" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.product.expiry.stock</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="stock.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//setting[@id='group_lot_on_delivery_slip']" position="after">
                <setting help="Expiration dates will appear on the delivery slip" invisible="not group_lot_on_delivery_slip or not module_product_expiry" id="group_expiry_date_on_delivery_slip">
                    <field name="group_expiry_date_on_delivery_slip"/>
                </setting>
            </xpath>
        </field>
    </record>
</odoo>
