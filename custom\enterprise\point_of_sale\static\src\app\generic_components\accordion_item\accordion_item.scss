@mixin hide-scrollbar {
  -ms-overflow-style: none;  // IE 10+
  scrollbar-width: none;  // Firefox

  &::-webkit-scrollbar {
    display: none;  // Safari and Chrome
  }
}

.accordion.disabled {
    pointer-events: none;
    opacity: 0.5;
}

.accordion-header {
    margin-left: 1px;
    
    &:not(.no-caret) {
        display: flex;
        align-items: center;
        gap: 5px;

        &:before {
            content: "\f0d7";
            font-family: FontAwesome;
            display: inline-block;

            transform: rotate(-90deg);
            transition: .25s ease-in-out;
        }

        &.open:before {
            transform: rotate(0deg);
        }
    }
}

.accordion-content-container {
    overflow: auto;
    transition: max-height 0.25s ease-in-out;

    @include hide-scrollbar;
}