<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <template id="sale_order_portal_content_inherit" inherit_id="sale.sale_order_portal_template">
        <xpath expr="//t[@t-set='entries']/div/div/div[hasclass('o_download_pdf')]" position="inside">
            <t t-if="sale_order.timesheet_count > 0 and sale_order.state == 'sale'">
                <a class="btn btn-light flex-grow-1" t-att-href="'/my/timesheets?search_in=so&amp;search=%s' % sale_order.name" title="View Timesheets" target="_blank" role="button">View Timesheets</a>
            </t>
        </xpath>
    </template>

    <template id="portal_my_timesheets_inherit" inherit_id="hr_timesheet.portal_my_timesheets">
        <xpath expr="//t[@t-foreach='grouped_timesheets']/tbody/tr[hasclass('table-light')]/th[hasclass('text-end')]" position="attributes">
            <attribute name="colspan">2</attribute>
        </xpath>
        <xpath expr="//t[@t-foreach='grouped_timesheets']/tbody/tr[hasclass('table-light')]/th[hasclass('text-end')]" position="before">
            <t t-elif="groupby == 'so_line'">
                <t t-set="sol" t-value="timesheets[0].so_line"/>
                <th colspan="5">
                    <t t-if="sol">
                        <span t-field="sol.display_name"/>
                        <t t-if="sol.remaining_hours_available">
                            <span class="text-muted fw-normal">
                                <t t-if="is_uom_day">
                                    (<span t-esc="timesheets._timesheet_convert_sol_uom(sol, 'uom.product_uom_day')" t-options='{"widget": "timesheet_uom"}'></span> Days Ordered, <span t-esc="timesheets._convert_hours_to_days(sol.remaining_hours)" t-options='{"widget": "timesheet_uom"}'></span> Days Remaining)
                                </t>
                                <t t-else="">
                                    (<span t-esc="timesheets._timesheet_convert_sol_uom(sol, 'uom.product_uom_hour')" t-options='{"widget": "float_time"}'></span> Hours Ordered, <span t-esc="sol.remaining_hours" t-options='{"widget": "float_time"}'></span> Hours Remaining)
                                </t>
                            </span>
                        </t>
                    </t>
                    <t t-else="">
                        Not Billed
                    </t>
                </th>
            </t>
            <t t-elif="groupby == 'order_id'">
                <t t-set="so" t-value="timesheets[0].order_id"/>
                <th colspan="6">
                    <t t-if="so">
                        <span t-field="so.display_name"/>
                    </t>
                    <t t-else="">
                        Not Billed
                    </t>
                </th>
            </t>
            <t t-elif="groupby == 'timesheet_invoice_id'">
                <t t-set="invoice" t-value="timesheets.timesheet_invoice_id"/>
                <th colspan="6">
                    <t t-if="invoice">
                        <span t-field="invoice.display_name"/>
                    </t>
                    <t t-else="">
                        No Invoice
                    </t>
                </th>
            </t>
        </xpath>
        <th name="t_label" position="before">
            <th t-if="not groupby == 'so_line'">Sales Order Item</th>
            <th t-if="not groupby == 'timesheet_invoice_id'">Invoice</th>
        </th>
        <xpath expr="//tbody//td[hasclass('text-end')]" position="before">
            <td t-if="not groupby == 'so_line'"><span t-field="timesheet.so_line" t-att-title="timesheet.so_line.display_name"></span></td>
            <td t-if="not groupby == 'timesheet_invoice_id'"><span t-field="timesheet.timesheet_invoice_id" t-att-title="timesheet.timesheet_invoice_id.display_name"></span></td>
        </xpath>
    </template>

    <template id="portal_invoice_page_inherit" inherit_id="account.portal_invoice_page">
        <xpath expr="//t[@t-set='entries']/div/div/div[hasclass('o_download_pdf')]" position="after">
            <t t-if="invoice.timesheet_count > 0">
                <t t-set="search_value" t-value="invoice.name"/>
                <t t-if="invoice.state == 'draft'" t-set="search_value" t-value="invoice.id"/>
                <a t-if="invoice.move_type == 'out_invoice' and invoice.state in ('draft', 'posted') and invoice.timesheet_count > 0"
                    target="_blank" t-att-href="'/my/timesheets?search_in=invoice&amp;search=%s' % search_value" class="btn btn-light" role="button" title="View Timesheet">View Timesheets</a>
            </t>
        </xpath>
    </template>

    <template id="portal_my_task_inherit" inherit_id="project.portal_my_task">
        <xpath expr="//div[@name='portal_my_task_second_column']" position="inside">
            <t t-if="task.project_id.allow_billable and env.user.has_group('sales_team.group_sale_salesman')">
                <div t-if="task.sale_order_id"><strong>Sales Order:</strong>
                    <span t-if="so_accessible"><a t-attf-href="{{ task.sale_order_id.access_url }}" t-field="task.sale_order_id"></a></span>
                    <span t-else="" t-field="task.sale_order_id"></span>
                </div>
                <div t-if="invoices_accessible"><strong>Invoices:</strong>
                    <span t-foreach="task.sale_order_id.invoice_ids" t-as="invoice_line">
                        <t t-if="invoice_line.id in invoices_accessible">
                            <a t-attf-href="/my/invoices/{{ invoice_line.id }}">
                                <i t-if="invoice_line.state == 'draft'">Draft Invoice</i>
                                <t t-else="" t-esc="invoice_line.name"/></a>
                            <span t-if="not invoice_line_last">,</span>
                        </t>
                        <t t-else=""><span t-esc="invoice_line.name"></span><span t-if="not invoice_line_last">,</span></t>
                    </span>
                </div>
                <div t-if="task.sale_line_id.untaxed_amount_invoiced > 0"><strong>Invoiced:</strong>
                    <span t-field="task.sale_line_id.untaxed_amount_invoiced"/>
                </div>
                <div name="amount_due" t-if="task.sale_line_id.untaxed_amount_to_invoice > 0"><strong>Amount Due:</strong>
                    <span t-field="task.sale_line_id.untaxed_amount_to_invoice"/>
                </div>
            </t>
        </xpath>
    </template>

</odoo>
