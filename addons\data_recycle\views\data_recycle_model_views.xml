<?xml version="1.0"?>
<odoo>
    <data>
        <record model="ir.ui.view" id="view_data_recycle_model_list">
            <field name="name">Field Recyle Model List</field>
            <field name="model">data_recycle.model</field>
            <field name="arch" type="xml">
                <list decoration-muted="not active">
                    <field name="name" />
                    <field name="res_model_id" />
                    <field name="recycle_mode" groups="base.group_no_one" />
                    <field name="recycle_action" groups="base.group_no_one" />
                    <field name="active" widget="boolean_toggle" />
                </list>
            </field>
        </record>

        <record model="ir.ui.view" id="view_data_merge_model_form">
            <field name="name">Field Recyle Model Form</field>
            <field name="model">data_recycle.model</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button name="action_recycle_records" type="object" string="Run Now" class="oe_highlight" />
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button class="oe_stat_button" name="open_records"
                                    type="object" icon="fa-bars">
                                <field name="records_to_recycle_count" string="Records" widget="statinfo"/>
                            </button>
                        </div>
                        <div class="oe_title">
                            <h1>
                                <field name="name" />
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="res_model_id" options="{'no_create': True, 'no_open': True}" />
                                <field name="res_model_name" invisible="1" />
                                <field name="active" widget="boolean_toggle" />
                            </group>
                            <group>
                                <field name="recycle_mode" widget="radio" options="{'horizontal': true}" />
                                <field name="recycle_action" widget="radio" options="{'horizontal': true}" />
                                <field name="include_archived" invisible="recycle_action != 'unlink'"/>

                                <!-- Manual cleaning -->
                                <label for="notify_user_ids" invisible="recycle_mode == 'automatic'" />
                                <div invisible="recycle_mode == 'automatic'">
                                    <field name="notify_user_ids" widget="many2many_tags"  options="{'no_create': True, 'no_edit': True}" domain="[('share', '=', False)]" nolabel="1"/>
                                    <div class="d-flex w-50" invisible="not notify_user_ids">
                                        <span class="me-1">Every</span>
                                        <field name="notify_frequency" required="notify_user_ids" />
                                        <field name="notify_frequency_period" required="notify_user_ids" />
                                    </div>
                                </div>
                            </group>
                        </group>
                        <group invisible="res_model_id">
                            <group>
                                <div class="alert alert-info" role="alert" colspan="2">
                                    Select a model to configure recycling actions
                                </div>
                            </group>
                        </group>
                        <group invisible="not res_model_id">
                            <field name="domain" widget="domain" options="{'model': 'res_model_name'}"/>
                            <field name="time_field_id"/>
                            <field name="time_field_delta"/>
                            <field name="time_field_delta_unit"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record model="ir.actions.act_window" id="action_data_recycle_config">
            <field name="name">Recyle Records Rules</field>
            <field name="res_model">data_recycle.model</field>
            <field name="view_mode">list,form</field>
            <field name="domain">['|', ('active', '=', False), ('active', '=', True)]</field>
        </record>
    </data>
</odoo>
