<?xml version="1.0" encoding='UTF-8'?>
<odoo>
	<data noupdate="1">

        <record id="action_compute_price_bom_product" model="ir.actions.server">
            <field name="name">Subscription: Generate delivery</field>
            <field name="model_id" ref="sale_subscription.model_sale_order"/>
            <field name="binding_model_id" ref="sale_subscription.model_sale_order"/>
            <field name="binding_view_types">list,form</field>
            <field name="state">code</field>
            <field name="code">
records.filtered('is_subscription').order_line._get_stock_subscription_lines()._action_launch_stock_rule()
            </field>
        </record>

    </data>

</odoo>
