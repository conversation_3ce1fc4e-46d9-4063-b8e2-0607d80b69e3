<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.PaymentScreenStatus">
        <div t-if="props.order.payment_ids.length == 0" class="text-center py-4 fs-4 my-auto">
    Please select a payment method 
        </div>
        <section t-else="" t-attf-class="paymentlines-container p-3 rounded-3 border {{ props.order.get_due() > 0 ? 'bg-danger-subtle border-danger text-danger' : 'bg-success-subtle border-success text-success'}}">
            <div class="payment-status-container d-flex flex-column-reverse flex-lg-row justify-content-between fs-2 pe-4 me-2">
                <div t-if="this.props.order.hasRemainingAmount()" class="payment-status-remaining d-flex justify-content-between flex-grow-1">
                    <span class="label pe-2">Remaining</span>
                    <span class="amount align-self-end pe-5" t-att-class="{ 'highlight text-danger fw-bolder': props.order.get_due() > 0 }">
                        <t t-esc="remainingText" />
                    </span>
                </div>
                <div t-else="" class="payment-status-change d-flex justify-content-between flex-grow-1">
                    <span class="label pe-2">Change</span>
                    <span class="amount align-self-end pe-5" t-att-class="{ 'highlight text-success fw-bolder': props.order.get_change() > 0 }">
                        <t t-esc="changeText" />
                    </span>
                </div>
            </div>
        </section>
    </t>
</templates>
