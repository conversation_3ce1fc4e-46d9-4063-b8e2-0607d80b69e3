
// The goal of this file is to contain CSS hacks related to allowing
// section and note on sale order and invoice.

table.o_section_and_note_list_view tr.o_data_row.o_is_line_note,
table.o_section_and_note_list_view tr.o_data_row.o_is_line_note textarea[name="name"],
div.oe_kanban_card.o_is_line_note {
    font-style: italic;
}
table.o_section_and_note_list_view tr.o_data_row.o_is_line_section,
div.oe_kanban_card.o_is_line_section {
    font-weight: bold;
    background-color: #DDDDDD;
}
table.o_section_and_note_list_view tr.o_data_row.o_is_line_section {
    border-top: 1px solid #BBB;
    border-bottom: 1px solid #BBB;
}

table.o_section_and_note_list_view tr.o_data_row {
    &.o_is_line_note,
    &.o_is_line_section {
        td {
            // There is an undeterministic CSS behaviour in Chrome related to
            // the combination of the row's and its children's borders.
            border: none !important;
        }
    }
}

.o_field_section_and_note_text {
    > span {
        white-space: pre-wrap !important;
    }
}
