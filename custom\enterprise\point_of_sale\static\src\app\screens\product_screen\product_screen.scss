.w-60 {
    width: 60%;
}

.product-list {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));

    @include media-breakpoint-down(xl) {
        grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
    }

    @include media-breakpoint-down(sm) {
        grid-template-columns: repeat(3, 1fr);
    }
}

.button-no-demo {
    color: #017e84 !important;
}

.text-wrap-categ {
    box-sizing: border-box;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.btn-switchpane, .pay-order-button {
    min-height: 70px;
}
