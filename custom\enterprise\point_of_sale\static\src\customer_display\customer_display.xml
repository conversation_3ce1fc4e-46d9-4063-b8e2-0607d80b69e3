<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="point_of_sale.CustomerDisplay">
        <t t-set="bgImgSrc" t-value="session.customer_display_bg_img ? 'url(/web/image/pos.config/' + pos.config.id + '/customer_display_bg_img)' : 'none'" />
        <div class="o_customer_display d-flex h-100">
            <div class="o_customer_display_sidebar d-flex flex-column align-items-center justify-content-center gap-2 p-4 bg-view" t-att-style="session.has_bg_img ? `background-image: url(/web/image/pos.config/${session.config_id}/customer_display_bg_img); min-width: 30%; min-height: 15%;` : ``">
                <div t-if="!session.has_bg_img" class="o_customer_display_logo d-flex mw-50 mw-md-100 p-2 rounded-3 bg-white">
                    <img class="img-fluid" t-attf-src="/logo?company={{session.company_id}}"/>
                </div>
                <div class="position-absolute bottom-0 mb-4 d-none d-lg-flex align-items-center ps-3 pe-2 py-1 rounded-3 text-bg-dark small">Powered by <OdooLogo style="'width: 3rem;'" monochrome="true"/></div>
            </div>
            <div class="o_customer_display_main d-flex flex-column flex-grow-1 overflow-auto">
                <OrderWidget t-if="!order.finalized" lines="order.lines" t-slot-scope="scope" class="'gap-0 p-0 mx-2 pb-3 bg-view'" style="'scroll-snap-type: y mandatory;'" generalNote="order.generalNote or ''">
                    <Orderline line="scope.line" class="{
                            'o_customer_display_orderline bg-white fs-3 rounded-0': true,
                            'selected': scope.line.isSelected
                        }"
                    />
                    <t t-set-slot="emptyCart">
                        <div class="d-flex flex-column align-items-center justify-content-center h-100 w-100">
                            <h1 class="display-2 w-75 mt-2 fw-bold text-center text-muted">Welcome.</h1>
                        </div>
                    </t>
                </OrderWidget>
                <div t-else="" class="d-flex flex-column align-items-center justify-content-center h-100 w-100">
                    <h1 class="display-2 w-75 mt-2 fw-bold text-center text-muted">Thank you.</h1>
                </div>
                <div t-if="order.scaleData">
                    <t t-set-slot="header">
                        <h4 class="modal-title text-break text-center w-100" t-att-class="{ 'me-auto': env.isSmall }">
                            <span class="me-2">Weighing Product:</span>
                            <t t-esc="order.scaleData.product.name"/>
                        </h4>
                    </t>
                    <div class="d-flex flex-column px-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div class="w-60 fs-5 me-2 form-control-plaintext">Gross Weight:</div>
                            <div class="weight fs-5 js-weight text-end form-control-plaintext" t-esc="order.scaleData.grossWeight"></div>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div class="w-60 fs-5 me-2 form-control-plaintext">Tare Weight:</div>
                            <div class="weight fs-5 js-weight text-end form-control-plaintext" t-esc="order.scaleData.tare"></div>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div class="w-60 fs-5 me-2 form-control-plaintext">Net Weight:</div>
                            <div class="text-end fs-5 form-control-plaintext" t-esc="order.scaleData.netWeight"></div>
                        </div>
                        <div class="d-flex px-5 flex-row gap-2 m-2 align-items-center">
                            <div class="product-price w-50 fs-5 text-center" t-esc="order.scaleData.unitPrice"/>
                            <div class="computed-price fd-flex flex-grow-1 p-2 rounded text-center text-bg-info bg-opacity-25 text-info fs-5 fw-bold" t-esc="order.scaleData.totalPrice" />
                        </div>
                    </div>
                </div>

                <div t-if="order.amount and !order.finalized" class="py-3 px-4 bg-view border-top">
                    <div class="d-flex flex-column justify-content-center gap-1 w-100 w-sm-50 ms-auto">
                        <div class="row fs-2 fw-bolder">
                            <div class="col text-end">Total</div>
                            <div class="col text-end" t-esc="order.amount"/>
                        </div>
                        <div t-foreach="order.paymentLines or []" t-as="line" t-key="line_index" class="row">
                            <div class="col text-end" t-esc="line.name" />
                            <div class="col text-end" t-esc="line.amount"/>
                        </div>
                        <div class="row" t-if="order.change">
                            <div class="col text-end">Change</div>
                            <div class="col text-end" t-esc="order.change"/>
                        </div>
                    </div>
                </div>
                <div class="d-flex justify-content-center" t-att-class="{ 'bg-view': order.amount and !order.finalized }">
                    <div class=" bottom-0 mb-4 d-flex align-items-center ps-3 pe-2 py-1 rounded-3 small">Powered by <OdooLogo style="'width: 3rem;'"/></div>
                </div>
            </div>
        </div>
        <MainComponentsContainer />
    </t>
</templates>
