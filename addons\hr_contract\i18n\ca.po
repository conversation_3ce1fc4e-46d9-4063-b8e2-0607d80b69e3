# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_contract
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# eriiik<PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> Fonseca <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <carles<PERSON><EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <carles<PERSON><EMAIL>>, 2024\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__contract_count
msgid "# Contracts"
msgstr "# Contractes"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_resource_calendar__contracts_count
msgid "# Contracts using it"
msgstr "# Contractes que l'utilitzen"

#. module: hr_contract
#. odoo-python
#: code:addons/hr_contract/report/hr_contract_history.py:0
msgid "%s's Contracts History"
msgstr "%s's Historial de contractes"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "/ month"
msgstr "/ mes"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form3
msgid ""
"<span class=\"o_stat_text text-danger\" invisible=\"not contract_warning\" title=\"In Contract Since\">\n"
"                                    In Contract Since\n"
"                                </span>"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form3
msgid ""
"<span class=\"o_stat_text text-success\" invisible=\"contract_warning\" "
"title=\"In Contract Since\"> In Contract Since</span>"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form3
msgid ""
"<span invisible=\"contracts_count != 1\" class=\"o_stat_text text-danger\">\n"
"                                    Contract\n"
"                                </span>\n"
"                                <span invisible=\"contracts_count == 1\" class=\"o_stat_text text-danger\">\n"
"                                    Contracts\n"
"                                </span>"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid ""
"<span invisible=\"contracts_count == 1\" class=\"o_stat_text\">\n"
"                                        Contracts\n"
"                                    </span>\n"
"                                    <span invisible=\"contracts_count &gt; 1\" class=\"o_stat_text\">\n"
"                                        Contract\n"
"                                    </span>"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.res_config_settings_view_form
msgid "<span>Days</span>"
msgstr "<span>Dies</span>"

#. module: hr_contract
#. odoo-python
#: code:addons/hr_contract/models/hr_contract.py:0
msgid ""
"According to Employee's Working Permit Expiration Date, this contract has "
"been put in red on the %s. Please advise and correct."
msgstr ""

#. module: hr_contract
#. odoo-python
#: code:addons/hr_contract/models/hr_contract.py:0
msgid ""
"According to the contract's end date, this contract has been put in red on "
"the %s. Please advise and correct."
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_needaction
msgid "Action Needed"
msgstr "Acció necessària"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__active
msgid "Active"
msgstr "Actiu"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__active_employee
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__active_employee
msgid "Active Employee"
msgstr "Treballador actiu"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Active Employees"
msgstr "Treballadors actius"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_ids
msgid "Activities"
msgstr "Activitats"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activitat d'excepció de decoració"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_state
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__activity_state
msgid "Activity State"
msgstr "Estat de l'activitat"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icona de tipus d'activitat"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_mail_activity_schedule
msgid "Activity schedule plan Wizard"
msgstr "Assistent per a planificar activitats"

#. module: hr_contract
#: model:res.groups,name:hr_contract.group_hr_contract_manager
msgid "Administrator"
msgstr "Administrador"

#. module: hr_contract
#. odoo-python
#: code:addons/hr_contract/models/hr_contract.py:0
msgid ""
"An employee can only have one contract at the same time. (Excluding Draft and Cancelled contracts).\n"
"\n"
"Employee: %(employee_name)s"
msgstr ""
"Un empleat només pot tenir un contracte al mateix temps. (Excloent contractes Esborrany i Cancel·lats).\n"
"\n"
"Empleat: %(employee_name)s"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Archived"
msgstr "Arxivat"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre d'adjunts"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_res_users__bank_account_id
msgid "Bank Account"
msgstr "Compte bancari"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_employee_base
msgid "Basic Employee"
msgstr "Empleat bàsic"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__calendar_mismatch
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__calendar_mismatch
msgid "Calendar Mismatch"
msgstr "El calendari no coincideix"

#. module: hr_contract
#. odoo-javascript
#: code:addons/hr_contract/static/src/widgets/tooltip_warning_widget.js:0
msgid ""
"Calendar Mismatch: The employee's calendar does not match this contract's "
"calendar. This could lead to unexpected behaviors."
msgstr ""

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__state__cancel
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__state__cancel
msgid "Cancelled"
msgstr "Cancel·lat"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_res_company
msgid "Companies"
msgstr "Empreses"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__company_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__company_id
msgid "Company"
msgstr "Empresa"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__vehicle
#: model:ir.model.fields,field_description:hr_contract.field_res_users__vehicle
msgid "Company Vehicle"
msgstr "Vehicle de la companyia"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__company_country_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__company_country_id
msgid "Company country"
msgstr "País de l'empresa"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_res_config_settings
msgid "Config Settings"
msgstr "Paràmetres de configuració"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__contract_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_departure_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract.res_config_settings_view_form
msgid "Contract"
msgstr "Contracte"

#. module: hr_contract
#. odoo-python
#: code:addons/hr_contract/models/hr_contract.py:0
msgid ""
"Contract %(contract)s: start date (%(start)s) must be earlier than contract "
"end date (%(end)s)."
msgstr ""
"Contracte %(contract)s: la data d'inici (%(start)s) ha de ser anterior a la "
"data de finalització del contracte (%(end)s)."

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__contracts_count
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__contracts_count
msgid "Contract Count"
msgstr "Recompte de contactes"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_departure_wizard_view_form
msgid "Contract End Date"
msgstr "Data de finalització del contracte"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.res_config_settings_view_form
msgid "Contract Expiration Notice Period"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_res_company__contract_expiration_notice_period
#: model:ir.model.fields,field_description:hr_contract.field_res_config_settings__contract_expiration_notice_period
msgid "Contract Expiry Notice Period"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Contract History"
msgstr "Historial de contractes"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__name
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Contract Name"
msgstr "Nom del contracte"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__name
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Contract Reference"
msgstr "Referència contracte"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Contract Start Date"
msgstr "Data d'inici del contracte"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__contract_type_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__contract_type_id
msgid "Contract Type"
msgstr "Tipus de contracte"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__contract_wage
msgid "Contract Wage"
msgstr "Guany de contracte"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__contract_warning
msgid "Contract Warning"
msgstr "Avís de contracte"

#. module: hr_contract
#: model:mail.message.subtype,description:hr_contract.mt_contract_pending
#: model:mail.message.subtype,description:hr_contract.mt_department_contract_pending
msgid "Contract about to expire"
msgstr "Contracte a punt de caducar"

#. module: hr_contract
#: model:mail.message.subtype,description:hr_contract.mt_contract_close
msgid "Contract expired"
msgstr "Contracte caducat"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_contract_history
msgid "Contract history"
msgstr "Historial de contractes"

#. module: hr_contract
#: model:mail.message.subtype,name:hr_contract.mt_department_contract_pending
msgid "Contract to Renew"
msgstr "Contracte a renovar"

#. module: hr_contract
#: model:ir.actions.act_window,name:hr_contract.action_hr_contract
#: model:ir.actions.act_window,name:hr_contract.hr_contract_history_view_form_action
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__contract_ids
#: model:ir.ui.menu,name:hr_contract.hr_menu_contract
#: model:ir.ui.menu,name:hr_contract.menu_hr_employee_contracts
#: model:ir.ui.menu,name:hr_contract.menu_human_resources_configuration_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_list
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_activity
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_tree
#: model_terms:ir.ui.view,arch_db:hr_contract.resource_calendar_view_form
msgid "Contracts"
msgstr "Contractes"

#. module: hr_contract
#: model:ir.actions.act_window,name:hr_contract.hr_contract_history_to_review_view_list_action
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Contracts to Review"
msgstr "Contractes per revisar"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Contracts to review"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__under_contract_state
msgid "Contractual Status"
msgstr "Estat contractual"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__country_id
msgid "Country"
msgstr "País"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__country_code
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__country_code
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__country_code
msgid "Country Code"
msgstr "Codi de país"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Create"
msgstr "Crear"

#. module: hr_contract
#: model_terms:ir.actions.act_window,help:hr_contract.action_hr_contract
msgid "Create a new contract"
msgstr "Crear un nou contracte"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__create_uid
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__create_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__create_date
msgid "Created on"
msgstr "Creat el"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__currency_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__currency_id
msgid "Currency"
msgstr "Divisa"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__contract_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Current Contract"
msgstr "Contracte actual"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Current Contracts"
msgstr "Contractes actuals"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_employee__contract_id
msgid "Current contract of the employee"
msgstr "Contracte actual de l'empleat"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Currently Under Contract"
msgstr "Actualment sota contracte"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__default_resource_calendar_id
msgid "Default Working Hours"
msgstr "Hores treballades per defecte"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_kanban
msgid "Delete"
msgstr "Eliminar"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__department_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__department_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Department"
msgstr "Departament"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Assistent de sortida"

#. module: hr_contract
#. odoo-python
#: code:addons/hr_contract/wizard/hr_departure_wizard.py:0
msgid ""
"Departure date can't be earlier than the start date of current contract."
msgstr ""
"La data de sortida no pot ser anterior a la data d'inici del contracte "
"actual."

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Details"
msgstr "Detalls"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__display_name
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__display_name
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__display_name
msgid "Display Name"
msgstr "Nom mostrat"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_kanban
msgid "Edit Contract"
msgstr "Modificar contracte"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_employee
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__employee_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__employee_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Employee"
msgstr "Empleat"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_contract
msgid "Employee Contract"
msgstr "Contracte de l'empleat"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__contract_ids
msgid "Employee Contracts"
msgstr "Contractes dels empleats"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Employee Information"
msgstr "Informació del treballador"

#. module: hr_contract
#: model:res.groups,name:hr_contract.group_hr_contract_employee_manager
msgid "Employee Manager"
msgstr "Gestor de treballadors"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_res_users__bank_account_id
msgid "Employee bank account to pay salaries"
msgstr "Compte bancari del treballador per pagar salaris"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__wage
#: model:ir.model.fields,help:hr_contract.field_hr_contract_history__wage
msgid "Employee's monthly gross wage."
msgstr "Salari brut mensual de l'empleat"

#. module: hr_contract
#: model:ir.actions.act_window,name:hr_contract.hr_contract_history_view_list_action
msgid "Employees"
msgstr "Empleats"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__date_end
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__date_end
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "End Date"
msgstr "Data de finalització"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__date_end
msgid "End date of the contract (if it's a fixed-term contract)."
msgstr "Data final del contracte (si és contracte de durada determinada)"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__trial_date_end
msgid "End date of the trial period (if there is one)."
msgstr "Fi del període prova (si n'hi ha)."

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__trial_date_end
msgid "End of Trial Period"
msgstr "Final del període de prova"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__state__close
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__state__close
#: model:mail.message.subtype,name:hr_contract.mt_contract_close
msgid "Expired"
msgstr "Expirat"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__first_contract_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__first_contract_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee_public__first_contract_date
msgid "First Contract Date"
msgstr "Primera data de contacte"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_follower_ids
msgid "Followers"
msgstr "Seguidors"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidors (Partners)"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icona Font Awesome p.e. fa-tasks"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_kanban
msgid "From"
msgstr "Des de"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid "Fully Flexible"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Future Activities"
msgstr "Activitats futures"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Group By"
msgstr "Agrupar per"

#. module: hr_contract
#: model:ir.actions.server,name:hr_contract.ir_cron_data_contract_update_state_ir_actions_server
msgid "HR Contract: update state"
msgstr "Contracte HR: actualitzar estat"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__hr_responsible_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__hr_responsible_id
msgid "HR Responsible"
msgstr "Responsable HR"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__has_message
msgid "Has Message"
msgstr "Té un missatge"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__date_hired
msgid "Hire Date"
msgstr "Data d'accés"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__id
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__id
msgid "ID"
msgstr "ID"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_exception_icon
msgid "Icon"
msgstr "Icona"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icona que indica una activitat d'excepció."

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Si està marcat, hi ha nous missatges que requereixen la vostra atenció."

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_has_error
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si està marcat, alguns missatges tenen un error d'entrega."

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__active_employee
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Si el camp actiu es desmarca, permet ocultar el registre del recurs sense "
"eliminar-ho."

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__is_under_contract
msgid "Is Currently Under Contract"
msgstr "Actualment està sota contracte"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_is_follower
msgid "Is Follower"
msgstr "És un seguidor"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__job_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__job_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Job Position"
msgstr "Lloc de treball"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__kanban_state
msgid "Kanban State"
msgstr "Estat kanban"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__write_uid
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__write_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Late Activities"
msgstr "Activitats endarrerides"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__legal_name
msgid "Legal Name"
msgstr ""

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_ir_ui_menu
msgid "Menu"
msgstr "Menú"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_has_error
msgid "Message Delivery error"
msgstr "Error d'entrega del missatge"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_ids
msgid "Messages"
msgstr "Missatges"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Monthly Wage"
msgstr "Salari mensual"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Venciment de l'activitat"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__state__draft
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__state__draft
msgid "New"
msgstr "Nou"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Proper esdeveniment del calendari d'activitats"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Data límit de la següent activitat"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_summary
msgid "Next Activity Summary"
msgstr "Resum de la següent activitat"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_type_id
msgid "Next Activity Type"
msgstr "Tipus de la següent activitat"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "No Contracts"
msgstr "Sense contractes"

#. module: hr_contract
#: model_terms:ir.actions.act_window,help:hr_contract.hr_contract_history_view_list_action
msgid "No data to display"
msgstr "Sense dades per mostrar"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__under_contract_state__blocked
msgid "Not Under Contract"
msgstr "No està sota contracte"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__notes
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Notes"
msgstr "Notes"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'accions"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.res_config_settings_view_form
msgid ""
"Number of days prior to the contract end date that a contract expiration "
"warning is triggered."
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.res_config_settings_view_form
msgid ""
"Number of days prior to the work permit expiration date that a warning is "
"triggered."
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'errors"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Nombre de missatges que requereixen una acció"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de missatges amb error d'entrega"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__kanban_state__normal
msgid "Ongoing"
msgstr "En marxa"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Open Contract"
msgstr "Contracte obert"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__hr_responsible_id
msgid "Person responsible for validating the employee's contracts."
msgstr "Persona responsable de validar els contractes de l'empleat."

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_employee_public
msgid "Public Employee"
msgstr "Empleat públic"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__rating_ids
msgid "Ratings"
msgstr "Valoracions"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__kanban_state__done
msgid "Ready"
msgstr "Preparat"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_list
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Reference Working Time"
msgstr "Referència del temps de treball"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "Detall del temps lliure dels recursos"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_resource_calendar
msgid "Resource Working Time"
msgstr "Temps de treball dels recursos"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_resource_resource
msgid "Resources"
msgstr "Recursos"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_user_id
msgid "Responsible User"
msgstr "Usuari responsable"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__state__open
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__state__open
msgid "Running"
msgstr "En procés"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Running Contracts"
msgstr "Contractes d'execució"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de lliurament SMS"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Salary Information"
msgstr "Informació salarial"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_payroll_structure_type
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__structure_type_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__structure_type_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__name
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Salary Structure Type"
msgstr "Tipus d'estructura salarial"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Search Contract"
msgstr "Cerca contracte"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Search Reference Contracts"
msgstr "Buscar contractes de referència"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_departure_wizard__set_date_end
msgid "Set Contract End Date"
msgstr "Estableix la data de finalització del contracte"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_departure_wizard__set_date_end
msgid "Set the end date on the current contract."
msgstr "Estableix la data de finalització del contracte actual."

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Show all records which have a next action date before today"
msgstr ""
"Mostra tots els registres que tenen una data d’acció següent abans d’avui"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__date_start
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__date_start
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Start Date"
msgstr "Data inicial"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__state
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__state
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Status"
msgstr "Estat"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__activity_state
#: model:ir.model.fields,help:hr_contract.field_hr_contract_history__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estat basat en activitats\n"
"Vençuda: La data límit ja ha passat\n"
"Avui: La data de l'activitat és avui\n"
"Planificat: Activitats futures."

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__state
msgid "Status of the contract"
msgstr "Estat del contracte"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__country_code
#: model:ir.model.fields,help:hr_contract.field_hr_contract_history__country_code
#: model:ir.model.fields,help:hr_contract.field_hr_payroll_structure_type__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"El codi del país ISO en dos caràcters.\n"
"Podeu utilitzar aquest camp per una recerca ràpida."

#. module: hr_contract
#. odoo-python
#: code:addons/hr_contract/models/hr_contract.py:0
msgid "The contract of %s is about to expire."
msgstr "El contracte de %s està a punt d'expirar."

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid "The default working hours are set in configuration."
msgstr ""

#. module: hr_contract
#. odoo-python
#: code:addons/hr_contract/models/hr_contract.py:0
msgid "The work permit of %s is about to expire."
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_kanban
msgid "To"
msgstr "Fins a"

#. module: hr_contract
#: model:mail.message.subtype,name:hr_contract.mt_contract_pending
msgid "To Renew"
msgstr "Per renovar"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Today Activities"
msgstr "Activitats d'avui"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Type in notes about this contract..."
msgstr "Escriviu notes sobre aquest contracte..."

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipus d'activitat d'excepció registrada."

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__under_contract_state__done
msgid "Under Contract"
msgstr "Sota contracte"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_res_users
msgid "User"
msgstr "Usuari"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__visa_no
msgid "Visa No"
msgstr "Número de visat"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__wage
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__wage
msgid "Wage"
msgstr "Salari"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__kanban_state__blocked
msgid "Warning"
msgstr "Avís"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__website_message_ids
msgid "Website Messages"
msgstr "Missatges del lloc web"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicacions del lloc web"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.res_config_settings_view_form
msgid "Work Permit Expiration Notice Period"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_res_company__work_permit_expiration_notice_period
#: model:ir.model.fields,field_description:hr_contract.field_res_config_settings__work_permit_expiration_notice_period
msgid "Work Permit Expiry Notice Period"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__permit_no
msgid "Work Permit No"
msgstr "Número de permís de treball"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__resource_calendar_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__resource_calendar_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Working Schedule"
msgstr "Planificació de treball"

#. module: hr_contract
#. odoo-python
#: code:addons/hr_contract/models/hr_employee.py:0
msgid "You cannot delete an employee with a running contract."
msgstr ""
