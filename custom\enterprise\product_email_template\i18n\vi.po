# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_email_template
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.product_template_form_view
msgid "Automatic Email at Invoice"
msgstr "Email tự động khi xuất hóa đơn"

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.email_template_form_simplified
msgid "Body"
msgstr "Thân"

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.email_template_form_simplified
#: model_terms:ir.ui.view,arch_db:product_email_template.product_template_form_view
msgid "Email Template"
msgstr "Mẫu email"

#. module: product_email_template
#: model:ir.model,name:product_email_template.model_account_move
msgid "Journal Entry"
msgstr "Bút toán"

#. module: product_email_template
#: model:ir.model,name:product_email_template.model_product_template
msgid "Product"
msgstr "Sản phẩm"

#. module: product_email_template
#: model:ir.model.fields,field_description:product_email_template.field_product_product__email_template_id
#: model:ir.model.fields,field_description:product_email_template.field_product_template__email_template_id
msgid "Product Email Template"
msgstr "Mẫu email sản phẩm"

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.product_template_form_view
msgid "Send a product-specific email once the invoice is validated"
msgstr "Gửi email về sản phẩm cụ thể sau khi hóa đơn được xác thực"

#. module: product_email_template
#: model:ir.model.fields,help:product_email_template.field_product_product__email_template_id
#: model:ir.model.fields,help:product_email_template.field_product_template__email_template_id
msgid ""
"When validating an invoice, an email will be sent to the customer based on "
"this template. The customer will receive an email for each product linked to"
" an email template."
msgstr ""
"Khi xác thực hóa đơn, một email sẽ được gửi cho khách hàng dựa trên mẫu này."
" Khách hàng sẽ nhận được email cho mỗi sản phẩm được liên kết với một mẫu "
"email."
