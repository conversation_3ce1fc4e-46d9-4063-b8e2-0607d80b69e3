# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_matrix
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid ""
"<small>Switch to the \"else\" condition of this block to view or edit the "
"table.</small>"
msgstr ""
"<small>Переключитесь на условие \"else\" этого блока, чтобы просмотреть или "
"отредактировать таблицу.</small>"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid "<strong>Product matrix block</strong>"
msgstr "<strong>Блок матрицы продукта</strong>"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_color_1
msgid "Blue"
msgstr "Синий"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid "Cell name"
msgstr "Название ячейки"

#. module: product_matrix
#. odoo-javascript
#: code:addons/product_matrix/static/src/xml/product_matrix_dialog.xml:0
msgid "Choose Product Variants"
msgstr "Выберите варианты продуктов"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid "Column name"
msgstr "Название колонки"

#. module: product_matrix
#. odoo-javascript
#: code:addons/product_matrix/static/src/xml/product_matrix_dialog.xml:0
msgid "Confirm"
msgstr "Подтвердить"

#. module: product_matrix
#. odoo-javascript
#: code:addons/product_matrix/static/src/xml/product_matrix_dialog.xml:0
msgid "Discard"
msgstr "Отменить"

#. module: product_matrix
#: model:product.attribute,name:product_matrix.product_attribute_gender
msgid "Gender"
msgstr "Пол"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_m
msgid "Men"
msgstr "Мужчина"

#. module: product_matrix
#: model:product.template,name:product_matrix.matrix_product_template_shirt
msgid "My Company Tshirt (GRID)"
msgstr "Футболка \"Моя компания\" (ГРИД)"

#. module: product_matrix
#. odoo-javascript
#: code:addons/product_matrix/static/src/xml/product_matrix.xml:0
msgid "Not available"
msgstr "Недоступно"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_color_2
msgid "Pink"
msgstr "Розовый"

#. module: product_matrix
#: model:ir.model,name:product_matrix.model_product_template
msgid "Product"
msgstr "Товар"

#. module: product_matrix
#: model:ir.model,name:product_matrix.model_product_template_attribute_value
msgid "Product Template Attribute Value"
msgstr "Значение атрибута шаблона продукта"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_color_4
msgid "Rainbow"
msgstr "Радуга"

#. module: product_matrix
#: model:product.template,description_sale:product_matrix.matrix_product_template_shirt
msgid "Show your company love around you =)."
msgstr "Покажите своей компании любовь вокруг вас =)."

#. module: product_matrix
#: model:product.attribute,name:product_matrix.product_attribute_size
msgid "Size"
msgstr "Размер"

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.matrix
msgid ""
"The matrix of product variants of this order will be displayed here, if "
"there are any."
msgstr ""
"Здесь будет отображена матрица вариантов продуктов для данного заказа, если "
"таковые имеются."

#. module: product_matrix
#: model_terms:ir.ui.view,arch_db:product_matrix.extra_price
msgid "Variant price"
msgstr "Цена варианта"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_w
msgid "Women"
msgstr "Женщины"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_size_xl
msgid "XL"
msgstr "XL"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_size_xs
msgid "XS"
msgstr "XS"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_color_3
msgid "Yellow"
msgstr "Желтый"
