# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_expense
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:02+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "$100.00"
msgstr "$100.00"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "$120.00"
msgstr "$120.00"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "$500.00"
msgstr "$500.00"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "$600.00"
msgstr "$600.00"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "%(date_from)s - %(date_to)s"
msgstr "%(date_from)s - %(date_to)s"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "%(employee_name)s: %(expense_name)s"
msgstr "%(employee_name)s: %(expense_name)s"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid ""
"%(user)s confirms this expense is not a duplicate with similar expense."
msgstr "يؤكد %(user)s بأن هذه النفقة ليست نسخة مكررة من نفقة أخرى. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "%s: It is not from your department"
msgstr "%s: ليس من قسمك "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "%s: It is your own expense"
msgstr "%s: إنها نفقتك الخاصة "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "%s: Your are not a Manager or HR Officer"
msgstr "%s: أنت لست مديراً أو مسؤولاً في الموارد البشرية "

#. module: hr_expense
#: model:ir.actions.report,print_report_name:hr_expense.action_report_hr_expense_sheet
msgid ""
"'Expenses - %s - %s' % (object.employee_id.name, (object.name).replace('/', "
"''))"
msgstr ""
"'النفقات - %s - %s' % (object.employee_id.name, (object.name).replace('/', "
"'')) "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "(incl"
msgstr "(شامل "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "1 %(exp_cur)s = %(rate)s %(comp_cur)s"
msgstr "1 %(exp_cur)s = %(rate)s %(comp_cur)s"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "2023-08-11"
msgstr "2023-08-11"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid "<b>Wasting time recording your receipts?</b> Let’s try a better way."
msgstr "<b>هل تضيع الوقت في تسجيل إيصالاتك؟</b> فلنجرب طريقة أخرى. "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "<i class=\"oi oi-arrow-right\"/> Setup your alias domain"
msgstr "<i class=\"oi oi-arrow-right\"/> إعداد نطاق اللقب الخاص بك "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_move_form_inherit_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_payment_form_inherit_expense
msgid "<span class=\"o_stat_text\">Expense Report</span>"
msgstr "<span class=\"o_stat_text\">تقرير النفقات</span> "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "<span class=\"o_stat_text\">Journal Entry</span>"
msgstr "<span class=\"o_stat_text\">قيد اليومية</span> "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "<span>@</span>"
msgstr "<span>@</span>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<span>Date:</span>"
msgstr "<span>التاريخ:</span> "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<span>Employee:</span>"
msgstr "<span>الموظف:</span>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<span>Manager:</span>"
msgstr "<span>المدير:</span> "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<span>Paid by:</span>"
msgstr "<span>تم الدفع بواسطة:</span> "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_split
msgid "<span>The total amount doesn't match the original amount.</span>"
msgstr "<span>لا يطابق المبلغ الإجمالي المبلغ الأصلي.</span>"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid ""
"A default outstanding account must be defined in the settings for company-"
"paid expenses. Or specify one in the Journal for the %(method)s payment "
"method."
msgstr ""
"يجب تحديد حساب مستحق افتراضي في الإعدادات الخاصة بالنفقات المدفوعة من قبل "
"الشركة. أو قم بتحديد واحد في دفتر يومية طريقة الدفع %(method)s. "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__account_id
msgid "Account"
msgstr "الحساب "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Accounting"
msgstr "المحاسبة "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__accounting_date
msgid "Accounting Date"
msgstr "تاريخ المحاسبة"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_needaction
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_needaction
msgid "Action Needed"
msgstr "إجراء مطلوب"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_ids
msgid "Activities"
msgstr "الأنشطة"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "زخرفة استثناء النشاط"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_state
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_state
msgid "Activity State"
msgstr "حالة النشاط"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_type_icon
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_type_icon
msgid "Activity Type Icon"
msgstr "أيقونة نوع النشاط"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.mail_activity_type_action_config_hr_expense
#: model:ir.ui.menu,name:hr_expense.hr_expense_menu_config_activity_type
msgid "Activity Types"
msgstr "أنواع الأنشطة "

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_manager
msgid "Administrator"
msgstr "المدير "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Alias"
msgstr "لقب"

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_user
msgid "All Approver"
msgstr "الموافق العام "

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_all
msgid "All Expense Reports"
msgstr "كافة تقارير النفقات "

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all
msgid "All Reports"
msgstr "كافة التقارير "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "All expenses in an expense report must have the same \"paid by\" criteria."
msgstr "يجب أن يكون لكافة النفقات في تقرير النفقات نفس فئة \"تم الدفع بواسطة\" "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "All payment methods allowed"
msgstr "كافة طرق الدفع مسموح بها "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__amount_residual
msgid "Amount Due"
msgstr "المبلغ المستحق"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "An"
msgstr " "

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/mixins/document_upload.js:0
msgid "An error occurred during the upload"
msgstr "حدث خطأ أثناء عملية الرفع "

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__account_id
msgid "An expense account is expected"
msgstr "من المتوقع وجود حساب نفقات "

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/expense_form_view.js:0
msgid "An expense of same category, amount and date already exists."
msgstr "هناك نفقة من نفس الفئة والمبلغ والتاريخ موجودة بالفعل. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "An expense report must contain only lines from the same company."
msgstr "يجب أن يحتوي تقرير النفقات على بنود من نفس الشركة. "

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_analytic_account
msgid "Analytic Account"
msgstr "الحساب التحليلي"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__analytic_distribution
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__analytic_distribution
msgid "Analytic Distribution"
msgstr "التوزيع التحليلي"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr "إمكانية تطبيق الخطة التحليلية "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__analytic_precision
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__analytic_precision
msgid "Analytic Precision"
msgstr "الدقة التحليلية "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__approval_date
msgid "Approval Date"
msgstr "تاريخ الموافقة"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__approval_state
msgid "Approval State"
msgstr "حالة الموافقة "

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Approve"
msgstr "موافقة"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/list.xml:0
msgid "Approve Report"
msgstr "الموافقة على التقرير "

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__approved
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__approval_state__approve
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__approve
#: model:mail.message.subtype,name:hr_expense.mt_expense_approved
msgid "Approved"
msgstr "تمت الموافقة "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__approved_by
msgid "Approved By"
msgstr "تمت الموافقة بواسطة "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__approved_on
msgid "Approved On"
msgstr "تمت الموافقة في "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Archived"
msgstr "مؤرشف"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Attach Receipt"
msgstr "إرفاق الإيصال "

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid "Attach a receipt - usually an image or a PDF file."
msgstr "أرفق الإيصال - عادة ما يكون صورة أو ملف PDF."

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_ir_attachment
msgid "Attachment"
msgstr "مرفق"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_attachment_count
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet_img
msgid "Attachment Name"
msgstr "اسم المرفق"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__attachment_ids
msgid "Attachments"
msgstr "المرفقات "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__attachment_ids
msgid "Attachments of expenses"
msgstr "مرفقات النفقات "

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_employee_base
msgid "Basic Employee"
msgstr "الموظف العادي "

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__tax_ids
msgid ""
"Both price-included and price-excluded taxes will behave as price-included "
"taxes for expenses."
msgstr ""
"كلا الأسعار الشاملة للضريبة والأسعار غير الشاملة للضريبة ستتصرف على أنها "
"أسعار شاملة للضريبة بالنسبة للنفقات. "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Business Trip"
msgstr "رحلة عمل "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__can_approve
msgid "Can Approve"
msgstr "بإمكانه الموافقة "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__can_reset
msgid "Can Reset"
msgstr "بإمكانه إعادة تعيين"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_split
msgid "Cancel"
msgstr "إلغاء"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__cannot_approve_reason
msgid "Cannot Approve Reason"
msgstr "سبب عدم الموافقة "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__product_ids
msgid "Categories"
msgstr "الفئات"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Category"
msgstr "الفئة "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Category:"
msgstr "الفئة: "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Category: not found"
msgstr "الفئة: غير موجودة "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_main_attachment_checksum
msgid "Checksum/SHA1"
msgstr "Checksum/SHA1"

#. module: hr_expense
#: model:product.template,name:hr_expense.expense_product_communication_product_template
msgid "Communication"
msgstr "التواصل "

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_res_company
msgid "Companies"
msgstr "الشركات"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__company_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__company_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__company_id
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__payment_mode__company_account
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Company"
msgstr "الشركة "

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_configuration
msgid "Configuration"
msgstr "التهيئة "

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"لا يمكن التحويل بين وحدات القياس إلا إذا كانت تنتمي لنفس الفئة. سيتم إجراء "
"التحويل بناءً على النسب."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_kanban_view
msgid "Cost:"
msgstr "التكلفة:"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: code:addons/hr_expense/static/src/views/kanban.xml:0
#: code:addons/hr_expense/static/src/views/list.xml:0
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Create Report"
msgstr "إنشاء تقرير"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_account
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_all
msgid "Create a new expense report"
msgstr "إنشاء تقرير نفقات جديد "

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid "Create a report to submit one or more expenses to your manager."
msgstr "أنشئ تقريراً لإرسال واحدة أو أكثر من نفقاتك إلى مديرك. "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Create expenses from incoming emails"
msgstr "أنشئ نفقات من رسائل البريد الإلكتروني الواردة "

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_all
msgid "Create new expenses to get statistics."
msgstr "أنشئ نفقات جديدة للحصول على الإحصاءات. "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Credit Card"
msgstr "البطاقة الائتمانية"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__currency_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__currency_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__currency_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__currency_id
msgid "Currency"
msgstr "العملة"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__currency_rate
msgid "Currency Rate"
msgstr "سعر صرف العملة"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Date"
msgstr "التاريخ"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Dear"
msgstr "عزيزي"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__hr_expense_alias_prefix
msgid "Default Alias Name for Expenses"
msgstr "اللقب الافتراضي للنفقات "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_company__expense_journal_id
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__expense_journal_id
msgid "Default Expense Journal"
msgstr "يومية النفقة الافتراضي"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Default accounting journal for expenses paid by employees."
msgstr "دفتر يومية المحاسبة الافتراضي للنفقات المدفوعة من قبل الموظفين."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Default outstanding account for expenses paid by company."
msgstr "الحساب المستحق الافتراضي للنفقات التي دفعتها الشركة. "

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_department
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__department_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Department"
msgstr "القسم"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__name
msgid "Description"
msgstr "الوصف"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Digitalize your receipts with OCR and Artificial Intelligence"
msgstr ""
"قم برقمنة إيصالاتك باستخدام تقنية تمييز الرموز ضوئياً والذكاء الاصطناعي "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__distribution_analytic_account_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "حساب التوزيع التحليلي "

#. module: hr_expense
#: model_terms:digest.tip,tip_description:hr_expense.digest_tip_hr_expense_0
msgid ""
"Do not keep your expense tickets in your pockets any longer. Just snap a "
"picture of your receipt and let Odoo digitalizes it for you. The OCR and "
"Artificial Intelligence will fill the data automatically."
msgstr ""
"لن تضطر إلى حمل تذاكر نفقاتك في جيوبك بعد الآن. فقط قم بالتقاط صورة من "
"الإيصال، وسيقوم أودو برقمنته من أجلك. سوف تقوم خاصية تمييز الرموز ضوئياً "
"والذكاء الاصطناعي بملء البيانات تلقائياً. "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_move_form_inherit_expense
msgid ""
"Do you really want to invoice your own company? Remove the \"Company Name\" "
"from the partner to fix the configuration. Cancel this invoice and start "
"again."
msgstr ""
"هل أنت متأكد من أنك ترغب في إصدار فاتورة لشركتك؟ أزل اسم الشركة من خانة "
"الشريك لإصلاح هذه التهيئة. قم بإلغاء هذه الفاتورة ثم ابدأ من جديد. "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "النطاق"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__done
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__done
msgid "Done"
msgstr "منتهي "

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/mixins/qrcode.js:0
msgid "Download our App"
msgstr "حمّل تطبيقنا "

#. module: hr_expense
#: model:mail.message.subtype,name:hr_expense.mt_expense_reset
msgid "Draft"
msgstr "مسودة"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/wizard/hr_expense_approve_duplicate.py:0
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__duplicate_expense_ids
msgid "Duplicate Expense"
msgstr "نفقة مكررة "

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_employee
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__employee_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__employee_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__employee_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Employee"
msgstr "الموظف"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__payment_mode__own_account
msgid "Employee (to reimburse)"
msgstr "الموظف (لرد الأموال) "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Employee Expense Journal"
msgstr "دفتر يومية نفقات الموظفين "

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_account
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_account_employee_expenses
msgid "Employee Expenses"
msgstr "نفقات الموظف "

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid ""
"Enter a name then choose a category and configure the amount of your "
"expense."
msgstr "أدخل اسماً ثم قم بتحديد الفئة وتهيئة مبلغ نفقاتك. "

#. module: hr_expense
#: model:account.journal,name:hr_expense.hr_expense_account_journal
#: model:ir.model,name:hr_expense.model_hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_account_move_line__expense_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee__expense_manager_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__expense_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__expense_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__expense_id
#: model:ir.model.fields,field_description:hr_expense.field_res_users__expense_manager_id
#: model:ir.model.fields.selection,name:hr_expense.selection__account_analytic_applicability__business_domain__expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Expense"
msgstr "النفقة "

#. module: hr_expense
#: model:mail.activity.type,name:hr_expense.mail_act_expense_approval
msgid "Expense Approval"
msgstr "الموافقة على النفقة "

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_approve_duplicate
msgid "Expense Approve Duplicate"
msgstr "تكرار الموافقة على النفقة"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_employee_tree_inherit_expense
msgid "Expense Approver"
msgstr "المُوافق على النفقات "

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_product
#: model:ir.ui.menu,name:hr_expense.menu_hr_product
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Expense Categories"
msgstr "فئات النفقة"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__date
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Expense Date"
msgstr "تاريخ النفقات "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Expense Digitalization (OCR)"
msgstr "رقمنة النفقات (تمييز الرموز ضوئياً) "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__journal_id
msgid "Expense Journal"
msgstr "دفتر يومية النفقات "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__expense_line_ids
msgid "Expense Lines"
msgstr "بنود النفقات "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__is_editable
msgid "Expense Lines Are Editable By Current User"
msgstr "يمكن تحرير بنود النفقات من قِبَل المستخدم الحالي "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee_public__expense_manager_id
msgid "Expense Manager"
msgstr "مدير النفقات "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Expense Outstanding Account"
msgstr "حساب النفقات المستحق "

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_refuse_wizard
msgid "Expense Refuse Reason Wizard"
msgstr "معالج سبب رفض النفقات "

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_sheet
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__sheet_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Expense Report"
msgstr "تقرير النفقات "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__accounting_date
msgid "Expense Report Date"
msgstr "تاريخ تقرير النفقات "

#. module: hr_expense
#: model:ir.actions.report,name:hr_expense.action_report_expense_sheet_img
msgid "Expense Report Image"
msgstr "صورة تقرير النفقات "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__name
msgid "Expense Report Summary"
msgstr "ملخص تقرير النفقة "

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_report
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Expense Reports"
msgstr "تقارير النفقات "

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_department_filtered
msgid "Expense Reports Analysis"
msgstr "تحليل تقارير النفقات "

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_department_to_approve
msgid "Expense Reports to Approve"
msgstr "تقارير النفقات المطلوب الموافقة عليها "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_account_bank_statement_line__expense_sheet_id
#: model:ir.model.fields,field_description:hr_expense.field_account_move__expense_sheet_id
#: model:ir.model.fields,field_description:hr_expense.field_account_payment__expense_sheet_id
msgid "Expense Sheet"
msgstr "ورقة النفقات "

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_split
msgid "Expense Split"
msgstr "تقسيم النفقات"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__expense_split_line_ids
msgid "Expense Split Line"
msgstr "بند تقسيم النفقات "

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_split_wizard
msgid "Expense Split Wizard"
msgstr "معالج تقسيم النفقات"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
msgid "Expense Validate Duplicate"
msgstr "تكرار التحقق من النفقة"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_product
msgid "Expense categories can be reinvoiced to your customers."
msgstr "فئات النفقات يمكن إعادة فوترتها الى عملائك."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/account_move.py:0
msgid "Expense entry created from: %s"
msgstr "تم إنشاء قيد نفقة من: %s "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "Expense refuse reason"
msgstr "سبب رفض النفقة "

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_approved
msgid "Expense report approved, entry created for accountant"
msgstr "تمت الموافقة على تقرير النفقات وتم إنشاء القيد للمحاسب "

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_paid
msgid "Expense report paid"
msgstr "تم سداد قيمة تقرير النفقة "

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_refused
msgid "Expense report refused"
msgstr "تم رفض تقرير النفقة "

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_reset
msgid "Expense report reset to Draft"
msgstr "تقارير النفقات المطلوب تعيينها كمسودات "

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all
msgid ""
"Expense reports regroup all the expenses incurred during a specific event."
msgstr ""
"تقوم تقارير النفقات بإعادة تجميع كافة النفقات الناجمة عن فعالية محددة. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "Expense split"
msgstr "تقسيم النفقات"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
#: model:ir.model.fields,field_description:hr_expense.field_product_product__can_be_expensed
#: model:ir.model.fields,field_description:hr_expense.field_product_template__can_be_expensed
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_root
#: model:product.template,name:hr_expense.product_product_no_cost_product_template
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_activity
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_activity
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.product_template_search_view_inherit_hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Expenses"
msgstr "النفقات "

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_actions_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_all_expenses
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_graph
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_graph
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_pivot
msgid "Expenses Analysis"
msgstr "تحليل النفقات "

#. module: hr_expense
#: model:ir.actions.report,name:hr_expense.action_report_hr_expense_sheet
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Expenses Report"
msgstr "تقرير النفقات "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_department__expense_sheets_to_approve_count
msgid "Expenses Reports to Approve"
msgstr "تقارير النفقات بانتظار الموافقة "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Expenses by Date"
msgstr "النفقات حسب التاريخ "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Expenses of Your Team Member"
msgstr "نفقات عضو فريقك "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "Expenses with a similar receipt to %(other_expense_name)s"
msgstr "النفقات التي بها إيصال مشابه لـ %(other_expense_name)s "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee__filter_for_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee_base__filter_for_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee_public__filter_for_expense
msgid "Filter For Expense"
msgstr "عامل تصفية النفقات "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Flight Ticket"
msgstr "تذكرة الطيران "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_follower_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_follower_ids
msgid "Followers"
msgstr "المتابعين"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_partner_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء) "

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_type_icon
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "أيقونة من Font awesome مثال: fa-tasks "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Former Employees"
msgstr "الموظفين السابقين"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Future Activities"
msgstr "الأنشطة المستقبلية"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "Generate Expenses"
msgstr "إنشاء النفقات "

#. module: hr_expense
#: model:product.template,name:hr_expense.expense_product_gift_product_template
msgid "Gifts"
msgstr "الهدايا "

#. module: hr_expense
#: model_terms:product.template,description:hr_expense.expense_product_gift_product_template
msgid "Gifts to customers or vendors"
msgstr "هدايا للعملاء أو البائعين"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "Go to settings"
msgstr "Go to settings"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Group By"
msgstr "تجميع حسب"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Guideline"
msgstr "الإرشادات "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__is_multiple_currency
msgid "Handle lines with different currencies"
msgstr "التعامل مع البنود مختلفة العملة"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__has_message
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__has_message
msgid "Has Message"
msgstr "يحتوي على رسالة "

#. module: hr_expense
#: model_terms:product.template,description:hr_expense.expense_product_travel_accommodation_product_template
msgid "Hotel, plane ticket, taxi, etc."
msgstr "فندق ، تذكرة طائرة ، سيارة أجرة ، إلخ."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__hr_expense_alias_domain_id
msgid "Hr Expense Alias Domain"
msgstr "نطاق لقب البريد الإلكتروني لنفقات الموارد البشرية "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__id
msgid "ID"
msgstr "المُعرف"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_exception_icon
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_exception_icon
msgid "Icon"
msgstr "الأيقونة"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_exception_icon
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "الأيقونة للإشارة إلى النشاط المستثنى. "

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_needaction
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_needaction
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة عليك رؤيتها. "

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_sms_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "In Payment"
msgstr "بانتظار التسوية "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__tax_ids
msgid "Included taxes"
msgstr "الضرائب المشمولة "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Incoming Emails"
msgstr "رسائل البريد الإلكتروني الواردة"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__description
msgid "Internal Notes"
msgstr "ملاحظات داخلية"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "Invalid attachments!"
msgstr "المرفقات غير صالحة! "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__is_editable
msgid "Is Editable By Current User"
msgstr "يمكن تحريره من قِبَل المستخدم الحالي "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_is_follower
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__is_multiple_currency
msgid "Is currency_id different from the company_currency_id"
msgstr " currency_id مختلف عن company_currency_id "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__product_has_cost
msgid "Is product with non zero cost selected"
msgstr "المنتج المحدد تكلفته ليست صفراً "

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid "It all begins here - let's go!"
msgstr "كل شيء يبدأ من هنا - فلننطلق! "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__employee_journal_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Journal"
msgstr "دفتر اليومية"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__account_move_ids
msgid "Journal Entries"
msgstr "قيود اليومية "

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_move
msgid "Journal Entry"
msgstr "قيد اليومية"

#. module: hr_expense
#: model:mail.message.subtype,name:hr_expense.mt_expense_entry_delete
msgid "Journal Entry Deleted"
msgstr "تم حذف قيد اليومية "

#. module: hr_expense
#: model:mail.message.subtype,name:hr_expense.mt_expense_entry_draft
msgid "Journal Entry Reset to Draft"
msgstr "تم إعادة تعيين قيود اليومية إلى حالة المسودة "

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_move_line
msgid "Journal Item"
msgstr "عنصر اليومية"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "Journal entries"
msgstr "قيود اليومية "

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_entry_delete
msgid "Journal entry deleted"
msgstr "تم حذف قيد اليومية "

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_entry_draft
msgid "Journal entry reset to draft"
msgstr "تم إعادة تعيين قيود اليومية إلى حالة المسودة "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__label_currency_rate
msgid "Label Currency Rate"
msgstr "تسمية سعر العملة "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Late Activities"
msgstr "الأنشطة المتأخرة"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__hr_expense_use_mailgateway
msgid "Let your employees record expenses by email"
msgstr "أتح لموظفيك تسجيل النفقات عن طريق البريد الإلكتروني "

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid "Let's check out where you can manage all your employees expenses"
msgstr "فلنلقِ نظرة على المكان الذي يمكنك إدارة كافة نفقات موظفيك فيه "

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid "Let's go back to your expenses."
msgstr "فلنعد إلى نفقاتك. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "Lunch with customer $12.32"
msgstr "غداء مع العميل $12.32 "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_main_attachment_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_main_attachment_id
msgid "Main Attachment"
msgstr "المرفق الرئيسي"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__user_id
msgid "Manager"
msgstr "المدير"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid ""
"Managers can approve the report here, then an accountant can post the "
"accounting entries."
msgstr ""
"بوسع المدراء الوافقة على التقرير من هنا، ثم يقوم المحاسب بترحيل القيود "
"المحاسبية. "

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid "Managers can inspect all expenses from here."
msgstr "يمكن للمديرين فحص جميع النفقات من هنا."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Marc Demo"
msgstr "مارك ديمو "

#. module: hr_expense
#: model:product.template,name:hr_expense.expense_product_meal_product_template
msgid "Meals"
msgstr "وجبات طعام"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_error
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: hr_expense
#: model:product.template,name:hr_expense.expense_product_mileage_product_template
msgid "Mileage"
msgstr "المسافات المقطوعة "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Mitchell Admin"
msgstr "ميتشل آدمن "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "الموعد النهائي لنشاطاتي "

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_actions_my_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_my_expenses
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_my_expenses_all
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "My Expenses"
msgstr "نفقاتي "

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_my_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_my_reports
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "My Reports"
msgstr "تقاريري"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "My Team"
msgstr "فريقي"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Name"
msgstr "الاسم"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/kanban.xml:0
#: code:addons/hr_expense/static/src/views/list.xml:0
msgid "New"
msgstr "جديد"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "New Expense Report, paid by %(paid_by)s"
msgstr "تقرير نفقات جديد مدفوع بواسطة %(paid_by)s "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "New Expense Reports"
msgstr "تقارير نفقة جديدة"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "الفعالية التالية في تقويم الأنشطة "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_date_deadline
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "الموعد النهائي للنشاط التالي"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_summary
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_summary
msgid "Next Activity Summary"
msgstr "ملخص النشاط التالي"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_type_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_type_id
msgid "Next Activity Type"
msgstr "نوع النشاط التالي"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "No attachment was provided"
msgstr "لم يتم توفير مرفق"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_department_filtered
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_all
msgid "No data yet!"
msgstr "لا توجد أي بيانات بعد! "

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_product
msgid "No expense categories found. Let's create one!"
msgstr "لم يتم العثور على فئات نفقات. فلنقم بإنشاء واحدة! "

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_my_all
msgid "No expense report found. Let's create one!"
msgstr "لم يتم العثور على تقرير نفقات. فلنقم بإنشاء واحد! "

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all
msgid "No expense reports found. Let's create one!"
msgstr "لم يتم العثور على تقارير نفقات. فلنقم بإنشاء واحد! "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "No work contact found for the employee %s, please configure one."
msgstr "لم يتم العثور على عقد عمل للموظف %s، يرجى تهيئة واحد. "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Not Refused"
msgstr "لم يتم الرفض "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_categories_tree_view
msgid "Note"
msgstr "الملاحظات"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Notes..."
msgstr "ملاحظات..."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_needaction_counter
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__nb_attachment
msgid "Number of Attachments"
msgstr "عدد المرفقات"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__nb_expense
msgid "Number of Expenses"
msgstr "عدد النفقات"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__nb_account_move
msgid "Number of Journal Entries"
msgstr "عدد قيود اليومية "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_error_counter
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء "

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_needaction_counter
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "عدد الرسائل التي تتطلب اتخاذ إجراء"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_error_counter
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/components/expense_dashboard.xml:0
msgid "Numbers computed from your personal expenses."
msgstr "تم احتساب الأرقام من نفقاتك الخاصة. "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register_no_user
msgid "Odoo"
msgstr "أودو"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_account
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_all
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_my_all
msgid ""
"Once you have created your expense, submit it to your manager who will "
"validate it."
msgstr "بمجرد إنشاء نفقتك، قم بإرسالها لمديرك للموافقة عليها. "

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid ""
"Once your <b>Expense Report</b> is ready, you can submit it to your manager "
"and wait for approval."
msgstr ""
"بمجرد تجهيز <b>تقرير نفقاتك</b>، يمكنك إرساله لمديرك وانتظار موافقته. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "Only HR Officers or the concerned employee can reset to draft."
msgstr ""
"وحدهم موظفو الموارد البشرية أو الموظف المعني يسعهم إعادة تعيين النفقة إلى "
"حالة المسودة. "

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid "Open bugger menu."
msgstr "فتح القائمة "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_split
msgid "Original Amount"
msgstr "المبلغ الأصلي"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_company__expense_outstanding_account_id
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__expense_outstanding_account_id
msgid "Outstanding Account"
msgstr "حساب مستحق "

#. module: hr_expense
#: model:mail.message.subtype,name:hr_expense.mt_expense_paid
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Paid"
msgstr "مدفوع"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__payment_mode
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__payment_mode
msgid "Paid By"
msgstr "مدفوعة بواسطة"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Partial"
msgstr "جزئي"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/list.xml:0
#: model:ir.model,name:hr_expense.model_account_payment_register
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Pay"
msgstr "الدفع "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__payment_method_line_id
msgid "Payment Method"
msgstr "طريقة الدفع "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__payment_state
msgid "Payment Status"
msgstr "حالة الدفع "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/account_payment.py:0
msgid "Payment created for: %s"
msgstr "تم إنشاء الدفع لـ: %s "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Payment method allowed for expenses paid by company."
msgstr "طريقة الدفع المسموح بها للنفقات التي تدفعها الشركة. "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Payment methods"
msgstr "طرق الدفع "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_company__company_expense_allowed_payment_method_line_ids
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__company_expense_allowed_payment_method_line_ids
msgid "Payment methods available for expenses paid by company"
msgstr "طرق الدفع المتاحة للنفقات التي تدفعها الشركة "

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_payment
msgid "Payments"
msgstr "الدفعات"

#. module: hr_expense
#: model_terms:product.template,description:hr_expense.expense_product_communication_product_template
msgid "Phone bills, postage, etc."
msgstr "فواتير الهاتف والبريد وما إلى ذلك."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid ""
"Please specify an expense journal in order to generate accounting entries."
msgstr "يُرجى تحديد دفتر يومية للنفقات حتى تتمكن من إنشاء قيود محاسبية. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid ""
"Please specify if the expenses for this report were paid by the company, or "
"the employee"
msgstr ""
"يرجى تحديد ما إذا كانت النفقات الخاصة بهذا التقرير قد تم دفعها من قِبَل "
"الشركة أو الموظف "

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/list.xml:0
msgid "Post Entries"
msgstr "ترحيل القيود "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Post Journal Entries"
msgstr "ترحيل قيود اليومية"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__post
msgid "Posted"
msgstr "مُرحّل "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register_no_user
msgid "Powered by"
msgstr "مشغل بواسطة "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Price:"
msgstr "السعر:"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_product_template
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__product_id
msgid "Product"
msgstr "المنتج"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_description
msgid "Product Description"
msgstr "وصف المنتج"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_has_cost
msgid "Product Has Cost"
msgstr "المنتج به تكلفة "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Product Name"
msgstr "اسم المنتج"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_product_product
msgid "Product Variant"
msgstr "متغير المنتج "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_tree_view
msgid "Product Variants"
msgstr "متغيرات المنتج "

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_employee_public
msgid "Public Employee"
msgstr "موظف في القطاع العام"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__quantity
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Quantity"
msgstr "الكمية"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__rating_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__rating_ids
msgid "Ratings"
msgstr "التقييمات "

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid ""
"Ready? You can save it manually or discard modifications from here. You "
"don't <em>need to save</em> - Odoo will save eveyrthing for you when you "
"navigate."
msgstr ""
"أأنت جاهز؟ يمكنك الحفظ يدوياً أو إهمال التعديلات من هنا. لست <em>بحاجة إلى "
"الحفظ</em> - سيقوم أودو بحفظ كل شيء من أجلك عندما تتصفح. "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__reason
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "Reason"
msgstr "السبب"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Reason:"
msgstr "السبب:"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_categories_tree_view
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Reference"
msgstr "المرجع "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Refund employees via their payslips."
msgstr "رد الأموال إلى الموظفين من خلال كشوف رواتبهم. "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Refuse"
msgstr "رفض"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_refuse_wizard_action
msgid "Refuse Expense"
msgstr "رفض النفقة "

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__refused
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__approval_state__cancel
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__cancel
#: model:mail.message.subtype,name:hr_expense.mt_expense_refused
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Refused"
msgstr "تم الرفض "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Refused Expenses"
msgstr "النفقات المرفوضة "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__module_hr_payroll_expense
msgid "Reimburse Expenses in Payslip"
msgstr "استرداد النفقات في إيصال الدفع "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Reimburse in Payslip"
msgstr "الاسترداد في إيصال الدفع "

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_ir_actions_report
msgid "Report Action"
msgstr "إجراء التقرير"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__company_currency_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__company_currency_id
msgid "Report Company Currency"
msgstr "تقرير عملة المؤسسة "

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_reports
msgid "Reporting"
msgstr "إعداد التقارير "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Reset to Draft"
msgstr "إعادة التعيين كمسودة "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_user_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_user_id
msgid "Responsible User"
msgstr "المستخدم المسؤول"

#. module: hr_expense
#: model_terms:product.template,description:hr_expense.expense_product_meal_product_template
msgid "Restaurants, business lunches, etc."
msgstr "المطاعم ووجبات غداء العمل ، إلخ."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_sms_error
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل النصية القصيرة "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_categories_tree_view
msgid "Sales Price"
msgstr "سعر البيع"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__same_receipt_expense_ids
msgid "Same Receipt Expense"
msgstr "نفقات في إيصال واحد "

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/kanban.xml:0
#: code:addons/hr_expense/static/src/views/list.xml:0
msgid "Scan"
msgstr "مسح"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/components/qrcode_action.xml:0
msgid "Scan this QR code to get the Odoo app:"
msgstr "امسح رمز QR للحصول على تطبيق أودو: "

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_employee__expense_manager_id
#: model:ir.model.fields,help:hr_expense.field_res_users__expense_manager_id
msgid ""
"Select the user responsible for approving \"Expenses\" of this employee.\n"
"If empty, the approval is done by an Administrator or Approver (determined in settings/users)."
msgstr ""
"قم بتحديد المستخدم المسؤول عن الموافقة على \"نفقات\" هذا الموظف. \n"
"إذا كان فارغاً، تتم الموافقة بواسطة مدير أو مانح الموافقة (يحدد في الإعدادات/المستخدمين). "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__selectable_payment_method_line_ids
msgid "Selectable Payment Method Line"
msgstr "بند طريقة الدفع القابل للتحديد "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid ""
"Send an email to this email alias with the receipt in attachment to create "
"an expense in one click. If the first word of the mail subject contains the "
"category's internal reference or the category name, the corresponding "
"category will automatically be set. Type the expense amount in the mail "
"subject to set it on the expense too."
msgstr ""
"أرسل بريداً إلكترونياً إلى لقب البريد الإلكتروني هذا مرفقاً الإيصال لإنشاء "
"نفقة بضغطة زر. إذا كانت الكلمة الأولى من موضوع البريد الإلكتروني تحتوي على "
"المرجع الداخلي للفئة أو اسم الفئة، سوف يتم تعيين الفئة المقابلة تلقائياً. "
"اكتب مبلغ النفقة في موضوع البريد الإلكتروني لتعيينه في النفقة أيضاً. "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__module_hr_expense_extract
msgid "Send bills to OCR to generate expenses"
msgstr "أرسل الفواتير لتمييز الرموز ضوئياً وإنشاء النفقات "

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_configuration
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_global_settings
msgid "Settings"
msgstr "الإعدادات"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__sheet_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__sheet_ids
msgid "Sheet"
msgstr "ورقة"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_account_bank_statement_line__show_commercial_partner_warning
#: model:ir.model.fields,field_description:hr_expense.field_account_move__show_commercial_partner_warning
msgid "Show Commercial Partner Warning"
msgstr "عرض تحذير الشريك التجاري "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Show all records which has next action date is before today"
msgstr ""
"عرض كافة السجلات التي يسبق تاريخ الإجراء التالي فيها تاريخ اليوم الجاري "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "Show missing work email employees"
msgstr "إظهار الموظفين الذين ينقصهم البريد الإلكتروني للعمل "

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__accounting_date
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__accounting_date
msgid "Specify the bill date of the related vendor bill."
msgstr "حدد تاريخ الفاتورة لفاتورة المورّد ذو الصلة. "

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_product_product__can_be_expensed
#: model:ir.model.fields,help:hr_expense.field_product_template__can_be_expensed
msgid "Specify whether the product can be selected in an expense."
msgstr "تحديد ما إذا كان بالإمكان اختيار المنتج في النفقات. "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_split
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Split Expense"
msgstr "تقسيم النفقات "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/wizard/hr_expense_split_wizard.py:0
msgid "Split Expenses"
msgstr "تقسيم النفقات "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__split_possible
msgid "Split Possible"
msgstr "يمكن التقسيم "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_product_product__standard_price_update_warning
msgid "Standard Price Update Warning"
msgstr "التحذير عند تحديث السعر القياسي "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__state
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__state
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Status"
msgstr "الحالة"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_state
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"الأنشطة المعتمدة على الحالة\n"
"المتأخرة: تاريخ الاستحقاق مر\n"
"اليوم: تاريخ النشاط هو اليوم\n"
"المخطط: الأنشطة المستقبلية."

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/list.xml:0
msgid "Submit"
msgstr "إرسال"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Submit to Manager"
msgstr "إرسال إلى المدير "

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__submitted
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__approval_state__submit
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__submit
msgid "Submitted"
msgstr "تم الإرسال"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Subtotal"
msgstr "الناتج الفرعي"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Subtotal In Currency"
msgstr "الإجمالي الفرغي بالعملة "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Subtotal in currency"
msgstr "الإجمالي الفرعي بالعملة "

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_tax
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__tax_ids
msgid "Tax"
msgstr "الضريبة"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Tax 15%"
msgstr "ضريبة 15% "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__tax_amount
msgid "Tax amount"
msgstr "مبلغ الضريبة"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__tax_amount_currency
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__tax_amount_currency
msgid "Tax amount in Currency"
msgstr "مبلغ الضريبة بالعملة"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__tax_amount
msgid "Tax amount in company currency"
msgstr "مبلغ الضريبة بعملة الشركة"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__tax_amount_currency
msgid "Tax amount in currency"
msgstr "مبلغ الضريبة بالعملة"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__total_tax_amount
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__tax_amount_currency
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_split
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Taxes"
msgstr "الضرائب"

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_team_approver
msgid "Team Approver"
msgstr "مانح موافقات الفريق "

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_res_company__expense_outstanding_account_id
#: model:ir.model.fields,help:hr_expense.field_res_config_settings__expense_outstanding_account_id
msgid ""
"The account used to record the outstanding amount of the company expenses."
msgstr "الحساب المستخدم لتسجيل المبلغ المستحق من نفقات الشركة. "

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_res_company__expense_journal_id
#: model:ir.model.fields,help:hr_expense.field_res_config_settings__expense_journal_id
msgid ""
"The company's default journal used when an employee expense is created."
msgstr "دفتر اليومية الافتراضي للشركة المستخدم عند إنشاء نفقات الموظف. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "The current user has no related employee. Please, create one."
msgstr "لا يوجد للمستخدم الحالي موظف مرتبط به. يرجى إنشاء واحد. "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid ""
"The first word of the email subject did not correspond to any category code."
" You'll have to set the category manually on the expense."
msgstr ""
"لم تتوافق أول كلمة من موضوع البريد الإلكتروني مع أي رمز فئة. سيتوجب عليك "
"تعيين الفئة يدوياً في النفقة. "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
msgid ""
"The following approved expenses have similar employee, amount and category "
"than some expenses of this report. Please verify this report does not "
"contain duplicates."
msgstr ""
"لدى النفقات التالية الموافق عليها نفس الموظف والمبلغ والفئة لنفقات أخرى في "
"هذا التقرير. يرجى التحقق من أن هذا التقرير لا يحتوي على بيانات مكررة. "

#. module: hr_expense
#: model:ir.model.constraint,message:hr_expense.constraint_hr_expense_sheet_journal_id_required_posted
msgid "The journal must be set on posted expense"
msgstr "يجب تعيين تفتر اليومية للنفقة المُرحلة. "

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__employee_journal_id
msgid "The journal used when the expense is paid by employee."
msgstr "دفتر اليومية المستخدم عندما يتم دفع النفقة من قِبَل الموظف. "

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__payment_method_line_id
msgid "The payment method used when the expense is paid by the company."
msgstr "طريقة الدفع المستخدمة عند سداد الشركة للنفقات. "

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid "The status of all your current expenses is visible from here."
msgstr "حالة كافة نفقاتك الحالية مرئية من هنا. "

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_split_wizard__split_possible
msgid "The sum of after split shut remain the same"
msgstr "المجموع بعد الانقسام يبقى كما هو"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid ""
"The work email of some employees is missing. Please add it on the employee "
"form"
msgstr ""
"البريد الإلكتروني للعمل لبعض الموظفين مفقود. يرجى إضافته إلى استمارة الموظف."
" "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/product_product.py:0
msgid ""
"There are unsubmitted expenses linked to this category. Updating the "
"category cost will change expense amounts. Make sure it is what you want to "
"do."
msgstr ""
"توجد نفقات لم يتم إرسالها مرتبطة بهذه الفئة. سيؤدي تحديث تكلفة الفئة إلى "
"تغيير مبالغ النفقات. تأكد من أنه ما ترغب القيام به. "

#. module: hr_expense
#: model_terms:web_tour.tour,rainbow_man_message:hr_expense.hr_expense_tour
msgid "There you go - expense management in a nutshell!"
msgstr "هذا كل شيء - إدارة النفقات باختصار! "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid ""
"This expense report is empty. You cannot submit or approve an empty expense "
"report."
msgstr ""
"تقرير النفقات هذا فارغ. لا يمكنك تقديم أو الموافقة على تقرير نفقات فارغ. "

#. module: hr_expense
#: model:digest.tip,name:hr_expense.digest_tip_hr_expense_0
#: model_terms:digest.tip,tip_description:hr_expense.digest_tip_hr_expense_0
msgid "Tip: Snap pictures of your receipts with the remote app"
msgstr "نصيحة: التقط صوراً لإيصالاتك باستخدام تطبيق التحكم عن بعد "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "Tip: try sending receipts by email"
msgstr "نصيحة: جرب إرسال الإيصالات عن طريق البريد الإلكتروني "

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__draft
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "To Report"
msgstr "لإعداد التقارير "

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__reported
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__draft
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "To Submit"
msgstr "للإرسال"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "To be Reimbursed"
msgstr "بانتظار التعويض "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Today Activities"
msgstr "أنشطة اليوم "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__total_amount
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__total_amount
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Total"
msgstr "الإجمالي"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__total_amount_currency
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Total Amount"
msgstr "المبلغ الإجمالي"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__total_amount_currency
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__total_amount_currency
msgid "Total In Currency"
msgstr "الإجمالي بالعملة "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
msgid "Total Taxes"
msgstr "إجمالي الضرائب"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__untaxed_amount_currency
msgid "Total Untaxed Amount In Currency"
msgstr "إجمالي المبلغ غير شامل الضريبة بالعملة "

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_split_wizard__total_amount_currency_original
msgid "Total amount of the original Expense that we are splitting"
msgstr "إجمالي مبلغ النفقات الأصلية التي نقوم بتقسيمها"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__total_amount_currency_original
msgid "Total amount original"
msgstr "إجمالي المبلغ الأصلي"

#. module: hr_expense
#: model:product.template,name:hr_expense.expense_product_travel_accommodation_product_template
msgid "Travel & Accommodation"
msgstr "السفر والإقامة"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_exception_decoration
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "نوع النشاط المستثنى في السجل. "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Under Validation"
msgstr "قيد التصديق "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__price_unit
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Unit Price"
msgstr "سعر الوحدة"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_uom_id
msgid "Unit of Measure"
msgstr "وحدة القياس"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__untaxed_amount
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Untaxed Amount"
msgstr "المبلغ دون الضريبة "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_uom_category_id
msgid "UoM Category"
msgstr "فئة وحدة القياس "

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/kanban.xml:0
#: code:addons/hr_expense/static/src/views/list.xml:0
msgid "Upload"
msgstr "رفع"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
msgid "Upload or drop an expense receipt"
msgstr "قم برفع أو وضع إيصال النفقات "

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
msgid "Use the breadcrumbs to go back to the list of expenses."
msgstr "استخدم آثار التتبع للعودة إلى قائمة النفقات. "

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_res_users
msgid "User"
msgstr "المستخدم"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_approve_duplicate_action
msgid "Validate Duplicate Expenses"
msgstr "تصديق النفقات المكررة "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__vendor_id
msgid "Vendor"
msgstr "المورّد "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "View Attachments"
msgstr "عرض المرفقات"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "View Expense"
msgstr "عرض النفقة "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "View Report"
msgstr "عرض التقرير"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__website_message_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع الإلكتروني "

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__website_message_ids
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع الإلكتروني "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid ""
"When the cost of an expense product is different than 0, then the user"
"                                             using this product won't be "
"able to change the amount of the expense,"
"                                             only the quantity. Use a cost "
"different than 0 for expense categories funded by"
"                                             the company at fixed cost like "
"allowances for mileage, per diem, accommodation"
"                                             or meal."
msgstr ""
"عندما تكون تكلفة منتج النفقات مختلفة عن 0، فلن يتمكن المستخدم الذي يستخدم "
"هذا المنتج من تغيير مبلغ النفقات، فقط الكمية. استخدم تكلفة مختلفة عن 0 لفئات"
" النفقات التي تمولها الشركة بتكلفة ثابتة مثل بدلات الأميال أو البدل اليومي "
"أو الإقامة أو الوجبة. "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_has_tax
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__product_has_tax
msgid "Whether tax is defined on a selected product"
msgstr "ما إذا كانت الضريبة محددة في منتج محدد أم لا "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__wizard_id
msgid "Wizard"
msgstr "المعالج"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "You are not authorized to edit this expense report."
msgstr "لا يسمح لك بتحرير تقرير النفقة. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "You are not authorized to edit this expense."
msgstr "لا يسمح لك بتحرير هذه النفقة. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "You can not create report without category."
msgstr "لا يمكنك إنشاء تقرير دون الفئة. "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "You can now submit it to the manager from the following link."
msgstr "بإمكانك الآن إرساله إلى المدير من الرابط التالي. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "You can only generate an accounting entry for approved expense(s)."
msgstr "يمكنك إنشاء قود محاسبي للنفقات التي تمت الموافقة عليها فقط. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "You cannot add expenses of another employee."
msgstr "لا يمكنك إضافة نفقات موظف آخر. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid ""
"You cannot approve:\n"
" %s"
msgstr ""
"لا يمكنك الموافقة:\n"
" %s "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "You cannot cancel an expense sheet linked to a posted journal entry"
msgstr "لا يمكنك حذف قائمة نفقات مرتبطة بقيد يومية مُرَحّل "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid ""
"You cannot create accounting entries for an expense report without expenses."
msgstr "لا يمكنك إنشاء قيود محاسبية لتطبيق نفقات دون نفقات. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "You cannot delete a posted or approved expense."
msgstr "لا يمكنك حذف نفقة تم ترحيلها أو الموافقة عليها. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid "You cannot delete a posted or paid expense."
msgstr "لا يمكنك حذف نفقة تم ترحيلها أو سداد قيمتها. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/analytic.py:0
msgid "You cannot delete an analytic account that is used in an expense."
msgstr "لا يمكنك حذف حساب تحليلي مستخدَم في نفقة. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/account_move.py:0
msgid ""
"You cannot delete only some entries linked to an expense report. All entries"
" must be deleted at the same time."
msgstr ""
"لا يمكنك أن تحذف فقط بعض القيود المرتبطة بتقرير نفقات. يجب حذف كافة النفقات "
"في الوقت ذاته. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/account_payment.py:0
msgid ""
"You cannot delete only some payments linked to an expense report. All "
"payments must be deleted at the same time."
msgstr ""
"لا يمكنك أن تحذف فقط بعض المدفوعات المرتبطة بتقرير نفقات. يجب حذف كافة "
"المدفوعات في الوقت ذاته. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/account_payment.py:0
msgid ""
"You cannot do this modification since the payment is linked to an expense "
"report."
msgstr "لا يمكنك إجراء تعديلات بما أن الدفع مرتبط بتقرير نفقات. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid ""
"You cannot refuse:\n"
" %s"
msgstr ""
"لا يمكنك الرفض:\n"
" %s "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid ""
"You cannot remove all expenses from a submitted, approved or paid expense "
"report."
msgstr ""
"لا يمكنك إزالة كافة النفقات من تقرير نفقات تم تقديمه أو الموافقة عليه أو دفع"
" قيمته. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "You cannot report expenses for different companies in the same report."
msgstr "لا يمكنك الإبلاغ عن نفقات عدة شركات مختلفة في نفس التقرير. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "You cannot report expenses for different employees in the same report."
msgstr "لا يمكنك الإبلاغ عن مصروفات موظفين مختلفين في نفس التقرير."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "You cannot report the expenses without amount!"
msgstr "لا يمكنك إعداد تقرير عن النفقات دون ذكر المبلغ! "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "You cannot report twice the same line!"
msgstr "لا يمكنك الإبلاغ عن نفس البند مرتين!"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid ""
"You cannot set the expense total in currency to 0 if it's linked to a "
"report."
msgstr "لا يمكنك تعيين إجمالي النفقات بالعملة إلى 0 إذا كانت مرتبطة بتقرير. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "You cannot set the expense total to 0 if it's linked to a report."
msgstr "لا يمكنك تعيين إجمالي النفقات إلى 0 إذا كانت مرتبطة بتقرير. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
msgid ""
"You do not have the rights to add or remove any expenses on an approved or "
"paid expense report."
msgstr ""
"لا تملك صلاحيات إضافة أو إزالة أي نفقات في تقرير نفقات قم تمت الموافقة عليه "
"بالفعل أو دفع قيمته. "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "You have no expense to report"
msgstr "لا تملك أي نفقات لإعداد تقرير عنها "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "You need to add a manual payment method on the journal (%s)"
msgstr "يجب أن تقوم بإضافة طريقة دفع يدوية في دفتر اليومية (%s) "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid ""
"You need to have at least one category that can be expensed in your database"
" to proceed!"
msgstr ""
"يجب أن تكون لديك فئة واحدة على الأقل يمكن تقييدها كنفقات في قاعدة بياناتك "
"للاستمرار! "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Your Expense Report"
msgstr "تقارير نفقاتك "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Your expense has been successfully registered."
msgstr "لقد تم تسجيل نفقتك بنجاح. "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "alias"
msgstr "اللقب "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "e.g. Lunch"
msgstr "مثلًا: الغداء"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "e.g. Lunch with Customer"
msgstr "مثلًا: غداء مع عميل"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "e.g. Restaurants: only week days, for lunch"
msgstr "مثال: المطاعم: أيام الأسبوع فقط للغداء "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "e.g. Trip to NY"
msgstr "مثلًا: رحلة إلى نيويورك"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "e.g. mycompany.com"
msgstr "مثال: mycompany.com "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "expense"
msgstr "نفقة "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "has been refused"
msgstr "تم رفضه"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "no taxes"
msgstr "دون ضرائب "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "per"
msgstr "لكل "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "tax)"
msgstr "ضريبة) "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "to be reimbursed"
msgstr "بانتظار التعويض "

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "to submit"
msgstr "للإرسال"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
msgid "under validation"
msgstr "قيد التصديق "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "use OCR to fill data from a picture of the bill"
msgstr "استخدم تمييز الرموز ضوئياً لملء البيانات من صورة لفاتورة "

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "with the same receipt already exists."
msgstr "مع نفس الإيصال موجود بالفعل. "
