<?xml version="1.0" encoding="UTF-8" ?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.ProductCard">
        <article tabindex="0"
            t-attf-class="{{props.class}} {{props.color ? `o_colorlist_item_color_transparent_${props.color}` : ''}} product position-relative btn btn-light d-flex align-items-stretch p-0 m-0 rounded-3 text-start cursor-pointer transition-base"
            t-on-keypress="(event) => event.code === 'Space' ? props.onClick(event) : ()=>{}"
            t-on-click="props.onClick"
            t-att-data-product-id="props.productId"
            t-attf-aria-labelledby="article_product_{{props.productId}}">
            <div t-if="props.productInfo" class="product-information-tag" t-on-click.stop="props.onProductInfoClick" t-att-class="{'red-tag' : props.showWarning}">
                <i class="product-information-tag-logo fa fa-info" role="img" aria-label="Product Information" title="Product Information" />
            </div>
            <div t-if="props.imageUrl" class="product-img rounded-top rounded-3">
                <img class="w-100 bg-100" t-att-src="props.imageUrl" t-att-alt="props.name" />
            </div>
            <div class="product-content d-flex flex-row px-2 justify-content-between rounded-bottom rounded-3 flex-shrink-1" t-att-class="{'h-100' : !props.imageUrl}">
                <div class="overflow-hidden lh-sm product-name my-2"
                    t-att-class="{'no-image d-flex justify-content-center align-items-center text-center fs-4': !props.imageUrl}"
                    t-attf-id="article_product_{{props.productId}}"
                    t-esc="props.name" />
                <h1 t-if="props.productCartQty"
                    t-out="this.productQty"
                    class="product-cart-qty text-muted display-6 fw-bolder m-0 mt-auto" />
            </div>
            <div t-if="props.comboExtraPrice" class="d-flex px-2 pb-1">
                <span class="price-extra px-2 py-0 rounded-pill text-bg-info">
                    + <t t-esc="props.comboExtraPrice"/>
                </span>
            </div>
        </article>
    </t>
</templates>
