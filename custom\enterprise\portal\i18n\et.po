# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* portal
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON> <armaged<PERSON><EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# Anna, 2024
# <PERSON><PERSON><PERSON>, 2024
# JanaAvalah, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 13:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: JanaAvalah, 2025\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "\" to validate your action."
msgstr "\", et kinnitada oma tegevust. "

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_sidebar.js:0
msgid "%s days overdue"
msgstr "%s päeva üle"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "1. Enter your password to confirm you own this account"
msgstr "1. Palun sisestage enda salasõna, et jätkata"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid ""
"2. Confirm you want to delete your account by\n"
"                                        copying down your login ("
msgstr ""
"2. Kinnita, et soovid oma kontot kustutada\n"
"                                        kopeerides oma kasutaja sisselogimiseks. ("

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.my_account_link
msgid ""
"<i class=\"fa fa-fw fa-id-card-o me-1 small text-primary text-primary-"
"emphasis\"/> My Account"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.user_dropdown
msgid ""
"<i class=\"fa fa-fw fa-sign-out me-1 small text-primary text-primary-"
"emphasis\"/> Logout"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.user_dropdown
msgid ""
"<i class=\"fa fa-fw fa-th me-1 small text-primary text-primary-emphasis\"/> "
"Apps"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.side_content
msgid "<i class=\"fa fa-pencil\"/> Edit information"
msgstr "<i class=\"fa fa-pencil\"/> Muuda informatsiooni"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
msgid "<i class=\"oi oi-arrow-right me-1\"/>Back to edit mode"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>Tagasi muutmisrešiimi"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.record_pager
msgid ""
"<i class=\"oi oi-chevron-left\" role=\"img\" aria-label=\"Previous\" "
"title=\"Previous\"/>"
msgstr ""
"<i class=\"oi oi-chevron-left\" role=\"img\" aria-label=\"Previous\" "
"title=\"Previous\"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.record_pager
msgid "<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"Next\" title=\"Next\"/>"
msgstr "<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"Next\" title=\"Next\"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>"
msgstr "<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">Riik...</option>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "<option value=\"\">select...</option>"
msgstr "<option value=\"\">vali...</option>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid ""
"<small class=\"form-text text-muted\">\n"
"                Company name, VAT Number and country can not be changed once document(s) have been issued for your account.\n"
"                <br/>Please contact us directly for that operation.\n"
"            </small>"
msgstr ""
"<small class=\"form-text text-muted\">\n"
"                Ettevõtte nime, KMK numbrit ja riike ei saa muuta peale dokumentide väljastamist teie kontole.\n"
"                <br/>Palun võtke selle toimingu jaoks meiega otse ühendust.\n"
"            </small>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.pager
msgid ""
"<span class=\"fa fa-chevron-left\" role=\"img\" aria-label=\"Previous\" "
"title=\"Previous\"/>"
msgstr ""
"<span class=\"fa fa-chevron-left\" role=\"img\" aria-label=\"Previous\" "
"title=\"Previous\"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.pager
msgid "<span class=\"fa fa-chevron-right\" role=\"img\" aria-label=\"Next\" title=\"Next\"/>"
msgstr "<span class=\"fa fa-chevron-right\" role=\"img\" aria-label=\"Next\" title=\"Next\"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small me-1 navbar-text\">Filter By:</span>"
msgstr "<span class=\"small me-1 navbar-text\">Filtreeri:</span>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small me-1 navbar-text\">Group By:</span>"
msgstr "<span class=\"small me-1 navbar-text\">Rühmita:</span>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small me-1 navbar-text\">Sort By:</span>"
msgstr "<span class=\"small me-1 navbar-text\">Sorteeri:</span>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "<strong>Open </strong>"
msgstr "<strong>Ava</strong>"

#. module: portal
#: model:mail.template,body_html:portal.mail_template_data_portal_welcome
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Account</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.user_id.name or ''\">Marc Demo</span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.user_id.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.user_id.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.user_id.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"object.user_id.name or ''\">Marc Demo</t>,<br/> <br/>\n"
"                        Welcome to <t t-out=\"object.user_id.company_id.name\">YourCompany</t>'s Portal!<br/><br/>\n"
"                        An account has been created for you with the following login: <t t-out=\"object.user_id.login\">demo</t><br/><br/>\n"
"                        Click on the button below to pick a password and activate your account.\n"
"                        <div style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"                            <a t-att-href=\"object.user_id.partner_id._get_signup_url()\" style=\"display: inline-block; padding: 10px; text-decoration: none; font-size: 12px; background-color: #875A7B; color: #fff; border-radius: 5px;\">\n"
"                                <strong>Activate Account</strong>\n"
"                            </a>\n"
"                        </div>\n"
"                        <t t-out=\"object.wizard_id.welcome_message or ''\">Welcome to our company's portal.</t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.user_id.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.user_id.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.user_id.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.user_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.user_id.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.user_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=portalinvite\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: portal
#: model:ir.model,name:portal.model_res_users_apikeys_description
msgid "API Key Description"
msgstr "API võtme kirjeldus"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
msgid "API Key Ready"
msgstr "API võti valmis"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/signature_form/signature_form.js:0
msgid "Accept & Sign"
msgstr "Kinnita & Allkirjasta"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_warning
#: model:ir.model.fields,field_description:portal.field_portal_share__access_warning
msgid "Access warning"
msgstr "Ligipääsuhoiatus"

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Account deleted!"
msgstr "Konto on kustutatud!"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Add a note"
msgstr "Lisa märkus"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Add attachment"
msgstr "Lisa manus"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Add contacts to share the document..."
msgstr "Lisa dokumendi jagamiseks kontakte..."

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_share__note
msgid "Add extra content to display in the email"
msgstr "Lisage täiendav sisu meilis kuvamiseks"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Added On"
msgstr "Lisatud"

#. module: portal
#: model:ir.model.fields.selection,name:portal.selection__portal_wizard_user__email_state__exist
msgid "Already Registered"
msgstr "Juba registreeritud"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Are you sure you want to do this?"
msgstr "Kas olete kindel, et soovite seda teha?"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Avatar"
msgstr "Avatar"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Cancel"
msgstr "Tühista"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Change Password"
msgstr "Muuda parooli"

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid ""
"Changing VAT number is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"KMKR numbri muutmine ei ole lubatud, kui teie kontole on juba esitatud "
"dokument(e). Palun võtke meiega otse ühendust, kui seda on vaja."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid ""
"Changing company name is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"Ettevõte nime muutmine ei ole lubatud, kui teie kontole on juba dokument "
"(dokumendid) väljastatud. Selle tegevuse sooritamiseks võtke palun meiega "
"otse ühendust."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid ""
"Changing the country is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"Riigi muutmine ei ole võimalik pärast dokumentide väljastamist teie kontole."
" Palun võtke selle toimingu jaoks meiega otse ühendust."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
msgid "Check failed"
msgstr "Kontroll ebaõnnestus"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "City"
msgstr "Linn"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/signature_form/signature_form.xml:0
msgid "Click here to see your document."
msgstr "Vajuta, et näha oma dokumenti."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
#: model_terms:ir.ui.view,arch_db:portal.side_content
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Close"
msgstr "Sulge"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Company Name"
msgstr "Ettevõtte nimi"

#. module: portal
#: model:ir.model,name:portal.model_res_config_settings
msgid "Config Settings"
msgstr "Seadistused"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_home
msgid "Configure your connection parameters"
msgstr "Konfigureerige oma ühenduse parameetrid"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
msgid "Confirm"
msgstr "Kinnitage"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
msgid "Confirm Password"
msgstr "Kinnitage salasõna"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_home
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Connection &amp; Security"
msgstr "Ühendus &amp; turvalisus"

#. module: portal
#: model:ir.model,name:portal.model_res_partner
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__partner_id
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
#: model_terms:ir.ui.view,arch_db:portal.portal_my_contact
#: model_terms:ir.ui.view,arch_db:portal.side_content
msgid "Contact"
msgstr "Kontakt"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Contact Details"
msgstr "Kontakti andmed"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Contacts"
msgstr "Kontaktid"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_composer.js:0
msgid "Could not save file <strong>%s</strong>"
msgstr "Faili ei saanud salvestada <strong>%s</strong>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Country"
msgstr "Riik"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__create_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard__create_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__create_uid
msgid "Created by"
msgstr "Loonud"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__create_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard__create_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__create_date
msgid "Created on"
msgstr "Loodud"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_res_config_settings__portal_allow_api_keys
msgid "Customer API Keys"
msgstr "Kliendi API võtmed"

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_mixin__access_url
msgid "Customer Portal URL"
msgstr "Kliendiportaali URL"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.res_config_settings_view_form
msgid "Customers can generate API Keys"
msgstr "Kliendid saavad genereerida API võtmeid"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "Dear"
msgstr "Tere"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Delete"
msgstr "Kustuta"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Delete Account"
msgstr "Kustuta konto"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Description"
msgstr "Kirjeldus"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_breadcrumbs
msgid "Details"
msgstr "Detailid"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Developer API Keys"
msgstr "Arendaja API võtmed"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid ""
"Disable your account, preventing any further login.<br/>\n"
"                                        <b>\n"
"                                            <i class=\"fa fa-exclamation-triangle text-danger\"/>\n"
"                                            This action cannot be undone.\n"
"                                        </b>"
msgstr ""
"Lülita välja enda konto, et takistada edasisi sisselogimisi.<br/>\n"
"                                        <b>\n"
"                                            <i class=\"fa fa-exclamation-triangle text-danger\"/>\n"
"                                            See tegevus ei ole tagasipööratav.\n"
"                                        </b>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Discard"
msgstr "Loobu"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__display_name
#: model:ir.model.fields,field_description:portal.field_portal_wizard__display_name
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__display_name
msgid "Display Name"
msgstr "Kuvatav nimi"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_sidebar.js:0
msgid "Due in %s days"
msgstr "Tähtaeg %späeva pärast"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_sidebar.js:0
msgid "Due today"
msgstr "tänaseni"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__email
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Email"
msgstr "E-post"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Email Address already taken by another user"
msgstr "See e-posti aadress on juba kasutusel "

#. module: portal
#: model:ir.model,name:portal.model_mail_thread
msgid "Email Thread"
msgstr "E-posti kirjavahetus"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Enter a description of and purpose for the key."
msgstr "Sisestage võtme kirjeldus ja eesmärk."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Expiration Date"
msgstr "Aegumiskuupäev"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Forgot password?"
msgstr "Unustasid parooli?"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Give a duration for the key's validity"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Go Back"
msgstr "Mine tagasi"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Grant Access"
msgstr "Anna ligipääs"

#. module: portal
#: model:ir.model,name:portal.model_portal_wizard
msgid "Grant Portal Access"
msgstr "Anna portaali ligipääs"

#. module: portal
#: model:ir.actions.act_window,name:portal.partner_wizard_action
#: model:ir.actions.server,name:portal.partner_wizard_action_create_and_open
msgid "Grant portal access"
msgstr "Anna portaali ligipääs"

#. module: portal
#: model:ir.model,name:portal.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Routing"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid ""
"Here is your new API key, use it instead of a password for RPC access.\n"
"                Your login is still necessary for interactive usage."
msgstr ""
"Siin on teie uus API võti, kasutage seda parooli asemel RPC-juurdepääsu jaoks.\n"
"                Teie sisselogimine on endiselt vajalik interaktiivseks kasutamiseks."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_breadcrumbs
msgid "Home"
msgstr "Kodu"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__id
#: model:ir.model.fields,field_description:portal.field_portal_wizard__id
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__id
msgid "ID"
msgstr "ID"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Important:"
msgstr "Tähtis:"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Internal User"
msgstr "Ettevõttesisene kasutaja"

#. module: portal
#: model:ir.model.fields.selection,name:portal.selection__portal_wizard_user__email_state__ko
msgid "Invalid"
msgstr "Kehtetu"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Invalid Email Address"
msgstr "Kehtetu e-posti aadress"

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Invalid Email! Please enter a valid email address."
msgstr "E-posti aadress ei ole korrektne! Palun kirjuta korrektne e-post."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Invalid report type: %s"
msgstr "Vigane aruande tüüp: %s"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__welcome_message
msgid "Invitation Message"
msgstr "Kutse sõnum"

#. module: portal
#: model:mail.template,description:portal.mail_template_data_portal_welcome
msgid "Invitation email to contacts to create a user account"
msgstr "E-maili kutse kontaktidele kasutajakonto loomiseks"

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_share.py:0
msgid "Invitation to access %s"
msgstr "Kutse ligipääsuks %s"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__is_internal
msgid "Is Internal"
msgstr "on sisemine"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__is_portal
msgid "Is Portal"
msgstr "on portaal"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid ""
"It is very important that this description be clear\n"
"                and complete,"
msgstr ""
"On väga oluline, et see kirjeldus on selge\n"
"ja täielik,"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Key Description"
msgstr ""

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__write_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard__write_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__write_uid
msgid "Last Updated by"
msgstr "Viimati uuendatud"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__write_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard__write_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__write_date
msgid "Last Updated on"
msgstr "Viimati uuendatud"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__login_date
msgid "Latest Authentication"
msgstr "Viimane autentimine"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Leave a comment"
msgstr "Jäta kommentaar"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__share_link
msgid "Link"
msgstr "Link"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Log out from all devices"
msgstr "Logi välja kõikidest seadmetest"

#. module: portal
#: model:ir.model,name:portal.model_mail_message
msgid "Message"
msgstr "Sõnum"

#. module: portal
#. odoo-python
#: code:addons/portal/models/mail_thread.py:0
msgid ""
"Model %(model_name)s does not support token signature, as it does not have "
"%(field_name)s field."
msgstr ""

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Multi company reports are not supported."
msgstr "Mitme ettevõtte aruandeid ei toetata."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
msgid "My account"
msgstr "Minu konto"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Name"
msgstr "Nimi"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Name your key"
msgstr "Nimeta oma võti"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "New API Key"
msgstr "Uus API võti"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "New Password:"
msgstr "Uus salasõna:"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Next"
msgstr "Järgmine"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__note
msgid "Note"
msgstr "Märkus"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "Odoo Logo"
msgstr "Odoo logo"

#. module: portal
#. odoo-python
#: code:addons/portal/models/res_users_apikeys_description.py:0
msgid "Only internal and portal users can create API keys"
msgstr "API võtmeid saavad luua ainult sisekasutajad ja portaali kasutajad."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Oops! Something went wrong. Try to reload the page and log in."
msgstr ""
"Ups! Midagi läks valesti. Proovi oma lehte uuendada ja siis uuesti sisse "
"logida."

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__partner_ids
msgid "Partners"
msgstr "Partnerid"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Password"
msgstr "Salasõna"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Password Updated!"
msgstr "Salasõna on uuendatud!"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Password:"
msgstr "Salasõna:"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Phone"
msgstr "Telefon"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Please enter your password to confirm you own this account"
msgstr "Palun sisestage enda salasõna, et jätkata"

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Portal Access Management"
msgstr "Portaali ligipääsu haldus"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_url
msgid "Portal Access URL"
msgstr "Portaali ligipääsu URL"

#. module: portal
#: model:ir.model,name:portal.model_portal_mixin
msgid "Portal Mixin"
msgstr "Portaali Mixin"

#. module: portal
#: model:ir.model,name:portal.model_portal_share
msgid "Portal Sharing"
msgstr "Portaali jagamine"

#. module: portal
#: model:ir.model,name:portal.model_portal_wizard_user
msgid "Portal User Config"
msgstr "Portaali kasutaja seadistused"

#. module: portal
#: model:mail.template,name:portal.mail_template_data_portal_welcome
msgid "Portal: User Invite"
msgstr "Portaal: Kutsu kasutaja"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "Powered by"
msgstr "Kiri saadetud läbi"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Previous"
msgstr "Eelmine"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid ""
"Put my email and phone in a block list to make sure I'm never contacted "
"again"
msgstr ""
"Lisa minu e-post ja telefon musta nimekirja, et tagada, et minuga ei võetaks"
" enam kunagi ühendust."

#. module: portal
#: model:ir.model,name:portal.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Re-Invite"
msgstr "Kutsu uuesti"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__partner_ids
msgid "Recipients"
msgstr "Saajad"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__resource_ref
msgid "Related Document"
msgstr "Seotud dokumendid"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__res_id
msgid "Related Document ID"
msgstr "Seotud dokumendi ID"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__res_model
msgid "Related Document Model"
msgstr "Seotud dokumendi mudel"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Revoke Access"
msgstr "Tühista ligipääs"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Revoke All Sessions"
msgstr "Tühista kõik sessioonid"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Save"
msgstr "Salvesta"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Scope"
msgstr "Maht"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "Search"
msgstr "Otsi"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Security"
msgstr "Turvalisus"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Security Control"
msgstr "Turvalisuse kontroll"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_token
msgid "Security Token"
msgstr "Turvamärgis"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid ""
"Select which contacts should belong to the portal in the list below.\n"
"                        The email address of each selected contact must be valid and unique.\n"
"                        If necessary, you can fix any contact's email address directly in the list."
msgstr ""
"Vali, millised kontaktid peaksid kuuluma portaali nimekirja.\n"
"                        Iga valitud e-posti aadress peab olema unikaalne ja kehtiv.\n"
"                        Kui vaja, siis e-posti aadressi on võimalik muuta otse siin nimekirjas."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_composer.js:0
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Send"
msgstr "Saada"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_contact
msgid "Send message"
msgstr "Saada e-kiri"

#. module: portal
#: model:ir.actions.act_window,name:portal.portal_share_action
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Share Document"
msgstr "Jaga dokumenti"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_ir_ui_view__customize_show
msgid "Show As Optional Inherit"
msgstr "Show As Optional Inherit"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.user_sign_in
msgid "Sign in"
msgstr "Logi sisse"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.frontend_layout
msgid "Skip to Content"
msgstr ""

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_composer.js:0
msgid ""
"Some fields are required. Please make sure to write a message or attach a "
"document"
msgstr ""
"Mõned väljad on kohustuslikud. Palun veenduge, et kirjutate sõnumi või "
"lisate dokumendi."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Some required fields are empty."
msgstr "Mõned nõutud väljad on tühjad."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "State / Province"
msgstr "Maakond"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__email_state
msgid "Status"
msgstr "Olek"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Street"
msgstr "Tänav"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Street 2"
msgstr "Tänav 2"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/signature_form/signature_form.xml:0
msgid "Thank You!"
msgstr "Aitäh!"

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "The attachment %s cannot be removed because it is linked to a message."
msgstr "Manust %s ei saa eemaldada, kuna see on seotud sõnumiga."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid ""
"The attachment %s cannot be removed because it is not in a pending state."
msgstr "Manust %s ei saa eemaldada, kuna see ei ole ootel olekus."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid ""
"The attachment does not exist or you do not have the rights to access it."
msgstr "Manust ei eksisteeri või teil ei ole õigusi sellele juurde pääseda."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "The contact \"%s\" does not have a valid email."
msgstr "Kontaktil \"%s\" ei ole kehtivat e-posti aadressi."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "The contact \"%s\" has the same email as an existing user"
msgstr "Kontaktil \"%s\" on sama e-posti aadress nagu olemasoleval kasutajal"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "The key cannot be retrieved later and provides"
msgstr ""

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "The key will be deleted once this period has elapsed."
msgstr ""

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "The new password and its confirmation must be identical."
msgstr "Uus salasõna ja kinnitus peavad olema identsed."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid ""
"The old password you provided is incorrect, your password was not changed."
msgstr "Sisestatud vana parool oli vale. Sinu parooli ei muudetud."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "The partner \"%s\" already has the portal access."
msgstr "Partneril \"%s\" juba on portaali ligipääs."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "The partner \"%s\" has no portal access or is internal."
msgstr "Partneril \"%s\" ei ole portaali ligipääsu või on sisemine."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid ""
"The template \"Portal: new user\" not found for sending email to the portal "
"user."
msgstr ""
"Mall \"Portal: uus kasutaja\" ei leitud portaalikasutajale e-kirja "
"saatmiseks."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "This document does not exist."
msgstr "Antud dokument ei eksisteeri. "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
msgid "This is a preview of the customer portal."
msgstr "See on kliendiportaali eelvaade."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid ""
"This partner is linked to an internal User and already has access to the "
"Portal."
msgstr ""
"See partner on seotud sisekasutajaga ja omab juba portaalile juurdepääsu."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid ""
"This text is included at the end of the email sent to new portal users."
msgstr ""
"See tekst on lisatud uutele portaalikasutajatele saadetud e-kirja lõppu."

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_wizard__welcome_message
msgid "This text is included in the email sent to new users of the portal."
msgstr ""
"Seda teksti kasutatakse uutele portaali kasutajatele saadetavas e-kirjas."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "Toggle filters"
msgstr "Lülita filtrid sisse/välja"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__user_id
msgid "User"
msgstr "Kasutaja"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__user_ids
msgid "Users"
msgstr "Kasutajad"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "VAT Number"
msgstr "KMKR nr"

#. module: portal
#: model:ir.model.fields.selection,name:portal.selection__portal_wizard_user__email_state__ok
msgid "Valid"
msgstr "Kehtiv"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Valid Email Address"
msgstr "Kehtiv e-posti aadress"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Verify New Password:"
msgstr "Uue salasõna kordus:"

#. module: portal
#: model:ir.model,name:portal.model_ir_ui_view
msgid "View"
msgstr "Vaade"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_account_analytic_account__website_message_ids
#: model:ir.model.fields,field_description:portal.field_calendar_event__website_message_ids
#: model:ir.model.fields,field_description:portal.field_crm_team__website_message_ids
#: model:ir.model.fields,field_description:portal.field_crm_team_member__website_message_ids
#: model:ir.model.fields,field_description:portal.field_discuss_channel__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle_log_contract__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle_log_services__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle_model__website_message_ids
#: model:ir.model.fields,field_description:portal.field_gamification_badge__website_message_ids
#: model:ir.model.fields,field_description:portal.field_gamification_challenge__website_message_ids
#: model:ir.model.fields,field_description:portal.field_iap_account__website_message_ids
#: model:ir.model.fields,field_description:portal.field_lunch_supplier__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_cc__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_main_attachment__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_phone__website_message_ids
#: model:ir.model.fields,field_description:portal.field_maintenance_equipment__website_message_ids
#: model:ir.model.fields,field_description:portal.field_maintenance_equipment_category__website_message_ids
#: model:ir.model.fields,field_description:portal.field_maintenance_request__website_message_ids
#: model:ir.model.fields,field_description:portal.field_phone_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_category__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_pricelist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_product__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_template__website_message_ids
#: model:ir.model.fields,field_description:portal.field_rating_mixin__website_message_ids
#: model:ir.model.fields,field_description:portal.field_res_partner__website_message_ids
#: model:ir.model.fields,field_description:portal.field_res_users__website_message_ids
msgid "Website Messages"
msgstr "Veebilehe sõnumid"

#. module: portal
#: model:ir.model.fields,help:portal.field_account_analytic_account__website_message_ids
#: model:ir.model.fields,help:portal.field_calendar_event__website_message_ids
#: model:ir.model.fields,help:portal.field_crm_team__website_message_ids
#: model:ir.model.fields,help:portal.field_crm_team_member__website_message_ids
#: model:ir.model.fields,help:portal.field_discuss_channel__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle_log_contract__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle_log_services__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle_model__website_message_ids
#: model:ir.model.fields,help:portal.field_gamification_badge__website_message_ids
#: model:ir.model.fields,help:portal.field_gamification_challenge__website_message_ids
#: model:ir.model.fields,help:portal.field_iap_account__website_message_ids
#: model:ir.model.fields,help:portal.field_lunch_supplier__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_cc__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_main_attachment__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_phone__website_message_ids
#: model:ir.model.fields,help:portal.field_maintenance_equipment__website_message_ids
#: model:ir.model.fields,help:portal.field_maintenance_equipment_category__website_message_ids
#: model:ir.model.fields,help:portal.field_maintenance_request__website_message_ids
#: model:ir.model.fields,help:portal.field_phone_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_product_category__website_message_ids
#: model:ir.model.fields,help:portal.field_product_pricelist__website_message_ids
#: model:ir.model.fields,help:portal.field_product_product__website_message_ids
#: model:ir.model.fields,help:portal.field_product_template__website_message_ids
#: model:ir.model.fields,help:portal.field_rating_mixin__website_message_ids
#: model:ir.model.fields,help:portal.field_res_partner__website_message_ids
#: model:ir.model.fields,help:portal.field_res_users__website_message_ids
msgid "Website communication history"
msgstr "Veebilehe suhtluse ajalugu"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "What's this key for?"
msgstr "Milleks see võti?"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__wizard_id
msgid "Wizard"
msgstr "Nõustaja"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Write a message..."
msgstr "Kirjuta sõnum ..."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/chatter/core/composer_patch.js:0
msgid "Write a message…"
msgstr ""

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Write down your key"
msgstr "Kirjutage oma võti üles"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Wrong password."
msgstr "Vale salasõna."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "You cannot leave any password empty."
msgstr "Sa ei saa jätta ühtegi salasõna tühjaks."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "You must be"
msgstr "Sa pead olema "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "You should enter \""
msgstr ""

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "You should first grant the portal access to the partner \"%s\"."
msgstr "Peaksite kõigepealt lubama ligipääsu partnerile \"%s\"."

#. module: portal
#: model:mail.template,subject:portal.mail_template_data_portal_welcome
msgid "Your account at {{ object.user_id.company_id.name }}"
msgstr "Teie konto {{ object.user_id.company_id.name }}"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_contact
msgid "Your contact"
msgstr "Sinu kontakt"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Zip / Postal Code"
msgstr "Postiindeks"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "full access"
msgstr "täielik juurdepääs"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "has invited you to access the following"
msgstr ""

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid ""
"it will be the only way to\n"
"                identify the key once created"
msgstr ""

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "logged in"
msgstr "sisse logitud"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "odoo"
msgstr "odoo"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "password"
msgstr "salasõna"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "to post a comment."
msgstr "kommentaari sisestamiseks."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "to your user account, it is very important to store it securely."
msgstr ""
