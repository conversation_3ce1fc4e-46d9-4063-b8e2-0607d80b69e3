<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="hr_expense.ExpenseDashboard">
        <div class="d-none d-md-flex o_expense_container position-sticky start-0 d-flex o_form_statusbar">
            <t t-foreach="Object.entries(state.expenses)" t-as="expense" t-key="expense[0]">
                <t t-set="name" t-value="expense[0]"/>
                <t t-set="data" t-value="expense[1]"/>
                <div t-attf-class="o_expense_card o_arrow_button flex-grow-1 d-flex flex-column p-3 border-bottom text-center cursor-pointer"
                     t-att-data-tooltip="data['tooltip']" t-on-click="() => this.applyFilter(name)">
                    <span t-out="renderMonetaryField(data['amount'], data['currency'])" class="h2 m-0 text-primary"/>
                    <b class="mx-2" t-out="data['description']"/>
                </div>
                <div t-if="name !== 'approved'" t-attf-class="o_expense_card o_arrow_button flex-grow-1 d-flex flex-column p-3 border-bottom text-center">
                    <i class="fa fa-angle-right fa-3x"/>
                </div>
            </t>
            <div class="fa fa-question-circle-o flex-grow-0.5 d-flex flex-column p-3 border-bottom text-center" t-if="env.debug" data-tooltip="Numbers computed from your personal expenses."/>
        </div>
    </t>
</templates>
