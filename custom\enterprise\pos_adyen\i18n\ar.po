# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_adyen
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: pos_adyen
#: model_terms:ir.ui.view,arch_db:pos_adyen.res_config_settings_view_form
msgid "Add tip through payment terminal (Adyen)"
msgstr "إضافة بقشيش من خلال جهاز الدفع بالبطاقة (Adyen) "

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_api_key
msgid "Adyen API key"
msgstr "مفتاح الواجهة البرمجية لـ Adyen "

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "Adyen Error"
msgstr "خطأ في Adyen "

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_latest_response
msgid "Adyen Latest Response"
msgstr "آخر استجابة لـ Adyen "

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_terminal_identifier
msgid "Adyen Terminal Identifier"
msgstr "معرف جهاز الدفع Adyen "

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_test_mode
msgid "Adyen Test Mode"
msgstr "وضع الاختبار لـ Adyen "

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "An unexpected error occurred. Message from Adyen: %s"
msgstr "حدث خطأ غير متوقع. رسالة من Adyen: %s "

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_config__adyen_ask_customer_for_tip
msgid "Ask Customers For Tip"
msgstr "اطلب البقشيش من العملاء "

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "Authentication failed. Please check your Adyen credentials."
msgstr "فشلت عملية المصادقة. يرجى التحقق من بيانات اعتماد Adyen. "

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid ""
"Cancelling the payment failed. Please cancel it manually on the payment "
"terminal."
msgstr ""
"لقد فشلت عملية إلغاء الدفع. يرجى إلغاؤها يدوياً في جهاز الدفع بالبطاقة. "

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "Cannot process transactions with negative amount."
msgstr "لا يمكن معالجة المعاملات التي بها مبلغ قيمته سالبة. "

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid ""
"Could not connect to the Odoo server, please check your internet connection "
"and try again."
msgstr ""
"تعذر الاتصال بخادم أودو. يرجى التحقق من اتصالك بالإنترنت ثم المحاولة من "
"جديد. "

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_event_url
msgid "Event URL"
msgstr "رابط URL للفعالية "

#. module: pos_adyen
#. odoo-python
#: code:addons/pos_adyen/models/pos_payment_method.py:0
msgid "Invalid Adyen request"
msgstr "طلب Adyen غير صالح "

#. module: pos_adyen
#. odoo-javascript
#: code:addons/pos_adyen/static/src/app/payment_adyen.js:0
msgid "Message from Adyen: %s"
msgstr "رسالة من Adyen: %s "

#. module: pos_adyen
#. odoo-python
#: code:addons/pos_adyen/models/pos_config.py:0
msgid ""
"Please configure a tip product for POS %s to support tipping with Adyen."
msgstr "يرجى تهيئة منتج بقشيش لنقطة البيع %s لدعم منح البقشيش مع Adyen. "

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_pos_config
msgid "Point of Sale Configuration"
msgstr "تهيئة نقطة البيع "

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "طرق الدفع في نقطة البيع "

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_res_config_settings__pos_adyen_ask_customer_for_tip
msgid "Pos Adyen Ask Customer For Tip"
msgstr "نقطة بيع Adyen طلب بقشيش من العميل "

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_test_mode
msgid "Run transactions in the test environment."
msgstr "تشغيل المعاملات في بيئة الاختبار. "

#. module: pos_adyen
#. odoo-python
#: code:addons/pos_adyen/models/pos_payment_method.py:0
msgid ""
"Terminal %(terminal)s is already used in company %(company)s on payment "
"method %(payment_method)s."
msgstr ""
"جهاز الدفع بالبطاقة %(terminal)s مستخدم بالفعل في الشركة %(company)s لطريقة "
"الدفع %(payment_method)s. "

#. module: pos_adyen
#. odoo-python
#: code:addons/pos_adyen/models/pos_payment_method.py:0
msgid ""
"Terminal %(terminal)s is already used on payment method %(payment_method)s."
msgstr ""
"جهاز الدفع بالبطاقة %(terminal)s مستخدم بالفعل في طريقة دفع "
"%(payment_method)s. "

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_event_url
msgid "This URL needs to be pasted on Adyen's portal terminal settings."
msgstr "عليك لصق عنوان URL هذا في إعدادات جهاز الدفع في بوابة Adyen. "

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_api_key
msgid ""
"Used when connecting to Adyen: https://docs.adyen.com/user-management/how-"
"to-get-the-api-key/#description"
msgstr ""
"يستخدم عند الاتصال بـ Adyen: https://docs.adyen.com/user-management/how-to-"
"get-the-api-key/#description "

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_terminal_identifier
msgid "[Terminal model]-[Serial number], for example: P400Plus-123456789"
msgstr ""
"[نموذج جهاز الدفع بالبطاقة]-[الرقم التسلسلي]، على سبيل المثال: "
"P400Plus-123456789 "
