<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="point_of_sale.OpeningControlPopup">
        <Dialog size="'md'">
            <t t-set-slot="header">
                <h4 class="modal-title text-break">
                    Opening Control
                </h4>
            </t>
            <div class="opening-cash-section mb-3" t-if="this.cashMethodCount">
                <label class="form-label">Opening cash</label>
                <div t-att-class="{ 'w-50': !ui.isSmall }" class="cash-input-sub-section d-flex gap-1">
                    <Input tModel="[state, 'openingCash']"
                        isValid.bind="env.utils.isValidFloat"
                        callback.bind="handleInputChange"
                        autofocus="true"
                        placeholder.translate="Opening Balance Eg: 123"/>
                    <div class="button icon btn btn-secondary btn-lg lh-lg" t-on-click="openDetailsPopup">
                        <i class="fa fa-money" role="img" title="Open the money details popup"/>
                    </div>
                </div>
            </div>
            <div class="opening-notes-container d-flex flex-column align-items-start">
                <label class="form-label" for="openingNotes">Opening note</label>
                <textarea class="opening-notes form-control" id="openingNotes" rows="4" t-model="state.notes" placeholder="Add an opening note..." />
            </div>
            <t t-set-slot="footer">
                <button class="button btn btn-lg btn-primary"
                    t-on-click="confirm"
                    t-att-disabled="!env.utils.isValidFloat(state.openingCash)">
                    Open Register
                </button>
                <button class="backend-button button btn btn-lg btn-secondary"
                    t-on-click="() => this.pos.showLoginScreen()">
                    Discard
                </button>
            </t>
        </Dialog>
    </t>
</templates>
