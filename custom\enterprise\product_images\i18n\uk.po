# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_images
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: product_images
#. odoo-python
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
msgid ""
"%(matching_images_count)s matching images have been found for "
"%(product_count)s products."
msgstr ""
"%(matching_images_count)s знайдено відповідні зображення для товарів "
"%(product_count)s."

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid ""
"<span invisible=\"nb_products_selected &lt;= 10000\">\n"
"                            As only 10,000 products can be processed per day, the remaining will be\n"
"                            done tomorrow.\n"
"                        </span>"
msgstr ""
"<span invisible=\"nb_products_selected &lt;= 10000\">\n"
"                            Оскільки за день можна обробити лише 10 000 товарів, \n"
"решту буде зроблено завтра.\n"
"                        </span>"

#. module: product_images
#. odoo-python
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
msgid ""
"A task to process products in the background is already running. Please try "
"againlater."
msgstr ""
"Завдання обробки товарів у фоновому режимі вже виконується. Будь-ласка "
"спробуйте пізніше."

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.res_config_settings_view_form
msgid "API Key"
msgstr "Ключ API "

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid "Cancel"
msgstr "Скасувати"

#. module: product_images
#: model:ir.model,name:product_images.model_res_config_settings
msgid "Config Settings"
msgstr "Налаштування"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__create_uid
msgid "Created by"
msgstr "Створив"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__create_date
msgid "Created on"
msgstr "Створено"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__display_name
msgid "Display Name"
msgstr "Назва для відображення"

#. module: product_images
#: model:ir.model,name:product_images.model_product_fetch_image_wizard
msgid ""
"Fetch product images from Google Images based on the product's barcode "
"number."
msgstr ""
"Отримайте зображення товару з Google Images на основі номера штрих-коду "
"товару."

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid "Get Pictures"
msgstr "Отримати зображення"

#. module: product_images
#: model:ir.actions.act_window,name:product_images.product_product_action_get_pic_with_barcode
#: model:ir.actions.act_window,name:product_images.product_template_action_get_pic_with_barcode
msgid "Get Pictures from Google Images"
msgstr "Отримати зображення з Google Images"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_res_config_settings__google_custom_search_key
msgid "Google Custom Search API Key"
msgstr "Ключ API кастомного пошуку Google"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__id
msgid "ID"
msgstr "ID"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_product__image_fetch_pending
msgid "Image Fetch Pending"
msgstr "Очікує отримання зображення"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__write_uid
msgid "Last Updated by"
msgstr "Востаннє оновив"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__write_date
msgid "Last Updated on"
msgstr "Останнє оновлення"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__nb_products_unable_to_process
msgid "Number of product unprocessable"
msgstr "Кількість товару, який не обробляється"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__nb_products_to_process
msgid "Number of products to process"
msgstr "Кількість товару для обробки"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__nb_products_selected
msgid "Number of selected products"
msgstr "Кількість обраних товарів"

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid ""
"Please note that some images might not be royalty-free. You should not\n"
"                        publish these on your website."
msgstr ""
"Зауважте, що деякі зображення можуть бути не безкоштовними. Ви не повинні \n"
"публікувати їх на своєму веб-сайті."

#. module: product_images
#: model:ir.actions.server,name:product_images.ir_cron_fetch_image_ir_actions_server
msgid "Product Images: Get product images from Google"
msgstr "Зображення товарів. Отримайте зображення товарів з Google"

#. module: product_images
#: model:ir.model,name:product_images.model_product_product
msgid "Product Variant"
msgstr "Варіант товару"

#. module: product_images
#. odoo-python
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
msgid "Product images"
msgstr "Зображення товарів"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__products_to_process
msgid "Products To Process"
msgstr "Товари для обробки"

#. module: product_images
#. odoo-python
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
msgid ""
"Products are processed in the background. Images will be updated "
"progressively."
msgstr ""
"Товари обробляються у фоновому режимі. Зображення будуть поступово "
"оновлюватися."

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.res_config_settings_view_form
msgid "Search Engine ID"
msgstr "ID пошукової системи"

#. module: product_images
#. odoo-python
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
msgid "The API Key and Search Engine ID must be set in the General Settings."
msgstr ""
"Ключ API та ID пошукової системи мають бути встановлені в Загальних "
"налаштуваннях."

#. module: product_images
#. odoo-python
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
msgid ""
"The Custom Search API is not enabled in your Google project. Please visit "
"your Google Cloud Platform project page and enable it, then retry. If you "
"enabled this API recently, please wait a few minutes and retry."
msgstr ""
"API користувацького пошуку не ввімкнено у вашому проекті Google. Відвідайте "
"сторінку проекту Google Cloud Platform і ввімкніть її, а потім повторіть "
"спробу. Якщо ви нещодавно ввімкнули цей API, зачекайте кілька хвилин і "
"повторіть спробу."

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_res_config_settings__google_pse_id
msgid "The identifier of the Google Programmable Search Engine"
msgstr "Ідентифікатор програмованої пошукової системи Google"

#. module: product_images
#: model:ir.model.fields,help:product_images.field_product_fetch_image_wizard__products_to_process
msgid ""
"The list of selected products that meet the criteria (have a barcode and no "
"image)"
msgstr ""
"Список вибраних товарів, які відповідають критеріям (мають штрих-код і немає"
" зображення)"

#. module: product_images
#. odoo-python
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
msgid ""
"The scheduled action \"Product Images: Get product images from Google\" has "
"been deleted. Please contact your administrator to have the action restored "
"or to reinstall the module \"product_images\"."
msgstr ""
"Заплановану дію \"Зображення товарів: отримувати зображення товарів від "
"Google\" видалено. Зверніться до свого адміністратора, щоб відновити дію або"
" перевстановити модуль \"product_images\"."

#. module: product_images
#. odoo-python
#: code:addons/product_images/models/ir_cron_trigger.py:0
msgid "This action is already scheduled. Please try again later."
msgstr "Ця акція вже запланована. Будь-ласка спробуйте пізніше."

#. module: product_images
#: model:ir.model,name:product_images.model_ir_cron_trigger
msgid "Triggered actions"
msgstr "Запущені дії"

#. module: product_images
#: model:ir.model.fields,help:product_images.field_product_product__image_fetch_pending
msgid "Whether an image must be fetched for this product. Handled by a cron."
msgstr ""
"Чи потрібно завантажувати зображення для цього продукту. Обробляється cron."

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid "You selected"
msgstr "Ви обрали"

#. module: product_images
#. odoo-python
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
msgid "Your API Key or your Search Engine ID is incorrect."
msgstr "Ваш ключ API або ID пошукової системи невірні."

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid "of which will be processed."
msgstr "якого буде оброблено."

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid ""
"products will not be\n"
"                            processed because they either already have an image or their barcode\n"
"                            number is not set."
msgstr ""
"товари не будуть оброблені, \n"
"оскільки вони вже мають зображення або їх номер штрих-коду \n"
"не встановлено."

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid "products,"
msgstr "товари,"
