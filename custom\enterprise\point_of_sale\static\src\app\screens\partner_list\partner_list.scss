.partner-list {
    tr {
        border: $border-width solid $border-color;
        
        &:hover {
            cursor: pointer;
        }
        
        &:active {
            background-color: $o-component-active-bg;
        }
    }
}

.partner-list table { 
    border-collapse: separate; 
    border-spacing: 0 8px; 
    margin-top: -8px;

    td {
        border: $border-width solid $border-color;
        border-style: solid none;
        padding: map-get($spacers, 3);
    }

    tr.selected td {
        border-color: $o-component-active-border;
    }

    td:first-child {
        border-left-style: solid;
        border-top-left-radius: $border-radius-lg;
        border-bottom-left-radius: $border-radius-lg;
    }

    td:last-child {
        border-right-style: solid;
        border-bottom-right-radius: $border-radius-lg;
        border-top-right-radius: $border-radius-lg;
    }
}

@include media-breakpoint-down(lg) {
    .partner-list {
        table {
            border: transparent;
        }
        .partner-list {
            thead {
                display: none;
            }
            td {
                padding: 0;
            }
        }
    }
}
