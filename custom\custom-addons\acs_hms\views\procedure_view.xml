<?xml version="1.0"?>
<odoo>

    <!-- Procedure Group -->
    <record id="view_procedure_group_tree" model="ir.ui.view">
        <field name="name">procedure.group.tree</field>
        <field name="model">procedure.group</field>
        <field name="arch" type="xml">
            <tree string="Procedure Group">
                <field name="name"/>
            </tree>
        </field>
    </record>

    <record id="view_procedure_group_form" model="ir.ui.view">
        <field name="name">procedure.group.form</field>
        <field name="model">procedure.group</field>
        <field name="arch" type="xml">
            <form string="Procedure Group">
                <sheet>
                    <div class="oe_title">
                        <label for="name" string="Name" class="oe_edit_only"/>
                        <h1>
                            <field name="name" placeholder="Procedure Group"/>
                        </h1>
                    </div>
                    <field name="line_ids">
                        <tree string="Lines" editable="bottom">
                            <field name="sequence" widget="handle"/>
                            <field name="product_id" context="{'default_detailed_type': 'service', 'default_hospital_product_type': 'procedure'}" domain="[('hospital_product_type','like','procedure')]"/>
                            <field name="days_to_add"/>
                            <field name="procedure_time" readonly="1" widget="float_time" sum="Total"/>
                            <field name="price_unit" readonly="1" sum="Total"/>
                        </tree>
                    </field>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_procedure_group" model="ir.actions.act_window">
        <field name="name">Procedure Group</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">procedure.group</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No Record Found
            </p>
            <p>
                Click to add new Procedure Group.
            </p>
        </field>
    </record>

    <!-- Patient Procedures View -->
    <record id="view_acs_patient_procedure_list" model="ir.ui.view">
        <field name="name">acs.patient.procedure.list</field>
        <field name="model">acs.patient.procedure</field>
        <field name="arch" type="xml">
            <tree string="Patient Procedure" decoration-info="state=='scheduled'" decoration-muted="state=='cancel'" decoration-danger="state=='done' and (invoice_id==False)">
                <field name="name"/>
                <field name="patient_id"/>
                <field name="product_id"/>
                <field name="price_unit" sum="total"/>
                <field name="physician_id"/>
                <field name="department_id" groups="acs_hms.group_allow_multi_department"/>
                <field name="date"/>
                <field name="date_stop"/>
                <field name="state"/>
                <field name="invoice_id" invisible="1"/>
            </tree>
        </field>
    </record>

    <record id="view_acs_patient_procedure_form" model="ir.ui.view">
        <field name="name">acs.patient.procedure.form</field>
        <field name="model">acs.patient.procedure</field>
        <field name="arch" type="xml">
             <form string="Patient Procedure">
                <header>
                    <button name="action_schedule" string="Schedule" type="object" states="cancel"/>
                    <button name="action_running" string="Running" type="object" states="scheduled" class="oe_highlight"/>
                    <button name="action_done" string="Done" type="object" states="running" class="oe_highlight" groups="acs_hms.group_hms_jr_doctor,acs_hms.group_hms_nurse"/>
                    <button name="action_cancel" string="Cancel" type="object" states="scheduled"/>
                    <button name="action_create_invoice" string="Create Invoice" type="object" invisible="invoice_id" class="oe_highlight" groups="account.group_account_invoice"/>
                    <button name="view_invoice" string="View Invoice" type="object" invisible="not invoice_id" class="oe_highlight" groups="account.group_account_invoice"/>
                    <field name="state" widget="statusbar" statusbar_visible="scheduled,running,done"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button class="oe_stat_button" type="object" name="action_view_attachments" icon="fa-files-o" invisible="attach_count == 0">
                            <field string="Documents" name="attach_count" widget="statinfo"/>
                        </button>
                        <button class="oe_inline oe_stat_button" type="object" name="action_attachments_preview" title="Preview Documents" icon="fa-files-o" string="Preview Documents" widget="statinfo"/>
                    </div>
                    <div class="oe_title">
                        <label for="name" string="Name" class="oe_edit_only"/>
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="patient_id"/>
                            <label for="date" string="Date"/>
                            <div class="o_row">
                                <field name="date" widget="daterange" nolabel="1" class="oe_inline" options="{'related_end_date': 'date_stop'}"/>
                                <i class="fa fa-long-arrow-right mx-2" aria-label="Arrow icon" title="Arrow"/>
                                <field name="date_stop" widget="daterange" nolabel="1" class="oe_inline" options="{'related_start_date': 'date'}"/>
                            </div>
                            <field name="duration" widget="float_time"/>
                            <field name="diseas_id"/>
                            <field name="treatment_id"/>
                            <field name="department_id" groups="acs_hms.group_allow_multi_department"/>
                            <field name="appointment_ids" widget="many2many_tags" domain="[('patient_id','=',patient_id)]"/>
                        </group>
                        <group>
                            <field name="physician_id"/>
                            <field name="product_id" context="{'default_detailed_type': 'service', 'default_hospital_product_type': 'procedure'}" domain="[('hospital_product_type','like','procedure')]"/>
                            <field name="price_unit"/>
                            <field name="invoice_id"/>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                            <field name="department_type" invisible="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page name="consumed_products" string="Consumed Products">
                            <label for="acs_kit_id" invisible="state in ('done', 'cancel')"/>
                            <div name="acs_kit_id" invisible="state in ('done', 'cancel')">
                                <field name="acs_kit_id" nolabel="1" options="{'no_create': True}"/>
                                <field name="acs_kit_qty" class="oe_inline" nolabel="1" invisible="not acs_kit_id"/>
                                <button name="get_acs_kit_lines" string="Add Kit Products" type="object" class="oe_inline fa fa-arrow-right oe_link" invisible="not acs_kit_id"/>
                            </div>
                            <field name="consumable_line_ids" nolabel="1" colspan="4" context="{'default_patient_id': patient_id, 'default_department_id': department_id, 'default_physician_id': physician_id}">
                                <tree string="Line" editable="top">
                                    <field name="product_id" expand="1" required="1"/>
                                    <field name="product_uom" required="1" groups="uom.group_uom"/>
                                    <field name="product_uom_category_id" invisible="1"/>
                                    <field name="qty" required="1"/>
                                    <field name="tracking" invisible="1"/>
                                    <field name="lot_id" context="{'acs_product_id': product_id}" options="{'no_create': True}" readonly="tracking == 'none'" required="tracking != 'none'"/>
                                    <field name="price_unit" readonly="1"/>
                                    <field name="subtotal" readonly="1" sum="Total" optional="show"/>
                                    <field name="date" required="1"/>
                                    <field name="note"/>
                                    <field name="move_id" invisible="1"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                    <field name="description"/>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="activity_ids" widget="mail_activity"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <record id="view_acs_patient_procedure_search" model="ir.ui.view">
        <field name="name">acs.patient.procedure.search</field>
        <field name="model">acs.patient.procedure</field>
        <field name="arch" type="xml">
            <search string="Procedure">
                <field name="name"/>
                <field name="patient_id"/>
                <field name="physician_id"/>
                <field name="product_id"/>
                <field name="appointment_ids"/>
                <field name="department_id" groups="acs_hms.group_allow_multi_department"/>
                <separator/>
                <filter name="my_procedures" string="My Procedure" domain="[('physician_id.user_id', '=',uid)]"/>
                <separator/>
                <filter name="not_done" string="Not Done" domain="[('state','!=','done')]"/>
                <filter name="scheduled" string="Schedule" domain="[('state','=','scheduled')]"/>
                <group expand="0" string="Group By...">
                    <filter name="group_by_physician_id" string="Physician" domain="[]" context="{'group_by':'physician_id'}"/>
                    <filter string="State" name="state_groupby" domain="[]" context="{'group_by':'state'}"/>
                    <filter name="group_by_patient" string="Patient" domain="[]" context="{'group_by':'patient_id'}"/>
                    <filter name="group_by_product" string="Procedures" domain="[]" context="{'group_by':'product_id'}"/>
                    <filter name="group_by_date" string="Date" domain="[]" context="{'group_by':'date'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="view_acs_patient_procedure_calendar" model="ir.ui.view">
        <field name="name">acs.patient.procedure.calendar</field>
        <field name="model">acs.patient.procedure</field>
        <field name="type">calendar</field>
        <field name="arch" type="xml">
            <calendar string="Patient Procedures" color="product_id" date_start="date" date_stop="date_stop">
                <field name="physician_id"/>
                <field name="patient_id"/>
                <field name="state"/>
            </calendar>
        </field>
    </record>

    <record id="view_vaccination_pivot" model="ir.ui.view">
        <field name="name">acs.patient.procedure.pivot</field>
        <field name="model">acs.patient.procedure</field>
        <field name="arch" type="xml">
            <pivot string="Patient Procedure">
                <field name="date" type="row"/>
                <field name="physician_id" type="row"/>
                <field name="patient_id" type="row"/>
            </pivot>
        </field>
    </record>

    <record id="action_acs_patient_procedure" model="ir.actions.act_window">
        <field name="name">Patient Procedures</field>
        <field name="res_model">acs.patient.procedure</field>
        <field name="view_mode">tree,form,calendar,pivot</field>
        <field name="context">{'search_default_not_done': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Click to add a Patient Patient Procedure.
            </p>
        </field>
    </record>

</odoo>