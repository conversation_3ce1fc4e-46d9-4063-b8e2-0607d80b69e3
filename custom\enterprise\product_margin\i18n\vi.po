# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_margin
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__purchase_num_invoiced
msgid "# Invoiced in Purchase"
msgstr "# Đã lập hoá đơn trong mua hàng"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__sale_num_invoiced
msgid "# Invoiced in Sale"
msgstr "# Đã lập hoá đơn trong bán hàng"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "# Purchased"
msgstr "# Đã mua"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Analysis Criteria"
msgstr "Tiêu chí phân tích"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__sale_avg_price
msgid "Avg. Price in Customer Invoices."
msgstr "Giá trung bình trên hóa đơn bán hàng"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__purchase_avg_price
msgid "Avg. Price in Vendor Bills"
msgstr "Giá trung bình trên hoá đơn mua hàng"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__purchase_avg_price
msgid "Avg. Purchase Unit Price"
msgstr "Đơn giá mua trung bình"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__sale_avg_price
msgid "Avg. Sale Unit Price"
msgstr "Đơn giá bán trung bình"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Avg. Unit Price"
msgstr "Đơn giá trung bình"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "Cancel"
msgstr "Hủy"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Catalog Price"
msgstr "Giá danh mục"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: product_margin
#: model:ir.model.fields.selection,name:product_margin.selection__product_margin__invoice_state__draft_open_paid
#: model:ir.model.fields.selection,name:product_margin.selection__product_product__invoice_state__draft_open_paid
msgid "Draft, Open and Paid"
msgstr "Nháp, mở và đã thanh toán"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__expected_margin
msgid "Expected Margin"
msgstr "Biên lợi nhuận dự kiến"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__expected_margin_rate
msgid "Expected Margin (%)"
msgstr "Biên lợi nhuận dự kiến (%)"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__sale_expected
msgid "Expected Sale"
msgstr "Bán hàng dự kiến"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__expected_margin
msgid "Expected Sale - Normal Cost"
msgstr "Bán hàng dự kiến - Chi phí thông thường"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__sales_gap
msgid "Expected Sale - Turn Over"
msgstr "Bán hàng dự kiến - Doanh số"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__expected_margin_rate
msgid "Expected margin * 100 / Expected Sale"
msgstr "Biên lợi nhuận dự kiến * 100/Bán hàng dự kiến"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__from_date
msgid "From"
msgstr "Từ"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "General Information"
msgstr "Thông tin Tổng quan"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__id
msgid "ID"
msgstr "ID"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__invoice_state
#: model:ir.model.fields,field_description:product_margin.field_product_product__invoice_state
msgid "Invoice State"
msgstr "Trạng thái hóa đơn"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__date_from
msgid "Margin Date From"
msgstr "Ngày tính biên lợi nhuận từ"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__date_to
msgid "Margin Date To"
msgstr "Ngày tính biên lợi nhuận đến"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Margins"
msgstr "Biên lợi nhuận"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__normal_cost
msgid "Normal Cost"
msgstr "Chi phí thông thường"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__purchase_gap
msgid "Normal Cost - Total Cost"
msgstr "Chi phí thông thường - Tổng chi phí"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "Open Margins"
msgstr "Biên lợi nhuận mở"

#. module: product_margin
#: model:ir.model.fields.selection,name:product_margin.selection__product_margin__invoice_state__open_paid
#: model:ir.model.fields.selection,name:product_margin.selection__product_product__invoice_state__open_paid
msgid "Open and Paid"
msgstr "Mở và đã thanh toán"

#. module: product_margin
#: model:ir.model.fields.selection,name:product_margin.selection__product_margin__invoice_state__paid
#: model:ir.model.fields.selection,name:product_margin.selection__product_product__invoice_state__paid
msgid "Paid"
msgstr "Đã thanh toán"

#. module: product_margin
#: model:ir.model,name:product_margin.model_product_margin
msgid "Product Margin"
msgstr "Biên lợi nhuận sản phẩm"

#. module: product_margin
#. odoo-python
#: code:addons/product_margin/wizard/product_margin.py:0
#: model:ir.actions.act_window,name:product_margin.product_margin_act_window
#: model:ir.ui.menu,name:product_margin.menu_action_product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_graph
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Product Margins"
msgstr "Biên lợi nhuận sản phẩm"

#. module: product_margin
#: model:ir.model,name:product_margin.model_product_product
msgid "Product Variant"
msgstr "Biến thể sản phẩm"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "Properties categories"
msgstr "Danh mục thuộc tính"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__purchase_gap
msgid "Purchase Gap"
msgstr "Chênh lệch mua hàng"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Purchases"
msgstr "Mua hàng"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Sales"
msgstr "Bán hàng"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__sales_gap
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Sales Gap"
msgstr "Chênh lệch bán hàng"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Standard Price"
msgstr "Giá tiêu chuẩn"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__normal_cost
msgid "Sum of Multiplication of Cost price and quantity of Vendor Bills"
msgstr "Tổng của bội số giữa giá vốn và số lượng trên hóa đơn mua hàng"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__turnover
msgid ""
"Sum of Multiplication of Invoice price and quantity of Customer Invoices"
msgstr "Tổng của bội số giữa giá hoá đơn và số lượng trên hóa đơn bán hàng"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__total_cost
msgid "Sum of Multiplication of Invoice price and quantity of Vendor Bills "
msgstr "Tổng của bội số giữa giá hoá đơn và số lượng trên hóa đơn mua hàng"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__sale_expected
msgid ""
"Sum of Multiplication of Sale Catalog price and quantity of Customer "
"Invoices"
msgstr ""
"Tổng của bội số giữa giá danh mục bán hàng và số lượng trên hóa đơn bán hàng"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__sale_num_invoiced
msgid "Sum of Quantity in Customer Invoices"
msgstr "Tổng số lượng trên hóa đơn bán hàng"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__purchase_num_invoiced
msgid "Sum of Quantity in Vendor Bills"
msgstr "Tổng số lượng trên hoá đơn mua hàng"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__to_date
msgid "To"
msgstr "Đến"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__total_cost
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Total Cost"
msgstr "Tổng chi phí"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__total_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Total Margin"
msgstr "Tổng biên lợi nhuận"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__total_margin_rate
msgid "Total Margin Rate(%)"
msgstr "Tổng tỷ lệ biên lợi nhuận(%)"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__total_margin_rate
msgid "Total margin * 100 / Turnover"
msgstr "Tổng biên lợi nhuận * 100/Doanh số"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__turnover
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Turnover"
msgstr "Doanh số"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__total_margin
msgid "Turnover - Total cost"
msgstr "Doanh số - Tổng chi phí"
