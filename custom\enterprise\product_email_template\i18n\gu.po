# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_email_template
#
# Translators:
# Qaidjohar <PERSON>bha<PERSON>, 2023
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-20 09:02+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: Qaidjohar Barbhaya, 2023\n"
"Language-Team: Gujarati (https://app.transifex.com/odoo/teams/41243/gu/)\n"
"Language: gu\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.product_template_form_view
msgid "Automatic Email at Invoice"
msgstr ""

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.email_template_form_simplified
msgid "Body"
msgstr ""

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.email_template_form_simplified
#: model_terms:ir.ui.view,arch_db:product_email_template.product_template_form_view
msgid "Email Template"
msgstr ""

#. module: product_email_template
#: model:ir.model,name:product_email_template.model_account_move
msgid "Journal Entry"
msgstr "Journal Entry"

#. module: product_email_template
#: model:ir.model,name:product_email_template.model_product_template
msgid "Product"
msgstr "Product"

#. module: product_email_template
#: model:ir.model.fields,field_description:product_email_template.field_product_product__email_template_id
#: model:ir.model.fields,field_description:product_email_template.field_product_template__email_template_id
msgid "Product Email Template"
msgstr ""

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.product_template_form_view
msgid "Send a product-specific email once the invoice is validated"
msgstr ""

#. module: product_email_template
#: model:ir.model.fields,help:product_email_template.field_product_product__email_template_id
#: model:ir.model.fields,help:product_email_template.field_product_template__email_template_id
msgid "When validating an invoice, an email will be sent to the customer based on this template. The customer will receive an email for each product linked to an email template."
msgstr ""
