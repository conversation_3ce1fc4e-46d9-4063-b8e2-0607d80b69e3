<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_pos_payment_form" model="ir.ui.view">
        <field name="name">pos.payment.form</field>
        <field name="model">pos.payment</field>
        <field name="arch" type="xml">
            <form string="Payments" create="0" edit="0" delete="0">
                <sheet>
                    <group>
                        <field name="session_id" readonly="0"/>
                        <field name="pos_order_id"/>
                        <field name="amount" readonly="0"/>
                        <field name="payment_method_id"/>
                        <field name="payment_method_payment_mode" invisible="not payment_method_payment_mode"/>
                        <field name="card_type" invisible="not card_type or not card_no"/>
                        <field name="card_brand" string="Card's Brand" invisible="not card_brand or not card_no"/>
                        <field name="card_no" invisible="not card_no"/>
                        <field name="cardholder_name" invisible="not cardholder_name or not card_no"/>
                        <field name="payment_method_issuer_bank" invisible="not payment_method_issuer_bank"/>
                        <field name="payment_method_authcode" invisible="not payment_method_authcode"/>
                        <field name="payment_ref_no" invisible="not payment_ref_no"/>
                        <field name="transaction_id" invisible="not transaction_id"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_pos_payment_tree" model="ir.ui.view">
        <field name="name">pos.payment.list</field>
        <field name="model">pos.payment</field>
        <field name="arch" type="xml">
            <list string="Payments" create="0" delete="0">
                <field name="currency_id" column_invisible="True" />
                <field name="payment_date"/>
                <field name="payment_method_id" />
                <field name="pos_order_id" />
                <field name="user_id" widget="many2one_avatar_employee"/>
                <field name="amount" />
            </list>
        </field>
    </record>

    <record id="view_pos_payment_search" model="ir.ui.view">
        <field name="name">pos.payment.search.view</field>
        <field name="model">pos.payment</field>
        <field name="arch" type="xml">
            <search string="Payments">
                <field name="name"/>
                <field name="amount"/>
                <field name="pos_order_id" />
                <group expand="0" string="Group By">
                    <filter name="group_by_payment_method" string="Payment Method" domain="[]"  context="{'group_by':'payment_method_id'}"/>
                    <filter name="group_by_session" string="Session" domain="[]"  context="{'group_by':'session_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_pos_payment_form" model="ir.actions.act_window">
        <field name="name">Payments</field>
        <field name="res_model">pos.payment</field>
        <field name="view_mode">list,form</field>
        <field name="view_id" eval="False"/>
        <field name="domain">[]</field>
        <field name="context">{'search_default_group_by_payment_method': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No orders found
            </p><p>
                To record new orders, start a new session.
            </p>
        </field>
    </record>

    <menuitem id="menu_pos_payment" parent="menu_point_of_sale" action="action_pos_payment_form" sequence="3" groups="group_pos_manager,group_pos_user"/>
</odoo>
