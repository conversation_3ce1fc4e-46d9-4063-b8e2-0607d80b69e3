<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-inherit="mail.AvatarCardPopover" t-inherit-mode="extension">
        <xpath expr="//a[hasclass('o-mail-avatar-card-tel')]" position="after">
            <span t-if="user.work_location_name" class="text-muted" data-tooltip="Work Location">
                <i t-if="user.work_location_type === 'office'" class="me-1 fa fa-fw fa-building-o"/>
                <i t-elif="user.work_location_type === 'home'" class="me-1 fa fa-fw fa-home"/>
                <i t-else="" class="me-1 fa fa-fw fa-map-marker"/>
                <t t-esc="user.work_location_name"/>
            </span>
        </xpath>
    </t>

    <t t-name="hr.avatarCardUserInfos">
        <small class="text-muted text-truncate" t-if="user.job_title" t-att-title="user.job_title" t-esc="user.job_title"/>
        <span class="text-muted text-truncate" t-if="user.department_id" data-tooltip="Department" t-att-title="user.department_id[1]" t-esc="user.department_id[1]"/>
    </t>
</templates>
