<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- sgv note - icons will be replaced. Design task is ongoing -->
        <record id="expense_product_meal" model="product.product">
            <field name="name">Meals</field>
            <field name="description">Restaurants, business lunches, etc.</field>
            <field name="standard_price">0.0</field>
            <field name="type">service</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="default_code">FOOD</field>
            <field name="can_be_expensed" eval="True"/>
            <field name="sale_ok" eval="False"/>
            <field name="purchase_ok" eval="False"/>
            <field name="image_1920" type="base64" file="hr_expense/static/img/food.svg"/>
        </record>
        <record id="expense_product_travel_accommodation" model="product.product">
            <field name="name">Travel &amp; Accommodation</field>
            <field name="description">Hotel, plane ticket, taxi, etc.</field>
            <field name="standard_price">0.0</field>
            <field name="type">service</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="default_code">TRANS &amp; ACC</field>
            <field name="can_be_expensed" eval="True"/>
            <field name="sale_ok" eval="False"/>
            <field name="purchase_ok" eval="False"/>
            <field name="image_1920" type="base64" file="hr_expense/static/img/transport.svg"/>
        </record>
        <record id="expense_product_mileage" model="product.product">
            <field name="name">Mileage</field>
            <field name="standard_price">1.0</field>
            <field name="type">service</field>
            <field name="uom_id" ref="uom.product_uom_km"/>
            <field name="uom_po_id" ref="uom.product_uom_km"/>
            <field name="default_code">MIL</field>
            <field name="can_be_expensed" eval="True"/>
            <field name="sale_ok" eval="False"/>
            <field name="purchase_ok" eval="False"/>
            <field name="image_1920" type="base64" file="hr_expense/static/img/mileage.svg"/>
        </record>
        <record id="expense_product_gift" model="product.product">
            <field name="name">Gifts</field>
            <field name="description">Gifts to customers or vendors</field>
            <field name="standard_price">0.0</field>
            <field name="type">service</field>
            <field name="uom_id" ref="uom.product_uom_km"/>
            <field name="uom_po_id" ref="uom.product_uom_km"/>
            <field name="default_code">GIFT</field>
            <field name="can_be_expensed" eval="True"/>
            <field name="sale_ok" eval="False"/>
            <field name="purchase_ok" eval="False"/>
            <field name="image_1920" type="base64" file="hr_expense/static/img/gift.svg"/>
        </record>
        <record id="expense_product_communication" model="product.product">
            <field name="name">Communication</field>
            <field name="description">Phone bills, postage, etc.</field>
            <field name="standard_price">0.0</field>
            <field name="type">service</field>
            <field name="uom_id" ref="uom.product_uom_km"/>
            <field name="uom_po_id" ref="uom.product_uom_km"/>
            <field name="default_code">COMM</field>
            <field name="can_be_expensed" eval="True"/>
            <field name="sale_ok" eval="False"/>
            <field name="purchase_ok" eval="False"/>
            <field name="image_1920" type="base64" file="hr_expense/static/img/communication.svg"/>
        </record>
        <record id="product_product_no_cost" model="product.product">
            <field name="name">Expenses</field>
            <field name="standard_price">0.0</field>
            <field name="type">service</field>
            <field name="default_code">EXP_GEN</field>
            <field name="categ_id" ref="product.cat_expense"/>
            <field name="can_be_expensed" eval="True"/>
            <field name="image_1920" type="base64" file="hr_expense/static/img/other.svg"/>
        </record>
    </data>
    <data>
        <function model="ir.config_parameter" name="set_param" eval="('hr_expense.use_mailgateway', True)"/>
    </data>
</odoo>
