<?xml version="1.0" encoding="utf-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="sale_subscription_stock.ForecastedDetails" t-inherit="stock.ForecastedDetails" t-inherit-mode="extension">
        <xpath expr="//tr[@name='draft_picking_out']" position="after">
            <tr t-if="props.docs.subscription_qty" name="subscription_picking_out">
                <td colspan="2">Subscriptions</td>
                <td t-out="_formatFloat(this.props.docs.subscription_qty)" class="text-end"/>
                <td>
                    <t t-foreach="props.docs.subscription_sale_orders" t-as="sale_order" t-key="sale_order_index">
                        <t t-if="sale_order_index > 0"> | </t>
                        <a t-attf-href="#" t-out="sale_order.name"
                           class="fw-bold"
                           t-on-click.prevent="() => this.props.openView('sale.order', 'form', sale_order.id)"/>
                    </t>
                </td>
            </tr>
        </xpath>
    </t>
</templates>
