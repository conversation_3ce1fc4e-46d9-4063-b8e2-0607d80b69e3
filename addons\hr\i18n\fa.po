# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr
# 
# Translators:
# <PERSON><PERSON><PERSON> moradi, 2025
# <PERSON>, 2025
# Naser mars, 2025
# <PERSON><PERSON><PERSON>hory <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2025\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_email_amount
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_email_amount
msgid "# emails to send"
msgstr "# ایمیل برای ارسال"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_job.py:0
msgid "%s (copy)"
msgstr "%s (کپی)"

#. module: hr
#: model:ir.actions.report,print_report_name:hr.hr_employee_print_badge
msgid "'Badge - %s' % (object.name).replace('/', '')"
msgstr "'%s - نشان' % (object.name).replace('/', '')"

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "1 Onsite Interview"
msgstr "1 مصاحبه حضوری"

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "1 Phone Call"
msgstr "1 تماس تلفنی"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "12 days / year, including <br>6 of your choice."
msgstr "12 روز / سال، شامل <br>6 روز به انتخاب شما."

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "2 open days"
msgstr "دو روز باز"

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "4 Days after Interview"
msgstr "۴ روز پس از مصاحبه"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid ""
"<b>Congratulations!</b> May I recommend you to setup an <a "
"href=\"%s\">onboarding plan?</a>"
msgstr ""
"<b> تبریک ! </b> آیا میتوانم شما را  توصیه کنم به راه اندازی <a href=\"%s\">"
" طرح پرکننده؟ </a>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "<i class=\"fa fa-building-o\" role=\"img\" aria-label=\"Company\" title=\"Company\"/>"
msgstr "<i class=\"fa fa-building-o\" role=\"img\" aria-label=\"شرکت\" title=\"شرکت\"/>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
msgid "<i class=\"fa fa-fw me-2 fa-envelope text-primary\" title=\"Email\"/>"
msgstr "<i class=\"fa fa-fw me-2 fa-envelope text-primary\" title=\"Email\"/>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
msgid "<i class=\"fa fa-fw me-2 fa-phone text-primary\" title=\"Phone\"/>"
msgstr "<i class=\"fa fa-fw me-2 fa-phone text-primary\" title=\"Phone\"/>"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "<small><b>READ</b></small>"
msgstr "<small><b>خواندن</b></small>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "<span class=\"flex-shrink-0 ml8 me-2\">IP Addresses</span>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "<span class=\"flex-shrink-0 ml8 me-2\">Sent Emails</span>"
msgstr "<span class=\"flex-shrink-0 ml8 me-2\">ارسال ایمیل</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid ""
"<span class=\"o_form_label o_hr_form_label cursor-default\">Close "
"Activities</span>"
msgstr ""
"<span class=\"o_form_label o_hr_form_label cursor-default\">بستن "
"فعالیت‌ها</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid ""
"<span class=\"o_form_label o_hr_form_label cursor-default\">Detailed "
"Reason</span>"
msgstr ""
"<span class=\"o_form_label o_hr_form_label cursor-default\">دلیل دقیق</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "<span class=\"o_form_label o_hr_form_label cursor-default\">HR Info</span>"
msgstr ""
"<span class=\"o_form_label o_hr_form_label cursor-default\">اطلاعات منابع "
"انسانی</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Not Connected\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    متصل نیست\n"
"                                </span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "<span class=\"o_stat_text\">Connected Since</span>"
msgstr "<span class=\"o_stat_text\">متصل شده از</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form_smartbutton_inherited
msgid "<span class=\"o_stat_text\">Contacts</span>"
msgstr "<span class=\"o_stat_text\">مخاطبین</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_partner_view_form
msgid "<span class=\"o_stat_text\">Employee</span>"
msgstr "<span class=\"o_stat_text\">کارمند</span>"

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "<span class=\"text-muted small\">Days to get an Offer</span>"
msgstr ""
"<span class=\"text-muted small\">روزهایی که می‌توان پیشنهاد دریافت "
"کرد</span>"

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "<span class=\"text-muted small\">Process</span>"
msgstr "<span class=\"text-muted small\">فرآیند</span>"

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "<span class=\"text-muted small\">Time to Answer</span>"
msgstr "<span class=\"text-muted small\">زمان پاسخگویی</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "<span>Reporting</span>"
msgstr "<span>گزارش</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "<span>View</span>"
msgstr "<span>نمایش</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "<span>new Employees</span>"
msgstr "<span>کارمندان جدید</span>"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "A full-time position <br>Attractive salary package."
msgstr "موقعیت شغلی تمام وقت <br>بسته حقوقی جذاب."

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_employee_user_uniq
msgid "A user cannot be linked to multiple employees in the same company."
msgstr "یک کاربر نمی‌تواند به کارکنان متعدد در همان شرکت مرتبط باشد."

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_presence_state__absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_presence_state__absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_presence_state__absent
msgid "Absent"
msgstr "غایب"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Achieve monthly sales objectives"
msgstr "اهداف فروش ماهانه را تحقق بخشید"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_needaction
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_needaction
#: model:ir.model.fields,field_description:hr.field_hr_job__message_needaction
msgid "Action Needed"
msgstr "اقدام مورد نیاز است"

#. module: hr
#: model_terms:digest.tip,tip_description:hr.digest_tip_hr_0
msgid ""
"Activate Remote Work to let Employees specify where they are working from."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__active
#: model:ir.model.fields,field_description:hr.field_hr_employee__active
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__active
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__active
#: model:ir.model.fields,field_description:hr.field_hr_job__active
#: model:ir.model.fields,field_description:hr.field_hr_work_location__active
msgid "Active"
msgstr "فعال"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_ids
msgid "Activities"
msgstr "فعالیت ها"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_exception_decoration
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "دکوراسیون استثنایی فعالیت"

#. module: hr
#: model:ir.model,name:hr.model_mail_activity_plan
#: model:ir.ui.menu,name:hr.menu_config_plan_plan
msgid "Activity Plan"
msgstr "طرح فعالیت"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_state
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_state
msgid "Activity State"
msgstr "وضعیت فعالیت"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_type_icon
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_type_icon
msgid "Activity Type Icon"
msgstr "آیکون نوع فعالیت"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_tree
msgid "Activity by"
msgstr "فعالیت توسط"

#. module: hr
#: model:ir.model,name:hr.model_mail_activity_plan_template
msgid "Activity plan template"
msgstr "قالب برنامه فعالیت"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.mail_activity_plan_action
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                    (e.g. \"Onboarding\", \"Offboarding\", ...)"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_mail_activity_schedule
msgid "Activity schedule plan Wizard"
msgstr "جادوگر برنامه‌ریزی زمان‌بندی فعالیت"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_employee_public_action
#: model_terms:ir.actions.act_window,help:hr.open_view_employee_list_my
msgid "Add a new employee"
msgstr "افزودن یک کارمند جدید"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__departure_description
#: model:ir.model.fields,field_description:hr.field_hr_employee__departure_description
msgid "Additional Information"
msgstr "اطلاعات اضافی"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid ""
"Additional Information: \n"
" %(description)s"
msgstr ""
"اطلاعات اضافی:\n"
"%(description)s"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__additional_note
#: model:ir.model.fields,field_description:hr.field_res_users__additional_note
msgid "Additional Note"
msgstr "یادداشت اضافه"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Additional languages"
msgstr "زبان‌های دیگر"

#. module: hr
#: model:hr.department,name:hr.dep_administration
msgid "Administration"
msgstr "مدیریت"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Administrative Work"
msgstr "کارهای اداری"

#. module: hr
#: model:res.groups,name:hr.group_hr_manager
msgid "Administrator"
msgstr "مدیر"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_presence
msgid "Advanced Presence Control"
msgstr "کنترل حضور پیشرفته"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Advanced presence of employees"
msgstr "حضور و غیاب پیشرفته کارکنان"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_crm_team__alias_contact
#: model:ir.model.fields,field_description:hr.field_mail_alias__alias_contact
#: model:ir.model.fields,field_description:hr.field_mail_group__alias_contact
#: model:ir.model.fields,field_description:hr.field_maintenance_equipment_category__alias_contact
msgid "Alias Contact Security"
msgstr "مستعار تماس امنیتی"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Allow employees to update their own data"
msgstr "اجازه به کارمندان برای به‌روز‌رسانی داده‌های خودشان"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Allow employees to update their own data."
msgstr "اجازه به کارمندان برای به‌روز‌رسانی داده‌های خودشان."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Application Settings"
msgstr "تنظیمات برنامه"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "Apply"
msgstr "اعمال"

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_apprenticeship
msgid "Apprenticeship"
msgstr "کارآموزی"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Approvers"
msgstr "تایید ‌کنندگان"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "Archive"
msgstr "بایگانی"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_archive
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_presence_state__archive
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_archive
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_presence_state__archive
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_archive
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_presence_state__archive
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Archived"
msgstr "بایگانی شده"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid ""
"As an employee of our company, you will <b>collaborate with each department to create and deploy\n"
"                                disruptive products.</b> Come work at a growing company that offers great benefits with opportunities to\n"
"                                moving forward and learn alongside accomplished leaders. We're seeking an experienced and outstanding member of staff.\n"
"                                <br><br>\n"
"                                This position is both <b>creative and rigorous</b> by nature you need to think outside the box.\n"
"                                We expect the candidate to be proactive and have a \"get it done\" spirit. To be successful,\n"
"                                you will have solid solving problem skills."
msgstr ""
"به عنوان یک کارمند در شرکت ما، شما با هر بخش برای ایجاد و توسعه <b>محصولات پیشرو و نوآورانه همکاری خواهید کرد.</b> در شرکتی کار کنید که در حال رشد است و مزایای عالی همراه با فرصت‌هایی برای پیشرفت و یادگیری در کنار رهبران با تجربه ارائه می‌دهد. ما به دنبال یک عضو کارمند مجرب و برجسته هستیم.\n"
"                                <br><br>\n"
"                                این موقعیت هم به <b>صورت خلاقانه و هم دقیق</b> است و شما باید خارج از چارچوب فکر کنید. ما انتظار داریم که کاندیدا روحیه‌ای پیشگیرانه داشته باشد و \"کار را انجام دهد\". برای موفقیت، شما باید مهارت‌های قوی در حل مسئله داشته باشید."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_mail_activity_plan_template__responsible_type
msgid "Assignment"
msgstr "تکلیف"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_attachment_count
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_attachment_count
#: model:ir.model.fields,field_description:hr.field_hr_job__message_attachment_count
msgid "Attachment Count"
msgstr "تعداد پیوست ها"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Attendance"
msgstr "حضور و غیاب"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Attendance/Point of Sale"
msgstr "حضور/نقطه فروش"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__mail_alias__alias_contact__employees
msgid "Authenticated Employees"
msgstr "کارکنان تاییدشده"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.discuss_channel_view_form
msgid "Auto Subscribe Departments"
msgstr "دپارتمان‌های اشتراک خودکار"

#. module: hr
#: model:ir.model.fields,help:hr.field_discuss_channel__subscription_department_ids
msgid "Automatically subscribe members of those departments to the channel."
msgstr "به طور خودکار اعضای آن دپارتمان‌ها را در کانال مشترک میکند."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Autonomy"
msgstr "خودمختاری"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Available"
msgstr "در دسترس"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.mail_activity_plan_view_form
msgid "Available for all Departments"
msgstr "موجود برای تمامی بخش ها"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_1920
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_1920
msgid "Avatar"
msgstr "آواتار"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_1024
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_1024
msgid "Avatar 1024"
msgstr "آواتار 1024"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_128
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_128
msgid "Avatar 128"
msgstr "آواتار 128"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_256
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_256
msgid "Avatar 256"
msgstr "آواتار 256"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_512
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_512
msgid "Avatar 512"
msgstr "آواتار 512"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Away"
msgstr "دور از کار"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__bachelor
msgid "Bachelor"
msgstr "لیسانس"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Bachelor Degree or Higher"
msgstr "دارای مدرک کارشناسی یا بالاتر"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__barcode
#: model:ir.model.fields,field_description:hr.field_res_users__barcode
msgid "Badge ID"
msgstr "شناسه نشان"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__bank_account_id
msgid "Bank Account"
msgstr "حساب بانکی"

#. module: hr
#: model:ir.model,name:hr.model_res_partner_bank
msgid "Bank Accounts"
msgstr "حساب‌های بانکی"

#. module: hr
#: model:ir.model,name:hr.model_base
msgid "Base"
msgstr "پایه"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_ip
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_ip
msgid "Based on IP Address"
msgstr "بر اساس آدرس IP"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_attendance
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_attendance
msgid "Based on attendances"
msgstr "بر اساس حضور و غیاب"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_email
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_email
msgid "Based on number of emails sent"
msgstr "بر اساس تعداد ایمیل‌های فرستاده‌شده"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_login
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_login
msgid "Based on user status in system"
msgstr "بر اساس وضعیت کاربر در سیستم"

#. module: hr
#: model:ir.model,name:hr.model_hr_employee_base
msgid "Basic Employee"
msgstr "کارمند پایه"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/background_image/background_image.xml:0
msgid "Binary file"
msgstr "فایل باینری"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__can_edit
msgid "Can Edit"
msgstr "میتواند ویرایش کند"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_users_simple_form
msgid "Cancel"
msgstr "لغو"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__employee_type
#: model:ir.model.fields,help:hr.field_res_users__employee_type
msgid ""
"Categorize your Employees by type. This field also has an impact on "
"contracts. Only Employees, Students and Trainee will have contract history."
msgstr ""
"کارمندان خود را بر اساس نوع دسته بندی کنید. این فیلد در قراردادها هم تاثیر "
"دارد. فقط کارمندان، دانشجویان و کارآموزان دارای سابقه قرارداد خواهند بود."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__certificate
#: model:ir.model.fields,field_description:hr.field_res_users__certificate
msgid "Certificate Level"
msgstr "سطح گواهینامه"

#. module: hr
#: model:ir.actions.act_window,name:hr.res_users_action_my
msgid "Change my Preferences"
msgstr "تغییر ترجیحات من"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/employee_chat/employee_chat.xml:0
msgid "Chat"
msgstr "گفتگو"

#. module: hr
#: model:hr.job,name:hr.job_ceo
msgid "Chief Executive Officer"
msgstr "مدیر اجرایی"

#. module: hr
#: model:hr.job,name:hr.job_cto
msgid "Chief Technical Officer"
msgstr "مدیر فنی"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__child_ids
msgid "Child Departments"
msgstr "دپارتمان‌های زیرمجموعه"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "Child departments"
msgstr "زیرمجموعه"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Citizenship"
msgstr "شهروندی"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "City"
msgstr "شهر"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__coach_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__coach_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__coach_id
#: model:ir.model.fields,field_description:hr.field_res_users__coach_id
#: model:ir.model.fields.selection,name:hr.selection__mail_activity_plan_template__responsible_type__coach
msgid "Coach"
msgstr "مربی"

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
msgid "Coach of employee %s is not set."
msgstr "مربی کارمند %s تعیین نشده است."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__code
msgid "Code"
msgstr "کد"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
#: model_terms:ir.ui.view,arch_db:hr.view_department_tree
msgid "Color"
msgstr "رنگ"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__color
#: model:ir.model.fields,field_description:hr.field_hr_employee__color
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__color
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__color
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__color
msgid "Color Index"
msgstr "رنگ پس زمینه"

#. module: hr
#: model:ir.model,name:hr.model_res_company
#: model_terms:ir.ui.view,arch_db:hr.view_department_tree
msgid "Companies"
msgstr "شرکت‌ها"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__company_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__company_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__company_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__company_id
#: model:ir.model.fields,field_description:hr.field_hr_job__company_id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__company_id
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Company"
msgstr "شرکت"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__company_country_id
msgid "Company Country"
msgstr "کشور شرکت"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.print_employee_badge
msgid "Company Logo"
msgstr "لوگو شرکت"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__resource_calendar_id
msgid "Company Working Hours"
msgstr "ساعات کاری شرکت"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_id
msgid "Company employee"
msgstr "کارمند شرکت"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__complete_name
msgid "Complete Name"
msgstr "نام کامل"

#. module: hr
#: model:ir.model,name:hr.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_human_resources_configuration
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "Configuration"
msgstr "پیکربندی"

#. module: hr
#: model:hr.job,name:hr.job_consultant
msgid "Consultant"
msgstr "مشاور"

#. module: hr
#: model:ir.model,name:hr.model_res_partner
msgid "Contact"
msgstr "مخاطب"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Contact Information"
msgstr "اطلاعات تماس"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__emergency_contact
#: model:ir.model.fields,field_description:hr.field_res_users__emergency_contact
msgid "Contact Name"
msgstr "نام تماس"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__emergency_phone
#: model:ir.model.fields,field_description:hr.field_res_users__emergency_phone
msgid "Contact Phone"
msgstr "شماره تماس"

#. module: hr
#: model:ir.model,name:hr.model_hr_contract_type
msgid "Contract Type"
msgstr "نوع قرارداد"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_contract_type_view_tree
msgid "Contract Types"
msgstr "انواع قراردادها"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__contractor
msgid "Contractor"
msgstr "پیمانکار"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__country_id
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Country"
msgstr "کشور"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__company_country_code
msgid "Country Code"
msgstr "کد کشور"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__country_of_birth
#: model:ir.model.fields,field_description:hr.field_res_users__country_of_birth
msgid "Country of Birth"
msgstr "کشور محل تولد"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_work_location__location_type
msgid "Cover Image"
msgstr "تصویر رویی"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__create_date
msgid "Create Date"
msgstr "ایجاد تاریخ"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_users_simple_form_inherit_hr
msgid "Create Employee"
msgstr "ایجاد کارمند"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Create User"
msgstr "ایجاد کاربر"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_department_kanban_action
#: model_terms:ir.actions.act_window,help:hr.hr_department_tree_action
msgid "Create a new department"
msgstr "ایجاد یک دپارتمان جدید"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_contract_type_action
msgid "Create a new employment type"
msgstr "ایجاد یک نوع استخدام جدید"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_work_location_action
msgid "Create a new work location"
msgstr "ایجاد یک موقعیت کاری جدید"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.mail_activity_plan_action
msgid "Create an Activity Plan"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Create content that will help our users on a daily basis"
msgstr "ایجاد محتوایی که به کاربران ما در زندگی روزمره کمک کند"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form
msgid "Create employee"
msgstr "ایجاد کارمند"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_department__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_job__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_work_location__create_uid
msgid "Created by"
msgstr "ایجاد شده توسط"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__create_date
#: model:ir.model.fields,field_description:hr.field_hr_department__create_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__create_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__create_date
#: model:ir.model.fields,field_description:hr.field_hr_employee__create_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__create_date
#: model:ir.model.fields,field_description:hr.field_hr_job__create_date
#: model:ir.model.fields,field_description:hr.field_hr_work_location__create_date
msgid "Created on"
msgstr "ایجادشده در"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__currency_id
msgid "Currency"
msgstr "ارز"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__no_of_employee
msgid "Current Number of Employees"
msgstr "تعداد کارمندان فعلی"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Customer Relationship"
msgstr "ارتباط با مشتری"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__birthday
#: model:ir.model.fields,field_description:hr.field_res_users__birthday
msgid "Date of Birth"
msgstr "تاریخ تولد"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_departure_reason.py:0
msgid "Default departure reasons cannot be deleted."
msgstr "دلایل ترک کار پیش فرض را نمی توان حذف کرد."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid ""
"Define the allowed IP to be displayed as Present. In case of multiple "
"addresses, separate them by a coma."
msgstr ""
"IP مجاز را برای نمایش به صورت Present تعریف کنید. در صورت وجود چندین آدرس، "
"آنها را با کما جدا کنید."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Define the minimum number of sent emails to be displayed as Present."
msgstr ""
"حداقل تعداد ایمیل های ارسال شده را برای نمایش به عنوان موجود تعریف کنید."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__resource_calendar_id
#: model:ir.model.fields,help:hr.field_res_users__employee_resource_calendar_id
msgid ""
"Define the working schedule of the resource. If not set, the resource will "
"have fully flexible working hours."
msgstr ""
"برنامه کاری منبع را تعریف کنید. اگر تنظیم نشود، منبع ساعت کاری کاملاً انعطاف"
" پذیر خواهد داشت."

#. module: hr
#: model_terms:hr.job,description:hr.job_ceo
msgid ""
"Demonstration of different Odoo services for each client and convincing the client about functionality of the application.\n"
"The candidate should have excellent communication skills.\n"
"Relationship building and influencing skills\n"
"Expertise in New Client Acquisition (NCAs) and Relationship Management.\n"
"Gathering market and customer information.\n"
"Coordinating with the sales and support team for adopting different strategies\n"
"Reviewing progress and identifying opportunities and new areas for development.\n"
"Building strong relationships with clients / customers for business growth profitability.\n"
"Keep regular interaction with key clients for better extraction and expansion."
msgstr ""
"نمایش خدمات مختلف اودو برای هر مشتری و متقاعد کردن مشتری در مورد کارایی نرم‌افزار.  \n"
"داوطلب باید مهارت‌های ارتباطی عالی داشته باشد.  \n"
"مهارت‌های ایجاد رابطه و تاثیرگذاری  \n"
"تخصص در جذب مشتریان جدید (NCAs) و مدیریت روابط.  \n"
"جمع‌آوری اطلاعات بازار و مشتری.  \n"
"هماهنگی با تیم فروش و پشتیبانی برای اتخاذ استراتژی‌های مختلف  \n"
"بررسی پیشرفت و شناسایی فرصت‌ها و بخش‌های جدید برای توسعه.  \n"
"ایجاد روابط قوی با مشتریان برای رشد سودآوری کسب‌وکار.  \n"
"حفظ تعاملات منظم با مشتریان کلیدی برای بهتر استخراج و گسترش."

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/avatar_card/avatar_card_popover_patch.xml:0
#: code:addons/hr/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
#: model:ir.model,name:hr.model_hr_department
#: model:ir.model.fields,field_description:hr.field_hr_employee__department_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__department_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__department_id
#: model:ir.model.fields,field_description:hr.field_hr_job__department_id
#: model:ir.model.fields,field_description:hr.field_mail_activity_plan__department_id
#: model:ir.model.fields,field_description:hr.field_mail_activity_schedule__department_id
#: model:ir.model.fields,field_description:hr.field_res_users__department_id
#: model:ir.model.fields,field_description:hr.field_resource_resource__department_id
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Department"
msgstr "دپارتمان"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_mail_activity_plan__department_assignable
msgid "Department Assignable"
msgstr "دپارتمان قابل واگذاری"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__name
msgid "Department Name"
msgstr "نام دپارتمان"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/department_chart/department_chart.xml:0
msgid "Department Organization"
msgstr "سازماندهی دپارتمان"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_department_kanban_action
#: model:ir.actions.act_window,name:hr.hr_department_tree_action
#: model:ir.ui.menu,name:hr.menu_hr_department_kanban
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
msgid "Departments"
msgstr "دپارتمان‌ها"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Departure"
msgstr "خروج"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__departure_date
#: model:ir.model.fields,field_description:hr.field_hr_employee__departure_date
msgid "Departure Date"
msgstr "تاریخ خروج"

#. module: hr
#: model:ir.model,name:hr.model_hr_departure_reason
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__departure_reason_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__departure_reason_id
msgid "Departure Reason"
msgstr "دلیل خروج"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_departure_reason_action
#: model:ir.ui.menu,name:hr.menu_hr_departure_reason_tree
msgid "Departure Reasons"
msgstr "دلایل ترک کار"

#. module: hr
#: model:ir.model,name:hr.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "ویزارد خروج"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Dependant"
msgstr "وابسته"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__child_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__child_ids
msgid "Direct subordinates"
msgstr "زیردستان مستقیم"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_hr_employee
msgid "Directory"
msgstr "دایرکتوری کارکنان"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "Discard"
msgstr "رها کردن"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Discover our products."
msgstr "محصولات ما را کشف کنید."

#. module: hr
#: model:ir.model,name:hr.model_discuss_channel
msgid "Discussion Channel"
msgstr "کانال گفتگو"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__display_name
#: model:ir.model.fields,field_description:hr.field_hr_department__display_name
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__display_name
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee__display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__display_name
#: model:ir.model.fields,field_description:hr.field_hr_job__display_name
#: model:ir.model.fields,field_description:hr.field_hr_work_location__display_name
msgid "Display Name"
msgstr "نام نمایش داده شده"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid ""
"Display remote work settings for each employee and dedicated reports. "
"Presence icons will be updated with remote work location."
msgstr ""
"نمایش تنظیمات دورکاری برای هر کارمند و گزارش‌های اختصاصی. آیکون‌های حضور با "
"محل دورکاری به‌روزرسانی خواهند شد."

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "Divorced"
msgstr "مطلقه"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__doctor
msgid "Doctor"
msgstr "دکتر"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__driving_license
msgid "Driving License"
msgstr "گواهینامه رانندگی"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid ""
"Each employee has a chance to see the impact of his work.\n"
"                            You can make a real contribution to the success of the company.\n"
"                            <br>\n"
"                            Several activities are often organized all over the year, such as weekly\n"
"                            sports sessions, team building events, monthly drink, and much more."
msgstr ""
"هر کارمند این شانس را دارد که تاثیر کار خود را ببیند.\n"
"                             شما می توانید سهم واقعی در موفقیت شرکت داشته باشید.\n"
"                            <br>\n"
"                             چندین فعالیت‌ اغلب در طول سال سازماندهی می شود، \n"
"                            مانند جلسات ورزشی هفتگی، رویدادهای تیم سازی، نوشیدنی ماهانه و موارد دیگر."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Eat &amp; Drink"
msgstr "خوردن &amp;  آشامیدن"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Education"
msgstr "آموزش"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__email
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__email
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__email
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Email"
msgstr "پست الکترونیک"

#. module: hr
#: model:ir.model,name:hr.model_mail_alias
msgid "Email Aliases"
msgstr "آدرس‌های ایمیل"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Emergency"
msgstr "اضطراری"

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_partner.py:0 code:addons/hr/models/res_users.py:0
#: model:hr.contract.type,name:hr.contract_type_employee
#: model:ir.model,name:hr.model_hr_employee
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__employee_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__employee_id
#: model:ir.model.fields,field_description:hr.field_hr_manager_department_report__employee_id
#: model:ir.model.fields,field_description:hr.field_resource_resource__employee_id
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__employee
#: model:ir.model.fields.selection,name:hr.selection__mail_activity_plan_template__responsible_type__employee
#: model:ir.ui.menu,name:hr.menu_config_employee
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_users_simple_form_inherit_hr
msgid "Employee"
msgstr "کارمند"

#. module: hr
#: model:ir.model,name:hr.model_hr_employee_category
msgid "Employee Category"
msgstr "دسته‌بندی کارمند"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_count
msgid "Employee Count"
msgstr "تعداد کارکنان"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_employee_self_edit
msgid "Employee Editing"
msgstr "ویرایش کارمند"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.print_employee_badge
msgid "Employee Image"
msgstr "تصویر کارمند"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_lang
msgid "Employee Lang"
msgstr "زبان کارمند"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__name
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
msgid "Employee Name"
msgstr "نام کارمند"

#. module: hr
#: model:ir.actions.act_window,name:hr.mail_activity_plan_action
msgid "Employee Plans"
msgstr "برنامه های کارکنان"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__employee_properties_definition
msgid "Employee Properties"
msgstr "ویژگی‌های کارمند"

#. module: hr
#: model:ir.actions.act_window,name:hr.open_view_categ_form
#: model:ir.model.fields,field_description:hr.field_res_users__category_ids
#: model_terms:ir.ui.view,arch_db:hr.view_employee_category_form
msgid "Employee Tags"
msgstr "برچسب‌های کارمند"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/views/archive_employee_hook.js:0
msgid "Employee Termination"
msgstr "خاتمه کارمند"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__employee_type
#: model:ir.model.fields,field_description:hr.field_res_users__employee_type
msgid "Employee Type"
msgstr "نوع کارمند"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Employee Update Rights"
msgstr "حق دسترسی به‌روز‌رسانی کارمند"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__bank_account_id
#: model:ir.model.fields,help:hr.field_res_users__employee_bank_account_id
msgid "Employee bank account to pay salaries"
msgstr "حساب بانکی کارمند برای پرداخت حقوق"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_bank_account_id
msgid "Employee's Bank Account Number"
msgstr "شماره حساب بانکی کارمند"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_country_id
msgid "Employee's Country"
msgstr "کشور کارمند"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Employee's Name"
msgstr "نام کارمند"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_resource_calendar_id
msgid "Employee's Working Hours"
msgstr "ساعت کاری کارمند"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_department.py:0
#: model:ir.actions.act_window,name:hr.hr_employee_public_action
#: model:ir.actions.act_window,name:hr.open_view_employee_list
#: model:ir.actions.act_window,name:hr.open_view_employee_list_my
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__employee_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__employee_ids
#: model:ir.model.fields,field_description:hr.field_res_partner__employee_ids
#: model:ir.ui.menu,name:hr.menu_hr_employee_payroll
#: model:ir.ui.menu,name:hr.menu_hr_employee_user
#: model:ir.ui.menu,name:hr.menu_hr_root
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_tree
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_view_activity
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_partner_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
#: model_terms:ir.ui.view,arch_db:hr.view_department_tree
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_tree
msgid "Employees"
msgstr "کارمندان"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_partner__employees_count
#: model:ir.model.fields,field_description:hr.field_res_users__employees_count
msgid "Employees Count"
msgstr "تعداد کارمندان"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_category_list
msgid "Employees Tags"
msgstr "برچسب‌های کارمندان"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__contract_type_id
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Employment Type"
msgstr "نوع استخدام"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_contract_type_action
#: model:ir.ui.menu,name:hr.menu_view_hr_contract_type
msgid "Employment Types"
msgstr "انواع اشتغال"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Enrich employee profiles with skills and resumes"
msgstr "تکمیل اطلاعات پروفایل کارکنان با مهارت‌ها و رزومه"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Expand your knowledge of various business industries"
msgstr "افزایش دانش خود درباره صنایع مختلف تجاری"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__expected_employees
msgid ""
"Expected number of employees for this job position after new recruitment."
msgstr ""
"تعداد کارمندان مورد انتظار برای این موقعیت شغلی پس از انجام استخدام جدید."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Experience in writing online content"
msgstr "تجربه در نوشتن محتوای آنلاین"

#. module: hr
#: model:hr.job,name:hr.job_developer
msgid "Experienced Developer"
msgstr "توسعه‌دهنده‌ی با تجربه"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__share
#: model:ir.model.fields,help:hr.field_hr_employee_base__share
#: model:ir.model.fields,help:hr.field_hr_employee_public__share
msgid ""
"External user with limited access, created only for the purpose of sharing "
"data."
msgstr ""
"کاربر خارجی با دسترسی محدود، که فقط برای به اشتراک گذاری داده ها ایجاد شده "
"است."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Family Status"
msgstr "وضعیت خانواده"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__gender__female
msgid "Female"
msgstr "زن"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__study_field
#: model:ir.model.fields,field_description:hr.field_res_users__study_field
msgid "Field of Study"
msgstr "رشته تحصیلی"

#. module: hr
#: model:hr.departure.reason,name:hr.departure_fired
msgid "Fired"
msgstr "اخراج شده"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_follower_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_follower_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_follower_ids
msgid "Followers"
msgstr "دنبال کنندگان"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_partner_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_partner_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_partner_ids
msgid "Followers (Partners)"
msgstr "پیروان (شرکاء)"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__activity_type_icon
#: model:ir.model.fields,help:hr.field_hr_employee__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "آیکون فونت عالی به عبارتی fa-tasks"

#. module: hr
#. odoo-python
#: code:addons/hr/models/discuss_channel.py:0
msgid ""
"For %(channels)s, channel_type should be 'channel' to have the department "
"auto-subscription."
msgstr ""
"برای %(channels)s، نوع کانال باید 'کانال' باشد تا اشتراک‌پذیری خودکار بخش را"
" داشته باشید."

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__freelance
msgid "Freelancer"
msgstr "فریلنسر"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Fruit, coffee and <br>snacks provided."
msgstr "میوه، قهوه و <br>تنقلات تأمین می‌شود."

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_full_time
msgid "Full-Time"
msgstr "تمام وقت"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Future Activities"
msgstr "فعالیت های آینده"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__gender
#: model:ir.model.fields,field_description:hr.field_res_users__gender
msgid "Gender"
msgstr "جنسیت"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Generate"
msgstr "ایجاد"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "Give more details about the reason of archiving the employee."
msgstr "دلیل بایگانی کردن کارمند را با جزئیات بیشتری توضیح دهید."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Google Adwords experience"
msgstr "تجربه تبلیغات گوگل"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__graduate
msgid "Graduate"
msgstr "فارغ التحصیل"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Great team of smart people, in a friendly and open culture"
msgstr "تیم بزرگی از افراد باهوش، در فرهنگی دوستانه و باز"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Group By"
msgstr "گروه‌بندی برمبنای"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_discuss_channel__subscription_department_ids
msgid "HR Departments"
msgstr "دپارتمان‌های منابع انسانی"

#. module: hr
#: model:ir.actions.server,name:hr.ir_cron_data_check_work_permit_validity_ir_actions_server
msgid "HR Employee: check work permit validity"
msgstr "بررسی اعتبار مجوز کار: کارمند منابع انسانی"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/hr_presence_status/hr_presence_status.js:0
msgid "HR Presence Status"
msgstr "وضعیت حضور منابع انسانی"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "HR Settings"
msgstr "تنظیمات منابع انسانی"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_manager_department_report__has_department_manager_access
msgid "Has Department Manager Access"
msgstr "آیا دسترسی مدیر بخش را دارد"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__has_message
#: model:ir.model.fields,field_description:hr.field_hr_employee__has_message
#: model:ir.model.fields,field_description:hr.field_hr_job__has_message
msgid "Has Message"
msgstr "آیا دارای پیام است"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Highly creative and autonomous"
msgstr "بسیار خلاق و مستقل"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__work_location_type__home
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__work_location_type__home
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__work_location_type__home
#: model:ir.model.fields.selection,name:hr.selection__hr_work_location__location_type__home
msgid "Home"
msgstr "خانه"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__distance_home_work
#: model:ir.model.fields,field_description:hr.field_res_users__distance_home_work
msgid "Home-Work Distance"
msgstr "کار در خانه با فاصله"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__km_home_work
#: model:ir.model.fields,field_description:hr.field_res_users__km_home_work
msgid "Home-Work Distance in Km"
msgstr "فاصله خانه تا محل کار بر حسب کیلومتر"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__distance_home_work_unit
#: model:ir.model.fields,field_description:hr.field_res_users__distance_home_work_unit
msgid "Home-Work Distance unit"
msgstr "واحد فاصله خانه تا محل کار"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__hr_icon_display
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__hr_icon_display
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__hr_icon_display
#: model:ir.model.fields,field_description:hr.field_resource_resource__hr_icon_display
msgid "Hr Icon Display"
msgstr "نمایش آیکون منابع انسانی"

#. module: hr
#: model:ir.model,name:hr.model_hr_manager_department_report
msgid "Hr Manager Department Report"
msgstr "گزارش بخش مدیر منابع انسانی"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__hr_presence_state
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__hr_presence_state
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__hr_presence_state
#: model:ir.model.fields,field_description:hr.field_res_users__hr_presence_state
msgid "Hr Presence State"
msgstr "وضعیت حضور منابع انسانی"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_hr_main
msgid "Human Resources"
msgstr "منابع انسانی"

#. module: hr
#: model:hr.job,name:hr.job_hrm
msgid "Human Resources Manager"
msgstr "مدیریت منابع انسانی"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__id
#: model:ir.model.fields,field_description:hr.field_hr_department__id
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__id
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__id
#: model:ir.model.fields,field_description:hr.field_hr_employee__id
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__id
#: model:ir.model.fields,field_description:hr.field_hr_job__id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__id
msgid "ID"
msgstr "شناسه"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__id_card
msgid "ID Card Copy"
msgstr "کپی کارت شناسایی"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__barcode
#: model:ir.model.fields,help:hr.field_res_users__barcode
msgid "ID used for employee identification."
msgstr "شناسه مورد استفاده برای شناسایی کارمند."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__im_status
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__im_status
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__im_status
msgid "IM Status"
msgstr "وضعیت IM"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_exception_icon
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_exception_icon
msgid "Icon"
msgstr "شمایل"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__activity_exception_icon
#: model:ir.model.fields,help:hr.field_hr_employee__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "آیکون برای نشان دادن یک فعالیت استثنا."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__identification_id
#: model:ir.model.fields,field_description:hr.field_res_users__identification_id
msgid "Identification No"
msgstr "شماره شناسایی"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_needaction
#: model:ir.model.fields,help:hr.field_hr_employee__message_needaction
#: model:ir.model.fields,help:hr.field_hr_job__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"اگر این گزینه را انتخاب کنید، پیام‌های جدید به توجه شما نیاز خواهند داشت."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_has_error
#: model:ir.model.fields,help:hr.field_hr_department__message_has_sms_error
#: model:ir.model.fields,help:hr.field_hr_employee__message_has_error
#: model:ir.model.fields,help:hr.field_hr_employee__message_has_sms_error
#: model:ir.model.fields,help:hr.field_hr_job__message_has_error
#: model:ir.model.fields,help:hr.field_hr_job__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "اگر علامت زده شود، برخی از پیام ها دارای خطای تحویل هستند."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"اگر فیلد فعال را روی خیر قرار دهید، رکورد منبع را بدون اینکه حذف کنید مخفی "
"خواهید کرد."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__private_car_plate
msgid "If you have more than one car, just separate the plates by a space."
msgstr "اگر بیش از یک خودرو دارید، فقط پلاک‌ها را با یک فاصله از هم جدا کنید."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_1920
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_1920
msgid "Image"
msgstr "تصویر"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_1024
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_1024
msgid "Image 1024"
msgstr "تصویر 1024"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_128
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_128
msgid "Image 128"
msgstr "تصویر 128"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_256
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_256
msgid "Image 256"
msgstr "تصویر 256"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_512
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_512
msgid "Image 512"
msgstr "تصویر 512"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "Import Template for Employees"
msgstr "ورود قالب برای کارمندان"

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_interim
msgid "Interim"
msgstr "موقت"

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_part_time
msgid "Intern"
msgstr "کارآموز"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__is_flexible
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__is_flexible
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__is_flexible
msgid "Is Flexible"
msgstr "آیا منعطف است"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_is_follower
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_is_follower
#: model:ir.model.fields,field_description:hr.field_hr_job__message_is_follower
msgid "Is Follower"
msgstr "آیا دنبال می کند"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__is_fully_flexible
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__is_fully_flexible
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__is_fully_flexible
msgid "Is Fully Flexible"
msgstr "آیا کاملا منعطف است"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__is_manager
msgid "Is Manager"
msgstr "آیا مدیر است"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__is_system
msgid "Is System"
msgstr "سیستم است"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_tree
msgid "Job"
msgstr "شغل"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__description
msgid "Job Description"
msgstr "شرح شغل"

#. module: hr
#: model:ir.model,name:hr.model_hr_job
#: model:ir.model.fields,field_description:hr.field_hr_employee__job_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__job_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__job_id
#: model:ir.model.fields,field_description:hr.field_hr_job__name
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Job Position"
msgstr "موقعیت شغلی"

#. module: hr
#: model:ir.actions.act_window,name:hr.action_hr_job
#: model:ir.ui.menu,name:hr.menu_view_hr_job
msgid "Job Positions"
msgstr "موقعیت های شغلی"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "Job Summary"
msgstr "خلاصه شغل"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__job_title
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__job_title
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__job_title
#: model:ir.model.fields,field_description:hr.field_res_users__job_title
#: model:ir.model.fields,field_description:hr.field_resource_resource__job_title
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Job Title"
msgstr "عنوان شغل"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__jobs_ids
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Jobs"
msgstr "شغل"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__lang
msgid "Lang"
msgstr "زبان"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Language"
msgstr "زبان"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__last_activity
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__last_activity
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__last_activity
#: model:ir.model.fields,field_description:hr.field_res_users__last_activity
msgid "Last Activity"
msgstr "آخرین فعالیت"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__last_activity_time
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__last_activity_time
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__last_activity_time
#: model:ir.model.fields,field_description:hr.field_res_users__last_activity_time
msgid "Last Activity Time"
msgstr "زمان آخرین فعالیت"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_department__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_job__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_work_location__write_uid
msgid "Last Updated by"
msgstr "آخرین بروز رسانی توسط"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__write_date
#: model:ir.model.fields,field_description:hr.field_hr_department__write_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__write_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee__write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__write_date
#: model:ir.model.fields,field_description:hr.field_hr_job__write_date
#: model:ir.model.fields,field_description:hr.field_hr_work_location__write_date
msgid "Last Updated on"
msgstr "آخرین بروز رسانی در"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Late Activities"
msgstr "فعالیتهای اخیر"

#. module: hr
#: model:ir.actions.act_window,name:hr.plan_wizard_action
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_tree
msgid "Launch Plan"
msgstr "راه‌اندازی برنامه"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Lead the entire sales cycle"
msgstr "رهبری کل چرخه فروش"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "Legal Cohabitant"
msgstr "هم زیستی حقوقی"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.action_hr_job
msgid "Let's create a job position."
msgstr "بیایید یک موقعیت شغلی ایجاد کنیم."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Location"
msgstr "مکان"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_work_location__location_number
msgid "Location Number"
msgstr "شماره مکان"

#. module: hr
#: model:hr.department,name:hr.dep_rd_ltp
msgid "Long Term Projects"
msgstr "پروژه‌های بلند مدت"

#. module: hr
#: model_terms:hr.job,description:hr.job_hrm
msgid ""
"Lorem Ipsum is simply dummy text of the printing and typesetting industry. "
"Lorem Ipsum has been the industry's standard dummy text ever since the "
"1500s, when an unknown printer took a galley of type and scrambled it to "
"make a type specimen book. It has survived not only five centuries, but also"
" the leap into electronic typesetting, remaining essentially unchanged. It "
"was popularised in the 1960s with the release of Letraset sheets containing "
"Lorem Ipsum passages, and more recently with desktop publishing software "
"like Aldus PageMaker including versions of Lorem Ipsum."
msgstr ""
"لورم ایپسوم به سادگی متنی ساختگی از صنعت چاپ و حروف‌چینی است. لورم ایپسوم از"
" قرن ۱۵۰۰ به عنوان متن استاندارد این صنعت مورد استفاده بوده است، زمانی که یک"
" چاپگر ناشناس مجموعه‌ای از نوع را گرفت و آن را برای ساختن یک کتاب نمونه "
"تایپی مرتب کرد. این متن نه تنها پنج قرن، بلکه جهش به حروف‌چینی الکترونیکی را"
" نیز تاب آورده است و در اصل بدون تغییر باقی مانده است. این متن در دهه ۱۹۶۰ "
"با انتشار ورق‌های لترست حاوی بخش‌های لورم ایپسوم محبوبیت یافت و اخیراً با "
"نرم‌افزارهای نشر رومیزی مانند آلدوس پیج‌میکر شامل نسخه‌های لورم ایپسوم، به "
"صورت گسترده مورد استفاده قرار می‌گیرد."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_main_attachment_id
msgid "Main Attachment"
msgstr "پیوست اصلی"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__gender__male
msgid "Male"
msgstr "مرد"

#. module: hr
#: model:hr.department,name:hr.dep_management
msgid "Management"
msgstr "مدیریت"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__manager_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__parent_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__parent_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__parent_id
#: model:ir.model.fields,field_description:hr.field_res_users__employee_parent_id
#: model:ir.model.fields.selection,name:hr.selection__mail_activity_plan_template__responsible_type__manager
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Manager"
msgstr "مدیر"

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
msgid "Manager of employee %s is not set."
msgstr "مدیر کارمند %s تنظیم نشده است."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__marital
#: model:ir.model.fields,field_description:hr.field_res_users__marital
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Marital Status"
msgstr "وضعیت تاهل"

#. module: hr
#: model:hr.job,name:hr.job_marketing
msgid "Marketing and Community Manager"
msgstr "مدیریت ارتباطات و بازاریابی"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "Married"
msgstr "متاهل"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__master
msgid "Master"
msgstr "کارشناسی ارشد"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__master_department_id
msgid "Master Department"
msgstr "دپارتمان اصلی"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Master demos of our software"
msgstr "نسخه‌های نمایشی اصلی نرم‌افزار ما"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__member_of_department
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__member_of_department
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__member_of_department
msgid "Member of department"
msgstr "عضو بخش"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__member_ids
msgid "Members"
msgstr "اعضا"

#. module: hr
#: model:ir.model,name:hr.model_ir_ui_menu
msgid "Menu"
msgstr "منو"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_has_error
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_has_error
#: model:ir.model.fields,field_description:hr.field_hr_job__message_has_error
msgid "Message Delivery error"
msgstr "خطای تحویل پیام"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_ids
msgid "Messages"
msgstr "پیام ها"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Must Have"
msgstr "ضروری"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr.field_hr_employee__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "موعد نهای فعالیت من"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "My Department"
msgstr "دپارتمان من"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/user_menu/my_profile.js:0
msgid "My Profile"
msgstr "پروفایل من"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "My Team"
msgstr "تیم من"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__name
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__name
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__name
msgid "Name"
msgstr "نام"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__country_id
msgid "Nationality (Country)"
msgstr "ملیت (کشور)"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Negotiate and contract"
msgstr "مذاکره و قرارداد"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_view_graph
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_view_pivot
msgid "New Employees Over Time"
msgstr "کارمندان جدید در طول زمان"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__newly_hired
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__newly_hired
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__newly_hired
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Newly Hired"
msgstr "استخدام جدید"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "رویداد تقویم فعالیت بعدی"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_date_deadline
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "موعد فعالیت بعدی"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_summary
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_summary
msgid "Next Activity Summary"
msgstr "خلاصه فعالیت بعدی"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_type_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_type_id
msgid "Next Activity Type"
msgstr "نوع فعالیت بعدی"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Nice to have"
msgstr "داشتن‌ آن خوب است"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/views/hr_graph_controller.xml:0
#: code:addons/hr/static/src/views/hr_pivot_controller.xml:0
msgid "No Data"
msgstr "بدون داده"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.open_view_categ_form
msgid "No Tags found ! Let's create one"
msgstr "هیچ برچسبی یافت نشد! بیایید یکی بسازیم"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "No dumb managers, no stupid tools to use, no rigid working hours"
msgstr ""
"بدون مدیران بی‌فکر، بدون ابزارهای احمقانه برای استفاده، بدون ساعات کاری "
"سختگیرانه"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid ""
"No waste of time in enterprise processes, real responsibilities and autonomy"
msgstr "بدون اتلاف وقت در فرآیندهای سازمانی، مسئولیت‌های واقعی و استقلال"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Not available"
msgstr "در دسترس نیست"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__note
msgid "Note"
msgstr "یادداشت"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__notes
msgid "Notes"
msgstr "یادداشت‌ها"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_needaction_counter
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_needaction_counter
#: model:ir.model.fields,field_description:hr.field_hr_job__message_needaction_counter
msgid "Number of Actions"
msgstr "تعداد اقدامات"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__children
#: model:ir.model.fields,field_description:hr.field_res_users__children
msgid "Number of Dependent Children"
msgstr "تعداد فرزندان تحت تکفل"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_tree
msgid "Number of Employees"
msgstr "تعداد کارکنان"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__no_of_employee
msgid "Number of employees currently occupying this job position."
msgstr "تعداد کارمندانی که در حال حاضر این موقعیت شغلی را اشغال کرده‌اند"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_has_error_counter
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_has_error_counter
#: model:ir.model.fields,field_description:hr.field_hr_job__message_has_error_counter
msgid "Number of errors"
msgstr "تعداد خطاها"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_needaction_counter
#: model:ir.model.fields,help:hr.field_hr_employee__message_needaction_counter
#: model:ir.model.fields,help:hr.field_hr_job__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "تعداد پیام هایی که نیاز به اقدام دارند"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_has_error_counter
#: model:ir.model.fields,help:hr.field_hr_employee__message_has_error_counter
#: model:ir.model.fields,help:hr.field_hr_job__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "تعداد پیامهای با خطای تحویل"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__no_of_recruitment
msgid "Number of new employees you expect to recruit."
msgstr "تعداد کارمندان جدیدی که می‌خواهید استخدام کنید"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__work_location_type__office
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__work_location_type__office
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__work_location_type__office
#: model:ir.model.fields.selection,name:hr.selection__hr_work_location__location_type__office
msgid "Office"
msgstr "دفتر کار"

#. module: hr
#: model:res.groups,name:hr.group_hr_user
msgid "Officer: Manage all employees"
msgstr "افسر: مدیریت همه کارکنان"

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
msgid ""
"Oops! It seems there is a problem with your team structure."
"                        We found a circular reporting loop and no one in "
"that loop is linked to a user.                        Please double-check "
"that everyone reports to the correct manager."
msgstr ""
"اوه! به نظر می رسد ساختار تیم شما مشکل دارد.                        ما یک "
"حلقه گزارش دایره ای پیدا کردیم و هیچ کس در آن حلقه به یک کاربر مرتبط نیست."
"                       لطفاً دوباره بررسی کنید که همه به مدیر صحیح گزارش "
"دهند."

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/core/web/thread_actions.js:0
msgid "Open Profile"
msgstr "باز کردن پروفایل"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee_base.py:0
msgid "Operation not supported"
msgstr "عملیات پشتیبانی نمی شود"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__other
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__gender__other
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__work_location_type__other
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__work_location_type__other
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__work_location_type__other
#: model:ir.model.fields.selection,name:hr.selection__hr_work_location__location_type__other
msgid "Other"
msgstr "دیگر"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Our Product"
msgstr "محصول ما"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "Out of Working Hours"
msgstr "خارج از ساعت کاری"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_out_of_working_hour
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_presence_state__out_of_working_hour
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_out_of_working_hour
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_presence_state__out_of_working_hour
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_out_of_working_hour
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_presence_state__out_of_working_hour
msgid "Out of Working hours"
msgstr "خارج از ساعت کاری"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__pin
#: model:ir.model.fields,field_description:hr.field_res_users__pin
msgid "PIN"
msgstr "پین"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "PIN Code"
msgstr "پین کد"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__pin
#: model:ir.model.fields,help:hr.field_res_users__pin
msgid ""
"PIN used to Check In/Out in the Kiosk Mode of the Attendance application (if"
" enabled in Configuration) and to change the cashier in the Point of Sale "
"application."
msgstr ""
"پین مورد استفاده برای ورود/خروج در حالت کیوسک برنامه حضور و غیاب (در صورت "
"فعال بودن در تنظیمات) و برای تغییر صندوقدار در برنامه نقطه فروش."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__parent_id
msgid "Parent Department"
msgstr "دپارتمان والد"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__parent_path
msgid "Parent Path"
msgstr "مسیر مادر"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__user_partner_id
#: model:ir.model.fields,help:hr.field_hr_employee_public__user_partner_id
msgid "Partner-related data of the user"
msgstr "داده مربوط به همکار کاربر"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Passion for software products"
msgstr "اشتیاق به محصولات نرم‌افزاری"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__passport_id
#: model:ir.model.fields,field_description:hr.field_res_users__passport_id
msgid "Passport No"
msgstr "شماره گذرنامه"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Perfect written English"
msgstr "مسلط به زبان انگلیسی نوشتاری"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Perks"
msgstr "مزایا"

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_permanent
msgid "Permanent"
msgstr "دائم"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Personal Evolution"
msgstr "تکامل شخصی"

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_users.py:0
msgid "Personal information update."
msgstr "به‌روزرسانی اطلاعات شخصی."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__phone
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Phone"
msgstr "تلفن"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__place_of_birth
#: model:ir.model.fields,field_description:hr.field_res_users__place_of_birth
msgid "Place of Birth"
msgstr "محل تولد"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__plan_ids
msgid "Plan"
msgstr "طرح"

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan.py:0
msgid ""
"Plan %(plan_names)s cannot use a department as it is used only for some HR "
"plans."
msgstr ""
"برنامه %(plan_names)s نمی تواند از یک بخش استفاده کند زیرا فقط برای برخی از "
"برنامه های منابع انسانی استفاده می شود.."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_mail_activity_schedule__plan_department_filterable
msgid "Plan Department Filterable"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan.py:0
msgid ""
"Plan activities %(template_names)s cannot use coach, manager or employee "
"responsible as it is used only for employee plans."
msgstr ""
"برنامه‌های فعالیتی %(template_names)s نمی‌توانند از مربی، مدیر یا کارمند "
"مسئول استفاده کنند زیرا تنها برای برنامه‌های کارمندی مورد استفاده قرار "
"می‌گیرند."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
msgid "Plans"
msgstr "برنامه‌ها"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__plans_count
msgid "Plans Count"
msgstr "تعداد برنامه‌ها"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Play any sport with colleagues, <br>the bill is covered."
msgstr "با همکاران هر ورزشی انجام دهید، <br>هزینه آن پوشش داده می‌شود."

#. module: hr
#: model:ir.model.fields,help:hr.field_crm_team__alias_contact
#: model:ir.model.fields,help:hr.field_mail_alias__alias_contact
#: model:ir.model.fields,help:hr.field_mail_group__alias_contact
#: model:ir.model.fields,help:hr.field_maintenance_equipment_category__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"خط مشی ارسال پیام در سند با استفاده از mailgateway.\n"
"- همه: همه می توانند پست کنند\n"
"- شرکا: فقط شرکای تأیید شده\n"
"- دنبال کنندگان: فقط دنبال کنندگان سند مرتبط یا اعضای کانال های زیر\n"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Presence Condition"
msgstr "شرایط حضور"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Presence Display"
msgstr "نمایش حضور"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Presence of employees"
msgstr "حضور کارکنان"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Presence reporting screen, email and IP address control."
msgstr "صفحه نمایش گزارش حضور، ایمیل و  کنترل آدرس ای‌پی."

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_presence_state__present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_presence_state__present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_presence_state__present
msgid "Present"
msgstr "حاضر"

#. module: hr
#: model:ir.actions.report,name:hr.hr_employee_print_badge
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Print Badge"
msgstr "چاپ نشان"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Private Address"
msgstr "آدرس خصوصی"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_car_plate
msgid "Private Car Plate"
msgstr "پلاک ماشین شخصی"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_city
#: model:ir.model.fields,field_description:hr.field_res_users__private_city
msgid "Private City"
msgstr "شهر خصوصی"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Private Contact"
msgstr "تماس شخصی"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_country_id
#: model:ir.model.fields,field_description:hr.field_res_users__private_country_id
msgid "Private Country"
msgstr "کشور خصوصی"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_email
#: model:ir.model.fields,field_description:hr.field_res_users__private_email
msgid "Private Email"
msgstr "ایمیل شخصی"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Private Information"
msgstr "اطلاعات خصوصی"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_phone
#: model:ir.model.fields,field_description:hr.field_res_users__private_phone
msgid "Private Phone"
msgstr "تلفن شخصی"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_state_id
#: model:ir.model.fields,field_description:hr.field_res_users__private_state_id
msgid "Private State"
msgstr "وضعیت خصوصی"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_street
#: model:ir.model.fields,field_description:hr.field_res_users__private_street
msgid "Private Street"
msgstr "خیابان خصوصی"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_street2
#: model:ir.model.fields,field_description:hr.field_res_users__private_street2
msgid "Private Street2"
msgstr "خیابان خصوصی 2"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_zip
#: model:ir.model.fields,field_description:hr.field_res_users__private_zip
msgid "Private Zip"
msgstr "زیپ خصوصی"

#. module: hr
#: model:hr.department,name:hr.dep_ps
msgid "Professional Services"
msgstr "خدمات حرفه‌ای"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__employee_properties
msgid "Properties"
msgstr "مشخصات"

#. module: hr
#: model:ir.model,name:hr.model_hr_employee_public
msgid "Public Employee"
msgstr "کارمند عمومی"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Qualify the customer needs"
msgstr "نیازهای مشتری را ارزیابی کنید"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_employee_public_action
#: model_terms:ir.actions.act_window,help:hr.open_view_employee_list_my
msgid ""
"Quickly find all the information you need for your employees such as contact"
" data, job position, availability, etc."
msgstr ""
"به سرعت تمام اطلاعات مورد نیاز کارمندان خود را پیدا کنید، مانند اطلاعات "
"تماس، موقعیت شغلی، در دسترس بودن و غیره."

#. module: hr
#: model:hr.department,name:hr.dep_rd_be
msgid "R&D USA"
msgstr "آر اند دی  آمریکا"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__rating_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__rating_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__rating_ids
msgid "Ratings"
msgstr "رتبه‌ها"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.action_hr_job
msgid "Ready to recruit more efficiently?"
msgstr "برای استخدام کارآمدتر آماده اید؟"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Real responsibilities and challenges in a fast evolving company"
msgstr "مسئولیت‌ها و چالش‌های واقعی در یک شرکت با سرعت رشد بالا"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__name
msgid "Reason"
msgstr "علت"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__reason_code
msgid "Reason Code"
msgstr "کد دلیل"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_config_recruitment
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "Recruitment"
msgstr "استخدام"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
#: model:ir.actions.act_window,name:hr.hr_departure_wizard_action
msgid "Register Departure"
msgstr "ثبت خروج"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form_smartbutton_inherited
msgid "Related Contacts"
msgstr "مخاطبین مرتبط"

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_partner.py:0 code:addons/hr/models/res_users.py:0
msgid "Related Employees"
msgstr "کارمندان مرتبط"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__related_partners_count
msgid "Related Partners Count"
msgstr "تعداد شرکای مرتبط"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Related User"
msgstr "کاربر مروبطه"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_ids
msgid "Related employee"
msgstr "کارمند‌های مرتبط"

#. module: hr
#: model:ir.model.fields,help:hr.field_res_partner__employee_ids
msgid "Related employees based on their private address"
msgstr "مربوط به کارمندان بر اساس آدرس خصوصی آنان"

#. module: hr
#: model:ir.model.fields,help:hr.field_resource_resource__user_id
msgid "Related user name for the resource to manage its access."
msgstr "نام کاربری مرتبط برای منبع برای مدیریت حق دسترسی آن."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_homeworking
msgid "Remote Work"
msgstr "کار از راه دور"

#. module: hr
#: model:ir.ui.menu,name:hr.hr_menu_hr_reports
#: model:ir.ui.menu,name:hr.menu_hr_reporting_timesheet
msgid "Reporting"
msgstr "گزارش"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__requirements
msgid "Requirements"
msgstr "نیازها"

#. module: hr
#: model:hr.department,name:hr.dep_rd
msgid "Research & Development"
msgstr "تحقیق و توسعه"

#. module: hr
#: model:hr.departure.reason,name:hr.departure_resigned
msgid "Resigned"
msgstr "استعفا داده"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__resource_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__resource_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__resource_id
msgid "Resource"
msgstr "منبع"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__resource_calendar_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__resource_calendar_id
msgid "Resource Calendar"
msgstr "تقویم منبع"

#. module: hr
#: model:ir.model,name:hr.model_resource_calendar
msgid "Resource Working Time"
msgstr "زمان کار منابع"

#. module: hr
#: model:ir.model,name:hr.model_resource_resource
msgid "Resources"
msgstr "منابع"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Responsibilities"
msgstr "مسئولیت‌ها"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_user_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_user_id
msgid "Responsible User"
msgstr "کاربر مسئول"

#. module: hr
#: model:hr.departure.reason,name:hr.departure_retired
msgid "Retired"
msgstr "بازنشست شده"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__sinid
msgid "SIN No"
msgstr "شماره بیمه اجتماعی"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_has_sms_error
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_has_sms_error
#: model:ir.model.fields,field_description:hr.field_hr_job__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطای تحویل پیامک"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__ssnid
#: model:ir.model.fields,field_description:hr.field_res_users__ssnid
msgid "SSN No"
msgstr "شماره تأمین اجتماعی"

#. module: hr
#: model:hr.department,name:hr.dep_sales
msgid "Sales"
msgstr "فروش"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_users_simple_form
msgid "Save"
msgstr "ذخیره"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Schedule"
msgstr "زمان‌بندی"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__study_school
#: model:ir.model.fields,field_description:hr.field_res_users__study_school
msgid "School"
msgstr "مدرسه"

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_seasonal
msgid "Seasonal"
msgstr "فصلی"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__coach_id
#: model:ir.model.fields,help:hr.field_hr_employee_base__coach_id
#: model:ir.model.fields,help:hr.field_hr_employee_public__coach_id
#: model:ir.model.fields,help:hr.field_res_users__coach_id
msgid ""
"Select the \"Employee\" who is the coach of this employee.\n"
"The \"Coach\" has no specific rights or responsibilities by default."
msgstr ""
"فردی را که مربی این کارمند است انتخاب کنید.\n"
"مربی به طور پیش‌فرض حقوق یا مسئولیت خاصی ندارد."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__sequence
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__sequence
#: model:ir.model.fields,field_description:hr.field_hr_job__sequence
msgid "Sequence"
msgstr "دنباله"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Set default company schedule to manage your employees working time"
msgstr "تنظیم برنامه شرکت پیش‌فرض برای مدیریت زمان کارکرد کارکنانتان"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_config_settings_action
#: model:ir.ui.menu,name:hr.hr_menu_configuration
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Settings"
msgstr "تنظیمات"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__share
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__share
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__share
msgid "Share User"
msgstr "اشتراک کاربر"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__show_hr_icon_display
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__show_hr_icon_display
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__show_hr_icon_display
#: model:ir.model.fields,field_description:hr.field_resource_resource__show_hr_icon_display
msgid "Show Hr Icon Display"
msgstr "نمایش آیکون منابع انسانی"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/department_chart/department_chart.xml:0
msgid "Show employees"
msgstr "نمایش کارکنان"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "Single"
msgstr "مجرد"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_skills
msgid "Skills Management"
msgstr "مدیریت مهارت‌ها"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__sinid
msgid "Social Insurance Number"
msgstr "شماره بیمه"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__ssnid
#: model:ir.model.fields,help:hr.field_res_users__ssnid
msgid "Social Security Number"
msgstr "شماره ملی"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee_base.py:0
msgid "Some employee already have a work contact"
msgstr "برخی از کارمندان قبلاً یک تماس کاری دارند"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Sport Activity"
msgstr "فعالیت ورزشی"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__spouse_birthdate
#: model:ir.model.fields,field_description:hr.field_res_users__spouse_birthdate
msgid "Spouse Birthdate"
msgstr "تاریخ تولد همسر"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__spouse_complete_name
#: model:ir.model.fields,field_description:hr.field_res_users__spouse_complete_name
msgid "Spouse Complete Name"
msgstr "نام کامل همسر"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Start Date"
msgstr "تاریخ آغاز"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "State"
msgstr "استان"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Status"
msgstr "وضعیت"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__activity_state
#: model:ir.model.fields,help:hr.field_hr_employee__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"وضعیت بر اساس فعالیت ها\n"
"سررسید: تاریخ سررسید گذشته است\n"
"امروز: تاریخ فعالیت امروز است\n"
"برنامه ریزی شده: فعالیت های آینده."

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_statutaire
msgid "Statutory"
msgstr "قانونی"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Street 2..."
msgstr "آدرس 2 ..."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Street..."
msgstr "آدرس ..."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Strong analytical skills"
msgstr "مهارت‌های تحلیلی قوی"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_department_kanban_action
#: model_terms:ir.actions.act_window,help:hr.hr_department_tree_action
msgid ""
"Structure Employees per department and have an overview of e.g.\n"
"                    expenses, timesheets, time off, recruitment, etc."
msgstr ""
"کارمندان را در هر بخش ساختار دهید و یک دید کلی از مثلاً داشته باشید.\n"
"                    هزینه ها، جدول زمانی، مرخصی، استخدام و غیره"

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_student
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__student
msgid "Student"
msgstr "دانشجو"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__name
msgid "Tag Name"
msgstr "نام تگ"

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_employee_category_name_uniq
msgid "Tag name already exists!"
msgstr "نام برچسب از قبل وجود دارد!"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__category_ids
#: model:ir.ui.menu,name:hr.menu_view_employee_category_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Tags"
msgstr "برچسب‌ها"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__no_of_recruitment
msgid "Target"
msgstr "هدف"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Technical Expertise"
msgstr "تخصص فنی"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__create_employee_id
msgid "Technical field, bind user to this employee on create"
msgstr "فیلد فنی، کاربر را هنگام ایجاد به این کارمند متصل کنید"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__create_employee
msgid "Technical field, whether to create an employee"
msgstr "فیلد فنی، آیا برای ایجاد یک کارمند"

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_temporary
msgid "Temporary"
msgstr "موقت"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid ""
"The Badge ID must be alphanumeric without any accents and no longer than 18 "
"characters."
msgstr ""

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_employee_barcode_uniq
msgid ""
"The Badge ID must be unique, this one is already assigned to another "
"employee."
msgstr ""
"شناسه نشان باید منحصر به فرد باشد، این یکی قبلا به کارمند دیگری اختصاص داده "
"شده است."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__company_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"کد کشور ISO در دو کاراکتر.\n"
"می توانید از این قسمت برای جستجوی سریع استفاده کنید."

#. module: hr
#: model_terms:hr.job,description:hr.job_marketing
msgid ""
"The Marketing Manager defines the mid- to long-term marketing strategy for his covered market segments in the World.\n"
"              He develops and monitors the annual budget in collaboration with Sales.\n"
"              He defines the products and customers portfolio according to the marketing plan.\n"
"              This mission requires strong collaboration with Technical Service and Sales."
msgstr ""
"بازاریابی مدیر استراتژی بازاریابی میان‌مدت و بلندمدت را برای بخش‌های بازار تحت پوشش خود در جهان تعریف می‌کند.\n"
"او بودجه سالانه را با همکاری فروش توسعه و نظارت می‌کند.\n"
"او سبد محصولات و مشتریان را بر اساس طرح بازاریابی تعریف می‌کند.\n"
"این ماموریت نیازمند همکاری قوی با خدمات فنی و فروش است."

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "The PIN must be a sequence of digits."
msgstr "پین‌کد باید دنباله‌ای از ارقام باشد."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "The default working hours are set in configuration."
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
msgid "The employee %s should be linked to a user."
msgstr "کارمند %s باید به یک کاربر متصل باشد."

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_job_no_of_recruitment_positive
msgid "The expected number of new employees must be positive."
msgstr "تعداد پیش‌بینی‌شده کارکنان جدید باید مثبت باشد."

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid ""
"The fields “%s”, which you are trying to read, are not available for "
"employee public profiles."
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_users.py:0
msgid "The following fields were modified by %s"
msgstr "فیلدهای زیر توسط %s تغییر داده شدند"

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
msgid "The manager of %s should be linked to a user."
msgstr "مدیر %s باید به یک کاربر مرتبط شود."

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_job_name_company_uniq
msgid "The name of the job position must be unique per department in company!"
msgstr "نام موقعیت شغلی باید در هر بخش در شرکت یکتا باشد!"

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
msgid "The user of %s's coach is not set."
msgstr "کاربر مربی %s تنظیم نشده است."

#. module: hr
#: model:res.groups,comment:hr.group_hr_user
msgid "The user will be able to approve document created by employees."
msgstr "کاربر قادر خواهد بود مدرک ایجاد شده توسط کارکنان را تصویب کند."

#. module: hr
#: model:res.groups,comment:hr.group_hr_manager
msgid ""
"The user will have access to the human resources configuration as well as "
"statistic reports."
msgstr ""
"کاربر به پیکربندی منابع انسانی و همچنین گزارش های آماری دسترسی خواهد داشت."

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "The work permit of %(employee)s expires at %(date)s."
msgstr "مجوز کار %(employee)s در تاریخ %(date)s منقضی می‌شود."

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_thesis
msgid "Thesis"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "This employee already has an user."
msgstr "این کارمند قبلاً یک کاربر دارد."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__tz
#: model:ir.model.fields,help:hr.field_hr_employee_base__tz
#: model:ir.model.fields,help:hr.field_hr_employee_public__tz
msgid ""
"This field is used in order to define in which timezone the employee will "
"work."
msgstr ""

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/views/hr_graph_controller.xml:0
#: code:addons/hr/static/src/views/hr_pivot_controller.xml:0
msgid ""
"This report gives you an overview of your employees based on the measures of"
" your choice."
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "This setting block is utilized to manage the frontend design."
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
msgid "Those responsible types are limited to Employee plans."
msgstr "تایپ‌های مسئول محدود به طرح‌های کارمندی هستند."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__tz
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__tz
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__tz
msgid "Timezone"
msgstr "منطقه‌ زمانی"

#. module: hr
#: model:digest.tip,name:hr.digest_tip_hr_0
#: model_terms:digest.tip,tip_description:hr.digest_tip_hr_0
msgid "Tip: Where's Bryan?"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid ""
"To avoid multi company issues (losing the access to your previous contracts,"
" leaves, ...), you should create another employee in the new company "
"instead."
msgstr ""
"برای جلوگیری از مشکلات چند شرکت (از دست دادن دسترسی به قراردادهای قبلی، "
"مرخصی‌ها و ...)، بهتر است در شرکت جدید یک کارمند دیگر ایجاد کنید."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Today Activities"
msgstr "فعالیت های امروز"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__expected_employees
msgid "Total Forecasted Employees"
msgstr "کل کارمندان پیش بینی شده"

#. module: hr
#: model:hr.job,name:hr.job_trainee
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__trainee
msgid "Trainee"
msgstr "تحت تعلیم"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Trainings"
msgstr "آموزش‌ها"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__activity_exception_decoration
#: model:ir.model.fields,help:hr.field_hr_employee__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "نوع فعالیت استثنایی برای رکورد."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "Unarchive"
msgstr "برگشت از بایگانی"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_undetermined
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_undetermined
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_undetermined
msgid "Undetermined"
msgstr "نامعین"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Unread Messages"
msgstr "پیام های ناخوانده"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.open_view_categ_form
msgid "Use tags to categorize your Employees."
msgstr "از برچسب‌ها برای دسته‌بندی کارکنان خود استفاده کنید."

#. module: hr
#: model:ir.model,name:hr.model_res_users
#: model:ir.model.fields,field_description:hr.field_hr_employee__user_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__user_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__user_id
#: model:ir.model.fields,field_description:hr.field_resource_resource__user_id
msgid "User"
msgstr "کاربر"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__user_partner_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__user_partner_id
msgid "User's partner"
msgstr "همکار کاربر"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_job_view_kanban
msgid "Vacancies:"
msgstr "موقعیت‌های خالی:"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_ip_list
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_ip_list
msgid "Valid IP addresses"
msgstr "آدرس‌های IP معتبر"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Valid work permit for Belgium"
msgstr "مجوز کار معتبر برای بلژیک"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__visa_expire
#: model:ir.model.fields,field_description:hr.field_res_users__visa_expire
msgid "Visa Expiration Date"
msgstr "تاریخ انقضای ویزا"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__visa_no
#: model:ir.model.fields,field_description:hr.field_res_users__visa_no
msgid "Visa No"
msgstr "شماره ویزا"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "Warning"
msgstr "هشدار"

#. module: hr
#: model_terms:hr.job,description:hr.job_consultant
msgid ""
"We are currently looking for someone like that to join our Consultant team."
msgstr ""
"ما در حال حاضر به دنبال فردی مثل شما برای پیوستن به تیم مشاوران ما هستیم."

#. module: hr
#: model_terms:hr.job,description:hr.job_developer
msgid ""
"We are currently looking for someone like that to join our Web team.\n"
"                Someone who can snap out of coding and perform analysis or meet clients to explain the technical possibilities that can meet their needs."
msgstr ""
"ما در حال حاضر به دنبال فردی هستیم که به تیم وب ما بپیوندد.\n"
"کسی که بتواند از برنامه‌نویسی خارج شده و تحلیل‌هایی انجام دهد یا با مشتریان ملاقات کند تا امکانات فنی که می‌تواند نیازهای آنها را برآورده کند توضیح دهد."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__website_message_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__website_message_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__website_message_ids
msgid "Website Messages"
msgstr "پیام های وب سایت"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__website_message_ids
#: model:ir.model.fields,help:hr.field_hr_employee__website_message_ids
#: model:ir.model.fields,help:hr.field_hr_job__website_message_ids
msgid "Website communication history"
msgstr "تاریخچه ارتباط با وبسایت"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "What We Offer"
msgstr "آنچه ما ارائه می‌دهیم"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "What's great in the job?"
msgstr "چه چیزی در شغل عالی است؟"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__member_of_department
#: model:ir.model.fields,help:hr.field_hr_employee_base__member_of_department
#: model:ir.model.fields,help:hr.field_hr_employee_public__member_of_department
msgid ""
"Whether the employee is a member of the active user's department or one of "
"it's child department."
msgstr "آیا کارمند عضو بخش کاربر فعال یا یکی از بخش‌های زیرمجموعه آن است."

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "Widower"
msgstr "بیوه"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__address_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__address_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__address_id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__address_id
#: model:ir.model.fields,field_description:hr.field_res_users__address_id
msgid "Work Address"
msgstr "آدرس کاری"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_contact_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_contact_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_contact_id
#: model:ir.model.fields,field_description:hr.field_res_users__work_contact_id
msgid "Work Contact"
msgstr "تماس کاری"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_email
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_email
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_email
#: model:ir.model.fields,field_description:hr.field_res_users__work_email
#: model:ir.model.fields,field_description:hr.field_resource_resource__work_email
msgid "Work Email"
msgstr "پست الکترونیکی کاری"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Work Information"
msgstr "اطلاعات کاری"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/avatar_card/avatar_card_popover_patch.xml:0
#: model:ir.model,name:hr.model_hr_work_location
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_location_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_location_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_location_id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__name
#: model:ir.model.fields,field_description:hr.field_res_users__work_location_id
#: model_terms:ir.ui.view,arch_db:hr.hr_work_location_form_view
#: model_terms:ir.ui.view,arch_db:hr.hr_work_location_tree_view
msgid "Work Location"
msgstr "مکان کاری"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_location_name
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_location_name
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_location_name
#: model:ir.model.fields,field_description:hr.field_res_users__work_location_name
msgid "Work Location Name"
msgstr "نام محل کار"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_location_type
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_location_type
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_location_type
#: model:ir.model.fields,field_description:hr.field_res_users__work_location_type
msgid "Work Location Type"
msgstr "نوع محل کار"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_work_location_action
#: model:ir.ui.menu,name:hr.menu_hr_work_location_tree
msgid "Work Locations"
msgstr "مکان های کاری"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__mobile_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__mobile_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__mobile_phone
#: model:ir.model.fields,field_description:hr.field_res_users__mobile_phone
msgid "Work Mobile"
msgstr "تلفن همراه کاری"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Work Organization"
msgstr "سازمانی کاری"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__has_work_permit
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Work Permit"
msgstr "مجوز کار"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_permit_expiration_date
msgid "Work Permit Expiration Date"
msgstr "تاریخ انقضای مجوز کار"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__permit_no
#: model:ir.model.fields,field_description:hr.field_res_users__permit_no
msgid "Work Permit No"
msgstr "شماره مجوز کار"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_permit_scheduled_activity
msgid "Work Permit Scheduled Activity"
msgstr "فعالیت برنامه‌ریزی شده مجوز کار"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_phone
#: model:ir.model.fields,field_description:hr.field_res_users__work_phone
#: model:ir.model.fields,field_description:hr.field_resource_resource__work_phone
msgid "Work Phone"
msgstr "تلفن محل کار"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__worker
msgid "Worker"
msgstr "کارگر"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__resource_calendar_id
msgid "Working Hours"
msgstr "ساعت های کاری"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_resource_calendar_view
msgid "Working Schedules"
msgstr "برنامه‌های کاری"

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_users.py:0
msgid ""
"You are not allowed to create an employee because the user does not have "
"access rights for %s"
msgstr ""
"شما مجاز به ایجاد کارمند نیستید زیرا کاربر حق دسترسی به آن را نداردشما مجاز "
"به ایجاد کارمند نیستید زیرا کاربر  %s حق دسترسی به آن را ندارد"

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_users.py:0
msgid ""
"You are only allowed to update your preferences. Please contact a HR officer"
" to update other information."
msgstr ""
"شما فقط مجاز به بروزرسانی ترجیحات خود هستید. لطفا برای بروز رسانی اطلاعات "
"دیگران با یک مدیر منابع انسانی تماس بگیرید."

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/store_service_patch.js:0
msgid "You can only chat with employees that have a dedicated user."
msgstr "شما فقط می توانید با کارمندانی که یک کاربر اختصاصی دارند چت کنید."

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_department.py:0
msgid "You cannot create recursive departments."
msgstr "شما نمی توانید دپارتمان‌های بازگشتی (تو در تو) ایجاد کنید."

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "You do not have access to this document."
msgstr "شما به این سند دسترسی ندارید."

#. module: hr
#: model_terms:hr.job,description:hr.job_trainee
msgid ""
"You participate to the update of our tutorial tools and pre-sales tools after the launch of a new version of Odoo. Indeed, any new version of the software brings significant improvements in terms of functionalities, ergonomics and configuration.\n"
"You will have to become familiar with the existing tools (books, class supports, Odoo presentation’s slides, commercial tools),\n"
"to participate to the update of those tools in order to make them appropriate for the new version of the software and, for sure,\n"
"to suggest improvements in order to cover the new domains of the software.\n"
"You join the Implementation Assistance department. This team of 3 people go with Odoo’s clients in the set up of the software. Your role will be\n"
"to animate webinars in order to show the different functionalities of the software.\n"
"to be involved in the support of the customers and\n"
"to answer to their questions.\n"
"You help the support manager to set up new support services by\n"
"being involved in the treatment of new cases,\n"
"contributing to the set up of a new politic,\n"
"being involved into satisfaction surveys in order to have a better knowledge of how the support given is seen by the customers."
msgstr ""
"شما در به‌روزرسانی ابزارهای آموزشی و پیش‌فروش ما پس از راه‌اندازی نسخه جدیدی از Odoo مشارکت می‌کنید. واقعاً، هر نسخه جدیدی از نرم‌افزار بهبودهای قابل توجهی در زمینه عملکردها، ارگونومی و پیکربندی به همراه دارد.\n"
"شما باید با ابزارهای موجود (کتاب‌ها، پشتیبانی‌های کلاس، اسلایدهای ارائه Odoo، ابزارهای تجاری) آشنا شوید،\n"
"در به‌روزرسانی این ابزارها برای مناسب‌سازی آن‌ها برای نسخه جدید نرم‌افزار مشارکت کنید و، مطمئناً،\n"
"پیشنهادهایی برای بهبود جهت پوشش دامنه‌های جدید نرم‌افزار ارائه دهید.\n"
"شما به بخش کمک به پیاده‌سازی ملحق می‌شوید. این تیم ۳ نفره به مشتریان Odoo در راه‌اندازی نرم‌افزار کمک می‌کند. نقش شما این خواهد بود\n"
".وبینارها را برای نشان دادن عملکردهای مختلف نرم‌افزار برگزاری کنید\n"
".در پشتیبانی مشتریان درگیر شوید و\n"
".به سوالات آنها پاسخ دهید\n"
"شما به مدیر پشتیبانی در راه‌اندازی خدمات پشتیبانی جدید با\n"
".درگیر شدن در رسیدگی به موارد جدید،\n"
".سهم‌گذاری در راه‌اندازی یک سیاست جدید،\n"
".و درگیر شدن در نظرسنجی‌های رضایت به منظور آگاهی بهتر از چگونگی دیده شدن پشتیبانی ارائه‌شده توسط مشتریان کمک می‌کنید"

#. module: hr
#: model_terms:hr.job,description:hr.job_cto
msgid ""
"You will take part in the consulting services we provide to our partners and customers: design, analysis, development, testing, project management, support/coaching. You will work autonomously as well as coordinate and supervise small distributed development teams for some projects. Optionally, you will deliver Odoo training sessions to partners and customers (8-10 people/session). You will report to the Head of Professional Services and work closely with all developers and consultants.\n"
"\n"
"The job is located in Grand-Rosière (1367), Belgium (between Louvain-La-Neuve and Namur)."
msgstr ""
"شما در خدمات مشاوره‌ای که به شرکا و مشتریان خود ارائه می‌دهیم، مشارکت خواهید کرد: طراحی، تحلیل، توسعه، آزمایش، مدیریت پروژه، پشتیبانی/مربی‌گری. شما به صورت مستقل کار خواهید کرد و همچنین تیم‌های توسعه کوچکی که در پراکندگی قرار دارند را هماهنگ و نظارت خواهید کرد. به صورت اختیاری، شما جلسات آموزشی اودو را به شرکا و مشتریان (8-10 نفر در هر جلسه) ارائه خواهید کرد. شما به رئیس خدمات حرفه‌ای گزارش خواهید داد و به طور نزدیک با تمامی توسعه‌دهندگان و مشاوران همکاری خواهید کرد.\n"
"\n"
"این کار در Grand-Rosière (1367)، بلژیک (بین Louvain-La-Neuve و Namur) قرار دارد."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "ZIP"
msgstr "کد پستی"

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_alias.py:0
msgid "addresses linked to registered employees"
msgstr "مکان‌هایی که با کارمندان ثبت‌شده مرتبط هستند"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
msgid "department"
msgstr "اداره"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "e.g. Building 2, Remote, etc."
msgstr "مثلا ساختمان2، دورکار، غیره"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
msgid "e.g. John Doe"
msgstr "برای مثال محمد احمدی"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "e.g. Sales Manager"
msgstr "برای مثال مدیر فروش"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "e.g. Summarize the position in one or two lines..."
msgstr "به عنوان مثال موقعیت را در یک یا دو خط خلاصه کنید..."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "e.g. <EMAIL>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "e.g. <EMAIL>"
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__distance_home_work_unit__kilometers
msgid "km"
msgstr "کیلومتر"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__distance_home_work_unit__miles
msgid "mi"
msgstr "mi"

#. module: hr
#. odoo-python
#: code:addons/hr/models/models.py:0
msgid "restricted to employees"
msgstr "محدود به کارکنان"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_permit_name
msgid "work_permit_name"
msgstr "نام_اجازه_کار"
