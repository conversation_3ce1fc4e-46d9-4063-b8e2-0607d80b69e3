<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <!-- Specialty -->
    <record id="view_physician_specialty_tree" model="ir.ui.view">
        <field name="name">physician.specialty.tree</field>
        <field name="model">physician.specialty</field>
        <field name="arch" type="xml">
            <tree string="Medicament Specialty">
                <field name="name"/>
            </tree>
        </field>
    </record>

    <record id="view_acs_physician_specialty_form" model="ir.ui.view">
        <field name="name">physician.specialty.form</field>
        <field name="model">physician.specialty</field>
        <field name="arch" type="xml">
            <form string="Medicament Specialtys">
                <sheet>
                    <div class="oe_title">
                        <label for="name" string="Name" class="oe_edit_only"/>
                        <h1>
                            <field name="name" placeholder="Medicament Specialty"/>
                        </h1>
                    </div>
                </sheet>
            </form>
        </field>
    </record>

    <record model="ir.actions.act_window" id="action_physician_specialty">
        <field name="name">Specialty</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">physician.specialty</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No Record Found
            </p>
            <p>
                Click to add a Specialty.
            </p>
        </field>
    </record>

    <!-- Physician Degree -->
    <record id="view_physician_degree_tree" model="ir.ui.view">
        <field name="name">physician.degree.tree</field>
        <field name="model">physician.degree</field>
        <field name="arch" type="xml">
            <tree string="Physician Degree">
                <field name="name"/>
            </tree>
        </field>
    </record>

    <record id="view_acs_physician_degree_form" model="ir.ui.view">
        <field name="name">physician.degree.form</field>
        <field name="model">physician.degree</field>
        <field name="arch" type="xml">
            <form string="Physician Degree">
                <sheet>
                    <div class="oe_title">
                        <label for="name" string="Name" class="oe_edit_only"/>
                        <h1>
                            <field name="name" placeholder="MBBS"/>
                        </h1>
                    </div>
                </sheet>
            </form>
        </field>
    </record>

    <record model="ir.actions.act_window" id="action_physician_degree">
        <field name="name">Physician Degree</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">physician.degree</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No Record Found
            </p>
            <p>
                Click to add a Physician Degree.
            </p>
        </field>
    </record>

    <!-- physician -->
    <record id="view_physician_tree" model="ir.ui.view">
        <field name="name">physician.tree</field>
        <field name="model">hms.physician</field>
        <field name="arch" type="xml">
            <tree string="Physician">
                <field name="name"/>
                <field name="code" string="Doctor ID"/>
                <field name="medical_license"/>
                <field name="mobile"/>
                <field name="email"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <field name="active" invisible="1"/>
            </tree>
        </field>
    </record>

    <record model="ir.ui.view" id="physician_kanban_view">
        <field name="name">physician.kanban</field>
        <field name="model">hms.physician</field>
        <field name="type">kanban</field>
        <field name="arch" type="xml">
            <kanban string="Physician">
                <field name="id"/>
                <field name="name"/>
                <field name="image_128"/>
                <field name="specialty_id"/>
                <field name="degree_ids"/>
                <field name="code"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_global_click o_kanban_record_has_image_fill">
                            <t t-set="placeholder" t-value="'/base/static/img/avatar_grey.png'"/>
                            <div class="o_kanban_image_fill_left d-none d-md-block" t-attf-style="background-image:url('#{kanban_image('hms.physician', 'image_128', record.id.raw_value, placeholder)}')"/>
                            <div class="oe_kanban_details">
                                <div class="o_kanban_record_top">
                                    <strong class="oe_partner_heading"><field name="display_name"/></strong>
                                    <div class="o_kanban_record_headings" id="dr_heading">
                                    </div>
                                </div>
                                <field name="degree_ids" widget="many2many_tags"/>
                                <ul id="code_ul">
                                    <li><b>Code:</b> <field name="code"/></li>
                                    <li t-if="record.specialty_id.raw_value"><b>Specialty:</b> <field name="specialty_id"/></li>
                                </ul>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="view_physician_form" model="ir.ui.view">
        <field name="name">physician.form</field>
        <field name="model">hms.physician</field>
        <field name="arch" type="xml">
            <form string="Physician">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                    </div>
                    <field name="image_1920" widget="image" class="oe_avatar" options="{'preview_image': 'image_128'}"/>
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" invisible="active"/>
                    <div class="oe_title">
                        <label for="name" string="Name" class="oe_edit_only"/>
                        <h1>
                            <field name="name" placeholder="Doctor's Name"/>
                        </h1>
                        <div>
                            <label for="degree_ids" string="Education" class="oe_edit_only"/>
                            <field name="degree_ids" widget="many2many_tags" placeholder="e.g. MBBS"/>
                        </div>
                    </div>
                    <group>
                        <group>
                            <field name="code" string="Doctor ID" style="width: 20%%" readonly="1"/>
                            <field name="medical_license"/>
                            <field name="user_id" required="False" invisible="1"/>
                            <field name="active" invisible="1"/>
                        </group>
                        <group>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                            <field name="specialty_id" required="True"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="General Information" name="gneral_info">
                            <group>
                                <group>
                                    <field name="type" invisible="not parent_id" groups="base.group_no_one"/>
                                    <label for="street" string="Address"/>
                                    <div>
                                        <field name="parent_id" invisible="1"/>
                                        <field name="street" placeholder="Street..." readonly="type == 'contact' and parent_id"/>
                                        <field name="street2" readonly="type == 'contact' and parent_id"/>
                                        <div class="address_format">
                                            <field name="city" placeholder="City" style="width: 40%%" readonly="type == 'contact' and parent_id"/>
                                            <field name="state_id" class="oe_no_button" placeholder="State" style="width: 37%%" options="{&quot;no_open&quot;: True}" readonly="type == 'contact' and parent_id"/>
                                            <field name="zip" placeholder="ZIP" style="width: 20%%" readonly="type == 'contact' and parent_id"/>
                                        </div>
                                        <field name="country_id" placeholder="Country" class="oe_no_button" options="{&quot;no_open&quot;: True}" readonly="type == 'contact' and parent_id"/>
                                    </div>
                                    <field name="website"/>
                                </group>
                                <group>
                                    <field name="phone" placeholder="e.g. +506 5555 5555" widget="phone"/>
                                    <field name="mobile" widget="phone"/>
                                    <field name="user_ids" invisible="1"/>
                                    <field name="email" widget="email" required="1"/>
                                    <field name="vat"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="activity_ids" widget="mail_activity"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <record id="view_physician_search" model="ir.ui.view">
        <field name="name">physician.search</field>
        <field name="model">hms.physician</field>
        <field name="arch" type="xml">
            <search string="Physician">
                <field name="name"/>
                <field name="code"/>
                <field name="specialty_id"/>
                <field name="mobile"/>
                <field name="medical_license"/>
                <field name="company_id"/>
                <field name="email"/>
                <separator/>
                <filter string="All" name="all" domain="['|', ('active', '=', False), ('active', '=', True)]"/>
                <separator/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By...">
                    <filter string="Company" name="company_group" domain="[]" context="{'group_by':'company_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_physician" model="ir.actions.act_window">
        <field name="name">Physician</field>
        <field name="res_model">hms.physician</field>
        <field name="view_mode">kanban,tree,form,search</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No Record Found
            </p>
            <p>
                Click to add a Physician.
            </p>
        </field>
    </record>

</odoo>