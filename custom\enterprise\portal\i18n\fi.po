# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* portal
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <ka<PERSON><PERSON>l<PERSON><PERSON>@emsystems.fi>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# Si<PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <jarmo.kortet<PERSON>@gmail.com>, 2024
# <AUTHOR> <EMAIL>, 2024
# Mikko <PERSON>mela <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 13:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "\" to validate your action."
msgstr "\" vahvistamaan toimintasi."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_sidebar.js:0
msgid "%s days overdue"
msgstr "%s päivää yli määräajan"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "1. Enter your password to confirm you own this account"
msgstr "1. Kirjoita salasanasi vahvistaaksesi, että omistat tämän tilin"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid ""
"2. Confirm you want to delete your account by\n"
"                                        copying down your login ("
msgstr ""
"2. Vahvista, että haluat poistaa tilisi\n"
"                                        kopioimalla tähän kenttään käyttäjätunnuksesi ("

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.my_account_link
msgid ""
"<i class=\"fa fa-fw fa-id-card-o me-1 small text-primary text-primary-"
"emphasis\"/> My Account"
msgstr ""
"<i class=\"fa fa-fw fa-id-card-o me-1 small text-primary text-primary-"
"emphasis\"/> Oma tili"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.user_dropdown
msgid ""
"<i class=\"fa fa-fw fa-sign-out me-1 small text-primary text-primary-"
"emphasis\"/> Logout"
msgstr ""
"<i class=\"fa fa-fw fa-sign-out me-1 small text-primary text-primary-"
"emphasis\"/> Kirjaudu ulos"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.user_dropdown
msgid ""
"<i class=\"fa fa-fw fa-th me-1 small text-primary text-primary-emphasis\"/> "
"Apps"
msgstr ""
"<i class=\"fa fa-fw fa-th me-1 small text-primary text-primary-emphasis\"/> "
"Sovellukset"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.side_content
msgid "<i class=\"fa fa-pencil\"/> Edit information"
msgstr "<i class=\"fa fa-pencil\"/> Muokkaa tietoja"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
msgid "<i class=\"oi oi-arrow-right me-1\"/>Back to edit mode"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>Takaisin muokkaustilaan"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.record_pager
msgid ""
"<i class=\"oi oi-chevron-left\" role=\"img\" aria-label=\"Previous\" "
"title=\"Previous\"/>"
msgstr ""
"<i class=\"oi oi-chevron-left\" role=\"img\" aria-label=\"Edellinen\" "
"title=\"Edellinen\"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.record_pager
msgid "<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"Next\" title=\"Next\"/>"
msgstr ""
"<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"Seuraava\" "
"title=\"Seuraava\"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>"
msgstr "<i title=\"Dokumentaatio\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">Maa...</option>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "<option value=\"\">select...</option>"
msgstr "<option value=\"\">valitse...</option>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid ""
"<small class=\"form-text text-muted\">\n"
"                Company name, VAT Number and country can not be changed once document(s) have been issued for your account.\n"
"                <br/>Please contact us directly for that operation.\n"
"            </small>"
msgstr ""
"<small class=\"form-text text-muted\">\n"
"                Yrityksen nimeä, ALV-numeroa ja maata ei voi muuttaa sen jälkeen, kun tilille on kirjattu tapahtumia.\n"
"               <br/>Ota yhteyttä suoraan meihin tätä toimintoa varten.\n"
"            </small>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.pager
msgid ""
"<span class=\"fa fa-chevron-left\" role=\"img\" aria-label=\"Previous\" "
"title=\"Previous\"/>"
msgstr ""
"<span class=\"fa fa-chevron-left\" role=\"img\" aria-label=\"Edellinen\" "
"title=\"Edellinen\"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.pager
msgid "<span class=\"fa fa-chevron-right\" role=\"img\" aria-label=\"Next\" title=\"Next\"/>"
msgstr ""
"<span class=\"fa fa-chevron-right\" role=\"img\" aria-label=\"Seuraava\" "
"title=\"Seuraava\"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small me-1 navbar-text\">Filter By:</span>"
msgstr "<span class=\"small me-1 navbar-text\">Suodata:</span>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small me-1 navbar-text\">Group By:</span>"
msgstr "<span class=\"small me-1 navbar-text\">Ryhmittele:</span>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small me-1 navbar-text\">Sort By:</span>"
msgstr "<span class=\"small me-1 navbar-text\">Lajittelu:</span>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "<strong>Open </strong>"
msgstr "<strong>Avoin </strong>"

#. module: portal
#: model:mail.template,body_html:portal.mail_template_data_portal_welcome
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Account</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.user_id.name or ''\">Marc Demo</span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.user_id.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.user_id.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.user_id.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"object.user_id.name or ''\">Marc Demo</t>,<br/> <br/>\n"
"                        Welcome to <t t-out=\"object.user_id.company_id.name\">YourCompany</t>'s Portal!<br/><br/>\n"
"                        An account has been created for you with the following login: <t t-out=\"object.user_id.login\">demo</t><br/><br/>\n"
"                        Click on the button below to pick a password and activate your account.\n"
"                        <div style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"                            <a t-att-href=\"object.user_id.partner_id._get_signup_url()\" style=\"display: inline-block; padding: 10px; text-decoration: none; font-size: 12px; background-color: #875A7B; color: #fff; border-radius: 5px;\">\n"
"                                <strong>Activate Account</strong>\n"
"                            </a>\n"
"                        </div>\n"
"                        <t t-out=\"object.wizard_id.welcome_message or ''\">Welcome to our company's portal.</t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.user_id.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.user_id.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.user_id.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.user_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.user_id.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.user_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=portalinvite\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Tilisi</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.user_id.name or ''\">Marc Demo</span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.user_id.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.user_id.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.user_id.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Hyvä <t t-out=\"object.user_id.name or ''\">Marc Demo</t>,<br/> <br/>\n"
"                        Tervetuloa <t t-out=\"object.user_id.company_id.name\">YourCompany</t>'s Portaliin!<br/><br/>\n"
"                        Sinulle on luotu tili seuraavilla tunnuksilla: <t t-out=\"object.user_id.login\">Demo</t><br/><br/>\n"
"                        Klikkaa alla olevaa painiketta valitaksesi salasanan ja aktivoidaksesi tilisi.\n"
"                        <div style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"                            <a t-att-href=\"object.user_id.partner_id._get_signup_url()\" style=\"display: inline-block; padding: 10px; text-decoration: none; font-size: 12px; background-color: #875A7B; color: #fff; border-radius: 5px;\">\n"
"                                <strong>Aktivoi tili</strong>\n"
"                            </a>\n"
"                        </div>\n"
"                       <t t-out=\"object.wizard_id.welcome_message or ''\">Tervetuloa yrityksemme portaaliin.</t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.user_id.company_id.name or ''\">Yrityksesi</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.user_id.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.user_id.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.user_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.user_id.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.user_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Voimana <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=portalinvite\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: portal
#: model:ir.model,name:portal.model_res_users_apikeys_description
msgid "API Key Description"
msgstr "API-avaimen kuvaus"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
msgid "API Key Ready"
msgstr "API-avain valmis"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/signature_form/signature_form.js:0
msgid "Accept & Sign"
msgstr "Hyväksy & allekirjoita"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_warning
#: model:ir.model.fields,field_description:portal.field_portal_share__access_warning
msgid "Access warning"
msgstr "Käyttöoikeusvaroitus"

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Account deleted!"
msgstr "Tili poistettu!"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Add a note"
msgstr "Lisää tekstirivi"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Add attachment"
msgstr "Lisää liite"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Add contacts to share the document..."
msgstr "Lisää kontaktit, joille dokumentti jaetaan..."

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_share__note
msgid "Add extra content to display in the email"
msgstr "Lisää sisältöä joka näytetään sähköpostissa"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Added On"
msgstr "Lisätty"

#. module: portal
#: model:ir.model.fields.selection,name:portal.selection__portal_wizard_user__email_state__exist
msgid "Already Registered"
msgstr "On jo rekisteröitynyt"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Are you sure you want to do this?"
msgstr "Haluatko varmasti tehdä tämän?"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Avatar"
msgstr "Avatar"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Cancel"
msgstr "Peruuta"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Change Password"
msgstr "Vaihda salasana"

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid ""
"Changing VAT number is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"ALV-numeron muuttaminen ei ole sallittua sen jälkeen kun dokumentti on "
"liitetty tiliisi. Jos tämä toiminto täytyy tehdä, ota meihin yhteyttä."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid ""
"Changing company name is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"Yrityksen nimen muuttaminen ei ole sallittua sen jälkeen kun dokumentti on "
"liitetty tiliisi. Jos tämä toiminto täytyy tehdä, ota meihin yhteyttä."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid ""
"Changing the country is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"Maan vaihtaminen ei ole sallittua sen jälkeen, kun asiakirjoja on kirjattu "
"tiliisi. Ota yhteyttä suoraan meihin tätä toimenpidettä varten."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
msgid "Check failed"
msgstr "Tarkistus epäonnistui"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "City"
msgstr "Kaupunki"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/signature_form/signature_form.xml:0
msgid "Click here to see your document."
msgstr "Paina tästä nähdäksesi dokumenttisi."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
#: model_terms:ir.ui.view,arch_db:portal.side_content
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Close"
msgstr "Sulje"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Company Name"
msgstr "Yrityksen nimi"

#. module: portal
#: model:ir.model,name:portal.model_res_config_settings
msgid "Config Settings"
msgstr "Asetukset"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_home
msgid "Configure your connection parameters"
msgstr "Määrittele kuinka haluat kirjautua"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
msgid "Confirm"
msgstr "Vahvista"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
msgid "Confirm Password"
msgstr "Vahvista salasana"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_home
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Connection &amp; Security"
msgstr "Yhteys &amp; Turvallisuus"

#. module: portal
#: model:ir.model,name:portal.model_res_partner
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__partner_id
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
#: model_terms:ir.ui.view,arch_db:portal.portal_my_contact
#: model_terms:ir.ui.view,arch_db:portal.side_content
msgid "Contact"
msgstr "Kontakti"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Contact Details"
msgstr "Yhteystiedot"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Contacts"
msgstr "Yhteystiedot"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_composer.js:0
msgid "Could not save file <strong>%s</strong>"
msgstr "Tallentaminen epäonnistui tiedostolle <strong>%s</strong>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Country"
msgstr "Maa"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__create_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard__create_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__create_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard__create_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__create_date
msgid "Created on"
msgstr "Luotu"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_res_config_settings__portal_allow_api_keys
msgid "Customer API Keys"
msgstr "Asiakkaan API-avaimet"

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_mixin__access_url
msgid "Customer Portal URL"
msgstr "Asiakasportaalin osoite"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.res_config_settings_view_form
msgid "Customers can generate API Keys"
msgstr "Asiakkaat voivat luoda API-avaimia"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "Dear"
msgstr "Hyvä"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Delete"
msgstr "Poista"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Delete Account"
msgstr "Poista käyttäjätunnus"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Description"
msgstr "Kuvaus"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_breadcrumbs
msgid "Details"
msgstr "Tiedot"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Developer API Keys"
msgstr "Kehittäjän API-avaimet"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid ""
"Disable your account, preventing any further login.<br/>\n"
"                                        <b>\n"
"                                            <i class=\"fa fa-exclamation-triangle text-danger\"/>\n"
"                                            This action cannot be undone.\n"
"                                        </b>"
msgstr ""
"Poista tilisi käytöstä, mikä estää uuden kirjautumisen.<br/>\n"
"                                       <b> \n"
"                                           <i class=\"fa fa-exclamation-triangle text-danger\"/>\n"
"                                            Tätä toimenpidettä ei voi perua.\n"
"                                       </b>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Discard"
msgstr "Hylkää"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__display_name
#: model:ir.model.fields,field_description:portal.field_portal_wizard__display_name
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_sidebar.js:0
msgid "Due in %s days"
msgstr "Erääntyy %s päivässä"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_sidebar.js:0
msgid "Due today"
msgstr "Eräpäivä tänään"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__email
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Email"
msgstr "Sähköposti"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Email Address already taken by another user"
msgstr "Sähköpostiosoite on jo toisen käyttäjän käytössä"

#. module: portal
#: model:ir.model,name:portal.model_mail_thread
msgid "Email Thread"
msgstr "Sähköpostiviestiketju"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Enter a description of and purpose for the key."
msgstr "Kirjoita avaimen kuvaus ja tarkoitus."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Expiration Date"
msgstr "Viim. voimassaolo"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Forgot password?"
msgstr "Unohtuiko salasana?"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Give a duration for the key's validity"
msgstr "Anna avaimen voimassaoloaika"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Go Back"
msgstr "Takaisin"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Grant Access"
msgstr "Anna pääsy"

#. module: portal
#: model:ir.model,name:portal.model_portal_wizard
msgid "Grant Portal Access"
msgstr "Anna pääsy portaaliin"

#. module: portal
#: model:ir.actions.act_window,name:portal.partner_wizard_action
#: model:ir.actions.server,name:portal.partner_wizard_action_create_and_open
msgid "Grant portal access"
msgstr "Anna pääsy asiakasportaaliin"

#. module: portal
#: model:ir.model,name:portal.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-reititys"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid ""
"Here is your new API key, use it instead of a password for RPC access.\n"
"                Your login is still necessary for interactive usage."
msgstr ""
"Tässä on uusi API-avaimesi, käytä sitä salasanan sijasta RPC-yhteyttä varten.\n"
"                Sisäänkirjautuminen on edelleen tarpeen interaktiiviseen käyttöön."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_breadcrumbs
msgid "Home"
msgstr "Etusivu"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__id
#: model:ir.model.fields,field_description:portal.field_portal_wizard__id
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__id
msgid "ID"
msgstr "ID"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Important:"
msgstr "Tärkeää:"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Internal User"
msgstr "Sisäinen käyttäjä"

#. module: portal
#: model:ir.model.fields.selection,name:portal.selection__portal_wizard_user__email_state__ko
msgid "Invalid"
msgstr "Virheellinen"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Invalid Email Address"
msgstr "Virheellinen sähköpostiosoite"

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Invalid Email! Please enter a valid email address."
msgstr "Sähköpostiosoite on virheellinen. Anna oikean muotoinen osoite."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Invalid report type: %s"
msgstr "Virheellinen raportin tyyppi: %s"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__welcome_message
msgid "Invitation Message"
msgstr "Kutsuviesti"

#. module: portal
#: model:mail.template,description:portal.mail_template_data_portal_welcome
msgid "Invitation email to contacts to create a user account"
msgstr "Kutsu sähköpostitse yhteystietoja käyttäjätilin luomista varten"

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_share.py:0
msgid "Invitation to access %s"
msgstr "Kutsu pääsylle %s"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__is_internal
msgid "Is Internal"
msgstr "Sisäinen"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__is_portal
msgid "Is Portal"
msgstr "Portaali"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid ""
"It is very important that this description be clear\n"
"                and complete,"
msgstr ""
"On erittäin tärkeää, että tämä kuvaus on selkeä\n"
"                ja täydellinen,"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Key Description"
msgstr "Avaimen kuvaus"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__write_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard__write_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__write_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard__write_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__login_date
msgid "Latest Authentication"
msgstr "Viimeisin todennus"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Leave a comment"
msgstr "Kirjoita kommentti"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__share_link
msgid "Link"
msgstr "Linkki"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Log out from all devices"
msgstr "Kirjaudu ulos kaikista laitteista"

#. module: portal
#: model:ir.model,name:portal.model_mail_message
msgid "Message"
msgstr "Viesti"

#. module: portal
#. odoo-python
#: code:addons/portal/models/mail_thread.py:0
msgid ""
"Model %(model_name)s does not support token signature, as it does not have "
"%(field_name)s field."
msgstr ""
"Malli %(model_name)s ei tue tunnuksen allekirjoitusta, sillä sillä ei ole "
"%(field_name)s kenttää."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Multi company reports are not supported."
msgstr "Usean yrityksen raportteja ei tueta."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
msgid "My account"
msgstr "Oma tili"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Name"
msgstr "Nimi"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Name your key"
msgstr "Nimeä avaimesi"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "New API Key"
msgstr "Uusi API-avain"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "New Password:"
msgstr "Uusi salasana:"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Next"
msgstr "Seuraava"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__note
msgid "Note"
msgstr "Muistiinpano"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "Odoo Logo"
msgstr "Odoo Logo"

#. module: portal
#. odoo-python
#: code:addons/portal/models/res_users_apikeys_description.py:0
msgid "Only internal and portal users can create API keys"
msgstr ""
"Vain sisäiset käyttäjät ja portaalin käyttäjät voivat luoda API-avaimia"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Oops! Something went wrong. Try to reload the page and log in."
msgstr "Oho! Jotain meni vikaa. Kokeile ladata sivu uudelleen ja kirjautua."

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__partner_ids
msgid "Partners"
msgstr "Kumppanit"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Password"
msgstr "Salasana"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Password Updated!"
msgstr "Salasana päivitetty!"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Password:"
msgstr "Salasana:"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Phone"
msgstr "Puhelin"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Please enter your password to confirm you own this account"
msgstr "Anna salasanasi vahvistaaksesi, että omistat tämän tilin"

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Portal Access Management"
msgstr "Portaalin käyttöoikeuksien hallinta"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_url
msgid "Portal Access URL"
msgstr "Portaalin osoite"

#. module: portal
#: model:ir.model,name:portal.model_portal_mixin
msgid "Portal Mixin"
msgstr "Portal Mixin"

#. module: portal
#: model:ir.model,name:portal.model_portal_share
msgid "Portal Sharing"
msgstr "Jako portaalissa"

#. module: portal
#: model:ir.model,name:portal.model_portal_wizard_user
msgid "Portal User Config"
msgstr "Portaalin käyttäjän määritykset"

#. module: portal
#: model:mail.template,name:portal.mail_template_data_portal_welcome
msgid "Portal: User Invite"
msgstr "Portaali: Kutsu käyttäjä"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "Powered by"
msgstr "Järjestelmää pyörittää"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Previous"
msgstr "Edellinen"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid ""
"Put my email and phone in a block list to make sure I'm never contacted "
"again"
msgstr ""
"Laita sähköpostini ja puhelinnumeroni markkinointikieltolistalle, jotta "
"minuun ei enää oteta yhteyttä"

#. module: portal
#: model:ir.model,name:portal.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Re-Invite"
msgstr "Kutsu uudelleen"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__partner_ids
msgid "Recipients"
msgstr "Vastaanottajat"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__resource_ref
msgid "Related Document"
msgstr "Aiheeseen liittyvä asiakirja(t)"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__res_id
msgid "Related Document ID"
msgstr "Liittyvä dokumentti ID"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__res_model
msgid "Related Document Model"
msgstr "Liittyvä dokumenttimalli"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Revoke Access"
msgstr "Evää pääsy"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Revoke All Sessions"
msgstr "Peruuta kaikki istunnot"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Save"
msgstr "Tallenna"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Scope"
msgstr "Laajuus"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "Search"
msgstr "Hae"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Security"
msgstr "Tietoturva"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_security.js:0
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Security Control"
msgstr "Suojaustarkastus"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_token
msgid "Security Token"
msgstr "Turvatunnus"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid ""
"Select which contacts should belong to the portal in the list below.\n"
"                        The email address of each selected contact must be valid and unique.\n"
"                        If necessary, you can fix any contact's email address directly in the list."
msgstr ""
"Valitse alla olevasta luettelosta kontaktit, joiden pitäisi kuulua portaaliin.\n"
"                        Sähköpostiosoite valitulle kontaktille pitää olla voimassa oleva ja ainutkertainen.\n"
"                        Jos tarpeen, voit korjata kontaktin sähköpostiosoitteen suoraan luetteloon."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_composer.js:0
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Send"
msgstr "Lähetä"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_contact
msgid "Send message"
msgstr "Lähetä viesti"

#. module: portal
#: model:ir.actions.act_window,name:portal.portal_share_action
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Share Document"
msgstr "Jaa dokumentti"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_ir_ui_view__customize_show
msgid "Show As Optional Inherit"
msgstr "Näytä valinnaisena periytymisenä"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.user_sign_in
msgid "Sign in"
msgstr "Kirjaudu sisään"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.frontend_layout
msgid "Skip to Content"
msgstr "Siirry sisältöön"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/js/portal_composer.js:0
msgid ""
"Some fields are required. Please make sure to write a message or attach a "
"document"
msgstr ""
"Jotkut kentät ovat pakollisia. Muista kirjoittaa viesti tai liittää "
"asiakirja"

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "Some required fields are empty."
msgstr "Täytä kaikki vaaditut kentät."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "State / Province"
msgstr "Osavaltio / provinssi"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__email_state
msgid "Status"
msgstr "Tila"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Street"
msgstr "Katuosoite"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Street 2"
msgstr "Osoite 2"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/signature_form/signature_form.xml:0
msgid "Thank You!"
msgstr "Kiitos!"

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "The attachment %s cannot be removed because it is linked to a message."
msgstr "Liitettä %s ei voi poistaa, koska se on liitetty viestiin."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid ""
"The attachment %s cannot be removed because it is not in a pending state."
msgstr "Liitettä %s ei voi poistaa, koska se ei ole odottavassa tilassa."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid ""
"The attachment does not exist or you do not have the rights to access it."
msgstr "Liitettä ei ole olemassa, tai sinulla ei ole oikeuksia nähdä sitä."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "The contact \"%s\" does not have a valid email."
msgstr "Yhteystiedolla \"%s\" ei ole kelvollista sähköpostiosoitetta."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "The contact \"%s\" has the same email as an existing user"
msgstr ""
"Yhteyshenkilöllä \"%s\" on sama sähköpostiosoite kuin olemassa olevalla "
"käyttäjällä"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "The key cannot be retrieved later and provides"
msgstr "Avainta ei voi hakea myöhemmin, ja se tarjoaa"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "The key will be deleted once this period has elapsed."
msgstr "Avain poistetaan, kun tämä aika on kulunut."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "The new password and its confirmation must be identical."
msgstr "Uusi salasana sekä vahvistus tulee olla samat."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid ""
"The old password you provided is incorrect, your password was not changed."
msgstr "Antamasi vanha salasana on väärä. Salasanaa ei vaihdettu."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "The partner \"%s\" already has the portal access."
msgstr "Kumppannilla \"%s\" on jo portaalin käyttöoikeus."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "The partner \"%s\" has no portal access or is internal."
msgstr "Kumppanilla \"%s\" ei ole portaaliyhteyttä tai se on sisäinen."

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid ""
"The template \"Portal: new user\" not found for sending email to the portal "
"user."
msgstr ""
"Mallipohjaa \"Portaali: uusi käyttäjä\" ei löytynyt sähköpostin lähettämistä"
" varten portaalin käyttäjälle."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "This document does not exist."
msgstr "Tätä dokumenttia ei ole olemassa."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
msgid "This is a preview of the customer portal."
msgstr "Tämä on esikatselunäkymä asiakasportaalissa."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid ""
"This partner is linked to an internal User and already has access to the "
"Portal."
msgstr ""
"Tämä kumppani on linkitetty sisäiseen käyttäjään jolla on jo pääsy "
"portaaliin."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid ""
"This text is included at the end of the email sent to new portal users."
msgstr ""
"Tämä teksti on uusille portaalin käyttäjille lähetetyn sähköpostin lopussa."

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_wizard__welcome_message
msgid "This text is included in the email sent to new users of the portal."
msgstr ""
"Tämä teksti liitetään uudelle portaalikäyttäjälle lähetettävään "
"sähköpostiin."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "Toggle filters"
msgstr "Kytke suotimet"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__user_id
msgid "User"
msgstr "Käyttäjä"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__user_ids
msgid "Users"
msgstr "Käyttäjät"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "VAT Number"
msgstr "Y-tunnus"

#. module: portal
#: model:ir.model.fields.selection,name:portal.selection__portal_wizard_user__email_state__ok
msgid "Valid"
msgstr "Vahvistettu"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Valid Email Address"
msgstr "Voimassa oleva sähköpostiosoite"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Verify New Password:"
msgstr "Vahvista uusi salasana:"

#. module: portal
#: model:ir.model,name:portal.model_ir_ui_view
msgid "View"
msgstr "Näytä"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_account_analytic_account__website_message_ids
#: model:ir.model.fields,field_description:portal.field_calendar_event__website_message_ids
#: model:ir.model.fields,field_description:portal.field_crm_team__website_message_ids
#: model:ir.model.fields,field_description:portal.field_crm_team_member__website_message_ids
#: model:ir.model.fields,field_description:portal.field_discuss_channel__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle_log_contract__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle_log_services__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle_model__website_message_ids
#: model:ir.model.fields,field_description:portal.field_gamification_badge__website_message_ids
#: model:ir.model.fields,field_description:portal.field_gamification_challenge__website_message_ids
#: model:ir.model.fields,field_description:portal.field_iap_account__website_message_ids
#: model:ir.model.fields,field_description:portal.field_lunch_supplier__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_cc__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_main_attachment__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_phone__website_message_ids
#: model:ir.model.fields,field_description:portal.field_maintenance_equipment__website_message_ids
#: model:ir.model.fields,field_description:portal.field_maintenance_equipment_category__website_message_ids
#: model:ir.model.fields,field_description:portal.field_maintenance_request__website_message_ids
#: model:ir.model.fields,field_description:portal.field_phone_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_category__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_pricelist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_product__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_template__website_message_ids
#: model:ir.model.fields,field_description:portal.field_rating_mixin__website_message_ids
#: model:ir.model.fields,field_description:portal.field_res_partner__website_message_ids
#: model:ir.model.fields,field_description:portal.field_res_users__website_message_ids
msgid "Website Messages"
msgstr "Verkkosivun ilmoitukset"

#. module: portal
#: model:ir.model.fields,help:portal.field_account_analytic_account__website_message_ids
#: model:ir.model.fields,help:portal.field_calendar_event__website_message_ids
#: model:ir.model.fields,help:portal.field_crm_team__website_message_ids
#: model:ir.model.fields,help:portal.field_crm_team_member__website_message_ids
#: model:ir.model.fields,help:portal.field_discuss_channel__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle_log_contract__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle_log_services__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle_model__website_message_ids
#: model:ir.model.fields,help:portal.field_gamification_badge__website_message_ids
#: model:ir.model.fields,help:portal.field_gamification_challenge__website_message_ids
#: model:ir.model.fields,help:portal.field_iap_account__website_message_ids
#: model:ir.model.fields,help:portal.field_lunch_supplier__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_cc__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_main_attachment__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_phone__website_message_ids
#: model:ir.model.fields,help:portal.field_maintenance_equipment__website_message_ids
#: model:ir.model.fields,help:portal.field_maintenance_equipment_category__website_message_ids
#: model:ir.model.fields,help:portal.field_maintenance_request__website_message_ids
#: model:ir.model.fields,help:portal.field_phone_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_product_category__website_message_ids
#: model:ir.model.fields,help:portal.field_product_pricelist__website_message_ids
#: model:ir.model.fields,help:portal.field_product_product__website_message_ids
#: model:ir.model.fields,help:portal.field_product_template__website_message_ids
#: model:ir.model.fields,help:portal.field_rating_mixin__website_message_ids
#: model:ir.model.fields,help:portal.field_res_partner__website_message_ids
#: model:ir.model.fields,help:portal.field_res_users__website_message_ids
msgid "Website communication history"
msgstr "Verkkosivun viestihistoria"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "What's this key for?"
msgstr "Mitä varten tämä avain on?"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__wizard_id
msgid "Wizard"
msgstr "Ohjattu toiminto"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "Write a message..."
msgstr "Kirjoita viesti..."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/chatter/core/composer_patch.js:0
msgid "Write a message…"
msgstr "Kirjoita viesti.."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "Write down your key"
msgstr "Kirjoita ylös avain"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Wrong password."
msgstr "Väärä salasana."

#. module: portal
#. odoo-python
#: code:addons/portal/controllers/portal.py:0
msgid "You cannot leave any password empty."
msgstr "Et voi jättää salasanaa tyhjäksi."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "You must be"
msgstr "Sinun on oltava"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "You should enter \""
msgstr "Sinun pitäisi kirjoittaa \""

#. module: portal
#. odoo-python
#: code:addons/portal/wizard/portal_wizard.py:0
msgid "You should first grant the portal access to the partner \"%s\"."
msgstr "Sinun tulee ensin myöntää kumppanille portaalin käyttöoikeus \"%s\"."

#. module: portal
#: model:mail.template,subject:portal.mail_template_data_portal_welcome
msgid "Your account at {{ object.user_id.company_id.name }}"
msgstr "Tilisi {{ object.user_id.company_id.name }}"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_contact
msgid "Your contact"
msgstr "Kontaktinne"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details_fields
msgid "Zip / Postal Code"
msgstr "Postinumero"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "full access"
msgstr "täydet oikeudet"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "has invited you to access the following"
msgstr "on kutsunut sinut tutustumaan seuraaviin"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid ""
"it will be the only way to\n"
"                identify the key once created"
msgstr ""
"se on ainoa tapa\n"
"                tunnistaa avain, kun se on kerran luotu"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "logged in"
msgstr "kirjautunut sisään"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "odoo"
msgstr "odoo"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "password"
msgstr "salasana"

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
msgid "to post a comment."
msgstr "kirjoittaaksesi kommentin."

#. module: portal
#. odoo-javascript
#: code:addons/portal/static/src/xml/portal_security.xml:0
msgid "to your user account, it is very important to store it securely."
msgstr "käyttäjätilillesi, on erittäin tärkeää säilyttää se turvallisesti."
